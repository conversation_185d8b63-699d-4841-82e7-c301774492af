<template>
  <div>
    <div class="sl-card mb8">
      <sl-block-title>工单信息</sl-block-title>
      <sl-form
        v-if="form"
        :show-block-title="false"
        :label-width="140"
        ref="slFormRef"
        v-model="form"
        :options="options"
      >
        <template #enumText="{ item }">
          <EnumText :form-model="form" :fields="item" :dic-collection="dicCollection" />
        </template>
      </sl-form>
    </div>

    <div class="sl-card mb8">
      <sl-block-title>工单附件信息</sl-block-title>
      <sl-form
        :show-block-title="false"
        :label-width="140"
        ref="slFormRef"
        v-model="form"
        :options="options2"
      >
        <template #upload="{ item }">
          <!-- 上传文件 -->
          <SlUpload
            style="width: 100%"
            v-model:file-list="form[item.key]"
            :file-type="item.fileType"
            v-bind="item.props?.upload"
          />
        </template>
      </sl-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { maskPhoneNumber } from '@/utils'
import EnumText from '@/views/approvalCenter/components/EnumText.vue'
import { ref } from 'vue'

const form = ref<FormDataType>({
  resourceApplyFiles: [],
  manufacturerMobile: '',
})

const dicCollection = ref({})

const initData = (data: any) => {
  form.value = JSON.parse(JSON.stringify(data))
  form.value.manufacturerMobile = maskPhoneNumber(data['manufacturerMobile'])
}
const options = [
  {
    groupName: '工单信息',
    groupItems: [
      {
        label: '申请人',
        type: 'slot',
        slotName: 'enumText',
        keyName: 'createdUserName',
        span: 8,
      },
      {
        label: '所属部门',
        type: 'slot',
        slotName: 'enumText',
        keyName: 'departmentName',
        span: 8,
      },
      {
        label: '工单类型',
        type: 'slot',
        slotName: 'enumText',
        keyName: 'orderType',
        span: 8,
      },
      {
        label: '标题',
        type: 'slot',
        slotName: 'enumText',
        keyName: 'orderTitle',
        span: 8,
      },
      {
        label: '业务系统',
        type: 'slot',
        slotName: 'enumText',
        keyName: 'businessSystemName',
        span: 8,
      },
      {
        label: '所属业务模块',
        type: 'slot',
        slotName: 'enumText',
        keyName: 'moduleName',
        span: 8,
      },
      {
        label: '局方负责人',
        type: 'slot',
        slotName: 'enumText',
        keyName: 'bureauUserName',
        span: 8,
      },
      {
        label: '厂家',
        type: 'slot',
        slotName: 'enumText',
        keyName: 'manufacturer',
        span: 8,
      },
      {
        label: '厂家负责人',
        type: 'slot',
        slotName: 'enumText',
        keyName: 'manufacturerContacts',
        span: 8,
      },
      {
        label: '电话',
        type: 'slot',
        slotName: 'enumText',
        keyName: 'manufacturerMobile',
        span: 8,
      },

      {
        label: '二级业务部门领导',
        type: 'slot',
        slotName: 'enumText',
        keyName: 'businessDepartLeaderName',
        span: 8,
      },
      {
        label: '三级业务部门领导',
        type: 'slot',
        slotName: 'enumText',
        keyName: 'levelThreeLeaderName',

        span: 8,
      },
      {
        label: '资源变更说明',
        type: 'slot',
        slotName: 'enumText',
        keyName: 'orderDesc',
        span: 16,
      },
    ],
  },
]

const options2 = [
  {
    groupName: '工单附件信息',
    groupItems: [
      {
        label: '资源变更说明书',
        type: 'slot',
        slotName: 'upload',
        key: 'resourceApplyFiles',
        span: 24,
        props: {
          upload: {
            drag: false,
            fileType: 'RESOURCE_EXPLAIN',
            disabled: true,
            style: {
              'max-width': '500px',
            },
          },
        },
      },
    ],
  },
]
defineExpose({
  initData,
})
</script>

<style></style>
