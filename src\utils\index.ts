import type {
  EpP<PERSON>,
  EpPropConvert,
  EpPropFinalized,
  EpPropInput,
  EpPropMergeType,
  IfEpProp,
  IfNativePropType,
  NativePropType,
} from './types'
import { warn, type PropType, type Component } from 'vue'
import type { FieldNamesProps } from '@/components/SlProTable/interface'

export const definePropType = <T>(val: any): PropType<T> => val
export const iconPropType = definePropType<string | Component>([String, Object, Function])
const epPropKey = '__epPropKey'

export const isEpProp = (val: unknown): val is EpProp<any, any, any> =>
  isObject(val) && !!(val as any)[epPropKey]

/**
 * 将键值对数组转换为对象
 * @param entries 键值对数组
 * @returns 生成的对象
 *
 * @example
 * fromPairs([['a', 1], ['b', 2]]) // => { a: 1, b: 2 }
 * fromPairs([[1, 'one'], [2, 'two']]) // => { '1': 'one', '2': 'two' }
 */
export function fromPairs<TValue, TKey extends PropertyKey>(
  entries: Iterable<readonly [TKey, TValue]>,
): Record<TKey, TValue> {
  const result = {} as Record<TKey, TValue>

  for (const entry of entries) {
    // 过滤无效条目
    if (!entry || !Array.isArray(entry) || entry.length < 2) continue

    const [key, value] = entry

    // 处理符号类型键
    if (typeof key === 'symbol') {
      result[key as TKey] = value
    } else {
      // 非符号键统一转换为字符串
      const validKey = String(key) as TKey
      result[validKey] = value
    }
  }

  return result
}

const hasOwnProperty = Object.prototype.hasOwnProperty
export const isNumber = (val: any): val is number => typeof val === 'number'
export const hasOwn = (val: object, key: string | symbol): key is keyof typeof val =>
  hasOwnProperty.call(val, key)

export const isArray: typeof Array.isArray = Array.isArray
export const isMap = (val: unknown): val is Map<any, any> => toTypeString(val) === '[object Map]'
export const isSet = (val: unknown): val is Set<any> => toTypeString(val) === '[object Set]'

export const isDate = (val: unknown): val is Date => toTypeString(val) === '[object Date]'
export const isRegExp = (val: unknown): val is RegExp => toTypeString(val) === '[object RegExp]'
export const isFunction = (val: unknown): val is Function => typeof val === 'function'
export const isString = (val: unknown): val is string => typeof val === 'string'
export const isSymbol = (val: unknown): val is symbol => typeof val === 'symbol'
export const isObject = (val: unknown): val is Record<any, any> =>
  val !== null && typeof val === 'object'

export const isPromise = <T = any>(val: unknown): val is Promise<T> => {
  return (
    (isObject(val) || isFunction(val)) &&
    isFunction((val as any).then) &&
    isFunction((val as any).catch)
  )
}

export const objectToString: typeof Object.prototype.toString = Object.prototype.toString
export const toTypeString = (value: unknown): string => objectToString.call(value)

export const toRawType = (value: unknown): string => {
  // extract "RawType" from strings like "[object RawType]"
  return toTypeString(value).slice(8, -1)
}
/**
 * @name 不规范数据->封装生成树形接口
 */

export function generateTree(data: any[], parentId: number) {
  const result: any[] = []

  // 遍历每个节点，构建树形结构
  data.forEach((child) => {
    // 创建当前子节点的副本，并设置 parentId
    const currentNode = { ...child.node, parentId }
    // 如果该项没有children字段，表示是根节点
    if (child.children && child.children.length) {
      // 递归处理子节点
      currentNode.children = generateTree(child.children, currentNode.id)
    }
    result.push(currentNode)
  })

  return result
}

/**
 * @description 使用递归过滤出需要渲染在左侧菜单的列表 (需剔除 isHide == true 的菜单)
 * @param {Array} menuList 菜单列表
 * @returns {Array}
 * */
export function getShowMenuList(menuList: Menu.MenuOptions[]) {
  let newMenuList: Menu.MenuOptions[] = JSON.parse(JSON.stringify(menuList))
  return newMenuList.filter((item) => {
    if (item.children?.length) item.children = getShowMenuList(item.children)
    return !item.meta?.isHide
  })
}

/**
 * @description 处理 prop 为多级嵌套的情况，返回的数据 (列如: prop: user.name)
 * @param {Object} row 当前行数据
 * @param {String} prop 当前 prop
 * @returns {*}
 * */
export function handleRowAccordingToProp(row: { [key: string]: any }, prop: string) {
  if (!prop.includes('.')) return row[prop] ?? '--'
  prop.split('.').forEach((item) => (row = row[item] ?? '--'))
  return row
}

/**
 * @description 处理 prop，当 prop 为多级嵌套时 ==> 返回最后一级 prop
 * @param {String} prop 当前 prop
 * @returns {String}
 * */
export function handleProp(prop: string) {
  const propArr = prop.split('.')
  if (propArr.length == 1) return prop
  return propArr[propArr.length - 1]
}

/**
 * @description 根据枚举列表查询当需要的数据（如果指定了 label 和 value 的 key值，会自动识别格式化）
 * @param {String|Array} callValue 当前单元格值，可以是单个值或数组
 * @param {Array} enumData 字典列表
 * @param {Array} fieldNames label && value && children 的 key 值
 * @param {String} type 过滤类型（目前只有 tag）
 * @returns {String}
 * */
export function filterEnum(
  callValue: any,
  enumData: any[],
  fieldNames?: FieldNamesProps,
  type?: 'link' | 'tag',
) {
  const value = fieldNames?.value ?? 'value'
  const label = fieldNames?.label ?? 'label'
  const children = fieldNames?.children ?? 'children'
  let filterData: { [key: string]: any }[] = []

  // 将 callValue 处理为数组以便统一处理
  const callValues = Array.isArray(callValue) ? callValue : [callValue]

  // 判断 enumData 是否为数组 在 enumData 中查找每个 callValue
  if (Array.isArray(enumData))
    filterData = callValues.map((cv) => findItemNested(enumData, cv, value, children))

  // 判断是否输出的结果为 tag 类型
  if (type && ['tag', 'link'].includes(type)) {
    return filterData.map((result) => result?.elType || '').join(' / ')
  } else {
    return filterData.map((result) => (result ? result[label] : '--')).join(' / ')
  }
}

/**
 * @description 递归查找 callValue 对应的 enum 值
 * */
export function findItemNested(enumData: any, callValue: any, value: string, children: string) {
  return enumData.reduce((accumulator: any, current: any) => {
    if (accumulator) return accumulator
    if (current[value] === callValue) return current
    if (current[children]) return findItemNested(current[children], callValue, value, children)
  }, null)
}

/**
 * @description 处理 ProTable 值为数组 || 无数据
 * @param {*} callValue 需要处理的值
 * @returns {String}
 * */
export function formatValue(callValue: any) {
  // 如果当前值为数组，使用 / 拼接（根据需求自定义）
  if (isArray(callValue)) return callValue.length ? callValue.join(' / ') : '--'
  return callValue ?? '--'
}

/**
 *
 * @param phoneNumber 手机号
 * @returns
 */

export function maskPhoneNumber(phoneNumber: string | string) {
  // 提取前3位，替换中间4位，提取后4位
  return phoneNumber.slice(0, 3) + '****' + phoneNumber.slice(7)
}

/**
 * 判断数据某几项不为空
 * @param arr 数组
 * @param keys  需要判断的key
 * @returns boolean
 */
export function areAllValuesNotEmpty<T>(arr: T[], keys: (keyof T)[]): boolean {
  return arr.every((item) =>
    keys.every(
      (key) => item[key] && item[key] !== null && item[key] !== undefined && item[key] !== '',
    ),
  )
}

/**
 * 提示组件
 * @param message 提示内容
 */
export function showTips(message: string) {
  ElMessageBox.alert(message, '提示', {
    type: 'warning',
    dangerouslyUseHTMLString: true,
  })
}

/**
 * 将表单日期格式转换为 startDate 和 endDate
 * @param from 表单数据
 * @param keys 需要转换的key
 * @returns  转换后的表单数据
 */

export function changeDateFormat(from: FormDataType, keys: string[]) {
  const params = { ...from }
  keys.forEach((key) => {
    if (params[key] && params[key] instanceof Array && params[key].length === 2) {
      params[`${key}Start`] = params[key][0]
      params[`${key}End`] = params[key][1]
      delete params[key]
    }
  })
  return params
}

type SuffixConfig = {
  startSuffix: string
  endSuffix: string
  key: string
}

/**
 * 将表单中的日期范围数组拆分为独立字段，并允许为每个字段单独指定后缀
 * @param from 表单数据对象
 * @param keyConfigs 配置对象，格式为 { [key]: { startSuffix: '自定义开始后缀', endSuffix: '自定义结束后缀' } }
 */
export function changeDateFormatKey<T extends Record<string, any>>(
  from: T,
  keyConfigs: SuffixConfig[],
): T {
  const params = { ...from }

  // 遍历所有配置的 key
  keyConfigs.forEach((suffixConfig) => {
    if (
      params[suffixConfig.key] &&
      Array.isArray(params[suffixConfig.key]) &&
      params[suffixConfig.key].length === 2
    ) {
      // 动态生成字段名
      const startKey = `${suffixConfig.startSuffix}` as keyof T
      const endKey = `${suffixConfig.endSuffix}` as keyof T

      // 赋值并删除原字段
      params[startKey] = params[suffixConfig.key][0]
      params[endKey] = params[suffixConfig.key][1]
      delete params[suffixConfig.key]
    }
  })

  return params
}

/**
 * 根据指定的多个键对对象数组进行去重
 * @param arr 待去重的对象数组
 * @param keys 用于去重的对象属性名数组
 * @returns 去重后的对象数组
 */
export function uniqueByKeys<T>(arr: T[], keys: (keyof T)[]): T[] {
  const seen = new Set<string>()

  return arr.filter((item) => {
    // 生成唯一的标识符，基于多个键
    const identifier = keys.map((key) => item[key]).join('|')
    if (seen.has(identifier)) {
      return false
    }
    seen.add(identifier)
    return true
  })
}

export function validateIpv4Range(ipRange: string) {
  const regex =
    /^((?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}\/(?:[0-9]|[12]\d|3[0-2]))(?:;(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}\/(?:[0-9]|[12]\d|3[0-2]))*$/
  return regex.test(ipRange)
}

export function validateIpv6Range(ipRange: string) {
  const regex =
    /^(?:(?:(?:[0-9A-Fa-f]{1,4}:){7}[0-9A-Fa-f]{1,4}|(?:(?:[0-9A-Fa-f]{1,4}:){1,7}:|:)|(?:(?:[0-9A-Fa-f]{1,4}:){1,6}:[0-9A-Fa-f]{1,4}|:)|(?:(?:[0-9A-Fa-f]{1,4}:){1,5}(?::[0-9A-Fa-f]{1,4}){1,2}|:)|(?:(?:[0-9A-Fa-f]{1,4}:){1,4}(?::[0-9A-Fa-f]{1,4}){1,3}|:)|(?:(?:[0-9A-Fa-f]{1,4}:){1,3}(?::[0-9A-Fa-f]{1,4}){1,4}|:)|(?:(?:[0-9A-Fa-f]{1,4}:){1,2}(?::[0-9A-Fa-f]{1,4}){1,5}|:)|(?:[0-9A-Fa-f]{1,4}:(?::[0-9A-Fa-f]{1,4}){1,6}|:)|(?::(?::[0-9A-Fa-f]{1,4}){1,7}|:))\/(?:0|12[0-8]|1[0-1][0-9]|[1-9]?[0-9]))(?:;(?:(?:[0-9A-Fa-f]{1,4}:){7}[0-9A-Fa-f]{1,4}|(?:(?:[0-9A-Fa-f]{1,4}:){1,7}:|:)|(?:(?:[0-9A-Fa-f]{1,4}:){1,6}:[0-9A-Fa-f]{1,4}|:)|(?:(?:[0-9A-Fa-f]{1,4}:){1,5}(?::[0-9A-Fa-f]{1,4}){1,2}|:)|(?:(?:[0-9A-Fa-f]{1,4}:){1,4}(?::[0-9A-Fa-f]{1,4}){1,3}|:)|(?:(?:[0-9A-Fa-f]{1,4}:){1,3}(?::[0-9A-Fa-f]{1,4}){1,4}|:)|(?:(?:[0-9A-Fa-f]{1,4}:){1,2}(?::[0-9A-Fa-f]{1,4}){1,5}|:)|(?:[0-9A-Fa-f]{1,4}:(?::[0-9A-Fa-f]{1,4}){1,6}|:)|(?::(?::[0-9A-Fa-f]{1,4}){1,7}|:))\/(?:0|12[0-8]|1[0-1][0-9]|[1-9]?[0-9]))*$/
  return regex.test(ipRange)
}
export function uuid(num: 8 | 16 = 8): string {
  // 生成一个标准的UUID
  const uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0,
      v = c === 'x' ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })

  // 截取前8位
  return uuid.substring(0, num)
}

export function generateRandom8DigitNumber(): number {
  // 生成一个 10000000 到 99999999 之间的随机整数
  const min = 10000000
  const max = 99999999
  return Math.floor(Math.random() * (max - min + 1)) + min
}

export function isObjectValuesNonEmpty(obj: Record<string, any>, keys?: string[]): boolean {
  const keysToCheck = keys ? keys : Object.keys(obj)

  for (const key of keysToCheck) {
    if (obj.hasOwnProperty(key)) {
      const value = obj[key]
      if (
        value === null ||
        value === undefined ||
        (typeof value === 'string' && value.trim() === '') ||
        (Array.isArray(value) && value.length === 0) ||
        (typeof value === 'object' && !Array.isArray(value) && Object.keys(value).length === 0)
      ) {
        return false
      }
    }
  }
  return true
}

export function isCombinedValueUniqueInArray<T extends Record<string, any>>(
  arr: T[],
  keys: (keyof T)[],
): boolean {
  const combinedValues = new Set<string>()

  for (const item of arr) {
    // 1. 过滤空值（null/undefined/空字符串）
    const nonEmptyValues = keys
      .map((key) => item[key])
      .filter((value) => value !== undefined && value !== null && value !== '')

    // 2. 如果所有值都为空，跳过校验
    if (nonEmptyValues.length === 0) continue

    // 3. 生成组合值并检查唯一性
    const combinedValue = JSON.stringify(nonEmptyValues)
    if (combinedValues.has(combinedValue)) {
      return false
    }
    combinedValues.add(combinedValue)
  }

  return true
}

export const buildProp = <
  Type = never,
  Value = never,
  Validator = never,
  Default extends EpPropMergeType<Type, Value, Validator> = never,
  Required extends boolean = false,
>(
  prop: EpPropInput<Type, Value, Validator, Default, Required>,
  key?: string,
): EpPropFinalized<Type, Value, Validator, Default, Required> => {
  // filter native prop type and nested prop, e.g `null`, `undefined` (from `buildProps`)
  if (!isObject(prop) || isEpProp(prop)) return prop as any

  const { values, required, default: defaultValue, type, validator } = prop

  const _validator =
    values || validator
      ? (val: unknown) => {
          let valid = false
          let allowedValues: unknown[] = []

          if (values) {
            allowedValues = Array.from(values)
            if (hasOwn(prop, 'default')) {
              allowedValues.push(defaultValue)
            }
            valid ||= allowedValues.includes(val)
          }
          if (validator) valid ||= validator(val)

          if (!valid && allowedValues.length > 0) {
            const allowValuesText = [...new Set(allowedValues)]
              .map((value) => JSON.stringify(value))
              .join(', ')
            warn(
              `Invalid prop: validation failed${
                key ? ` for prop "${key}"` : ''
              }. Expected one of [${allowValuesText}], got value ${JSON.stringify(val)}.`,
            )
          }
          return valid
        }
      : undefined

  const epProp: any = {
    type,
    required: !!required,
    validator: _validator,
    [epPropKey]: true,
  }
  if (hasOwn(prop, 'default')) epProp.default = defaultValue
  return epProp
}

export const buildProps = <
  Props extends Record<
    string,
    { [epPropKey]: true } | NativePropType | EpPropInput<any, any, any, any, any>
  >,
>(
  props: Props,
): {
  [K in keyof Props]: IfEpProp<
    Props[K],
    Props[K],
    IfNativePropType<Props[K], Props[K], EpPropConvert<Props[K]>>
  >
} =>
  fromPairs(
    Object.entries(props).map(([key, option]) => [key, buildProp(option as any, key)]),
  ) as any

export const getUid = (str: string) => {
  const uuidRegex = /<!--\s*uuid==([a-zA-Z0-9]+)==uuid\s*-->/
  const match = str.match(uuidRegex)
  if (match && match[1]) {
    const uuid = match[1]
    return uuid
  } else {
    console.log('未找到 UUID 注释')
    return ''
  }
}

export const getLocalUid = () => {
  const str = document.head.innerHTML
  return getUid(str)
}

export const fetchUid = async () => {
  const res = await fetch('/')
  const str = await res.text()
  return getUid(str)
}

export const shouldUpdate = async () => {
  const localUid = getLocalUid()
  const uid = await fetchUid()
  return localUid !== uid
}
