<template>
  <SlProTable
    ref="proTable"
    highlight-current-row
    :columns="columns"
    style="min-height: 300px"
    :request-api="natRuleList"
    :init-param="queryParams"
    :current-change="currentChange"
    hidden-table-header
  >
  </SlProTable>
</template>
<script setup lang="ts" name="RuleDataList">
import { reactive, ref } from 'vue'
import type { ColumnProps, ProTableInstance } from '@/components/SlProTable/interface'
import { natRuleList } from '@/api/modules/resourecenter'

const { queryParams } = defineProps<{
  orderId: string
  queryParams: any
}>()

const emit = defineEmits(['currentChange'])
const currentChange = (currentRow: any, oldCurrentRow: any) => {
  emit('currentChange', currentRow, oldCurrentRow)
}
// 表格配置项
const columns = reactive<ColumnProps<any>[]>([
  { type: 'index', label: '序号', minWidth: 55 },
  { prop: 'sourceAddress', label: '源地址', minWidth: 150 },
  { prop: 'transformAddress', label: '转换地址', minWidth: 200 },
  { prop: 'ruleType', label: '规则类型', minWidth: 150 },
  { prop: 'domainName', label: '所属云', minWidth: 150 },
  { prop: 'resourcePoolName', label: '资源池', minWidth: 150, filter: true },
  { prop: 'createdTime', label: '开通时间', minWidth: 150 },
  { prop: 'userName', label: '申请人', minWidth: 100 },
])

const proTable = ref<ProTableInstance>()
defineExpose({
  proTable,
})
</script>
