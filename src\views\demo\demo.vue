<script setup lang="ts">
import * as vNG from 'v-network-graph'
import { useData } from './data'
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { Delete, CopyDocument } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const { nodes, edges, layouts, configs } = useData()
const graph = ref<vNG.Instance>()
const nextNodeIndex = ref(Object.keys(nodes).length + 1)
const nextEdgeIndex = ref(Object.keys(edges).length + 1)
const selectedNodes = ref<string[]>([])
const selectedEdges = ref<string[]>([])
// 右键菜单状态
const contextMenu = ref({
  show: false,
  x: 0,
  y: 0,
  nodeId: '',
})
// 计算右键菜单位置样式
const contextMenuStyle = computed(() => ({
  left: contextMenu.value.x + 'px',
  top: contextMenu.value.y + 'px',
}))

function handleKeyDown(e: KeyboardEvent) {
  if (e.key === 'Backspace') {
    for (const nodeId of selectedNodes.value) {
      deleteNode(nodeId)
    }
    for (const edgeId of selectedEdges.value) {
      deleteEdge(edgeId)
    }
  }
}
// 按backspace删除节点
onMounted(() => {
  document.addEventListener('keydown', handleKeyDown)
})
onUnmounted(() => {
  document.removeEventListener('keydown', handleKeyDown)
})

// 工具栏项目
const toolItems = [
  { name: '云主机', icon: 'ecs.png', type: 'ecs' },
  { name: 'GPU云主机', icon: 'gcs.png', type: 'gcs' },
  { name: '弹性公网', icon: 'eip.png', type: 'eip' },
  { name: '网络', icon: 'network.png', type: 'network' },
  { name: 'NAT网关', icon: 'nat.png', type: 'nat' },
  { name: '负载均衡', icon: 'slb.png', type: 'slb' },
  { name: 'VPC', icon: 'vpc.png', type: 'vpc' },
  { name: '云硬盘', icon: 'evs.png', type: 'evs' },
  { name: '对象存储', icon: 'obs.png', type: 'obs' },
]

// 导入所有图片
const images: Record<string, string> = {
  'ecs.png': new URL('./imgs/ecs.png', import.meta.url).href,
  'gcs.png': new URL('./imgs/gcs.png', import.meta.url).href,
  'eip.png': new URL('./imgs/eip.png', import.meta.url).href,
  'evs.png': new URL('./imgs/evs.png', import.meta.url).href,
  'nat.png': new URL('./imgs/nat.png', import.meta.url).href,
  'slb.png': new URL('./imgs/slb.png', import.meta.url).href,
  'vpc.png': new URL('./imgs/vpc.png', import.meta.url).href,
  'network.png': new URL('./imgs/network.png', import.meta.url).href,
  'obs.png': new URL('./imgs/obs.png', import.meta.url).href,
}

const layers = {
  images: 'nodes', // 将图片图层放在节点位置
}

// 删除节点
const deleteNode = (nodeId: string) => {
  if (nodeId) {
    resetLinking()
    // 删除相关的边
    Object.entries(edges).forEach(([edgeId, edge]) => {
      if (edge.source === nodeId || edge.target === nodeId) {
        delete edges[edgeId]
      }
    })
    // 删除节点
    delete nodes[nodeId]
    delete layouts.nodes[nodeId]
  }
}

// 删除边
const deleteEdge = (edgeId: string) => {
  resetLinking()
  delete edges[edgeId]
}

// 复制节点
const copyNode = () => {
  resetLinking()
  if (contextMenu.value.nodeId) {
    const sourceNode = nodes[contextMenu.value.nodeId]
    const sourceLayout = layouts.nodes[contextMenu.value.nodeId]
    const newNodeId = `node${nextNodeIndex.value}`

    // 复制节点数据
    nodes[newNodeId] = { ...sourceNode }
    // 在原节点位置偏移一点创建新节点
    layouts.nodes[newNodeId] = {
      x: sourceLayout.x + 50,
      y: sourceLayout.y + 50,
    }
    nextNodeIndex.value++
  }
  hideContextMenu()
}
// 添加节点
const addNode = (item: (typeof toolItems)[0]) => {
  resetLinking()
  // 添加节点数据
  nodes[`node${nextNodeIndex.value}`] = {
    name: item.name,
    face: item.icon,
    type: item.type,
  }
  // 计算新节点的位置 - 默认添加到画布中心并添加随机偏移
  layouts.nodes[`node${nextNodeIndex.value}`] = {
    x: 150 + Math.random() * 50,
    y: -50 + Math.random() * 50,
  }
  nextNodeIndex.value++
}

// 隐藏右键菜单
const hideContextMenu = () => {
  contextMenu.value.show = false
}

// 连线状态
const linking = ref({
  isLinking: false,
  sourceNodeId: '',
  mouseX: 0,
  mouseY: 0,
})

// 获取节点在画布中的实际位置
const getNodePosition = (nodeId: string) => {
  const pos = layouts.nodes[nodeId]
  return pos ? { x: pos.x, y: pos.y } : null
}

// 计算临时连线的路径
const temporaryLinkPath = computed(() => {
  const sourcePosOnView = getPosOnView(linking.value.sourceNodeId)
  if (!sourcePosOnView) return ''
  // 计算从起始节点到鼠标位置的路径
  return `M ${sourcePosOnView.x} ${sourcePosOnView.y} L ${linking.value.mouseX} ${linking.value.mouseY}`
})

// 处理鼠标移动
const handleMouseMove = (event: MouseEvent) => {
  if (linking.value.isLinking) {
    linking.value.mouseX = event.clientX
    linking.value.mouseY = event.clientY
  }
}
//-----------校验逻辑开始-----------
const couldConnect = (sourceId: string, targetId: string) => {
  const source = nodes[sourceId]
  const target = nodes[targetId]
  if (['vpc', 'network', 'obs'].includes(source.type)) return false
  if (['nat', 'slb'].includes(source.type)) {
    return target.type === 'vpc'
  }
  if (['ecs', 'gcs'].includes(source.type)) {
    return ['vpc', 'network'].includes(target.type)
  }
  if (source.type === 'eip') {
    return ['nat', 'slb', 'ecs', 'gcs'].includes(target.type)
  }
  if (source.type === 'evs') {
    return ['ecs', 'gcs'].includes(target.type)
  }
  return true
}
// 连接校验：检查两个节点之间是否已经存在连接
const hasConnection = (sourceId: string, targetId: string) => {
  return Object.values(edges).some(
    (edge) =>
      (edge.source === sourceId && edge.target === targetId) ||
      (edge.source === targetId && edge.target === sourceId),
  )
}

const connectValid = (sourceId: string, targetId: string) => {
  if (hasConnection(sourceId, targetId)) {
    ElMessage.warning('这两个节点之间已经存在连接')
    return false
  }
  if (!couldConnect(sourceId, targetId)) {
    const source = nodes[sourceId]
    const target = nodes[targetId]
    ElMessage.warning(`【${source.name}】节点不支持连接到【${target.name}】节点`)
    return false
  }
  return true
}
//-----------校验逻辑结束-----------

const eventHandlers: vNG.EventHandlers = {
  'node:click': ({ node, event }) => {
    if (linking.value.isLinking) {
      if (linking.value.sourceNodeId !== node) {
        if (connectValid(linking.value.sourceNodeId, node)) {
          const edgeId = `edge${nextEdgeIndex.value}`
          edges[edgeId] = {
            source: linking.value.sourceNodeId,
            target: node,
          }
          nextEdgeIndex.value++
        }
      }
      resetLinking()
    } else if (!event.shiftKey) {
      linking.value.isLinking = true
      linking.value.sourceNodeId = node

      const posOnView = getPosOnView(node)
      // 初始化target和起点坐标一致
      if (posOnView) {
        linking.value.mouseX = posOnView.x
        linking.value.mouseY = posOnView.y
      }
    }
    nodes[node].active = !nodes[node].active
  },
  'node:contextmenu': ({ node, event }) => {
    event.preventDefault()
    contextMenu.value = {
      show: true,
      x: event.clientX,
      y: event.clientY,
      nodeId: node,
    }
  },
  'view:click': () => {
    resetLinking()
    hideContextMenu()
  },
}
function resetLinking() {
  linking.value.isLinking = false
  linking.value.sourceNodeId = ''
}
function getPosOnView(nodeId: string) {
  if (!graph.value) return ''
  const pos = getNodePosition(nodeId)
  if (!pos) return ''
  const svgPoint = graph.value.translateFromSvgToDomCoordinates({
    x: pos.x,
    y: pos.y,
  })
  return { x: svgPoint.x, y: svgPoint.y }
}
function handleContextDelete() {
  deleteNode(contextMenu.value.nodeId)
  hideContextMenu()
}
</script>

<template>
  <div class="container">
    <!-- 工具栏 -->
    <div class="toolbar">
      <div
        class="toolbar-item"
        v-for="(item, key) in toolItems"
        :key="key"
        @click="addNode(item)"
        title="点击添加到画布"
      >
        <img :src="images[item.icon]" :alt="item.name" class="toolbar-icon" />
        <span class="toolbar-text">{{ item.name }}</span>
      </div>
    </div>

    <!-- 右键菜单 -->
    <div v-show="contextMenu.show" :style="contextMenuStyle" class="context-menu">
      <div class="context-menu-item" @click="handleContextDelete">
        <el-icon><Delete /></el-icon>
        <span>删除节点</span>
      </div>
      <div class="context-menu-item" @click="copyNode">
        <el-icon><CopyDocument /></el-icon>
        <span>复制节点</span>
      </div>
    </div>

    <!-- 画布 -->
    <div
      style="width: 100vw; height: 100vh; background-color: #fefeee; overflow: hidden"
      @click="hideContextMenu"
      :class="{ 'linking-cursor': linking.isLinking }"
    >
      <v-network-graph
        ref="graph"
        :nodes="nodes"
        :edges="edges"
        :layouts="layouts"
        :configs="configs"
        :layers="layers"
        :event-handlers="eventHandlers"
        v-model:selected-nodes="selectedNodes"
        v-model:selected-edges="selectedEdges"
        class="network-graph"
        @mousemove="handleMouseMove"
      >
        <template #images="{ scale }">
          <image
            v-for="(pos, nodeId) in layouts.nodes"
            :key="nodeId"
            :x="pos.x - 20 * scale"
            :y="pos.y - 20 * scale"
            :width="40 * scale"
            :height="40 * scale"
            :href="images[nodes[nodeId].face]"
            style="pointer-events: none"
          />
        </template>
        <template #custom-layers>
          <!-- 临时连线 -->
          <path
            v-if="linking.isLinking"
            :d="temporaryLinkPath"
            stroke="#de8802"
            stroke-width="3"
            stroke-dasharray="3,3"
            fill="none"
            pointer-events="none"
          />
        </template>
      </v-network-graph>
    </div>
  </div>
</template>

<style scoped>
.container {
  position: relative;
}

.toolbar {
  position: fixed;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.toolbar-item {
  display: flex;
  align-items: center;
  padding: 8px;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s;
}

.toolbar-item:hover {
  background-color: #f5f5f5;
  transform: translateX(2px);
}

.toolbar-icon {
  width: 20px;
  height: 20px;
  margin-right: 8px;
}

.toolbar-text {
  font-size: 12px;
  color: #333;
}

.context-menu {
  position: fixed;
  background: white;
  border-radius: 4px;
  padding: 4px 0;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  min-width: 120px;
}

.context-menu-item {
  display: flex;
  align-items: center;
  padding: 6px 12px;
  cursor: pointer;
  transition: background-color 0.2s;
  color: #333;
  font-size: 12px;
}

.context-menu-item:hover {
  background-color: #f5f5f5;
}

.context-menu-item .el-icon {
  margin-right: 8px;
  font-size: 14px;
}

.network-graph :deep(.v-network-graph-node-label) {
  font-weight: bold !important;
}

.network-graph :deep(.v-network-graph) {
  position: relative;
}

.network-graph :deep(.v-network-graph-nodes) {
  z-index: 2;
}

.network-graph :deep(.v-network-graph-edges) {
  z-index: 1;
}

.network-graph :deep(.v-network-graph-custom-layers) {
  z-index: 2;
}

.linking-cursor {
  cursor:
    url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='32' height='32' viewBox='0 0 32 32'><line x1='16' y1='8' x2='16' y2='24' stroke='%23666' stroke-width='2'/><line x1='8' y1='16' x2='24' y2='16' stroke='%23666' stroke-width='2'/></svg>")
      16 16,
    crosshair !important;
}

.network-graph :deep(.v-network-graph-node) {
  cursor: pointer;
}
</style>
