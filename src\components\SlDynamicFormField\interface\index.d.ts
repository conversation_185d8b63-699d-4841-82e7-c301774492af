/**
 * @name 描述表单中每个字段的配置
 */
export interface FieldConfigType {
  type: string // 字段类型，如 'text', 'select', 'radio', 'date', 'time', 'checkbox', 'switch', 'number', 'slider' 等
  model: string // 绑定的模型键，对应 formData 中的属性
  label: string // 字段标签，显示在表单控件旁边
  rules?: Array<object> // 此字段的验证规则数组，每个规则对象通常包含验证类型、消息和触发条件
  options?: FieldOptionType[] // 用于 'select', 'radio', 'checkbox' 等类型的选项数组
  labelField?: string // 自定义选项对象中用作标签的字段名（默认为'label'） --- 如果需要提交name则使用labelField ,valueField
  valueField?: string // 自定义选项对象中用作值的字段名（默认为'value'） -- ElTreeSelect 组件 需要配置 node-key,props属性
  attrs?: Record<string, any> // 额外的 HTML 属性或 Vue 组件属性
  emitChange?: boolean // 是否应该抛出 'change' 事件（默认为 false，可以被启用来触发自定义逻辑）
  optionString?: string // 选项字符串当存在的时候默认为当前存在的选项
}

/**
 * @name 描述选择控件（如下拉菜单、单选按钮）中的选项
 */
export interface FieldOptionType {
  label?: string // 选项显示的文本
  value?: any // 选项对应的值
  [key: string]: any
}

/**
 * @name 表单数据的整体结构，这里使用了一个通用的定义
 */
export interface FormDataType {
  [key: string]: any // 表单键值对，键名由 FieldConfig 中的 model 定义，值的类型可以根据实际表单字段决定
}
