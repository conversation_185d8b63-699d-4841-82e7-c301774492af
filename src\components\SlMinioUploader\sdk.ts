import SparkMD5 from 'spark-md5'

import {
  checkMd5,
  merge,
  getPresignedUrl,
  //  directRequest
} from '@/api/modules'
import { type UploaderManager } from './manage'
export interface IMinioResumableUploadOptions {
  chunkSize?: number
  concurrent?: number
  sampleThreshold?: number
  sampleSize?: number
  md5ChunkSize?: number
  middleSampleCount?: number
  autoStart?: boolean
  md5?: string
}
export class MinioResumableUpload {
  private file: File
  private chunkSize: number // 分片大小
  private sampleThreshold: number // 抽样阈值,超过这个阈值的文件使用完整哈希值
  private sampleSize: number // 样本大小
  private concurrent: number // 并发数
  public md5: string // 文件哈希值
  private uploadedParts: number[] // 已上传的分块
  private presignedUrls: string[] // 预签名URL
  private totalChunksCount: number // 总分块数
  private md5ChunkSize: number // 完整文件哈希值分片大小
  private middleSampleCount: number // 中间样本点数量
  private options: IMinioResumableUploadOptions
  private autoStart: boolean // 是否自动开始上传
  private progress: number // 上传进度
  private onProgressFn: null | ((progress: number, url?: string) => void) // 上传进度回调
  private isUploading: boolean // 上传状态标志
  private abortControllers: AbortController[] // 用于取消请求的控制器数组
  private extraArgs: any // 额外参数
  private manager: UploaderManager | null

  constructor(options: IMinioResumableUploadOptions = {}, manager?: UploaderManager) {
    this.autoStart = true
    this.options = options
    this.file = new File([], '')
    this.chunkSize = 0
    this.sampleThreshold = 0
    this.sampleSize = 0
    this.concurrent = 0
    this.md5 = ''
    this.uploadedParts = []
    this.presignedUrls = []
    this.totalChunksCount = 0
    this.md5ChunkSize = 0
    this.middleSampleCount = 0
    this.progress = 0
    this.onProgressFn = null
    this.isUploading = false
    this.abortControllers = []
    this.extraArgs = {}
    this.initState()
    this.manager = manager || null
  }

  public setExtraArgs(args: any) {
    Object.assign(this.extraArgs, args)
  }

  private initState(): void {
    this.sampleThreshold = (this.options.sampleThreshold || 10) * 1024 * 1024 // 默认10MB
    this.sampleSize = (this.options.sampleSize || 1) * 1024 * 1024 // 默认1MB
    this.chunkSize = (this.options.chunkSize || 5) * 1024 * 1024 // 默认5MB
    this.md5ChunkSize = (this.options.md5ChunkSize || 2) * 1024 * 1024 // 默认2MB
    this.concurrent = this.options.concurrent || 3 // 并发数
    this.middleSampleCount = this.options.middleSampleCount || 10 // 中间样本点数量
    this.autoStart = this.options.autoStart ?? true
  }

  resetState(): void {
    this.file = new File([], '')
    this.totalChunksCount = 0
    this.md5 = ''
    this.uploadedParts = []
    this.presignedUrls = []
    this.progress = 0
    this.extraArgs = {}
    this.onProgressFn = null
    // 重新初始化
    this.initState()
  }

  stop(): void {
    this.isUploading = false

    // 取消所有正在进行的请求
    this.abortControllers.forEach((controller) => {
      try {
        controller.abort()
      } catch (error) {
        console.error('取消请求失败:', error)
      }
    })

    // 清空控制器数组
    this.abortControllers = []

    console.log('已暂停所有上传任务')
  }

  async loadFile(file: File): Promise<void> {
    this.file = file
    this.totalChunksCount = Math.ceil(file.size / this.chunkSize) // 总分块数
    this.md5 = this.options.md5 || (await this.generateFileHash(this.file))
    if (this.autoStart) this.start()
  }
  setIdentification(identification: string): void {
    if (this.manager) this.manager.uploaders.set(identification, this)
  }
  onProgress(onProgress: (progress: number) => void): void {
    this.onProgressFn = onProgress
  }

  // 初始化上传（获取 md5 和预签名 URL）
  async start(args?: any): Promise<void> {
    if (args) {
      Object.assign(this.extraArgs, args)
    }
    const { entity } = await getPresignedUrl({
      md5: this.md5,
      totalChunksCount: this.totalChunksCount,
    })
    this.presignedUrls = entity // 预先生成所有分块的预签名 URL
    await this.loadProgress()
    // 并发上传
    this.isUploading = true
    const chunks = Array.from({ length: this.totalChunksCount }, (_, i) => i)
    const queue = new DynamicQueue(this.concurrent)
    if (this.uploadedParts.length === this.totalChunksCount) {
      return await this.completeUpload()
    }
    chunks.forEach((chunk) => {
      queue.add(async () => {
        // 如果已经停止上传，则不执行上传任务
        if (!this.isUploading) return

        await this.uploadChunk(chunk)
        this.updateProgress()
      })
    })
  }

  // 加载已上传的进度
  private async loadProgress(): Promise<void> {
    const { entity } = await checkMd5(this.md5)
    this.uploadedParts = (entity ? entity.split(':') : []).map((e: any) => Number(e))
  }

  private uploadChunk(chunkIndex: number): Promise<void> {
    return new Promise(async (resolve, reject) => {
      if (this.uploadedParts.some((p) => p === chunkIndex + 1)) {
        resolve()
        return // 跳过已上传的分块
      }

      // 如果已停止上传，则直接返回
      if (!this.isUploading) {
        resolve()
        return
      }

      const start = chunkIndex * this.chunkSize
      const end = Math.min(start + this.chunkSize, this.file.size)
      const chunk = this.file.slice(start, end)

      // 创建AbortController用于取消请求
      const controller = new AbortController()
      this.abortControllers.push(controller)

      try {
        const response = await fetch(this.presignedUrls[chunkIndex], {
          method: 'PUT',
          body: chunk,
          headers: { 'Content-Type': 'application/octet-stream' },
          signal: controller.signal, // 添加信号用于取消请求
        })

        // 从控制器数组中移除已完成的控制器
        const index = this.abortControllers.indexOf(controller)
        if (index > -1) {
          this.abortControllers.splice(index, 1)
        }

        if (!response.ok) {
          reject(new Error('上传失败'))
          return
        }

        this.uploadedParts.push(chunkIndex + 1)
        if (this.uploadedParts.length === this.totalChunksCount) {
          await this.completeUpload()
        }

        resolve()
      } catch (error) {
        // 从控制器数组中移除已完成的控制器
        const index = this.abortControllers.indexOf(controller)
        if (index > -1) {
          this.abortControllers.splice(index, 1)
        }

        // 如果是因为取消请求导致的错误，不显示错误信息
        if ((error as Error).name === 'AbortError') {
          console.log(`分块 ${chunkIndex + 1} 上传已取消`)
          resolve()
          return
        }

        console.error(`分块 ${chunkIndex} 上传失败:`, error)
        reject(error)
      }
    })
  }

  // 更新进度条
  private updateProgress(): void {
    this.progress = Math.ceil((this.uploadedParts.length / this.totalChunksCount) * 100)
    this.onProgressFn?.(this.progress)
  }

  // 完成上传并清理
  private async completeUpload(): Promise<void> {
    const params = {
      md5: this.md5,
      fileName: this.file.name,
      ...this.extraArgs,
    }
    const result = await merge(params)
    if (this.onProgressFn) this.onProgressFn(100, result.entity)
  }
  /**
   * 计算完整文件的哈希值
   * 使用分片策略减少计算时间和内存消耗
   */
  private calculateCompleteFileHash(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const chunkSize = this.md5ChunkSize
      const chunks = Math.ceil(file.size / chunkSize)
      let currentChunk = 0
      const spark = new SparkMD5.ArrayBuffer()
      const fileReader = new FileReader()

      fileReader.onload = (e) => {
        spark.append(e.target?.result as ArrayBuffer)
        currentChunk++

        if (currentChunk < chunks) {
          loadNext()
        } else {
          const md5 = spark.end()
          resolve(md5)
        }
      }

      fileReader.onerror = (e) => {
        reject(e)
      }

      function loadNext() {
        const start = currentChunk * chunkSize
        const end = Math.min(start + chunkSize, file.size)
        fileReader.readAsArrayBuffer(file.slice(start, end))
      }

      loadNext()
    })
  }
  /**
   * 生成整个文件的哈希值
   * 使用抽样策略减少计算时间和内存消耗
   */
  private async generateFileHash(file: File): Promise<string> {
    if (!file) return ''
    console.time('Hash Generation')
    // 对于小文件(小于设定阈值)，直接计算完整哈希值
    if (file.size < this.sampleThreshold) {
      const hash = await this.calculateCompleteFileHash(file)
      console.timeEnd('Hash Generation')
      return hash
    }

    // 对于大文件，采用抽样策略

    const totalSize = file.size
    const samples = []

    // 取文件头部样本
    samples.push(file.slice(0, Math.min(this.sampleSize, totalSize)))

    // 取文件尾部样本
    if (totalSize > this.sampleSize * 2) {
      samples.push(file.slice(Math.max(0, totalSize - this.sampleSize), totalSize))
    }

    // 从中间均匀取样
    if (totalSize > this.sampleSize * 10) {
      const middleCount = this.middleSampleCount
      for (let i = 1; i <= middleCount; i++) {
        const position = Math.floor((totalSize * i) / (middleCount + 1))
        samples.push(
          file.slice(
            Math.max(0, position - this.sampleSize / 2),
            Math.min(position + this.sampleSize / 2, totalSize),
          ),
        )
      }
    }

    // 添加文件大小和名称作为额外信息，降低哈希冲突的可能性
    const fileInfo = `${file.name}:${file.size}:${file.lastModified}`
    const infoBlob = new Blob([fileInfo])
    samples.push(infoBlob)
    // 合并所有样本并计算哈希
    const combinedBlob = new Blob(samples)

    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = (e) => {
        try {
          const spark = new SparkMD5.ArrayBuffer()
          spark.append(e.target?.result as ArrayBuffer)
          // 添加 'sampled-' 前缀表示这是一个抽样哈希
          const hash = 'sampled' + spark.end()
          console.log(`抽样哈希生成完成: ${hash}`)
          console.timeEnd('Sampled Hash Generation')
          console.timeEnd('Hash Generation')
          resolve(hash)
        } catch (error) {
          console.error('生成哈希失败:', error)
          reject(error)
        }
      }
      reader.onerror = (e) => {
        console.error('读取文件失败:', e)
        reject(e)
      }
      reader.readAsArrayBuffer(combinedBlob)
    })
  }
}

class DynamicQueue {
  private maxConcurrent: number
  private activeCount: number
  private queue: (() => Promise<void>)[]

  constructor(maxConcurrent: number) {
    this.maxConcurrent = maxConcurrent
    this.activeCount = 0
    this.queue = []
  }

  // 添加任务到队列中
  add(task: () => Promise<void>): void {
    this.queue.push(task)
    this.run()
  }

  // 运行队列中的任务
  private async run(): Promise<void> {
    while (this.activeCount < this.maxConcurrent && this.queue.length) {
      const task = this.queue.shift()
      if (task) {
        this.activeCount++

        try {
          await task()
        } finally {
          this.activeCount--
          this.run()
        }
      }
    }
  }
}
