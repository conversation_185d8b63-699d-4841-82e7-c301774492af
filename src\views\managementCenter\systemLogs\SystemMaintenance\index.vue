<template>
  <div class="table-main">
    <SlProTable ref="proTable" :columns="columns" :request-api="getList" row-key="id">
      <template #search>
        <div class="pl10">
          <el-button v-permission="'1Plus'" @click="() => handleEditOrSave()" type="primary">
            <el-icon class="el-icon--left"><Plus /></el-icon>新 建
          </el-button>
        </div>
      </template>
    </SlProTable>

    <!-- 弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle + '系统维护日志'"
      width="640px"
      :close-on-click-modal="false"
      @close="close"
    >
      <sl-form
        v-if="dialogVisible"
        ref="formRef"
        :show-block-title="false"
        v-model="formData"
        :options="options"
      >
        <template #enumText="{ item }">
          <EnumText :form-model="formData" :fields="item" :dic-collection="{}" />
        </template>
      </sl-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">关 闭</el-button>
          <el-button v-if="dialogTitle !== '查看'" type="primary" @click="submit">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="tsx">
import {
  getSystemMaintenanceDetailApi,
  getSystemMaintenanceListApi,
  systemMaintenancesDeleteApi,
  systemMaintenancesSaveApi,
} from '@/api/modules/managementCenter'
import SlMessage from '@/components/base/SlMessage'
import EnumText from '@/views/approvalCenter/components/EnumText.vue'
import type { ColumnProps, ProTableInstance } from '@/components/SlProTable/interface'
import { computed, ref } from 'vue'
import { Edit, Delete, View, Plus } from '@element-plus/icons-vue'
import { changeDateFormatKey } from '@/utils'

const getList = async (data: any) => {
  const params = changeDateFormatKey(data, [
    {
      key: 'createTime',
      startSuffix: 'startTime',
      endSuffix: 'endTime',
    },
  ])

  return getSystemMaintenanceListApi(params)
}

const columns: ColumnProps[] = [
  { type: 'index', label: '序号', width: 55 },
  {
    prop: 'maintenanceType',
    label: '维护类型',
    width: 100,
    enum: [
      {
        label: '升级',
        value: 'UPGRADE',
      },
      {
        label: '备份',
        value: 'BACKUP',
      },
      {
        label: '恢复',
        value: 'RESTORE',
      },
    ],
    search: { el: 'select', checked: true, defaultDisabled: true },
  },
  { prop: 'maintenanceContent', label: '维护内容', minWidth: 230 },
  { prop: 'version', label: '版本', minWidth: 230 },

  {
    prop: 'createTime',
    label: '时间',
    minWidth: 220,
    search: {
      el: 'date-picker',
      span: 1,
      props: {
        type: 'datetimerange',
        format: 'YYYY-MM-DD HH:mm:ss',
        valueFormat: 'YYYY-MM-DD HH:mm:ss',
        rangeSeparator: '至',
        startPlaceholder: '开始时间',
        endPlaceholder: '结束时间',
      },
      checked: true,
      defaultDisabled: true,
    },
  },
  {
    prop: 'publisher',
    label: '发布人',
    minWidth: 230,
    search: { el: 'input' },
  },
  {
    prop: 'operations',
    label: '操作',
    width: 220,
    fixed: 'right',
    render: (scope) => (
      <>
        <el-button
          type="primary"
          link
          icon={View}
          onClick={() => handleEditOrSave(scope.row, '查看')}
        >
          详情
        </el-button>
        <el-button
          v-permission="1Edit"
          type="primary"
          link
          icon={Edit}
          onClick={() => handleEditOrSave(scope.row, '编辑')}
        >
          编辑
        </el-button>
        <el-button
          v-permission="1Delete"
          type="danger"
          link
          icon={Delete}
          onClick={() => handleDelete(scope.row.id)}
        >
          删除
        </el-button>
      </>
    ),
  },
]

const proTable = ref<ProTableInstance>()

// 1. 添加
// 2. 编辑
const dialogTitle = ref('新建')

const handleEditOrSave = async (row?: any, type: string = '新建') => {
  dialogTitle.value = type

  dialogVisible.value = true
  if (!row) return
  const { entity } = await getSystemMaintenanceDetailApi({ id: row.id })
  formData.value.id = entity.id
  formData.value.maintenanceContent = entity.maintenanceContent
  formData.value.version = entity.version
  formData.value.maintenanceType = entity.maintenanceType
  formData.value.publisher = entity.publisher
}

const disabledValue = computed(() => dialogTitle.value === '查看')
const options = ref([
  {
    groupItems: [
      {
        label: '维护内容',
        type: computed(() => (dialogTitle.value === '查看' ? 'slot' : 'input')),
        key: 'maintenanceContent',
        keyName: 'maintenanceContent',
        slotName: 'enumText',
        props: {
          type: 'textarea',
          rows: 4,
          maxlength: 500,
          showWordLimit: true,
          disabled: disabledValue,
        },
        span: 24,
        rules: [{ required: true, message: '请输入维护内容', trigger: ['blur', 'change'] }],
      },
      {
        label: '维护类型',
        type: computed(() => (dialogTitle.value === '查看' ? 'slot' : 'select')),
        key: 'maintenanceType',
        slotName: 'enumText',
        span: 24,
        options: [
          {
            label: '升级',
            value: 'UPGRADE',
          },
          {
            label: '备份',
            value: 'BACKUP',
          },
          {
            label: '恢复',
            value: 'RESTORE',
          },
        ],
        props: {
          select: {
            disabled: disabledValue,
            clearable: true,
            filterable: true,
          },
        },
        rules: [{ required: true, message: '请选择维护类型', trigger: ['blur', 'change'] }],
      },
      {
        label: '版本',
        type: computed(() => (dialogTitle.value === '查看' ? 'slot' : 'input')),
        key: 'version',
        keyName: 'version',
        slotName: 'enumText',
        rules: [{ required: true, message: '请输入版本', trigger: ['blur', 'change'] }],
        props: {
          type: 'textarea',
          rows: 1,
          maxlength: 20,
          showWordLimit: true,
          disabled: disabledValue,
        },
        span: 24,
      },
      {
        label: '发布人',
        type: computed(() => (dialogTitle.value === '查看' ? 'slot' : 'input')),
        key: 'publisher',
        keyName: 'publisher',
        slotName: 'enumText',
        rules: [{ required: true, message: '请输入发布人', trigger: ['blur', 'change'] }],
        props: {
          type: 'textarea',
          rows: 1,
          maxlength: 20,
          showWordLimit: true,
          disabled: disabledValue,
        },
        span: 24,
      },
    ],
  },
])

const dialogVisible = ref(false)
const formData = ref<FormDataType>({})

const formRef = ref<any>(null)
//修改添加
const submit = async () => {
  if (!(await formRef.value?.validate(() => true))) return
  await systemMaintenancesSaveApi(formData.value)
  dialogVisible.value = false
  SlMessage.success(formData.value.id ? '编辑成功' : '新建成功')
  proTable.value?.getTableList()
}
// 3. 删除
const handleDelete = async (id: number | string) => {
  await ElMessageBox.confirm('确认删除该日志吗？', '信息提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
  })
  await systemMaintenancesDeleteApi({ id: id })
  SlMessage.success('删除成功')
  proTable.value?.getTableList()
}

// 4. 重置弹窗
const close = () => {
  formData.value = {
    maintenanceType: '',
    maintenanceContent: '',
    version: '',
  }
  formRef.value?.resetFields()
}
</script>

<style scoped lang="sass"></style>
