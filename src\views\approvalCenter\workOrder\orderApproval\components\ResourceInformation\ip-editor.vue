<template>
  <div class="ip-editor">
    <!-- 编辑模式：显示IP输入框和校验按钮 -->
    <div v-if="isEditing" class="edit-mode">
      <el-input
        v-model="currentIp"
        :disabled="isValidating"
        :class="{ 'is-invalid': !isValid && isValidated }"
      />
      <div class="action-buttons">
        <el-button type="primary" size="small" @click="confirmIp" :disabled="!currentIp">
          确认
        </el-button>
        <el-button
          v-if="validateApi"
          type="success"
          size="small"
          @click="validateIp"
          :loading="isValidating"
          :disabled="!currentIp"
        >
          校验
        </el-button>
        <el-button type="default" size="small" @click="cancelEdit"> 取消 </el-button>
      </div>
      <div v-if="!isValid && isValidated" class="error-message">
        <el-icon class="error-icon"><Warning /></el-icon>
        <span>{{ validationMessage }}</span>
      </div>
    </div>

    <!-- 查看模式：显示IP字符串和编辑按钮 -->
    <div v-else class="view-mode">
      <span class="ip-text">{{ modelValue || '暂无IP地址' }}</span>
      <el-button v-show="!disabled" type="primary" link @click="startEdit"> 编辑 </el-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'
// import ipInput from './ip-input.vue'
import { Warning } from '@element-plus/icons-vue'

// 定义props
const props = defineProps({
  modelValue: {
    type: String,
    default: '',
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  // 校验IP的API函数
  validateApi: {
    type: Function,
    default: null,
  },
})

// 定义事件
const emit = defineEmits(['update:modelValue', 'validated'])

// 组件状态
const isEditing = ref(false)
const isValidating = ref(false)
const isValid = ref(true)
const isValidated = ref(false)
const currentIp = ref('')
const validationMessage = ref('')

// 监听外部传入的IP变化
watch(
  () => props.modelValue,
  (newVal) => {
    if (!isEditing.value) {
      currentIp.value = newVal
    }
  },
  { immediate: true },
)

// 开始编辑
const startEdit = () => {
  currentIp.value = props.modelValue
  isEditing.value = true
  isValidated.value = false
  isValid.value = true
}

// 取消编辑
const cancelEdit = () => {
  isEditing.value = false
  currentIp.value = props.modelValue
  isValidated.value = false
}

// 确认IP（不进行校验）
const confirmIp = () => {
  if (!currentIp.value) return

  // 直接更新modelValue并退出编辑模式
  emit('update:modelValue', currentIp.value)
  isEditing.value = false

  // 触发validated事件，但标记为未经校验
  emit('validated', {
    ip: currentIp.value,
    valid: true,
    message: '已保存（未校验）',
    validated: false,
  })
}

// 校验IP
const validateIp = async () => {
  if (!currentIp.value) return

  isValidating.value = true
  isValidated.value = false

  try {
    // 如果提供了校验API，则调用它
    if (props.validateApi) {
      const result = await props.validateApi(currentIp.value)

      // 假设API返回的结果格式为 { success: boolean, message: string }
      isValid.value = result.success
      validationMessage.value = result.message || (isValid.value ? '校验通过' : '校验失败')

      // 如果校验通过，更新modelValue并退出编辑模式
      if (isValid.value) {
        emit('update:modelValue', currentIp.value)
        isEditing.value = false
      }
    } else {
      // 如果没有提供校验API，则默认校验通过
      isValid.value = true
      validationMessage.value = '校验通过'
      emit('update:modelValue', currentIp.value)
      isEditing.value = false
    }

    // 触发validated事件
    emit('validated', {
      ip: currentIp.value,
      valid: isValid.value,
      message: validationMessage.value,
      validated: true,
    })
  } catch (error) {
    isValid.value = false
    validationMessage.value = '校验过程中发生错误'
    console.error('IP校验错误:', error)
  } finally {
    isValidating.value = false
    isValidated.value = true
  }
}
</script>

<style lang="scss" scoped>
.ip-editor {
  display: flex;
  flex-direction: column;

  .edit-mode {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .action-buttons {
      display: flex;
      gap: 8px;
    }

    .error-message {
      display: flex;
      align-items: flex-start;
      color: #f56c6c;
      font-size: 12px;
      margin-top: 4px;
      max-width: 270px;
      background-color: #fef0f0;
      border-radius: 4px;
      padding: 8px 10px;

      /* 增强的文本换行处理 */
      white-space: normal;
      word-wrap: break-word;
      word-break: break-word; /* 比 break-all 更友好的换行方式 */
      overflow-wrap: break-word;
      line-height: 1.5;
      overflow: hidden;

      .error-icon {
        margin-right: 6px;
        font-size: 14px;
        margin-top: 1px;
      }

      span {
        flex: 1;
      }
    }

    .is-invalid {
      :deep(.ip-input-container) {
        box-shadow: 0 0 0 1px #f56c6c inset;
      }
    }
  }

  .view-mode {
    display: flex;
    align-items: center;
    gap: 8px;

    .ip-text {
      font-size: 14px;
      color: #606266;
    }
  }
}
</style>
