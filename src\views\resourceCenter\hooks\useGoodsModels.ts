import { useUserStore } from '@/stores/modules/user'
import { type Ref } from 'vue'

export interface IEcsModel {
  // @产品名称
  instanceName: string
  // @功能模块
  functionalModule: string
  functionMedelLabel: string
  // @实例规格
  ecs: any[]
  ecsLabel: string
  // @系统
  imageOs: any[]
  imageOsLabel: string
  // @系统盘
  sysDisk: any[]
  sysDiskLabel: string
  // @是否容灾
  disasterRecovery: string
  disasterRecoveryLabel: string
  // @是否挂载数据盘
  isMountEvs: '0' | '1'
  // @数据盘
  evs: any[][]
  evsLabel: string
  // @是否绑定公网IP
  isBindPublicNetworkIp: '0' | '1'
  // @带宽
  eipAttrId: number
  eipValue: number
  // @网络平面
  planeValue: any[]
  planeLabel: string
  // @开通数量
  numbers: number
  // @申请时长
  time: string
  timeLabel: string
  // @备注
  remarks: string
  // @产品类型
  productType: 'ecs'

  // @ productType 对应的产品名称
  goodsName: '云主机'
  goodsId: string

  // 表单实例 提交需删
  ref?: Ref<any> | null
}
export interface IMysqlModel {
  // @产品名称
  instanceName: string
  // @功能模块
  functionalModule: string
  functionMedelLabel: string
  // @实例规格
  ecs: any[]
  ecsLabel: string
  // @系统
  imageOs: any[]
  imageOsLabel: string
  // @系统盘
  sysDisk: any[]
  sysDiskLabel: string
  // @是否容灾
  disasterRecovery: string
  disasterRecoveryLabel: string
  // @是否挂载数据盘
  isMountEvs: '0' | '1'
  // @数据盘
  evs: any[][]
  evsLabel: string
  // @是否绑定公网IP
  isBindPublicNetworkIp: '0' | '1'
  // @带宽
  eipAttrId: number
  eipValue: number
  // @网络平面
  planeValue: any[]
  planeLabel: string
  // @开通数量
  numbers: number
  // @申请时长
  time: string
  timeLabel: string
  // @备注
  remarks: string
  // @产品类型
  productType: 'mysql'

  // @ productType 对应的产品名称
  goodsName: '云数据库'
  goodsId: string
  // 表单实例 提交需删
  ref?: Ref<any> | null
}

export interface IRedisModel {
  // @产品名称
  instanceName: string
  // @功能模块
  functionalModule: string
  functionMedelLabel: string
  // @实例规格
  ecs: any[]
  ecsLabel: string
  // @系统
  imageOs: any[]
  imageOsLabel: string
  // @系统盘
  sysDisk: any[]
  sysDiskLabel: string
  // @是否容灾
  disasterRecovery: string
  disasterRecoveryLabel: string
  // @是否挂载数据盘
  isMountEvs: '0' | '1'
  // @数据盘
  evs: any[][]
  evsLabel: string
  // @是否绑定公网IP
  isBindPublicNetworkIp: '0' | '1'
  // @带宽
  eipAttrId: number
  eipValue: number
  // @网络平面
  planeValue: any[]
  planeLabel: string
  // @开通数量
  numbers: number
  // @申请时长
  time: string
  timeLabel: string
  // @备注
  remarks: string
  // @产品类型
  productType: 'redis'

  // @ productType 对应的产品名称
  goodsName: '云中间件'
  goodsId: string
  // 表单实例 提交需删
  ref?: Ref<any> | null
}

export interface IGcsModel {
  // @产品名称
  instanceName: string
  // @功能模块
  functionalModule: string
  // @实例规格
  gcs: any[]
  // @系统
  imageOs: any[]
  // @系统盘
  sysDisk: any[]
  // @是否容灾
  disasterRecovery: string
  // @是否挂载数据盘
  isMountEvs: '0' | '1'
  // @数据盘
  evs: any[][]
  // @是否绑定公网IP
  isBindPublicNetworkIp: '0' | '1'
  // @带宽
  eipAttrId: number
  eipValue: number
  // @网络平面
  planeValue: any[]
  // @开通数量
  numbers: number
  // @申请时长
  time: string
  // @备注
  remarks: string
  // @产品类型
  productType: 'gcs'
  goodsId: string
  // @ productType 对应的产品名称
  goodsName: 'GPU云主机'
}
export interface IEvsModel {
  // @产品名称
  instanceName: string
  // @功能模块
  functionalModule: string
  // @数据盘
  evs: any[][]
  // @开通数量
  numbers: number
  // @申请时长
  time: string
  // @备注
  remarks: string
  // @产品类型
  productType: 'evs'

  isMountEcs: '0' | '1'

  // @ productType 对应的产品名称
  goodsName: '云硬盘'
  goodsId: string
  ecsName: string
  vmId: string
}
export interface IEipModel {
  // @产品名称
  instanceName: string
  // @功能模块
  functionalModule: string
  // @开通数量
  numbers: number
  // @申请时长
  time: string
  // @产品类型
  productType: 'eip'

  // @ productType 对应的产品名称
  goodsName: '弹性IP'
  goodsId: string
  ecsName: string
  vmId: string
  eipValue: number
}
export interface IObsModel {
  goodsId: string
  // @产品名称
  instanceName: string
  // @功能模块
  functionalModule: string
  // @是否挂载数据盘
  isMountobs: '1' | '0'
  // @数据盘
  obs: [string, number]
  // @开通数量
  numbers: number
  // @申请时长
  time: string
  // @备注
  remarks: string
  // @产品类型
  productType: 'obs'

  // @ productType 对应的产品名称
  goodsName: '对象存储'
}
export interface ISlbModel {
  // @产品名称
  instanceName: string
  // @功能模块
  functionalModule: string
  // @实例规格
  slb: string
  desc: string
  // @是否绑定公网IP
  isBindPublicNetworkIp: '0' | '1'
  // @带宽
  eipAttrId: number
  eipValue: number
  // @开通数量
  numbers: number
  // @申请时长
  time: string
  // @备注
  remarks: string
  // @产品类型
  productType: 'slb'
  // @ productType 对应的产品名称
  goodsName: '负载均衡'
  goodsId: string
}
export interface INatModel {
  // @产品名称
  instanceName: string
  // @功能模块
  functionalModule: string
  // @实例规格
  nat: string
  desc: string
  // @是否绑定公网IP
  isBindPublicNetworkIp: '0' | '1'
  // @带宽
  eipAttrId: number
  eipValue: number
  // @开通数量
  numbers: number
  // @申请时长
  time: string
  // @备注
  remarks: string
  // @产品类型
  productType: 'nat'

  // @ productType 对应的产品名称
  goodsName: 'NAT网关'
  goodsId: string
}
export interface ICqModel {
  // 产品名称
  instanceName: string
  // // 物理GPU算力大小
  // gpuRatio: number
  // // 物理GPU核心数
  // gpuCore: number
  // // 物理GPU显存大小
  // gpuVirtualMemory: number
  gpu: [number, number, number]
  // // 容器配额大小
  // vCpus: number
  // // 容器配额内存
  // ram: number
  cpu: [number, number]
  // 4A账号数
  a4Accoun: string
  // 4A手机号
  a4Phone: string
  // 是否使用GPU
  isUseGpu: '0' | '1'
  // 申请时长
  time: string
}
export interface IBaseModel {
  applicant: string
  department: string
  workOrdertype: string
  orderTitle: string
  // @工单id
  id: string
  // @业务系统id
  busiSystemId: string
  // 局方负责人
  applyUserName: string
  // @厂家
  manufacturer: string
  // @厂家负责人
  manufacturerContacts: string
  // @厂家电话
  manufacturerMobile: string
  // @所属业务模块
  moduleId: string
  // @二级部门领导
  busiDepartLeaderId: string
  // @三级部门领导
  levelThreeLeaderId: string
  // @资源申请说明
  orderDesc: string
  // @资源上云说明书
  files: any[]

  // @操作名称
  operationName: string
  moduleName: string
  busiDepartLeaderLabel: string
  levelThreeLeaderLabel: string
  busiSystemName: string
  // 4A账号数
  a4Account?: string
  // 4A手机号
  a4Phone?: string
}

export function useEcsModel() {
  const model: IEcsModel = {
    // @产品名称
    instanceName: '',
    // @功能模块
    functionalModule: '',
    functionMedelLabel: '',
    // @实例规格
    ecs: [],
    ecsLabel: '',
    // @系统
    imageOs: [],
    imageOsLabel: '',
    // @系统盘
    sysDisk: ['', 40],
    sysDiskLabel: '',
    // @是否容灾
    disasterRecovery: '-1',
    disasterRecoveryLabel: '',
    // @是否挂载数据盘
    isMountEvs: '0',
    // @数据盘
    evs: [[]],
    evsLabel: '',
    // @是否绑定公网IP
    isBindPublicNetworkIp: '0',
    // @带宽
    eipAttrId: 0,
    eipValue: 5,
    // @网络平面
    planeValue: [],
    planeLabel: '',
    // @开通数量
    numbers: 1,
    // @申请时长
    time: '',
    timeLabel: '',
    // @备注
    remarks: '',
    // @产品类型
    productType: 'ecs',

    // @ productType 对应的产品名称
    goodsName: '云主机',
    goodsId: '',
  }
  return model
}
export function useRedisModel() {
  const model: IRedisModel = {
    // @产品名称
    instanceName: '',
    // @功能模块
    functionalModule: '',
    functionMedelLabel: '',
    // @实例规格
    ecs: [],
    ecsLabel: '',
    // @系统
    imageOs: [],
    imageOsLabel: '',
    // @系统盘
    sysDisk: ['', 40],
    sysDiskLabel: '',
    // @是否容灾
    disasterRecovery: '-1',
    disasterRecoveryLabel: '',
    // @是否挂载数据盘
    isMountEvs: '0',
    // @数据盘
    evs: [[]],
    evsLabel: '',
    // @是否绑定公网IP
    isBindPublicNetworkIp: '0',
    // @带宽
    eipAttrId: 0,
    eipValue: 5,
    // @网络平面
    planeValue: [],
    planeLabel: '',
    // @开通数量
    numbers: 1,
    // @申请时长
    time: '',
    timeLabel: '',
    // @备注
    remarks: '',
    // @产品类型
    productType: 'redis',

    // @ productType 对应的产品名称
    goodsName: '云中间件',
    goodsId: '',
  }
  return model
}
export function useMysqlModel() {
  const model: IMysqlModel = {
    // @产品名称
    instanceName: '',
    // @功能模块
    functionalModule: '',
    functionMedelLabel: '',
    // @实例规格
    ecs: [],
    ecsLabel: '',
    // @系统
    imageOs: [],
    imageOsLabel: '',
    // @系统盘
    sysDisk: ['', 40],
    sysDiskLabel: '',
    // @是否容灾
    disasterRecovery: '-1',
    disasterRecoveryLabel: '',
    // @是否挂载数据盘
    isMountEvs: '0',
    // @数据盘
    evs: [[]],
    evsLabel: '',
    // @是否绑定公网IP
    isBindPublicNetworkIp: '0',
    // @带宽
    eipAttrId: 0,
    eipValue: 5,
    // @网络平面
    planeValue: [],
    planeLabel: '',
    // @开通数量
    numbers: 1,
    // @申请时长
    time: '',
    timeLabel: '',
    // @备注
    remarks: '',
    // @产品类型
    productType: 'mysql',

    // @ productType 对应的产品名称
    goodsName: '云数据库',
    goodsId: '',
  }
  return model
}
export function useGcsModel() {
  const model: IGcsModel = {
    // @产品名称
    instanceName: '',
    // @功能模块
    functionalModule: '',
    // @实例规格
    gcs: [],
    // @系统
    imageOs: [],
    // @系统盘
    sysDisk: ['', 40],
    // @是否容灾
    disasterRecovery: '-1',
    // @是否挂载数据盘
    isMountEvs: '0',
    // @数据盘
    evs: [[]],
    // @是否绑定公网IP
    isBindPublicNetworkIp: '0',
    // @带宽
    eipAttrId: 0,
    eipValue: 5,
    // @网络平面
    planeValue: [],
    // @开通数量
    numbers: 1,
    // @申请时长
    time: '',
    // @备注
    remarks: '',
    // @产品类型
    productType: 'gcs',
    goodsId: '',

    // @ productType 对应的产品名称
    goodsName: 'GPU云主机',
  }
  return model
}
export function useEvsModel() {
  const model: IEvsModel = {
    // @产品名称
    instanceName: '',
    // @功能模块
    functionalModule: '',
    // @数据盘
    evs: [['', 20]],
    // @开通数量
    numbers: 1,
    // @申请时长
    time: '',
    // @备注
    remarks: '',
    // @产品类型
    productType: 'evs',

    isMountEcs: '0',

    // @ productType 对应的产品名称
    goodsName: '云硬盘',
    goodsId: '',
    ecsName: '',
    vmId: '',
  }
  return model
}
export function useEipModel() {
  const model: IEipModel = {
    // @产品名称
    instanceName: '',
    // @功能模块
    functionalModule: '',
    // @开通数量
    numbers: 1,
    // @申请时长
    time: '',
    // @产品类型
    productType: 'eip',

    // @ productType 对应的产品名称
    goodsName: '弹性IP',
    goodsId: '',
    ecsName: '',
    vmId: '',
    eipValue: 1,
  }
  return model
}

export function useSlbModel() {
  const model: ISlbModel = {
    // @产品名称
    instanceName: '',
    // @功能模块
    functionalModule: '',
    // @实例规格
    slb: '',
    desc: '',
    // @是否绑定公网IP
    isBindPublicNetworkIp: '0',
    // @带宽
    eipAttrId: 0,
    eipValue: 5,
    // @开通数量
    numbers: 1,
    // @申请时长
    time: '',
    // @备注
    remarks: '',
    // @产品类型
    productType: 'slb',

    // @ productType 对应的产品名称
    goodsName: '负载均衡',
    goodsId: '',
  }
  return model
}
export function useCqModel() {
  const model: ICqModel = {
    // 产品名称
    instanceName: '',
    cpu: [0, 0],
    gpu: [0, 0, 0],
    // 4A账号数
    a4Accoun: '',
    // 4A手机号
    a4Phone: '',
    // 是否使用GPU
    isUseGpu: '0',
    // 申请时长
    time: '',
  }
  return model
}
export function useNatModel() {
  const model: INatModel = {
    // @产品名称
    instanceName: '',
    // @功能模块
    functionalModule: '',
    // @实例规格
    nat: '',
    desc: '',
    // @是否绑定公网IP
    isBindPublicNetworkIp: '0',
    // @带宽
    eipAttrId: 0,
    eipValue: 5,
    // @开通数量
    numbers: 1,
    // @申请时长
    time: '',
    // @备注
    remarks: '',
    // @产品类型
    productType: 'nat',

    // @ productType 对应的产品名称
    goodsName: 'NAT网关',
    goodsId: '',
  }
  return model
}
export function useObsModel() {
  const model: IObsModel = {
    goodsId: '',
    // @产品名称
    instanceName: '',
    // @功能模块
    functionalModule: '',
    // @是否挂载数据盘
    isMountobs: '1',
    // @数据盘
    obs: ['', 1],
    // @开通数量
    numbers: 1,
    // @申请时长
    time: '',
    // @备注
    remarks: '',
    // @产品类型
    productType: 'obs',

    // @ productType 对应的产品名称
    goodsName: '对象存储',
  }
  return model
}
export function useBaseModel() {
  const userStore = useUserStore()
  const model: IBaseModel = {
    applicant: userStore.userInfo.userName,
    department: userStore.userInfo.sysDeptName,
    workOrdertype: '开通申请',
    orderTitle: '',
    // @工单id
    id: '',
    // @业务系统id
    busiSystemId: '',
    // 局方负责人
    applyUserName: '',
    // @厂家
    manufacturer: '',
    // @厂家负责人
    manufacturerContacts: '',
    // @厂家电话
    manufacturerMobile: '',
    // @所属业务模块
    moduleId: '',
    // @二级部门领导
    busiDepartLeaderId: '',
    // @三级部门领导
    levelThreeLeaderId: '',
    // @资源申请说明
    orderDesc: '',
    // @资源上云说明书
    files: [],

    // @操作名称
    operationName: '开通资源申请单',
    moduleName: '',
    busiDepartLeaderLabel: '',
    levelThreeLeaderLabel: '',
    busiSystemName: '',
    a4Account: '',
    a4Phone: '',
  }
  return model
}

export interface ICycleBinBaseModel {
  applicant: string
  department: string
  workOrdertype: string
  orderTitle: string
  // @工单id
  id: string
  // @业务系统id
  busiSystemId: string
  // 局方负责人
  applyUserName: string
  // @厂家
  manufacturer: string
  // @厂家负责人
  manufacturerContacts: string
  // @厂家电话
  manufacturerMobile: string
  // @三级部门领导
  levelThreeLeaderId: string
  // @资源申请说明
  orderDesc: string
  // @资源上云说明书
  files: any[]

  // @操作名称
  operationName: string
  levelThreeLeaderLabel: string
  busiSystemName: string
}

export function useCycleBaseModel() {
  const userStore = useUserStore()
  const model: ICycleBinBaseModel = {
    applicant: userStore.userInfo.userName,
    department: userStore.userInfo.sysDeptName,
    workOrdertype: '回收申请',
    orderTitle: '',
    // @工单id
    id: '',
    // @业务系统id
    busiSystemId: '',
    // 局方负责人
    applyUserName: '',
    // @厂家
    manufacturer: '',
    // @厂家负责人
    manufacturerContacts: '',
    // @厂家电话
    manufacturerMobile: '',
    // @三级部门领导
    levelThreeLeaderId: '',
    // @资源申请说明
    orderDesc: '',
    // @资源上云说明书
    files: [],

    // @操作名称
    operationName: '回收资源申请单',
    levelThreeLeaderLabel: '',
    busiSystemName: '',
  }
  return model
}
