<template>
  <div class="ip-input-container" :class="{ 'is-disabled': disabled, 'is-error': !!errorMessage }">
    <div class="ip-segments">
      <!-- 第一段 -->
      <el-input
        v-model="ipSegments[0]"
        :maxlength="3"
        class="segment-input"
        :class="{ 'has-error': segmentErrors[0] }"
        @input="handleInput(0)"
        @keyup.right="focusNext(0)"
        @keyup.delete="handleDelete(0)"
        @keydown.enter="handleEnter"
        @focus="handleFocus(0)"
        @blur="handleBlur(0)"
        :disabled="disabled"
        :placeholder="placeholders[0]"
        :ref="
          (el) => {
            if (el) inputRefs[0] = el
          }
        "
      />
      <span class="ip-dot">.</span>

      <!-- 第二段 -->
      <el-input
        v-model="ipSegments[1]"
        :maxlength="3"
        class="segment-input"
        :class="{ 'has-error': segmentErrors[1] }"
        @input="handleInput(1)"
        @keyup.right="focusNext(1)"
        @keyup.left="focusPrev(1)"
        @keyup.delete="handleDelete(1)"
        @keydown.enter="handleEnter"
        @focus="handleFocus(1)"
        @blur="handleBlur(1)"
        :disabled="disabled"
        :placeholder="placeholders[1]"
        :ref="
          (el) => {
            if (el) inputRefs[1] = el
          }
        "
      />
      <span class="ip-dot">.</span>

      <!-- 第三段 -->
      <el-input
        v-model="ipSegments[2]"
        :maxlength="3"
        class="segment-input"
        :class="{ 'has-error': segmentErrors[2] }"
        @input="handleInput(2)"
        @keyup.right="focusNext(2)"
        @keyup.left="focusPrev(2)"
        @keyup.delete="handleDelete(2)"
        @keydown.enter="handleEnter"
        @focus="handleFocus(2)"
        @blur="handleBlur(2)"
        :disabled="disabled"
        :placeholder="placeholders[2]"
        :ref="
          (el) => {
            if (el) inputRefs[2] = el
          }
        "
      />
      <span class="ip-dot">.</span>

      <!-- 第四段 -->
      <el-input
        v-model="ipSegments[3]"
        :maxlength="3"
        class="segment-input"
        :class="{ 'has-error': segmentErrors[3] }"
        @input="handleInput(3)"
        @keyup.left="focusPrev(3)"
        @keyup.delete="handleDelete(3)"
        @keydown.enter="handleEnter"
        @focus="handleFocus(3)"
        @blur="handleBlur(3)"
        :disabled="disabled"
        :placeholder="placeholders[3]"
        :ref="
          (el) => {
            if (el) inputRefs[3] = el
          }
        "
      />
    </div>

    <div v-if="errorMessage" class="error-container">
      <el-tooltip :content="errorMessage" effect="dark" placement="top">
        <el-icon class="error-icon"><WarningFilled /></el-icon>
      </el-tooltip>
    </div>

    <div v-if="showClearButton && !disabled" class="clear-button" @click="clearIp">
      <el-icon><CircleClose /></el-icon>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, nextTick, computed, onMounted } from 'vue'
import { WarningFilled, CircleClose } from '@element-plus/icons-vue'

// 定义props类型
const props = defineProps({
  modelValue: {
    type: String,
    default: '',
  },
  placeholder: {
    type: String,
    default: '0',
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  clearable: {
    type: Boolean,
    default: true,
  },
  validateOnInput: {
    type: Boolean,
    default: false,
  },
  autoFocus: {
    type: Boolean,
    default: false,
  },
})

// 定义emits类型
const emits = defineEmits<{
  (event: 'update:modelValue', value: string): void
  (event: 'blur', value: string): void
  (event: 'focus', value: string): void
  (event: 'enter', value: string): void
  (event: 'change', value: string): void
  (event: 'clear'): void
}>()

// 初始化IP段
const ipSegments = ref<string[]>(['', '', '', ''])
const errorMessage = ref<string>('')
const segmentErrors = ref<boolean[]>([false, false, false, false])
const inputRefs = ref<any[]>([])
const isFocused = ref<boolean>(false)

// 计算属性
const showClearButton = computed(() => {
  return props.clearable && ipSegments.value.some((segment) => segment !== '')
})

const placeholders = computed(() => {
  return Array(4).fill(props.placeholder)
})

// IP验证正则
const IP_REGEX =
  /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/

// 验证单个IP段
const validateSegment = (segment: string, index: number): boolean => {
  if (!segment) return true // 空值不验证

  const num = parseInt(segment, 10)
  if (isNaN(num) || num < 0 || num > 255) {
    segmentErrors.value[index] = true
    return false
  }

  segmentErrors.value[index] = false
  return true
}

// 验证整个IP地址
const validateIp = (): boolean => {
  // 如果有空段，不进行完整验证
  if (ipSegments.value.some((segment) => segment === '')) {
    return true
  }

  const ip = ipSegments.value.join('.')
  const isValid = IP_REGEX.test(ip)

  if (!isValid) {
    errorMessage.value = 'IP地址格式不正确'
    return false
  }

  errorMessage.value = ''
  return true
}

// 监听外部传入的IP变化
watch(
  () => props.modelValue,
  (newVal: string) => {
    if (!newVal) {
      ipSegments.value = ['', '', '', '']
      errorMessage.value = ''
      segmentErrors.value = [false, false, false, false]
      return
    }

    const parts = newVal.split('.')
    if (parts.length === 4) {
      ipSegments.value = parts

      // 验证每个段
      parts.forEach((part, index) => {
        validateSegment(part, index)
      })

      // 验证整个IP
      validateIp()
    } else {
      errorMessage.value = 'IP地址格式不正确'
    }
  },
  { immediate: true },
)

// 监听IP段变化，更新整体IP
watch(
  ipSegments,
  () => {
    // 检查是否所有段都为空
    const allEmpty = ipSegments.value.every((segment) => segment === '')

    // 当所有段都为空时，返回空字符串，否则始终使用点号连接
    const ipValue = allEmpty ? '' : ipSegments.value.join('.')

    // 始终同步赋值，无论是否所有段都填写
    emits('update:modelValue', ipValue)
    emits('change', ipValue)

    // 如果设置了输入时验证，则验证IP
    if (props.validateOnInput) {
      validateIp()
    }
  },
  { deep: true },
)

// 处理输入
const handleInput = (index: number) => {
  // 限制只能输入数字
  ipSegments.value[index] = ipSegments.value[index].replace(/[^\d]/g, '')

  // 验证当前段
  validateSegment(ipSegments.value[index], index)

  // 自动跳到下一段
  if (ipSegments.value[index].length === 3 && index < 3) {
    focusNext(index)
  }
}

// 处理焦点
const handleFocus = (index: number) => {
  console.log(index)

  isFocused.value = true

  // 当所有段都为空时，返回空字符串而不是 "..."
  const allEmpty = ipSegments.value.every((segment) => segment === '')
  const ipValue = allEmpty ? '' : ipSegments.value.join('.')
  emits('focus', ipValue)
}

// 处理失焦
const handleBlur = (index: number) => {
  isFocused.value = false

  // 格式化当前段
  formatSegment(index)

  // 验证整个IP
  validateIp()

  // 当所有段都为空时，返回空字符串而不是 "..."
  const allEmpty = ipSegments.value.every((segment) => segment === '')
  const ipValue = allEmpty ? '' : ipSegments.value.join('.')
  emits('blur', ipValue)
}

// 格式化IP段
const formatSegment = (index: number) => {
  let segment = ipSegments.value[index]

  // 空值不处理
  if (!segment) return

  // 限制范围0-255
  const num = parseInt(segment, 10)
  if (isNaN(num)) {
    ipSegments.value[index] = ''
    return
  }

  if (num > 255) {
    ipSegments.value[index] = '255'
    segmentErrors.value[index] = true
  } else if (num < 0) {
    ipSegments.value[index] = '0'
    segmentErrors.value[index] = true
  } else {
    // 移除前导零
    ipSegments.value[index] = num.toString()
    segmentErrors.value[index] = false
  }
}

// 处理删除键
const handleDelete = (index: number) => {
  // 如果当前段为空且不是第一段，则移动到前一段
  if (ipSegments.value[index] === '' && index > 0) {
    focusPrev(index)
  }
}

// 焦点移动到下一个输入框
const focusNext = (index: number) => {
  if (index < 3) {
    nextTick(() => {
      if (inputRefs.value[index + 1]) {
        inputRefs.value[index + 1].focus()
      }
    })
  }
}

// 焦点移动到上一个输入框
const focusPrev = (index: number) => {
  if (index > 0) {
    nextTick(() => {
      if (inputRefs.value[index - 1]) {
        inputRefs.value[index - 1].focus()
      }
    })
  }
}

// 清空IP地址
const clearIp = () => {
  ipSegments.value = ['', '', '', '']
  errorMessage.value = ''
  segmentErrors.value = [false, false, false, false]
  emits('update:modelValue', '')
  emits('clear')
}

// 处理回车键
const handleEnter = () => {
  validateIp()
  // 当所有段都为空时，返回空字符串而不是 "..."
  const allEmpty = ipSegments.value.every((segment) => segment === '')
  const ipValue = allEmpty ? '' : ipSegments.value.join('.')
  emits('enter', ipValue)
}

// 组件挂载后，如果设置了自动聚焦，则聚焦第一个输入框
onMounted(() => {
  if (props.autoFocus && !props.disabled) {
    nextTick(() => {
      if (inputRefs.value[0]) {
        inputRefs.value[0].focus()
      }
    })
  }
})
</script>

<style lang="scss" scoped>
.ip-input-container {
  display: flex;
  align-items: center;
  position: relative;
  width: 250px; /* 固定宽度 */
  box-sizing: border-box;
  height: 32px; /* 与 Element Plus 输入框高度一致 */

  /* 模拟 Element Plus 输入框的样式 */
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 0 0 1px #dcdfe6 inset;
  transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  padding: 1px 11px;

  &:hover {
    box-shadow: 0 0 0 1px #c0c4cc inset;
  }

  &:focus-within {
    box-shadow: 0 0 0 1px #409eff inset;
  }

  &.is-disabled {
    background-color: #f5f7fa;
    box-shadow: 0 0 0 1px #e4e7ed inset;
    color: #c0c4cc;
    cursor: not-allowed;
  }

  &.is-error {
    box-shadow: 0 0 0 1px #f56c6c inset;
  }

  .ip-segments {
    display: flex;
    align-items: center;
    width: 220px; /* 固定宽度 */
    position: relative;

    .segment-input {
      width: 45px;
      text-align: center;
      margin: 0;

      &.has-error :deep(.el-input__inner) {
        color: #f56c6c;
      }

      :deep(.el-input__inner) {
        text-align: center;
        padding: 0;
        border: none;
        height: 30px;
        line-height: 30px;
        font-size: 14px;
        color: #606266;

        &:focus {
          outline: none;
        }

        &::placeholder {
          color: #c0c4cc;
        }
      }

      :deep(.el-input__wrapper) {
        box-shadow: none !important;
        padding: 0;
        background: transparent;
        border-radius: 0;
      }

      :deep(.el-input__suffix) {
        display: none; /* 隐藏后缀图标 */
      }
    }

    .ip-dot {
      display: inline-block;
      width: 8px;
      text-align: center;
      color: #606266;
      font-weight: normal;
      margin: 0;
      user-select: none;
      font-size: 14px;
    }
  }

  .error-container {
    margin-left: 8px;
    display: flex;
    align-items: center;

    .error-icon {
      color: #f56c6c;
      font-size: 14px;
      cursor: pointer;
    }
  }

  .clear-button {
    margin-left: 8px;
    color: #c0c4cc;
    cursor: pointer;
    display: flex;
    align-items: center;
    font-size: 14px;

    &:hover {
      color: #909399;
    }
  }
}
</style>
