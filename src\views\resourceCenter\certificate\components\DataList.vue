<template>
  <SlProTable
    ref="proTable"
    highlight-current-row
    :columns="columns"
    style="min-height: 300px"
    :request-api="getCertificateList"
    :init-param="queryParams"
    :current-change="currentChange"
    hidden-table-header
    row-key="id"
  >
  </SlProTable>
</template>

<script setup lang="tsx" name="DataList">
import { ref, computed } from 'vue'
import type { ColumnProps, ProTableInstance } from '@/components/SlProTable/interface'
import { getCertificateList, deleteCertificate } from '@/api/modules/resourecenter'
import { ElMessageBox, ElMessage } from 'element-plus'
import type { CertificateItem } from '@/views/resourceCenter/types'

const props = defineProps({
  queryParams: {
    type: Object,
    default: () => ({}),
  },
  isSelectMode: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['currentChange', 'selected'])

const currentChange = (currentRow: any, oldCurrentRow: any) => {
  emit('currentChange', currentRow, oldCurrentRow)
}

// 表格列配置
const columns = computed(() => {
  const baseColumns: ColumnProps<CertificateItem>[] = [
    { type: 'index', label: '编号', width: 55, fixed: 'left' },
    { prop: 'certificateName', label: '证书名称', fixed: 'left' },
    { prop: 'businessSystemName', label: '业务系统' },
    { prop: 'catalogueDomainName', label: '所属云' },
    { prop: 'regionName', label: '所属资源池' },
    {
      prop: 'certificateType',
      label: '证书类型',
      enum: [
        { label: '服务器证书', value: 'SERVER' },
        { label: '客户端证书', value: 'CLIENT' },
      ],
    },
    { prop: 'associatedListeners', label: '关联监听' },
    { prop: 'createTime', label: '创建时间', width: 180 },
  ]

  // 如果不是选择模式，添加操作列
  if (!props.isSelectMode) {
    baseColumns.push({
      prop: 'operation',
      label: '操作',
      width: 120,
      fixed: 'right',
      render: operationRender,
    })
  } else {
    // 选择模式下添加操作列
    baseColumns.push({
      prop: 'operation',
      label: '操作',
      width: 100,
      fixed: 'right',
      render: selectOperationRender,
    })
  }

  return baseColumns
})

const proTable = ref<ProTableInstance>()

// 操作列渲染
function operationRender({ row }: { row: CertificateItem }) {
  return (
    <>
      <el-button onClick={() => handleDelete(row)} type="primary" link>
        删除
      </el-button>
    </>
  )
}

// 选择模式下的操作列渲染
function selectOperationRender({ row }: { row: CertificateItem }) {
  return (
    <>
      <el-button onClick={() => handleSelect(row)} type="primary" link>
        选择
      </el-button>
    </>
  )
}

// 删除证书
const handleDelete = (row: CertificateItem) => {
  ElMessageBox.confirm(`确定要删除证书"${row.certificateName}"吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      try {
        const { code, message } = await deleteCertificate({ id: row.id })
        if (code === 200) {
          ElMessage.success('删除成功')
          // 刷新表格
          proTable.value?.getTableList()
        } else {
          ElMessage.error(message || '删除失败')
        }
      } catch (error) {
        console.error('删除证书失败', error)
        ElMessage.error('删除证书失败，请稍后重试')
      }
    })
    .catch(() => {
      // 取消删除
    })
}

// 选择证书
const handleSelect = (row: CertificateItem) => {
  emit('selected', row)
}

defineExpose({
  proTable,
})
</script>

<style lang="scss" scoped>
.data-list {
  width: 100%;
}
</style>
