import { changeDateFormat } from '@/utils'
// 对象存储名称检验
export function validObsName(input: string): boolean {
  // 检查长度是否在 3-64 之间
  if (input.length < 3 || input.length > 64) {
    return false
  }

  // 检查是否只包含小写字母和数字
  if (!/^[a-z0-9]+$/.test(input)) {
    return false
  }

  // 检查是否以小写字母或数字开头和结尾
  if (!/^[a-z0-9]/.test(input) || !/[a-z0-9]$/.test(input)) {
    return false
  }
  return true
}

/**
 * 检验字符串是否只包含字母、数字和下划线
 * @param input 输入的字符串
 * @returns 如果字符串只包含字母、数字和下划线，返回 true；否则返回 false
 */
export function isValidAlphanumericWithUnderscore(input: string): boolean {
  // 定义正则表达式：只允许中文、字母、数字、下划线、中划线'
  const regex = /^[a-zA-Z0-9_\u4e00-\u9fa5-]+$/

  // 使用正则表达式匹配输入字符串
  return regex.test(input)
}

export const validateGoodsName = (rule: any, value: any, callback: any) => {
  if (!isValidAlphanumericWithUnderscore(value)) {
    callback(new Error('只能包含中文、字母、数字、下划线、中划线'))
  } else {
    callback()
  }
}
export const validateObsName = (rule: any, value: any, callback: any) => {
  if (!validObsName(value)) {
    callback(new Error('名称不符合规范'))
  } else {
    callback()
  }
}

/**
 * 计算字符串的 CRC32 校验值
 * @param {string} str - 输入的字符串
 * @returns {string} - 返回 CRC32 校验值的十六进制字符串
 */
export function crc32(str: string) {
  const crcTable = (function () {
    const table = []
    for (let i = 0; i < 256; i++) {
      let crc = i
      for (let j = 0; j < 8; j++) {
        crc = crc & 1 ? (crc >>> 1) ^ 0xedb88320 : crc >>> 1
      }
      table[i] = crc
    }
    return table
  })()
  let crc = 0xffffffff
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i)
    crc = (crc >>> 8) ^ crcTable[(crc ^ char) & 0xff]
  }
  return (crc ^ 0xffffffff).toString(16).padStart(8, '0')
}

export function generateUUID8(): string {
  // 生成一个标准的UUID
  const uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0,
      v = c === 'x' ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })

  // 截取前8位
  return uuid.substring(0, 8)
}

export function normalizeExportArgs(
  api: (...args: any) => Promise<any>,
  type: string,
  fileName: string,
  params: { [key in string]: any },
) {
  return [
    api,
    fileName,
    {
      type,
      object: changeDateFormat(params, ['effectiveTime', 'expireTime', 'createdTime']),
    },
  ] as const
}
