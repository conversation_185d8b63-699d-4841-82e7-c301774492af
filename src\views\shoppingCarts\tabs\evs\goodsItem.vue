<template>
  <div>
    <sl-form
      class="goods-info-form"
      size="small"
      ref="slFormRef"
      :options="goodsInfoOptions"
      :model-value="goods.orderJson"
    >
      <!-- 删除按钮 -->
      <template #globalFormSlot>
        <div @click="handleGoodsDelete" class="goods-del-btn">
          <el-icon><CircleCloseFilled /></el-icon>
        </div>
      </template>
      <template #evsSlot="{ form, item }">
        <el-form-item
          class="evs-item"
          v-for="(evs, evsIndex) in form[item.key]"
          :key="evsIndex"
          :prop="item.key + '.' + evsIndex"
          :rules="evsRules"
        >
          <div class="evs-item-content">
            <el-select
              style="flex: 1"
              clearable
              :disabled="item.disabled"
              v-model="form[item.key][evsIndex][0]"
            >
              <el-option
                :key="option.value"
                v-for="option in item.options"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
            <el-input-number
              :disabled="item.disabled"
              v-bind="item.props"
              v-model="form[item.key][evsIndex][1]"
              style="margin: 0 4px; min-width: 90px"
            />
            <span>GB</span>
          </div>
        </el-form-item>
      </template>
      <template #isMountEcsSlot="{ form, item }">
        <div style="flex-grow: 0.12">
          <el-switch v-model="form[item.key]" active-value="1" inactive-value="0" />
        </div>
        <div class="sle" style="width: 120px">
          <el-input disabled :value="form[item.ecsName] || ''"></el-input>
        </div>
      </template>
    </sl-form>
    <ecs-dailog
      @confirm="selectEcs"
      @dialogClose="closeEscDialog"
      v-if="dialogVisible"
      title="选择云主机"
      v-model="dialogVisible"
    ></ecs-dailog>
  </div>
</template>
<script setup lang="ts">
import { reactive, ref, watch } from 'vue'
import type { IEvsModel } from '@/views/resourceCenter/hooks/useGoodsModels'
import type { IGoodsItem } from '@/views/resourceCenter/hooks/useShoppingCarts'
import { CircleCloseFilled } from '@element-plus/icons-vue'
import { useGlobalDicStore } from '@/stores/modules/dic'
import { validateEmpty } from '@/utils/validate'
import eventBus from '@/utils/eventBus'
import ecsDailog from '../ecsDailog.vue'
import slForm from '@/components/form/SlForm.vue'
import { useRoute } from 'vue-router'

const rouete = useRoute()

const globalDic = useGlobalDicStore()
const { getDic } = globalDic

const props = defineProps<{
  goods: IGoodsItem<IEvsModel>
}>()
function handleGoodsDelete() {
  eventBus.emit('shoppingCarts:deleteGoods', {
    goods: props.goods,
    isEdit: rouete.query.orderId ? true : false,
  })
}
const formModel = props.goods.orderJson
const dialogVisible = ref(false)
watch(
  () => formModel.isMountEcs,
  () => {
    if (formModel.isMountEcs === '1') {
      dialogVisible.value = true
    } else {
      formModel.vmId = ''
      formModel.ecsName = ''
    }
  },
)
const slFormRef = ref()
setRef(props.goods)
function setRef(goods: IGoodsItem<IEvsModel>) {
  goods.ref = slFormRef
}

const validateevsTypeevsSize = (rule: any, value: any, callback: any) => {
  let error = ''
  if (!value[0] && !value[1]) {
    error = '请选择数据盘类型并输入数据盘大小'
  } else if (!value[0] && value[1]) {
    error = '请选择数据盘类型'
  } else if (value[0] && !value[1]) {
    error = '请输入数据盘大小'
  }
  error ? callback(new Error(error)) : callback()
}
function selectEcs(ecsItem: any) {
  formModel.vmId = ecsItem.deviceId
  formModel.ecsName = ecsItem.deviceName
}
function closeEscDialog() {
  formModel.isMountEcs = '0'
}

const evsRules = [
  {
    validator: validateevsTypeevsSize,
    trigger: 'change',
  },
]

const goodsInfoOptions = reactive([
  {
    style: 'margin:0;padding:0;background: rgb(237 245 255);padding-top: 18px;',
    gutter: 40,
    groupItems: [
      {
        label: '数据盘',
        type: 'slot',
        slotName: 'evsSlot',
        key: 'evs',
        options: getDic('evs'),
        span: 8,
        required: true,
        props: {
          min: 20,
          max: 2048,
          step: 1,
        },
      },
      {
        label: '功能模块',
        type: 'select',
        key: 'functionalModule',
        options: getDic('functionalModule'),
        span: 8,
        rules: {
          required: true,
          message: '请选择功能模块',
          trigger: ['blur', 'change'],
        },
      },
      {
        label: '是否挂载云主机',
        type: 'slot',
        slotName: 'isMountEcsSlot',
        key: 'isMountEcs',
        ecsName: 'ecsName',
        options: getDic('trueOrFalse'),
        span: 8,
        suffixSlot: true,
        rules: [{ required: true, message: '请选择是否是否挂云主机', trigger: ['blur', 'change'] }],
      },
      {
        label: '申请时长',
        type: 'select',
        key: 'time',
        span: 8,
        options: getDic('time'),
        rules: [{ required: true, message: '请选择申请时长', trigger: ['blur', 'change'] }],
      },
      {
        label: '开通数量',
        type: 'inputNumber',
        key: 'numbers',
        props: {
          min: 1,
          step: 1,
          max: 100,
        },
        required: true,
        span: 8,
        rules: [
          { validator: validateEmpty, message: '请选择开通数量', trigger: ['blur', 'change'] },
        ],
      },
    ],
  },
])
</script>
<style scoped>
.goods-del-btn {
  position: absolute;
  top: -7px;
  right: -6px;
  z-index: 10;
  color: var(--el-color-danger);
  font-size: 1.2rem;
  cursor: pointer;
  display: none;
}
.goods-info-form:hover .goods-del-btn {
  display: block;
}

.evs-item {
  width: 100%;
  margin-bottom: 15px;
  .evs-item-content {
    width: 100%;
    display: flex;
    align-items: center;
  }
  &:last-child {
    margin-bottom: 0;
  }
  .evs-icons {
    display: flex;
    align-items: center;
    margin-left: 10px;
    .el-icon {
      cursor: pointer;
    }
  }
}
</style>
