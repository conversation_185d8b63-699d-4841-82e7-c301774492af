<template>
  <el-dialog
    v-model="dialogFormVisible"
    :title="title"
    :width="width"
    @close="clearForm(ruleFormRef)"
  >
    <el-form :model="model" :rules="rules" ref="ruleFormRef">
      <el-form-item
        v-for="(field, index) in form"
        :key="index"
        :label="field.label"
        :prop="field.prop"
        :label-width="field.formLabelWidth"
      >
        <el-input
          v-if="field.type == 'input'"
          v-model="model[field.prop]"
          :placeholder="field.placeholder"
          autocomplete="off"
        />
        <el-input
          v-if="field.type == 'textarea'"
          v-model="model[field.prop]"
          maxlength="30"
          :placeholder="field.placeholder"
          show-word-limit
          :label-width="field.formLabelWidth"
          type="textarea"
        />
        <el-select
          v-if="field.type == 'select'"
          clearable
          v-model="model[field.prop]"
          :placeholder="field.placeholder"
          :style="{ width: field.placeholder }"
        >
          <el-option
            v-for="item in field.optionsleader"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="confirmdialogFormVisible">
          {{ confirmText || '确认' }}
        </el-button>
        <el-button @click="dialogFormVisible = false">{{ cancelText || '取消' }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script lang="ts" setup>
import type { FormRules, FormInstance } from 'element-plus'
import { reactive, ref } from 'vue'
// 定义 props 接收父组件传递的 message 数据
interface FormField {
  label: string
  prop: string
  placeholder: string
  formLabelWidth: string
  value: string
  type: string
  optionsleader?: Array<{ value: string; label: string }>
}
interface FormModel {
  [key: string]: string | number
}
const props = defineProps({
  title: {
    type: String,
    required: true,
  },
  width: {
    type: Number,
    required: true,
  },
  model: {
    type: Object as () => FormModel,
    required: true,
  },
  form: {
    type: Array as () => FormField[],
    required: true,
  },
  rules: {
    type: Object,
    required: true,
  },
  confirmText: {
    type: String,
    required: false,
  },
  cancelText: {
    type: String,
    required: false,
  },
})
// 创建表单实例和验证规则
const form = reactive(props.form)
const model = reactive<FormModel>(props.model)
const rules = reactive<FormRules<any>>(props.rules)

// 创建本地副本
const ruleFormRef = ref<FormInstance>()
const dialogFormVisible = ref(false)
// 定义暴露的方法
const childMethod = () => {
  dialogFormVisible.value = true
}
// 使用 defineExpose 将子组件的方法暴露给父组件
defineExpose({
  childMethod,
})
const emit = defineEmits<{
  (e: 'submit', model: any): void // 双向绑定事件
}>()
//确认
const confirmdialogFormVisible = async () => {
  if (!ruleFormRef.value) return

  await ruleFormRef.value.validate((valid: boolean, fields: any) => {
    if (valid) {
      console.log('提交成功', model)
      emit('submit', model)
      dialogFormVisible.value = false
    } else {
      console.log('验证失败', fields)
    }
  })
}

const clearForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
}
</script>
