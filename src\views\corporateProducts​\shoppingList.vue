<template>
  <div id="CorporateProducts" class="table-box">
    <sl-page-header
      title="购物清单"
      :icon="{
        Vnode: ShoppingCart,
        color: '#0052D9',
        size: '20px',
      }"
    ></sl-page-header>
    <sl-tabs :tabs="tenantTabs" v-model="activeTenantTab" @update:modelValue="tenantTabClick">
    </sl-tabs>
    <div class="tab-content">
      <el-tabs v-model="activeShoppingListTab">
        <el-tab-pane
          :label="item.label"
          :name="item.name"
          v-for="item in shoppingListTabs"
          :key="item.name"
        >
          <template #label>
            <span class="tab-label">{{ item.label }}</span>
            <span class="tab-count">（{{ item.count }}）</span>
          </template>
        </el-tab-pane>
      </el-tabs>

      <!-- 表格部分 -->
      <SlProTable
        ref="proTable"
        :data="currentTabData"
        :columns="currentColumns"
        :pagination="false"
        v-if="currentTabData.length > 0 && currentColumns.length > 0"
      >
      </SlProTable>

      <!-- 底部操作区 -->
      <div class="shopping-cart-footer">
        <div class="footer-actions">
          <el-button @click="handleCancel">取消</el-button>
          <el-button type="danger" @click="handleCancelOrder">取消订购</el-button>
          <el-button type="primary" @click="handleConfirmOrder">确认</el-button>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts" name="CorporateProducts">
import { ref, computed, onMounted, watch } from 'vue'
import { ShoppingCart } from '@element-plus/icons-vue'
import {
  getCorporateOrderTemSave,
  corporateOrderApi,
  corporateOrderTemSaveDeleteAll,
  corporateOrderTemSaveDelete,
} from '@/api/modules/resourecenter'
import { useShoppingListColumns } from './hooks/useShoppingListColumns'
import { ElMessageBox, ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'

// 路由
const router = useRouter()

// 导入表格列配置钩子
const { getColumnsByResourceType } = useShoppingListColumns()

const tenantTabs = ref<any>([])
const activeTenantTab = ref('')
const shoppingList = ref<any>([])

const handleGetTenantList = async () => {
  tenantTabs.value = []
  const res = await getCorporateOrderTemSave({})
  if (res.entity && Array.isArray(res.entity)) {
    res.entity.forEach((item: any) => {
      tenantTabs.value.push({
        label: item.tenantName,
        name: String(item.tenantId),
      })
    })

    if (tenantTabs.value.length > 0) {
      activeTenantTab.value = tenantTabs.value[0].name
    }

    shoppingList.value = res.entity
  }
}

const tenantTabClick = (tab: any) => {
  activeTenantTab.value = tab
  const firstTab = shoppingListTabs.value[0]
  if (firstTab) {
    activeShoppingListTab.value = firstTab.name
  }
}

const currentTenant = computed(() => {
  return shoppingList.value.find((item: any) => item.tenantId == activeTenantTab.value)
})

const activeShoppingListTab = ref('')

// tab栏切换
const allTabs = ref([
  { label: '云主机', name: 'ecs', count: 0 },
  { label: 'GPU云主机', name: 'gcs', count: 0 },
  { label: '云硬盘', name: 'evs', count: 0 },
  { label: '弹性公网', name: 'eip', count: 0 },
  { label: '对象存储', name: 'obs', count: 0 },
  { label: '负载均衡', name: 'slb', count: 0 },
  { label: 'NAT网关', name: 'nat', count: 0 },
  { label: 'VPN', name: 'vpn', count: 0 },
])

// 动态生成shoppingListTabs
const shoppingListTabs = computed(() => {
  const tabs: any = []
  allTabs.value.forEach((item: any) => {
    const orderJson = currentTenant.value?.orderJson
    if (orderJson && orderJson[item.name + 'List'] && orderJson[item.name + 'List'].length > 0) {
      tabs.push({
        label: item.label,
        name: item.name,
        count: orderJson[item.name + 'List'].length,
      })
    }
  })
  return tabs
})

// 计算当前要显示的表格数据
const currentTabData = computed(() => {
  if (!currentTenant.value || !activeShoppingListTab.value) return []

  const listKey = activeShoppingListTab.value + 'List'
  return currentTenant.value.orderJson?.[listKey] || []
})

// 使用ref而不是computed，以便可以修改
const currentColumns = ref<any[]>([])

// 更新列配置的函数
const updateColumns = () => {
  currentColumns.value = []
  setTimeout(() => {
    if (activeShoppingListTab.value) {
      currentColumns.value = getColumnsByResourceType(activeShoppingListTab.value, handleDeleteItem)
    } else {
      currentColumns.value = []
    }
  }, 200)
}

// 监听activeShoppingListTab变化，强制刷新列配置
watch(activeShoppingListTab, () => {
  updateColumns()
})

// 初始更新列配置
watch([shoppingListTabs], ([newTabs]) => {
  if (
    newTabs.length > 0 &&
    (!activeShoppingListTab.value ||
      !newTabs.find((t: any) => t.name === activeShoppingListTab.value))
  ) {
    activeShoppingListTab.value = newTabs[0].name
  }
  // 更新列配置
  updateColumns()
})

// 取消操作
const handleCancel = () => {
  router.go(-1)
}

// 取消订购操作
const handleCancelOrder = () => {
  if (!activeTenantTab.value) {
    ElMessage.warning('请先选择租户')
    return
  }

  ElMessageBox.confirm('确认取消订购当前租户的所有商品吗？此操作将清空购物清单。', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      try {
        await corporateOrderTemSaveDeleteAll({
          tenantId: activeTenantTab.value,
        })
        ElMessage.success('已取消订购')
        // 刷新数据
        handleGetTenantList()
      } catch (error) {
        console.error('取消订购失败:', error)
        ElMessage.error('取消订购失败')
      }
    })
    .catch(() => {
      // 用户取消操作
    })
}

// 确认订单
const handleConfirmOrder = () => {
  if (!activeTenantTab.value || !currentTenant.value) {
    ElMessage.warning('请先选择租户')
    return
  }

  const orderJson = currentTenant.value.orderJson || {}
  // 检查是否有商品
  const hasItems = Object.keys(orderJson).some(
    (key) => key.endsWith('List') && Array.isArray(orderJson[key]) && orderJson[key].length > 0,
  )

  if (!hasItems) {
    ElMessage.warning('购物清单为空，请先添加商品')
    return
  }

  ElMessageBox.confirm('确认提交订单吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      try {
        // 构造请求参数
        const params = {
          tenantId: activeTenantTab.value,
          ecsModelList: orderJson.ecsList || [],
          evsModelList: orderJson.evsList || [],
          eipModelList: orderJson.eipList || [],
          networkModelList: orderJson.networkList || [],
          imageModelList: orderJson.imageList || [],
          diskModelList: orderJson.diskList || [],
          // 添加其他可能的字段
          gcsModelList: orderJson.gcsList || [],
          obsList: orderJson.obsList || [],
          slbModelList: orderJson.slbList || [],
          natModelList: orderJson.natList || [],
          vpnModelList: orderJson.vpnList || [],
        }

        await corporateOrderApi(params)
        ElMessage.success('订单已提交')
        // 刷新数据
        handleGetTenantList()
      } catch (error) {
        console.error('提交订单失败:', error)
        ElMessage.error('提交订单失败')
      }
    })
    .catch(() => {
      // 用户取消操作
    })
}

// 删除单个商品
const handleDeleteItem = (row: any) => {
  if (!activeTenantTab.value || !activeShoppingListTab.value) {
    ElMessage.warning('操作失败，请重新选择商品')
    return
  }

  ElMessageBox.confirm('确认从购物清单中移除该商品吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      try {
        await corporateOrderTemSaveDelete({
          tenantId: activeTenantTab.value,
          type: activeShoppingListTab.value,
          id: row.tempSaveId,
        })

        ElMessage.success('商品已提交删除')

        // 刷新数据
        handleGetTenantList()
      } catch (error) {
        console.error('删除失败:', error)
        ElMessage.error('删除失败')
      }
    })
    .catch(() => {
      // 用户取消操作
    })
}

// 组件挂载时获取数据
onMounted(() => {
  handleGetTenantList()
  // 初始化列配置
  updateColumns()
})
</script>
<style lang="scss" scoped>
.shopping-list-content {
  width: 100%;
  height: 100%;
  margin: 8px;
  background: #fff;
}

.tab-content {
  background-color: white;
  margin: 8px;
  padding: 8px 24px 0;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 180px);

  :deep(.el-tabs__item) {
    min-width: 120px;
  }
}

.shopping-cart-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  padding: 16px 0px;
  border-top: 1px solid #ebeef5;
  background-color: #fff;
  display: flex;
  justify-content: flex-end;
  .footer-actions {
    display: flex;
  }
}

.tab-label {
  margin-right: 4px;
}
</style>
