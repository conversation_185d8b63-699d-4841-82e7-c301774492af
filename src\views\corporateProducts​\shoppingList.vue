<template>
  <div id="CorporateProducts" class="table-box">
    <sl-page-header
      title="购物清单"
      :icon="{
        Vnode: ShoppingCart,
        color: '#0052D9',
        size: '20px',
      }"
    ></sl-page-header>
    <template v-if="tenantTabs.length > 0">
      <sl-tabs :tabs="tenantTabs" v-model="activeTenantTab" @update:modelValue="tenantTabClick">
      </sl-tabs>
      <div class="tab-content">
        <ShoppingListTable
          :current-tenant="currentTenant"
          :all-tabs="allTabs"
          :get-columns-by-resource-type="getColumnsByResourceType"
          :handle-delete-item="handleDeleteItem"
          @update:activeTab="handleActiveTabChange"
        />

        <!-- 底部操作区 -->
        <div class="shopping-cart-footer">
          <div class="footer-actions">
            <el-button @click="handleCancel">取消</el-button>
            <el-button type="danger" @click="handleCancelOrder">取消订购</el-button>
            <el-button type="primary" @click="handleConfirmOrder" v-permission="'Submit'">
              确认
            </el-button>
          </div>
        </div>
      </div>
    </template>
    <template v-else>
      <el-empty description="暂无数据" />
    </template>
  </div>
</template>
<script setup lang="ts" name="CorporateProducts">
import { ref, computed, onMounted } from 'vue'
import { ShoppingCart } from '@element-plus/icons-vue'
import {
  getCorporateOrderTemSave,
  corporateOrderApi,
  corporateOrderTemSaveDeleteAll,
  corporateOrderTemSaveDelete,
} from '@/api/modules/resourecenter'
import { useShoppingListColumns } from './hooks/useShoppingListColumns'
import { ElMessageBox, ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import ShoppingListTable from './components/ShoppingListTable.vue'
import eventBus from '@/utils/eventBus'

// 路由
const router = useRouter()

// 导入表格列配置钩子
const { getColumnsByResourceType } = useShoppingListColumns()

const tenantTabs = ref<any>([])
const activeTenantTab = ref('')
const shoppingList = ref<any>([])
const activeShoppingListTab = ref('')

// tab栏配置
const allTabs = ref([
  { label: '云主机', name: 'ecs', count: 0 },
  { label: 'GPU云主机', name: 'gcs', count: 0 },
  { label: '云硬盘', name: 'evs', count: 0 },
  { label: '对象存储', name: 'obs', count: 0 },
  { label: '负载均衡', name: 'slb', count: 0 },
  { label: 'NAT网关', name: 'nat', count: 0 },
  { label: 'VPN', name: 'vpn', count: 0 },
])

const handleGetTenantList = async () => {
  tenantTabs.value = []
  const res = await getCorporateOrderTemSave({})
  if (res.entity && Array.isArray(res.entity)) {
    res.entity.forEach((item: any) => {
      tenantTabs.value.push({
        label: item.tenantName,
        name: String(item.tenantId),
      })
    })

    if (tenantTabs.value.length > 0) {
      activeTenantTab.value = tenantTabs.value[0].name
    }
    shoppingList.value = res.entity
  }
}

const tenantTabClick = (tab: any) => {
  activeTenantTab.value = tab
}

const currentTenant = computed(() => {
  return shoppingList.value.find((item: any) => item.tenantId == activeTenantTab.value)
})

// 处理子组件标签页变化
const handleActiveTabChange = (tab: string) => {
  activeShoppingListTab.value = tab
}

// 取消操作
const handleCancel = () => {
  router.go(-1)
  eventBus.emit('corporateShoppingList:updateCount')
}

// 取消订购操作
const handleCancelOrder = () => {
  if (!activeTenantTab.value) {
    ElMessage.warning('请先选择租户')
    return
  }

  ElMessageBox.confirm('确认取消订购当前租户的所有商品吗？此操作将清空购物清单。', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      await corporateOrderTemSaveDeleteAll({
        tenantId: activeTenantTab.value,
      })
      ElMessage.success('已取消订购')
      // 刷新数据
      handleGetTenantList()
      // 刷新头部购物车数量
      eventBus.emit('corporateShoppingList:updateCount')
    })
    .catch(() => {
      // 用户取消操作
    })
}

// 确认订单
const handleConfirmOrder = () => {
  if (!activeTenantTab.value || !currentTenant.value) {
    ElMessage.warning('请先选择租户')
    return
  }

  const orderJson = currentTenant.value.orderJson || {}
  // 检查是否有商品
  const hasItems = Object.keys(orderJson).some(
    (key) => key.endsWith('List') && Array.isArray(orderJson[key]) && orderJson[key].length > 0,
  )

  if (!hasItems) {
    ElMessage.warning('购物清单为空，请先添加商品')
    return
  }

  ElMessageBox.confirm('确认提交订单吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      // 构造请求参数
      const params = {
        tenantId: activeTenantTab.value,
        directSubmit: false,
        ecsModelList: orderJson.ecsList || [],
        gcsModelList: orderJson.gcsList || [],
        evsModelList: orderJson.evsList || [],
        eipModelList: orderJson.eipList || [],
        obsModelList: orderJson.obsList || [],
        slbModelList: orderJson.slbList || [],
        natModelList: orderJson.natList || [],
        vpnModelList: orderJson.vpnList || [],
      }

      await corporateOrderApi(params)
      ElMessage.success('订单已提交')
      // 刷新数据
      handleGetTenantList()
      // 刷新头部购物车数量
      eventBus.emit('corporateShoppingList:updateCount')
    })
    .catch(() => {
      // 用户取消操作
    })
}

// 删除单个商品
const handleDeleteItem = (row: any) => {
  if (!activeTenantTab.value || !activeShoppingListTab.value) {
    ElMessage.warning('操作失败，请重新选择商品')
    return
  }

  ElMessageBox.confirm('确认从购物清单中移除该商品吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      await corporateOrderTemSaveDelete({
        tenantId: activeTenantTab.value,
        type: activeShoppingListTab.value,
        id: row.tempSaveId,
      })

      ElMessage.success('商品已提交删除')

      // 刷新数据
      handleGetTenantList()
      // 刷新头部购物车数量
      eventBus.emit('corporateShoppingList:updateCount')
    })
    .catch(() => {
      // 用户取消操作
    })
}

// 组件挂载时获取数据
onMounted(() => {
  handleGetTenantList()
})
</script>
<style lang="scss" scoped>
.shopping-list-content {
  width: 100%;
  height: 100%;
  margin: 8px;
  background: #fff;
}

.tab-content {
  background-color: white;
  margin: 8px;
  padding: 8px 24px 0;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 180px);
}

.shopping-cart-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  padding: 16px 0px;
  border-top: 1px solid #ebeef5;
  background-color: #fff;
  display: flex;
  justify-content: flex-end;
  .footer-actions {
    display: flex;
  }
}
</style>
