# SlTabs 组件样式重构总结

## 重构目标
参照 SlBaseTabs 的样式设计，重构 SlTabs 组件的选中与平时的样式，**保留 Element Plus 的所有功能**，仅修改视觉样式。

## 重构策略

### 保留 Element Plus 功能
**策略**: 继续使用 `el-tabs` 组件，通过 `:deep()` 样式穿透重写样式

```vue
<el-tabs v-model="activeTab" type="card" @tab-change="handleTabChange">
  <el-tab-pane v-for="item in tabs" :key="item.name" :label="item.label" :name="item.name">
    <template #label>
      <span class="tab-label">{{ item.label }}</span>
      <span v-if="showCount && item.count !== undefined" class="tab-count">{{ item.count }}</span>
    </template>
  </el-tab-pane>
</el-tabs>
```

**优势**:
- ✅ 保留 Element Plus 的所有内置功能（懒加载、动画、事件等）
- ✅ 保留 `el-tab-pane` 的所有属性和功能
- ✅ 保留原生的键盘导航、无障碍访问等特性
- ✅ 保留所有 Element Plus 的 API 和事件

## 主要改动

### 1. 样式重写策略

#### 使用 `:deep()` 样式穿透
通过深度选择器重写 Element Plus 的默认样式，而不破坏组件功能：

```scss
:deep(.el-tabs) {
  .el-tabs__header {
    margin: 0;
    border-bottom: none;
    background: transparent;
  }
  
  .el-tabs__item {
    border: none !important;
    background: transparent;
    color: #606266;
    font-size: 14px;
    padding: 8px 20px;
    position: relative;
    
    // 选中状态
    &.is-active {
      color: var(--el-color-primary);
      font-weight: 400;
      background: #f2f3f5;
      border-radius: 8px 8px 0 0;
    }
  }
}
```

### 2. 视觉效果实现

#### 选中状态样式
- **颜色**: 使用主题色 `var(--el-color-primary)`
- **背景**: 浅灰色背景 `#f2f3f5`
- **圆角**: 上方圆角 `border-radius: 8px 8px 0 0`
- **字体**: 400 字重

#### 相邻元素圆角过渡
使用现代 CSS `:has()` 选择器实现相邻标签的圆角过渡效果：
```scss
&:has(+ .is-active)::before {
  border-bottom-right-radius: 12px;
}

&.is-active + &::before {
  border-bottom-left-radius: 12px;
}
```

#### 数量标签样式
- **背景**: 半透明蓝色 `rgba(72, 127, 239, 0.1)`
- **字体**: 12px 小字体
- **间距**: 2px 4px 内边距，2px 圆角
- **颜色**: 主题色文字
- **位置**: 标题右侧 4px 间距

#### 悬停效果
- **颜色**: 鼠标悬停时颜色变为 `#409eff`
- **背景**: 非选中状态悬停时保持透明

#### 内容区域
- **背景**: 浅灰色 `#f2f3f5`
- **最小高度**: 200px
- **无内边距**: 让内容完全控制布局

### 3. 功能增强

#### 新增属性
- **showCount**: 布尔值，控制是否显示数量标签
- **tabChange**: 事件，标签切换时触发

#### 类型安全
- 完整的 TypeScript 类型定义
- 兼容 Element Plus 的事件类型

## 使用方式

### 基础用法
```vue
<sl-tabs v-model="activeTab" :tabs="tabs" @tab-change="handleTabChange">
  <el-tab-pane name="tab1">内容1</el-tab-pane>
  <el-tab-pane name="tab2">内容2</el-tab-pane>
</sl-tabs>
```

### 显示数量
```vue
<sl-tabs v-model="activeTab" :tabs="tabs" show-count>
  <el-tab-pane name="tab1">内容1</el-tab-pane>
  <el-tab-pane name="tab2">内容2</el-tab-pane>
</sl-tabs>
```

### 数据格式
```typescript
const tabs = [
  { name: 'tab1', label: '标签一', count: 10 },
  { name: 'tab2', label: '标签二', count: 5 },
]
```

## 技术特点

### CSS 现代特性
- **`:has()` 选择器**: 实现相邻元素的样式联动
- **CSS 自定义属性**: 使用 `var(--el-color-primary)` 主题色
- **深度选择器**: 使用 `:deep()` 穿透组件样式

### Vue 3 特性
- **Composition API**: 使用 `ref`, `watch` 等组合式 API
- **TypeScript**: 完整的类型定义
- **事件系统**: 兼容 Element Plus 的事件机制

## 优势对比

### 相比完全自定义实现
- ✅ **功能完整**: 保留所有 Element Plus 功能
- ✅ **稳定性高**: 基于成熟的组件库
- ✅ **维护成本低**: 不需要重新实现复杂逻辑
- ✅ **兼容性好**: 与现有代码无缝集成

### 相比原始 Element Plus
- ✅ **视觉更佳**: 参照 SlBaseTabs 的现代化设计
- ✅ **一致性强**: 与项目整体设计语言统一
- ✅ **功能增强**: 支持数量显示等额外功能

## 兼容性说明

- **浏览器支持**: 现代浏览器（支持 `:has()` 选择器）
- **Vue 版本**: Vue 3.x
- **Element Plus**: 兼容当前版本
- **TypeScript**: 完整支持

## 总结

重构后的 SlTabs 组件成功实现了"仅修改样式，保留功能"的目标。通过样式穿透技术，在不破坏 Element Plus 原有功能的基础上，实现了与 SlBaseTabs 一致的视觉效果。这种方案既满足了设计需求，又保持了技术的稳定性和可维护性。
