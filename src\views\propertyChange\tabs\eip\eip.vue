<template>
  <div class="form-container">
    <sl-form size="small" show-block-title :options="formOptions">
      <!-- 配置信息 -->
      <template #goodsInfoSlot>
        <div style="padding-top: 6px">
          <template v-for="(goods, goodsIndex) in goodsList" :key="goods">
            <goodsItem
              @deleteGoods="deleteGoods(goodsList, goodsIndex)"
              :image-options="imageOptions"
              :flavor-options="flavorOptions"
              :style="goodsIndex !== 0 ? 'margin-top:20px' : ''"
              :goods="goods"
            ></goodsItem>
          </template>
        </div>
      </template>
    </sl-form>
  </div>
</template>
<script setup lang="ts">
import type { IPropsListItem } from '@/views/resourceCenter/hooks/usePropertyChangeModels'
import slForm from '@/components/form/SlForm.vue'
import { reactive } from 'vue'
import goodsItem from './goodsItem.vue'
import { useImageTree } from '@/views/resourceCenter/hooks/useImageTree'
import { useFlavorTree } from '@/views/resourceCenter/hooks/useFlavorTree'
import eventBus from '@/utils/eventBus'

const imageOptions = useImageTree()
const flavorOptions = useFlavorTree('eip')

defineProps<{
  goodsList: IPropsListItem<'eip'>[]
}>()

const deleteGoods = (goodsList: IPropsListItem<'eip'>[], goodsIndex: number) => {
  goodsList.splice(goodsIndex, 1)
  eventBus.emit('propertyChange:updateGoods')
}

const formOptions = reactive([
  {
    blockTitleStyle: 'position: sticky;top:0;z-index:99;background: #fff;',
    gutter: 0,
    groupName: '变更信息',
    groupItems: [
      {
        span: 24,
        noFormItem: true,
        type: 'slot',
        slotName: 'goodsInfoSlot',
      },
    ],
  },
])
</script>
<style scoped>
.scroll-view {
  height: calc(100vh - 468px);
}
.goods-del-btn {
  position: absolute;
  top: -7px;
  right: -6px;
  z-index: 10;
  color: var(--el-color-danger);
  font-size: 1.2rem;
  cursor: pointer;
  display: none;
}
.goods-info-form:hover .goods-del-btn {
  display: block;
}
.form-container {
  display: flex;
  flex-direction: column;
}
</style>
