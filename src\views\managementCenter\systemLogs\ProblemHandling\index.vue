<template>
  <div class="table-main">
    <SlProTable ref="proTable" :columns="columns" :request-api="getList" row-key="id">
      <template #search>
        <div class="pl10">
          <el-button v-permission="'3Plus'" @click="() => handleEditOrSave()" type="primary">
            <el-icon class="el-icon--left"><Plus /></el-icon>新 建
          </el-button>
        </div>
      </template>
    </SlProTable>

    <!-- 弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle + '问题处理日志'"
      width="640px"
      :close-on-click-modal="false"
      @close="close"
    >
      <sl-form
        v-if="dialogVisible"
        ref="formRef"
        :show-block-title="false"
        v-model="formData"
        :options="options"
      >
        <template #enumText="{ item }">
          <EnumText :form-model="formData" :fields="item" :dic-collection="{}" />
        </template>
      </sl-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">关 闭</el-button>
          <el-button v-if="dialogTitle !== '查看'" type="primary" @click="submit">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="tsx">
import {
  getProblemHandlingListApi,
  problemHandlingsDeleteApi,
  problemHandlingsSaveApi,
} from '@/api/modules/managementCenter'
import SlMessage from '@/components/base/SlMessage'
import type { ColumnProps, ProTableInstance } from '@/components/SlProTable/interface'
import { computed, ref } from 'vue'
import { Edit, Delete, View, Plus } from '@element-plus/icons-vue'
import EnumText from '@/views/approvalCenter/components/EnumText.vue'
import { changeDateFormatKey } from '@/utils'

const columns: ColumnProps[] = [
  { type: 'index', label: '序号', width: 55 },
  {
    prop: 'problemContent',
    label: '问题详情',
    minWidth: 230,
    search: { el: 'input', checked: true, defaultDisabled: true },
  },
  {
    prop: 'problemStatus',
    label: '问题状态',
    width: 100,
    enum: [
      {
        label: '未解决',
        value: '未解决',
      },
      {
        label: '解决中',
        value: '解决中',
      },
      {
        label: '已解决',
        value: '已解决',
      },
    ],
    search: { el: 'select', checked: true, defaultDisabled: true },
  },

  {
    prop: 'publishTime',
    label: '发现时间',
    minWidth: 220,
    search: {
      el: 'date-picker',
      span: 1,
      props: {
        type: 'datetimerange',
        format: 'YYYY-MM-DD HH:mm:ss',
        valueFormat: 'YYYY-MM-DD HH:mm:ss',
        rangeSeparator: '至',
        startPlaceholder: '开始时间',
        endPlaceholder: '结束时间',
      },
    },
  },
  {
    prop: 'completeTime',
    label: '解决时间',
    minWidth: 220,
    search: {
      el: 'date-picker',
      span: 1,
      props: {
        type: 'datetimerange',
        format: 'YYYY-MM-DD HH:mm:ss',
        valueFormat: 'YYYY-MM-DD HH:mm:ss',
        rangeSeparator: '至',
        startPlaceholder: '开始时间',
        endPlaceholder: '结束时间',
      },
    },
  },
  {
    prop: 'publisher',
    label: '问题解决人',
    minWidth: 230,
    search: { el: 'input' },
  },
  {
    prop: 'operations',
    label: '操作',
    width: 220,
    fixed: 'right',
    render: (scope) => (
      <>
        <el-button
          type="primary"
          link
          icon={View}
          onClick={() => handleEditOrSave(scope.row, '查看')}
        >
          详情
        </el-button>
        <el-button
          v-permission="3Edit"
          type="primary"
          link
          icon={Edit}
          onClick={() => handleEditOrSave(scope.row, '编辑')}
        >
          编辑
        </el-button>
        <el-button
          v-permission="3Delete"
          type="danger"
          link
          icon={Delete}
          onClick={() => handleDelete(scope.row.id)}
        >
          删除
        </el-button>
      </>
    ),
  },
]

const proTable = ref<ProTableInstance>()

const getList = async (data: any) => {
  // const params = changeDateFormat(data, ['publishTime', 'completeTime'])
  const params = changeDateFormatKey(data, [
    {
      key: 'publishTime',
      startSuffix: 'publishTimeStart',
      endSuffix: 'publishTimeEnd',
    },
    {
      key: 'completeTime',
      startSuffix: 'completeTimeStart',
      endSuffix: 'completeTimeEnd',
    },
  ])
  return getProblemHandlingListApi(params)
}

// 1. 添加
// 2. 编辑
const dialogTitle = ref('新建')

const handleEditOrSave = async (row?: any, type: string = '新建') => {
  dialogTitle.value = type

  dialogVisible.value = true
  if (!row) return
  formData.value.id = row.id
  formData.value.problemContent = row.problemContent
  formData.value.problemStatus = row.problemStatus
  formData.value.completeTime = row.completeTime
  formData.value.publishTime = row.publishTime
  formData.value.publisher = row.publisher
}

const disabledValue = computed(() => dialogTitle.value === '查看')
const options = ref([
  {
    groupItems: [
      {
        label: '问题详情',
        type: computed(() => (dialogTitle.value === '查看' ? 'slot' : 'input')),
        key: 'problemContent',
        keyName: 'problemContent',
        slotName: 'enumText',
        rules: [{ required: true, message: '请输入版本', trigger: ['blur', 'change'] }],
        props: {
          type: 'textarea',
          rows: 1,
          maxlength: 100,
          showWordLimit: true,
          disabled: disabledValue,
        },
        span: 24,
      },
      {
        label: '问题状态',
        type: computed(() => (dialogTitle.value === '查看' ? 'slot' : 'select')),
        key: 'problemStatus',
        slotName: 'enumText',
        span: 24,
        options: [
          {
            label: '未解决',
            value: '未解决',
          },
          {
            label: '解决中',
            value: '解决中',
          },
          {
            label: '已解决',
            value: '已解决',
          },
        ],
        props: {
          select: {
            disabled: disabledValue,
            clearable: true,
            filterable: true,
          },
        },
        rules: [{ required: true, message: '请选择维护类型', trigger: ['blur', 'change'] }],
      },
      {
        label: '发现时间',
        type: computed(() => (dialogTitle.value === '查看' ? 'slot' : 'date')),
        key: 'publishTime',
        keyName: 'publishTime',
        slotName: 'enumText',
        props: {
          type: 'datetime',
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
        },
        span: 24,
        rules: [{ required: true, message: '请输入维护内容', trigger: ['blur', 'change'] }],
      },
      {
        label: '解决时间',
        type: computed(() => (dialogTitle.value === '查看' ? 'slot' : 'date')),
        key: 'completeTime',
        keyName: 'completeTime',
        slotName: 'enumText',
        props: {
          type: 'datetime',
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
        },
        span: 24,
      },

      {
        label: '问题解决人',
        type: computed(() => (dialogTitle.value === '查看' ? 'slot' : 'input')),
        key: 'publisher',
        keyName: 'publisher',
        slotName: 'enumText',
        props: {
          type: 'textarea',
          rows: 1,
          maxlength: 20,
          showWordLimit: true,
          disabled: disabledValue,
        },
        span: 24,
      },
    ],
  },
])

const dialogVisible = ref(false)
const formData = ref<FormDataType>({})

const formRef = ref<any>(null)
//修改添加
const submit = async () => {
  if (!(await formRef.value?.validate(() => true))) return
  await problemHandlingsSaveApi(formData.value)
  dialogVisible.value = false
  SlMessage.success(formData.value.id ? '编辑成功' : '新建成功')
  proTable.value?.getTableList()
}
// 3. 删除
const handleDelete = async (id: number | string) => {
  await ElMessageBox.confirm('确认删除该日志吗？', '信息提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
  })
  await problemHandlingsDeleteApi({ id: id })
  SlMessage.success('删除成功')
  proTable.value?.getTableList()
}

// 4. 重置弹窗
const close = () => {
  formData.value = {
    maintenanceType: '',
    maintenanceContent: '',
    version: '',
  }
  formRef.value?.resetFields()
}
</script>

<style scoped lang="sass"></style>
