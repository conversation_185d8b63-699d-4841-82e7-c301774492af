import 'element-plus/dist/index.css'
import 'element-plus/theme-chalk/dark/css-vars.css'
import '@/assets/styles/element.scss'

// iconfont css
import '@/assets/iconfont/iconfont.scss'

import '@/assets/styles/global.scss'
import '@/assets/styles/override.css'
import '@/assets/styles/common.scss'

import '@/assets/styles/publicCss.scss'
import VNetworkGraph from 'v-network-graph'
import 'v-network-graph/lib/style.css'

// pinia store
import pinia from '@/stores'
import App from './App.vue'
import { createApp } from 'vue'
import WujieVue from 'wujie-vue3'
import { preloadApp, setupApp } from 'wujie'

const app = createApp(App)
import router from './router'
import permissionDirective from '@/directives/permission'

// 初始化无界
const wujieOptions = {
  name: 'mric-main',
  plugins: [
    {
      htmlLoader: (html: string) => {
        return html
      },
    },
  ],
  // 全局跨域请求配置
  fetch: (input: RequestInfo, init?: RequestInit) => {
    return window.fetch(input, {
      ...init,
      credentials: 'include',
    })
  },
}

setupApp(wujieOptions)

// 预加载子应用
preloadApp({
  name: 'mric-sub',
  url: 'http://localhost:8082',
})

app
  .use(router)
  .use(pinia)
  .use(VNetworkGraph)
  .use(WujieVue)
  .directive('permission', permissionDirective)
  .mount('#app')
