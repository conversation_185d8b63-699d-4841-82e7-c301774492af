<template>
  <div id="ObsDetail" class="table-box">
    <sl-page-header
      title="对象存储详情"
      :icon="{
        Vnode: Platform,
        color: '#0052D9',
        size: '20px',
      }"
      :show-back="true"
      :back="{
        title: '返回列表',
        function: handleGoBack,
      }"
    >
    </sl-page-header>
    <el-scrollbar wrap-class="slb-detail-scroll-view" class="scroll-view">
      <div class="sl-card">
        <sl-form
          :show-block-title="false"
          :label-width="140"
          ref="slFormRef"
          v-model="detailData"
          :options="formOptions"
        >
        </sl-form>
      </div>
    </el-scrollbar>
  </div>
</template>

<script setup lang="tsx">
import { onMounted, ref, reactive } from 'vue'
import { Platform } from '@element-plus/icons-vue'
import SlPageHeader from '@/components/SlPageHeader/index.vue'
import { useRouter, useRoute } from 'vue-router'
import { getResourceDetail } from '@/api/modules/resourecenter'

const router = useRouter()
const route = useRoute()

// 获取资源详情ID
const resourceId = ref<string>((route.query.id as string) || '')

const detailData = reactive<any>({
  deviceName: '',
  deviceId: '',
  storeType: '',
  capacity: '',
  accessKey: '',
  secretKey: '',
  publicAddress: '',
  internalAddress: '',
  applyTime: '',
  tenantName: '',
  businessSysName: '',
  cloudPlatform: '',
  resourcePoolName: '',
  orderCode: '',
  projectName: '',
  createTime: '',
  expireTime: '',
  billId: '',
  deviceStatusCn: '',
  deviceStatus: '',
  recoveryStatusCn: '',
  changeStatusCn: '',
  applyUserName: '',
})

const formOptions = reactive<any[]>([
  {
    groupItems: [
      {
        label: '对象存储名称',
        type: 'text',
        key: 'deviceName',
        span: 8,
      },
      {
        label: '资源ID',
        type: 'text',
        key: 'deviceId',
        span: 8,
      },
      {
        label: '存储类型',
        type: 'text',
        key: 'storeType',
        span: 8,
      },
      {
        label: '容量',
        type: 'text',
        key: 'capacity',
        span: 8,
      },
      {
        label: '公钥',
        type: 'text',
        key: 'accessKey',
        span: 8,
      },
      {
        label: '私钥',
        type: 'text',
        key: 'secretKey',
        span: 8,
      },
      {
        label: '公网访问地址',
        type: 'text',
        key: 'publicAddress',
        span: 8,
      },
      {
        label: '私网访问地址',
        type: 'text',
        key: 'internalAddress',
        span: 8,
      },
      {
        label: '申请时长',
        type: 'text',
        key: 'applyTime',
        span: 8,
      },
      {
        label: '租户',
        type: 'text',
        key: 'tenantName',
        span: 8,
      },
      {
        label: '业务系统',
        type: 'text',
        key: 'businessSysName',
        span: 8,
      },
      {
        label: '所属云',
        type: 'text',
        key: 'cloudPlatform',
        span: 8,
      },
      {
        label: '资源池',
        type: 'text',
        key: 'resourcePoolName',
        span: 8,
      },
      {
        label: '工单编号',
        type: 'text',
        key: 'orderCode',
        span: 8,
      },
      {
        label: '开通时间',
        type: 'text',
        key: 'createTime',
        span: 8,
      },
      {
        label: '到期时间',
        type: 'text',
        key: 'expireTime',
        span: 8,
      },
      {
        label: '计费号',
        type: 'text',
        key: 'billId',
        span: 8,
      },
      {
        label: '状态',
        type: 'text',
        key: 'deviceStatusCn',
        span: 8,
      },
      {
        label: '回收状态',
        type: 'text',
        key: 'recoveryStatusCn',
        span: 8,
      },
      {
        label: '变更状态',
        type: 'text',
        key: 'changeStatusCn',
        span: 8,
      },
      {
        label: '申请人',
        type: 'text',
        key: 'applyUserName',
        span: 8,
      },
    ],
  },
])

const fetchResouceDetail = async () => {
  const res = await getResourceDetail({
    id: resourceId.value,
  })
  if (res && res.entity) {
    for (const key in detailData) {
      detailData[key] = res.entity[key]
    }
  }
}

// 返回上一页
const handleGoBack = () => {
  router.push({
    path: '/obsList',
  })
}

// 初始化
onMounted(async () => {
  if (resourceId.value) {
    await fetchResouceDetail()
  }
})
</script>

<style lang="scss" scoped>
.table-box {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.scroll-view {
  flex: 1;
  height: calc(100vh - 140px);
  overflow-x: hidden;
}

.sl-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 0;
  margin: 8px;
}
</style>
