<template>
  <div class="protocol-step">
    <sl-form
      ref="formRef"
      show-block-title
      :options="formOptions"
      :model-value="props.formData"
      @update:model-value="handleUpdate"
      label-width="140px"
      label-position="right"
    >
    </sl-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, inject } from 'vue'
import slForm from '@/components/form/SlForm.vue'
import type { FormInstance } from 'element-plus'
import { validateNumber } from '@/utils/validate'
import { getCertificateList } from '@/api/modules/resourecenter'
const props = defineProps({
  formData: {
    type: Object,
    required: true,
  },
})
const slbDetail = inject<any>('slbDetail')

// 表单引用
const formRef = ref<FormInstance>()

// 处理表单数据更新
const handleUpdate = (value: any) => {
  // 使用事件将更新后的值发送到父组件，而不是直接修改props
  Object.assign(props.formData, value)
}

// 检查是否是VMware云平台
const isVMwarePlatform = computed(() => {
  return slbDetail?.value?.cloudPlatform === '融合边缘云-VMware'
})
const serverCertificateOptions = ref([])
// 获取服务器证书列表
const getServerCertificateList = async () => {
  try {
    const { entity } = await getCertificateList({
      pageNum: 1,
      pageSize: 9999,
      certificateType: 'server_certificate',
    })
    serverCertificateOptions.value = entity.records || []
  } catch (error) {
    console.error('获取服务器证书失败', error)
  }
}

const caCertificateOptions = ref([])
// 获取CA证书列表
const getCaCertificateList = async () => {
  try {
    const { entity } = await getCertificateList({
      pageNum: 1,
      pageSize: 9999,
      certificateType: 'client_certificate',
    })
    caCertificateOptions.value = entity.records || []
  } catch (error) {
    console.error('获取CA证书失败', error)
  }
}

// 协议监听表单配置
const formOptions = reactive([
  {
    groupName: '协议监听配置',
    groupItems: [
      {
        label: '负载均衡协议',
        type: 'select',
        key: 'protocol',
        options: [
          { label: 'TCP', value: 'tcp' },
          { label: 'HTTP', value: 'http' },
          { label: 'HTTPS', value: 'https' },
          { label: 'UDP', value: 'udp' },
        ],
        rules: [{ required: true, message: '请选择负载均衡协议', trigger: 'change' }],
        span: 24,
        onChange: () => {
          // 当协议变化时，检查是否需要清除证书相关选项
          formRef.value?.clearValidate('serverCertificate')
          formRef.value?.clearValidate('caCertificate')
        },
      },
      {
        label: '监听名称',
        type: 'input',
        key: 'listenerName',
        rules: [
          { required: true, message: '请输入监听名称', trigger: 'blur' },
          { min: 2, max: 12, message: '长度在2到12个字符之间', trigger: 'blur' },
          {
            pattern: /^[\u4e00-\u9fa5a-zA-Z0-9_-]+$/,
            message: '只允许中文、字母、数字、下划线和连字符',
            trigger: 'blur',
          },
        ],
        span: 24,
        props: {
          placeholder: '请输入监听名称',
        },
      },
      {
        label: '监听端口',
        type: 'input',
        key: 'port',
        props: {
          placeholder: '请输入端口号',
        },
        rules: [
          { required: true, message: '请输入监听端口', trigger: 'blur' },
          {
            validator: (rule: any, value: any, callback: any) => {
              if (value < 1 || value > 65535) {
                callback(new Error('端口范围为1-65535'))
              } else {
                validateNumber(rule, value, callback)
              }
            },
            trigger: ['blur', 'change'],
          },
        ],
        span: 24,
      },
      {
        label: '服务器证书',
        type: 'select',
        key: 'serverCertificate',
        options: serverCertificateOptions,
        labelField: 'certificateName',
        valueField: 'resourceId',
        hidden: computed(() => props.formData.protocol !== 'https' || !isVMwarePlatform.value),
        span: 24,
        props: {
          placeholder: '请选择服务器证书',
        },
      },
      {
        label: 'CA证书',
        type: 'select',
        key: 'caCertificate',
        options: caCertificateOptions,
        labelField: 'certificateName',
        valueField: 'resourceId',
        hidden: computed(() => props.formData.protocol !== 'https'),
        rules: [
          {
            required: computed(() => props.formData.protocol === 'https'),
            message: '请选择CA证书',
            trigger: 'change',
          },
        ],
        span: 24,
        props: {
          placeholder: '请选择',
        },
      },
    ],
  },
  {
    groupName: '高级配置',
    isCollapse: true,
    groupItems: [
      {
        label: '调度算法',
        type: 'select',
        key: 'lbAlgorithm',
        options: [
          { label: '加权轮询(WRR)', value: 'ROUND_ROBIN' },
          { label: '加权最小连接数(WLC)', value: 'LEAST_CONNECTION' },
        ],
        rules: [{ required: true, message: '请选择调度算法', trigger: 'change' }],
        span: 24,
        props: {
          placeholder: '请选择',
        },
      },
      {
        label: '开启会话保持',
        type: 'switch',
        key: 'sessionPersistence',
        span: 24,
        props: {
          activeColor: '#0052D9',
          disabled: computed(() => !['http', 'https'].includes(props.formData.protocol)),
        },
      },
      {
        label: 'Cookie处理方式',
        type: 'select',
        key: 'cookieHandling',
        options: [
          { label: '植入', value: '1' },
          { label: '重写', value: '2' },
        ],
        hidden: computed(() => !props.formData.sessionPersistence),
        rules: [
          {
            required: computed(() => props.formData.sessionPersistence),
            message: '请选择Cookie处理方式',
            trigger: 'change',
          },
        ],
        span: 24,
        props: {
          placeholder: '请选择',
        },
      },
    ],
  },
])

// 验证表单
const validate = async () => {
  let valid = false
  await formRef.value?.validate((isValid: boolean) => {
    valid = isValid
  })
  return valid
}

// 暴露方法给父组件
defineExpose({
  validate,
})

// 初始化
onMounted(async () => {
  // 加载证书选项
  await getServerCertificateList()
  await getCaCertificateList()
})
</script>

<style scoped>
.protocol-step {
  padding: 20px 0;
}

.form-actions {
  margin-top: 24px;
  display: flex;
  justify-content: center;
  gap: 12px;
}

:deep(.el-form-item__content) {
  max-width: 400px;
}

:deep(.sl-radio) {
  display: flex;
  gap: 20px;
}

:deep(.el-collapse-item__header) {
  font-size: 14px;
  font-weight: 500;
  color: #606266;
}

:deep(.el-select) {
  width: 100%;
}

:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-form-item) {
  margin-bottom: 22px;
}
</style>
