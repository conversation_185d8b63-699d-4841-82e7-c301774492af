<template>
  <SlProTable
    ref="proTable"
    highlight-current-row
    :columns="columns"
    style="min-height: 300px"
    :request-api="flavorList"
    :init-param="queryParams"
    :current-change="currentChange"
    hidden-table-header
    row-key="id"
  >
  </SlProTable>
</template>
<script setup lang="tsx" name="dataList">
import { ref, reactive } from 'vue'
import type { ColumnProps, ProTableInstance } from '@/components/SlProTable/interface'
import SlMessage from '@/components/base/SlMessage'
import {
  flavorList,
  changeStatusFlavorByIdApi,
  batchDeleteImageByIdApi,
} from '@/api/modules/configCenter'

const { queryParams } = defineProps<{
  queryParams: any
}>()

const emit = defineEmits(['currentChange'])
const currentChange = (currentRow: any, oldCurrentRow: any) => {
  emit('currentChange', currentRow, oldCurrentRow)
}
// 表格配置项
const columns = reactive<ColumnProps<any>[]>([
  { type: 'selection', width: 55 },
  { type: 'index', label: '序号', minWidth: 55 },
  {
    prop: 'domainName',
    label: '云平台',
    minWidth: 150,
  },
  {
    prop: 'serviceName',
    label: '规格名称',
    minWidth: 150,
  },
  // { prop: 'specCode', label: '规格编码', minWidth: 150 },
  { prop: 'regionName', label: '资源池', minWidth: 200 },
  // { prop: 'specInfo', label: '规格信息', minWidth: 150 },
  { prop: 'categoryCode', label: '产品分类类型', minWidth: 150 },
  { prop: 'categoryName', label: '产品分类名称', minWidth: 100 },
  { prop: 'ram', label: '内存(GB)', width: 100 },
  { prop: 'vcpus', label: 'CPU(核)', width: 100 },
  {
    prop: 'status',
    label: '状态',
    width: 100,
    render: ({ row }) => <span> {row.status == '0' ? '下线' : '上线'}</span>,
  },
  // { prop: 'vgpus', label: 'VGPU规格', width: 100 },
  // { prop: '描述', label: '描述', minWidth: 100 },
  {
    prop: 'operation',
    label: '操作',
    width: '150',
    fixed: 'right',
    render: ({ row }) => {
      return (
        <>
          <el-button type="primary" link onClick={() => deletePermission(row)}>
            {row.status == '0' ? '上线' : '下线'}
          </el-button>
        </>
      )
    },
  },
])

const proTable = ref<ProTableInstance>()

const reloadTableList = () => {
  proTable.value?.clearSelection()
  proTable.value?.getTableList()
}

/**
 * 删除
 * @param id
 */
const deletePermission = async (row: any) => {
  let text = row.status == 0 ? '上线' : '下线'
  await ElMessageBox.confirm('确认' + text + row.serviceName + '吗？', '信息提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
  })
  await changeStatusFlavorByIdApi({
    id: row.flavorId,
    status: row.status == 0 ? 1 : 0,
  })
  SlMessage.success('删除成功')
  proTable.value?.getTableList()
  proTable.value?.clearSelection()
}
// 批量删除功能
const handleBatchRecycle = async () => {
  const selectedList = proTable.value?.selectedList || []
  if (selectedList.length == 0) {
    return SlMessage.warning('请先选择规格')
  }
  const ids = selectedList.map((i) => i.id.trim())
  await ElMessageBox.confirm('确认批量删除规格吗？', '信息提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
  })
  await batchDeleteImageByIdApi({
    ids: ids,
  })
  SlMessage.success('删除成功')
  proTable.value?.clearSelection()
  proTable.value?.getTableList()
}

defineExpose({
  handleBatchRecycle,
  reloadTableList,
})
</script>
