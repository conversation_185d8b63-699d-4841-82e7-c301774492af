<template>
  <div class="demo-container">
    <h2>SlTabs 重构示例</h2>
    
    <h3>基础用法</h3>
    <sl-tabs v-model="activeTab1" :tabs="basicTabs">
      <div v-if="activeTab1 === 'tab1'" class="tab-content-demo">
        <p>这是第一个标签页的内容</p>
      </div>
      <div v-if="activeTab1 === 'tab2'" class="tab-content-demo">
        <p>这是第二个标签页的内容</p>
      </div>
      <div v-if="activeTab1 === 'tab3'" class="tab-content-demo">
        <p>这是第三个标签页的内容</p>
      </div>
    </sl-tabs>

    <h3>显示数量</h3>
    <sl-tabs v-model="activeTab2" :tabs="countTabs" show-count>
      <div v-if="activeTab2 === 'all'" class="tab-content-demo">
        <p>全部工单内容 (共 {{ countTabs.find(t => t.name === 'all')?.count }} 个)</p>
      </div>
      <div v-if="activeTab2 === 'pending'" class="tab-content-demo">
        <p>待处理工单内容 (共 {{ countTabs.find(t => t.name === 'pending')?.count }} 个)</p>
      </div>
      <div v-if="activeTab2 === 'completed'" class="tab-content-demo">
        <p>已完成工单内容 (共 {{ countTabs.find(t => t.name === 'completed')?.count }} 个)</p>
      </div>
    </sl-tabs>

    <h3>工单审批示例</h3>
    <sl-tabs v-model="activeTab3" :tabs="workOrderTabs" show-count>
      <div v-if="activeTab3 === 'ecs'" class="tab-content-demo">
        <p>云主机工单列表</p>
        <ul>
          <li>ECS-001 - 申请云主机实例</li>
          <li>ECS-002 - 扩容云主机配置</li>
          <li>ECS-003 - 云主机续费</li>
        </ul>
      </div>
      <div v-if="activeTab3 === 'mysql'" class="tab-content-demo">
        <p>MySQL数据库工单列表</p>
        <ul>
          <li>MySQL-001 - 申请MySQL实例</li>
          <li>MySQL-002 - 数据库扩容</li>
        </ul>
      </div>
      <div v-if="activeTab3 === 'redis'" class="tab-content-demo">
        <p>Redis缓存工单列表</p>
        <ul>
          <li>Redis-001 - 申请Redis实例</li>
        </ul>
      </div>
    </sl-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import SlTabs from '@/components/base/SlTabs/index.vue'

const activeTab1 = ref('tab1')
const activeTab2 = ref('all')
const activeTab3 = ref('ecs')

const basicTabs = [
  { name: 'tab1', label: '标签一' },
  { name: 'tab2', label: '标签二' },
  { name: 'tab3', label: '标签三' },
]

const countTabs = [
  { name: 'all', label: '全部', count: 156 },
  { name: 'pending', label: '待处理', count: 23 },
  { name: 'completed', label: '已完成', count: 133 },
]

const workOrderTabs = [
  { name: 'ecs', label: '云主机', count: 15 },
  { name: 'mysql', label: 'MySQL数据库', count: 8 },
  { name: 'redis', label: 'Redis缓存', count: 3 },
]
</script>

<style lang="scss" scoped>
.demo-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

h2 {
  color: #303133;
  margin-bottom: 20px;
}

h3 {
  color: #606266;
  margin: 30px 0 15px 0;
  font-size: 16px;
}

.tab-content-demo {
  padding: 20px;
  background: #fff;
  border-radius: 0 0 8px 8px;
  min-height: 150px;
  
  p {
    margin: 0 0 15px 0;
    color: #606266;
    font-size: 14px;
  }
  
  ul {
    margin: 0;
    padding-left: 20px;
    
    li {
      margin-bottom: 8px;
      color: #606266;
      font-size: 14px;
    }
  }
}
</style>
