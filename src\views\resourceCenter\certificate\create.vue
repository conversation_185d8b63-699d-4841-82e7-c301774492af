<template>
  <div class="certificate-create">
    <sl-page-header :title="pageTitle" :show-back="true" @back="handleBack"> </sl-page-header>
    <div class="form-container">
      <el-form
        ref="formRef"
        :model="formModel"
        :rules="formRules"
        label-width="120px"
        class="certificate-form"
      >
        <el-form-item label="证书名称" prop="certificateName">
          <el-input v-model="formModel.certificateName" placeholder="请输入证书名称" />
        </el-form-item>

        <el-form-item label="业务系统" prop="businessSystemCode">
          <el-select
            v-model="formModel.businessSystemCode"
            placeholder="请选择业务系统"
            style="width: 100%"
            filterable
            clearable
            @change="handleBusinessSystemChange"
          >
            <el-option
              v-for="item in busiSystemOptions"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="云类型" prop="catalogueDomainCode">
          <el-select
            v-model="formModel.catalogueDomainCode"
            placeholder="请选择云类型"
            style="width: 100%"
            filterable
            clearable
            @change="handleCloudTypeChange"
          >
            <el-option
              v-for="item in cloudTypeOptions"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="云平台" prop="domainCode">
          <el-select
            v-model="formModel.domainCode"
            placeholder="请选择云平台"
            style="width: 100%"
            filterable
            clearable
            @change="handleCloudPlatformChange"
          >
            <el-option
              v-for="item in cloudPlatformOptions"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="资源池" prop="regionId">
          <el-select
            v-model="formModel.regionId"
            placeholder="请选择资源池"
            style="width: 100%"
            filterable
            clearable
            @change="handleResourcePoolChange"
          >
            <el-option
              v-for="item in resourcePoolOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="证书类型" prop="certificateType">
          <el-select
            v-model="formModel.certificateType"
            placeholder="请选择证书类型"
            style="width: 100%"
            clearable
          >
            <el-option label="服务器证书" value="SERVER" />
            <el-option label="客户端证书" value="CLIENT" />
          </el-select>
        </el-form-item>

        <el-form-item label="公钥证书内容" prop="publicKeyCertificate">
          <el-input
            v-model="formModel.publicKeyCertificate"
            type="textarea"
            :rows="6"
            placeholder="请输入公钥证书内容"
          />
        </el-form-item>

        <el-form-item label="私钥证书内容" prop="privateKeyCertificate">
          <el-input
            v-model="formModel.privateKeyCertificate"
            type="textarea"
            :rows="6"
            placeholder="请输入私钥证书内容"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSubmit">提交</el-button>
          <el-button @click="handleBack">取消</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, onMounted, reactive, ref } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance } from 'element-plus'
import { useRouter } from 'vue-router'
import { createCertificate } from '@/api/modules/resourecenter'
import useBusiSystemOptions from '../hooks/useBusiSystemOptions'
import SlPageHeader from '@/components/SlPageHeader/index.vue'
import { getCloudTypeDic, getCloudPlatformDic, getResourcePoolsDic } from '@/api/modules/dic'

const router = useRouter()
const { busiSystemOptions } = useBusiSystemOptions()

// 表单实例
const formRef = ref<FormInstance>()
// 是否正在加载
const loading = ref(false)

// 表单数据
const formModel = reactive({
  certificateName: '', // 证书名称
  businessSystemCode: '', // 业务系统编码
  businessSystemName: '', // 业务系统名称
  catalogueDomainCode: '', // 云类型编码
  catalogueDomainName: '', // 云类型名称
  domainCode: '', // 云平台编码
  domainName: '', // 云平台名称
  regionId: '', // 资源池ID
  regionName: '', // 资源池名称
  regionCode: '', // 资源池编码
  certificateType: '', // 证书类型
  publicKeyCertificate: '', // 公钥证书内容
  privateKeyCertificate: '', // 私钥证书内容
})

// 表单校验规则
const formRules = {
  certificateName: [{ required: true, message: '请输入证书名称', trigger: 'blur' }],
  businessSystemCode: [{ required: true, message: '请选择业务系统', trigger: 'change' }],
  catalogueDomainCode: [{ required: true, message: '请选择云类型', trigger: 'change' }],
  domainCode: [{ required: true, message: '请选择云平台', trigger: 'change' }],
  regionId: [{ required: true, message: '请选择资源池', trigger: 'change' }],
  certificateType: [{ required: true, message: '请选择证书类型', trigger: 'change' }],
  publicKeyCertificate: [{ required: true, message: '请输入公钥证书内容', trigger: 'blur' }],
  privateKeyCertificate: [{ required: true, message: '请输入私钥证书内容', trigger: 'blur' }],
}

// 字典数据
const cloudTypeOptions = ref<any[]>([])
const cloudPlatformOptions = ref<any[]>([])
const resourcePoolOptions = ref<any[]>([])

// 标题文本
const pageTitle = computed(() => '创建证书')

// 初始化数据
onMounted(async () => {
  // 获取云类型数据
  await getCloudTypeList()
})

// 获取云类型列表
const getCloudTypeList = async () => {
  try {
    const { entity } = await getCloudTypeDic(null)
    cloudTypeOptions.value = entity || []
  } catch (error) {
    console.error('获取云类型失败', error)
  }
}

// 获取云平台列表
const getCloudPlatformList = async () => {
  try {
    const { entity } = await getCloudPlatformDic({
      parentCode: formModel.catalogueDomainCode,
    })
    cloudPlatformOptions.value = entity || []
  } catch (error) {
    console.error('获取云平台失败', error)
  }
}

// 获取资源池列表
const getResourcePools = async () => {
  try {
    const { entity } = await getResourcePoolsDic({
      domainCode: formModel.domainCode,
    })
    resourcePoolOptions.value = entity || []
  } catch (error) {
    console.error('获取资源池失败', error)
  }
}

// 业务系统变更处理
const handleBusinessSystemChange = () => {
  formModel.businessSystemName = ''
  if (formModel.businessSystemCode) {
    const system = busiSystemOptions.value.find(
      (item: any) => item.code === formModel.businessSystemCode,
    )
    if (system) {
      formModel.businessSystemName = system.name
    }
  }
}

// 云类型变更处理
const handleCloudTypeChange = () => {
  formModel.catalogueDomainName = ''
  if (formModel.catalogueDomainCode) {
    const cloudType = cloudTypeOptions.value.find(
      (item: any) => item.code === formModel.catalogueDomainCode,
    )
    if (cloudType) {
      formModel.catalogueDomainName = cloudType.name
    }
  }

  // 重置关联字段
  formModel.domainCode = ''
  formModel.domainName = ''
  formModel.regionId = ''
  formModel.regionName = ''
  formModel.regionCode = ''

  // 清空选项
  cloudPlatformOptions.value = []
  resourcePoolOptions.value = []

  // 加载云平台
  if (formModel.catalogueDomainCode) {
    getCloudPlatformList()
  }
}

// 云平台变更处理
const handleCloudPlatformChange = () => {
  formModel.domainName = ''
  if (formModel.domainCode) {
    const platform = cloudPlatformOptions.value.find(
      (item: any) => item.code === formModel.domainCode,
    )
    if (platform) {
      formModel.domainName = platform.name
    }
  }

  // 重置关联字段
  formModel.regionId = ''
  formModel.regionName = ''
  formModel.regionCode = ''

  // 清空选项
  resourcePoolOptions.value = []

  // 加载资源池
  if (formModel.domainCode) {
    getResourcePools()
  }
}

// 资源池变更处理
const handleResourcePoolChange = () => {
  formModel.regionName = ''
  formModel.regionCode = ''
  if (formModel.regionId) {
    const pool = resourcePoolOptions.value.find((item: any) => item.id === formModel.regionId)
    if (pool) {
      formModel.regionName = pool.name
      formModel.regionCode = pool.code
    }
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        loading.value = true
        const { code, message } = await createCertificate(formModel)
        if (code === 200) {
          ElMessage.success('创建证书成功')
          // 返回列表页
          router.push('/certificateList')
        } else {
          ElMessage.error(message || '创建证书失败')
        }
      } catch (error) {
        console.error('创建证书失败', error)
        ElMessage.error('创建证书失败，请稍后重试')
      } finally {
        loading.value = false
      }
    }
  })
}

// 返回
const handleBack = () => {
  router.push('/certificateList')
}
</script>

<style lang="scss" scoped>
.certificate-create {
  background-color: #fff;
  height: 100%;
  padding: 0 16px 16px;
}

.form-container {
  padding: 20px;
}

.certificate-form {
  max-width: 800px;
}
</style>
