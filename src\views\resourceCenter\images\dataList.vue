<template>
  <SlProTable
    ref="proTable"
    highlight-current-row
    :columns="columns"
    style="min-height: 300px"
    :request-api="getImageList"
    :init-param="queryParams"
    :current-change="currentChange"
    @selection-change="handleSelectionChange"
    hidden-table-header
    row-key="id"
  >
  </SlProTable>
</template>
<script setup lang="tsx" name="dataList">
import { ref, reactive, type VNode } from 'vue'
import type { ColumnProps, ProTableInstance } from '@/components/SlProTable/interface'
import { getImageList } from '@/api/modules/resourecenter'

const { queryParams } = defineProps<{
  queryParams: any
}>()
const emit = defineEmits(['currentChange'])
const radioValue = ref()
const currentChange = (currentRow: any, oldCurrentRow: any) => {
  if (!currentRow) return
  radioValue.value = currentRow.id
  emit('currentChange', currentRow, oldCurrentRow)
}

// 表格配置项
const columns = reactive<ColumnProps<any>[]>([
  { type: 'selection', width: 55 },
  { type: 'index', label: '序号', width: 55 },
  { prop: 'imageName', label: '镜像名称', width: 150 },
  { prop: 'osName', label: '操作系统名称', width: 150 },
  { prop: 'osVersion', label: '操作系统版本', width: 120 },
  { prop: 'size', label: '大小', width: 100 },
  { prop: 'format', label: '格式', width: 100 },
  {
    prop: 'uploadTime',
    label: '上传时间',
    width: 250,
    render: ({ row }: any) => {
      if (!row.uploadCompleted) {
        return <el-progress percentage={row.progress} stroke-width={6}></el-progress>
      }
      return row.uploadTime1
    },
  },
  { prop: 'operation', label: '操作', fixed: 'right', width: 180, render: operationRender },
])

function operationRender({ row }: any): VNode {
  return (
    <div style="display: flex; flex-wrap: wrap; gap: 8px; justify-content: center;">
      <div style="width: 40px;">
        {!row.uploadCompleted ? (
          <el-button onClick={() => handleUpload(row)} type="primary" link>
            重传
          </el-button>
        ) : (
          <el-button onClick={() => handleDownload(row)} type="primary" link>
            下载
          </el-button>
        )}
      </div>

      <div style="width: 40px;">
        {row.uploadCompleted && (
          <el-button onClick={() => handleEdit(row)} type="primary" link>
            编辑
          </el-button>
        )}
      </div>

      <div style="width: 40px;">
        <el-button onClick={() => handleDelete(row)} type="danger" link>
          删除
        </el-button>
      </div>
    </div>
  )
}

const proTable = ref<ProTableInstance>()
const handleSelectionChange = (selection: any[]) => {
  console.log('selected rows:', selection)
}

// 处理上传
const handleUpload = (row: any) => {
  console.log('处理上传镜像', row)
  // 这里实现上传镜像的逻辑
}

// 处理下载
const handleDownload = (row: any) => {
  console.log('下载镜像', row)
  // 这里实现下载镜像的逻辑
}

// 处理编辑
const handleEdit = (row: any) => {
  console.log('编辑镜像', row)
  // 这里实现编辑镜像的逻辑
}

// 处理删除
const handleDelete = (row: any) => {
  console.log('删除镜像', row)
  // 这里实现删除镜像的逻辑
}

defineExpose({
  handleUpload,
})
</script>
