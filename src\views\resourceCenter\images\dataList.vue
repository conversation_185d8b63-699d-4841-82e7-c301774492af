<template>
  <SlProTable
    ref="proTable"
    highlight-current-row
    :columns="columns"
    style="min-height: 300px"
    :request-api="getImageList"
    :init-param="queryParams"
    :current-change="currentChange"
    :data-callback="dataCallBack"
    @selection-change="handleSelectionChange"
    hidden-table-header
    row-key="id"
  >
  </SlProTable>
</template>
<script setup lang="tsx" name="dataList">
import { ref, reactive, type VNode } from 'vue'
import type { ColumnProps, ProTableInstance } from '@/components/SlProTable/interface'
import { getImageList, imageDelete, imageUpdate } from '@/api/modules/resourecenter'
import { uploaderManager } from '@/components/SlMinioUploader/manage'
import aDownload from '@/utils/aDownload'
import SlMinioUploader from '@/components/SlMinioUploader/index.vue'

const { queryParams } = defineProps<{
  queryParams: any
}>()
const emit = defineEmits(['currentChange'])
const radioValue = ref()
const currentChange = (currentRow: any, oldCurrentRow: any) => {
  if (!currentRow) return
  radioValue.value = currentRow.id
  emit('currentChange', currentRow, oldCurrentRow)
}
const dataCallBack = (data: any) => {
  data.records.forEach((item: any, index: number) => {
    const uploader = uploaderManager.getUploader(item.id)
    if (uploader) {
      const fn = (progress: number, url?: string) => {
        if (proTable.value) {
          proTable.value.tableData[index].progress = progress
          if (url && progress === 100) {
            proTable.value.tableData[index].downloadUrl = url
            proTable.value.tableData[index].uploadCompleted = true
          }
        }
      }
      if (!item.uploadCompleted) {
        uploader.onProgress(fn)
      }
    }
  })
  console.log('return-data')
  return data
}
// 表格配置项
const columns = reactive<ColumnProps<any>[]>([
  { type: 'index', label: '序号', width: 55 },
  { prop: 'imageName', label: '镜像名称', width: 200 },
  { prop: 'osName', label: '操作系统名称', width: 150 },
  { prop: 'osVersion', label: '操作系统版本', width: 150 },
  { prop: 'size', label: '大小', width: 150 },
  { prop: 'format', label: '格式', width: 150 },
  {
    prop: 'uploadTime',
    label: '上传时间',
    render: ({ row }: any) => {
      if (!row.uploadCompleted) {
        return <el-progress percentage={row.progress} stroke-width={6}></el-progress>
      }
      return row.uploadTime1
    },
  },
  { prop: 'operation', label: '操作', fixed: 'right', width: 150, render: operationRender },
])

function operationRender({ row }: any): VNode {
  return (
    <div style="display: flex; flex-wrap: wrap; gap: 8px; justify-content: center;">
      <div style="width: 40px;">
        {!row.uploadCompleted ? (
          <SlMinioUploader
            drag={false}
            triggerText="重传"
            onReady={(uploader) => {
              uploader.setIdentification(row.id)
              uploader.start({ imageId: row.id })
              imageUpdate({
                id: row.id,
                md5: uploader.md5,
                fileName: uploader.file.name,
                totalChunksCount: uploader.totalChunksCount,
                fileSize: uploader.file.size,
              }).then(() => {
                // 刷新表格
                proTable.value?.getTableList()
              })
            }}
            ref="uploaderRef"
            maxFileSize={30720}
          />
        ) : (
          <el-button onClick={() => handleDownload(row)} type="primary" link>
            下载
          </el-button>
        )}
      </div>

      {/* <div style="width: 40px;">
        {row.uploadCompleted && (
          <el-button onClick={() => handleEdit(row)} type="primary" link>
            编辑
          </el-button>
        )}
      </div> */}

      <div style="width: 40px;">
        <el-button onClick={() => handleDelete(row)} type="danger" link>
          删除
        </el-button>
      </div>
    </div>
  )
}

const proTable = ref<ProTableInstance>()
const handleSelectionChange = (selection: any[]) => {
  console.log('selected rows:', selection)
}

// 处理上传
const handleUpload = (row: any) => {
  console.log('处理上传镜像', row)
  // 这里实现上传镜像的逻辑
}

// 处理下载
const handleDownload = (row: any) => {
  console.log('下载镜像', row)
  aDownload(row.downloadUrl, row.imageName)
}

// // 处理编辑
// const handleEdit = (row: any) => {
//   console.log('编辑镜像', row)
//   // 这里实现编辑镜像的逻辑
// }

// 处理删除
const handleDelete = async (row: any) => {
  ElMessageBox.confirm('确定删除镜像文件吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    await imageDelete({ id: row.id })
    ElMessage.success('删除成功')
    proTable.value?.getTableList()
  })
}

defineExpose({
  handleUpload,
  proTable,
})
</script>
