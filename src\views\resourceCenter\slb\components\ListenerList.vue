<template>
  <SlProTable
    ref="proTable"
    highlight-current-row
    :columns="columns"
    style="min-height: 300px"
    :request-api="getSlbListenerList"
    :init-param="queryParams"
    hidden-table-header
    row-key="goodsOrderId"
  >
    <template #search>
      <el-button type="primary" @click="handleAddListener" style="margin-left: -10px">
        创建监听
      </el-button>
    </template>
  </SlProTable>
</template>

<script setup lang="tsx" name="ListenerList">
import { ref, reactive, type VNode, computed } from 'vue'
import type { ColumnProps, ProTableInstance } from '@/components/SlProTable/interface'
import { getSlbListenerList, deleteSlbListener } from '@/api/modules/resourecenter'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter, useRoute } from 'vue-router'

const router = useRouter()

// 定义props
const props = defineProps<{
  slbId: string
}>()

const route = useRoute()

const isPublic = computed(() => route.query.sourceType === 'DG')

// 表格ref
const proTable = ref<ProTableInstance>()

// 查询参数
const queryParams = reactive({
  slbResourceDetailId: props.slbId,
})

// 表格配置项
const columns = reactive<ColumnProps<any>[]>([
  { type: 'index', label: '序号', width: 55, fixed: 'left' },
  {
    prop: 'listenerName',
    label: '监听名称',
    width: 220,
    fixed: 'left',
    render: ({ row }: any): VNode => {
      return (
        <el-button type="primary" link onClick={() => handleEditListener(row, 'edit')}>
          {row.listenerName}
        </el-button>
      )
    },
  },
  {
    prop: 'deviceId',
    label: '后端协议/端口',
    render: ({ row }: any) => {
      return (
        row.slbListenerProtocolModel.protocolType + '/' + row.slbListenerProtocolModel.listenerPort
      )
    },
  },
  { prop: 'runningStatus', label: '运行状态' },
  {
    prop: 'healthCheckStatus',
    label: '健康检测状态',
    render: () => {
      return '健康'
    },
  },
  {
    prop: 'bandWidth',
    label: '服务器组',
    render: ({ row }: any) => {
      return row.slbListenerServerGroup.groupName
    },
  },
  {
    prop: 'status',
    label: '操作状态',
    render: ({ row }: any) => {
      return row.status == 1 ? '操作执行中' : '--'
    },
  },
  {
    prop: 'operation',
    label: '操作',
    width: 200,
    fixed: 'right',
    render: ({ row }: any): VNode => {
      return (
        <>
          <el-button
            type="primary"
            link
            disabled={row.status == 1}
            onClick={() => handleDeleteListener(row)}
          >
            删除
          </el-button>
        </>
      )
    },
  },
])

// 新建监听
const handleAddListener = () => {
  handleEditListener(null, 'add')
}

// 编辑监听
const handleEditListener = (row: any, type: string) => {
  router.push({
    path: '/slbListenerForm',
    query: {
      id: type === 'add' ? '' : row.id,
      slbId: props.slbId,
      sourceType: isPublic.value ? 'DG' : '',
    },
  })
}

const safeLock = ref(false)
// 删除监听
const handleDeleteListener = (row: any) => {
  if (safeLock.value) return
  safeLock.value = true
  ElMessageBox.confirm(`确认删除监听 "${row.listenerName}" 吗？`, '提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    try {
      // 调用删除接口
      await deleteSlbListener({ id: row.id })
      ElMessage.success('已提交删除，请稍后查看')
      proTable.value?.getTableList()
    } catch (error) {
      console.error('删除失败:', error)
    } finally {
      setTimeout(() => {
        safeLock.value = false
      }, 1000)
    }
  })
}

// 暴露刷新方法供父组件调用
defineExpose({
  refresh: () => {
    proTable.value?.getTableList()
  },
})
</script>
