import { validateEcsPassword } from '@/utils/validate'
import SlMessage from '@/components/base/SlMessage'
import { executeResourceOperation } from '@/api/modules/resourecenter'

export interface ResourceOperationModel {
  operationType: string // 操作类型
  orderId: number // 工单 ID
  goodsOrderId: string // 商品单 ID
  deviceStatus?: string // 状态
  newPwd?: string // 密码
  confirmNewPwd?: string // 确认密码
}

// 获取回收表单模型
export function useResourceOperationFormModel() {
  const resourceOperationModel = {
    operationType: '', // 操作类型
    orderId: 0, // 工单 ID
    goodsOrderId: '', // 商品单 ID
    deviceStatus: '', // 状态
    newPwd: '', // 密码
    confirmNewPwd: '', // 确认密码
  }
  return resourceOperationModel
}

// 自定义校验函数，用于比较密码和确认密码
const validateConfirmPassword = (
  rule: any,
  value: string,
  callback: (error?: Error) => void,
  model: ResourceOperationModel,
) => {
  if (value !== model.newPwd) {
    callback(new Error('确认密码与新密码不一致'))
  } else {
    callback()
  }
}

// 获取修改密码表单配置项
export function useUpdatePasswordFormOptions(model: ResourceOperationModel) {
  const updatePasswordOptions = [
    {
      groupName: '',
      groupItems: [
        {
          label: '新密码',
          type: 'input',
          key: 'newPwd',
          span: 24,
          props: {
            maxlength: 50,
            showWordLimit: true,
            showPassword: true,
          },
          rules: [
            { required: true, message: '请输入新密码', trigger: ['blur', 'change'] },
            { validator: validateEcsPassword, trigger: ['blur', 'change'] },
          ],
        },
        {
          label: '确认密码',
          type: 'input',
          key: 'confirmNewPwd',
          span: 24,
          props: {
            maxlength: 50,
            showWordLimit: true,
            showPassword: true,
          },
          rules: [
            { required: true, message: '请输入确认密码', trigger: ['blur', 'change'] },
            { validator: validateEcsPassword, trigger: ['blur', 'change'] },
            {
              validator: (rule: any, value: string, callback: (error?: Error) => void) => {
                validateConfirmPassword(rule, value, callback, model)
              },
              trigger: ['blur'],
            },
          ],
        },
      ],
    },
  ]
  return updatePasswordOptions
}

export async function executeResourceOperationConfirm(
  resourceOperationModel: ResourceOperationModel,
) {
  const { operationType } = resourceOperationModel
  if (operationType === 'RESETPWD') {
    if (['STOP'].includes(resourceOperationModel.deviceStatus || ''))
      return SlMessage.error('当前状态不允许修改密码')
  }
  const { code, entity, message } = await executeResourceOperation(resourceOperationModel)
  if (code === 200) {
    SlMessage.success(entity || '操作成功')
  } else {
    SlMessage.error(message || '操作失败')
  }
}

export function useResourceOperationFormClear(
  useResourceOperationFormClear: ResourceOperationModel,
) {
  Object.assign(useResourceOperationFormClear, useResourceOperationFormModel())
}
