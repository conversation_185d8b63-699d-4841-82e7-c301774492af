<template>
  <div style="width: 100%; display: flex; flex-direction: column; height: 400px">
    <div style="margin-bottom: 10px">
      <el-row :gutter="16">
        <div style="margin-right: 16px">筛选</div>
        <el-col :span="4">
          <el-input
            class="image-search-input"
            clearable
            v-model="queryModel.vcpus"
            placeholder="输入vCPU"
          >
          </el-input>
        </el-col>
        <el-col :span="4">
          <el-input clearable v-model="queryModel.ram" placeholder="输入内存"></el-input>
        </el-col>
        <el-col :span="12">
          <el-button :icon="Search" @click="handleSearch" type="primary">查询</el-button>
        </el-col>
      </el-row>
    </div>
    <div
      style="flex: 1; min-height: 0; overflow: hidden"
      :style="{ width: totalWidth + 20 + 'px' }"
    >
      <SlProTable
        ref="proTable"
        highlight-current-row
        :columns="columns"
        :height="300"
        :request-api="getEcsSpecList"
        :init-param="queryParams"
        :current-change="currentChange"
        @selection-change="handleSelectionChange"
        hidden-table-header
        row-key="id"
      >
      </SlProTable>
    </div>
  </div>
</template>
<script setup lang="tsx" name="dataList">
import { ref, reactive, computed, watch } from 'vue'
import type { ColumnProps, ProTableInstance } from '@/components/SlProTable/interface'
import { getEcsSpecList } from '@/api/modules/resourecenter'
import { Search } from '@element-plus/icons-vue'

const { form, item } = defineProps<{
  form: any
  item: any
}>()

const model = form

const queryParams = ref({
  regionCode: model.resourcePool?.code,
  vcpus: '',
  ram: '',
})
const queryModel = ref({
  regionCode: model.resourcePool?.code,
  vcpus: '',
  ram: '',
})

watch(
  () => model.resourcePool?.code,
  (regionCode) => {
    queryParams.value.regionCode = regionCode
  },
)
const handleSearch = () => {
  queryParams.value = { ...queryModel.value }
}
const radioValue = ref()
const currentChange = (currentRow: any) => {
  if (!currentRow) return
  radioValue.value = currentRow.id
  model[item.key] = currentRow
}
// 表格配置项
const columns = reactive<ColumnProps<any>[]>([
  {
    render: ({ row }) => {
      return <el-radio modelValue={radioValue.value} value={row.id} size="large"></el-radio>
    },
    label: '选择',
    width: 55,
  },
  { prop: 'type', label: '规格类型', minWidth: 150 },
  { prop: 'vcpus', label: 'vCPU', minWidth: 100 },
  { prop: 'ram', label: '内存(MB)', minWidth: 120 },
  { prop: 'gpuType', label: 'GPU', minWidth: 200 },
])

const totalWidth = computed(() =>
  item.totalWidth
    ? item.totalWidth
    : columns.reduce((acc: number, column: any) => acc + Number(column.width || 150), 0),
)

const proTable = ref<ProTableInstance>()

// 多选数据
const multipleSelection = ref<any[]>([])

// // 处理确认
// const handleConfirm = () => {
//   proTable.value?.getTableList()
// }

// 选择变化事件
const handleSelectionChange = (selection: any[]) => {
  multipleSelection.value = selection
}

defineExpose({
  selectedList: multipleSelection,
})
</script>
<style lang="scss" scoped>
:deep(.el-input__wrapper) {
  border-color: #dcdfe6 !important;
  box-shadow: 0 0 0 1px #dcdfe6 inset !important;
}
:deep(.el-input__wrapper):hover {
  border-color: #c0c4cc !important;
  box-shadow: 0 0 0 1px #c0c4cc inset !important;
}

:deep(.el-input__wrapper.is-focus) {
  border-color: #409eff !important;
  box-shadow: 0 0 0 1px #409eff inset !important;
}
:deep(.el-select__wrapper) {
  border-color: #dcdfe6 !important;
  box-shadow: 0 0 0 1px #dcdfe6 inset !important;
}
:deep(.el-select__wrapper):hover {
  border-color: #c0c4cc !important;
  box-shadow: 0 0 0 1px #c0c4cc inset !important;
}

:deep(.el-select__wrapper.is-focus) {
  border-color: #409eff !important;
  box-shadow: 0 0 0 1px #409eff inset !important;
}
.warning {
  width: 90%;
  margin: 0 auto;
  margin-top: -10px;
  word-break: break-all;
  color: red;
}

// 去除表格阴影，增加边框
:deep(.sl-pro-table) {
  box-shadow: none;
  border: 1px solid #e4e7ed;

  .el-table {
    box-shadow: none;
    border: 1px solid #e4e7ed;
  }

  .el-table__header-wrapper {
    border-bottom: 1px solid #e4e7ed;
  }

  .el-table__body-wrapper {
    border: none;
  }
}
</style>
