import http from '@/api'
import { WOC } from '../config/servicePort'
import { changeDateFormat } from '@/utils'

/**
 * @name 云主机开通资源==获取业务模块
 */
export const belongingBusiness = (config: { businessSystemId: string }) => {
  return http.get(WOC + '/business/moduleList', config)
}

/**
 * @name 云主机开通资源==获取厂家
 */
export const manuFacturerapi = () => http.get(WOC + '/config/manufacturerList')
/**
 * @name 云主机开通资源==获取厂家负责人电话
 */
export const manuFacturerapiiphone = (config: { manufacturerName: string }) =>
  http.get(WOC + '/config/manufacturerNameList', config)
/**
 * @name 云主机开通资源==获取领导
 */
export const queryUsersByRoleCode = (config: { roleCode: string; domainCode: string }) =>
  http.post(WOC + '/user/queryUsersByRoleCode', config)

/**
 * @name 云主机开通资源==提交资源申请
 */
export const submitResourceRequest = (config: any) =>
  http.post(WOC + '/standardWorkorder/create', config)

/**
 * @name 业务系统详情
 */
export const busisystemDetail = (config: { id: number | undefined | string }) =>
  http.get<any>(WOC + '/business/detail', config)

/**
 * @name 获取可用区
 */
export const getAz = (config: { resourcePoolId: string }) =>
  http.get<any>(WOC + '/az/query', config)

/**
 * @name 云主机开通资源==提交资源申请
 */
export const vpcCreate = (config: any) => http.post(WOC + '/vpc/vpcCreate', config)

export const vpcCreateBatch = (config: any) => http.post(WOC + '/vpc/vpcCreateBatch', config)

export const addVpcSubnetBatch = (config: any) =>
  http.post(WOC + '/vpc/createVpcSubnetBatch', config)
/**
 * @name vpc列表
 */
export const vpcList = (config: any) => {
  return http.post<any>(WOC + '/form/queryVpcListPage', changeDateFormat(config, ['createdTime']))
}

/**
 * @name 网络列表
 */
export const networkList = (config: any) => {
  return http.post<any>(
    WOC + '/form/queryNetworkListPage',
    changeDateFormat(config, ['createdTime']),
  )
}

/**
 * @name nat网关规则列表
 */
export const natRuleList = (config: any) =>
  http.post(WOC + '/nat/queryNatRulesPage', changeDateFormat(config, ['createdTime']))

/**
 * 获取 nat 详情
 */
export const getNatDetails = (config: any) =>
  http.post<any>(WOC + '/nat/getNatRulesDetails', config)

export const natRuleCreate = (config: any) =>
  http.post<any>(WOC + '/nat/rulesCreate', config, { loading: true })

/**
 * @name 获取资源列表
 */
export const getResourceList = (config: any) => {
  return http.post<any>(
    WOC + '/resource/page',
    changeDateFormat(config, ['effectiveTime', 'expireTime']),
  )
}

/**
 * @name 订单回显
 */
export const displayBackOrder = (config: any) =>
  http.post<any>(WOC + '/standardWorkorder/detail', config)

/**
 * @name 购物车 创建
 */
export const shoppingCartCreate = (config: any = {}) =>
  http.post<any>(WOC + '/order/shoppingCart/create', config)

/**
 * @name 购物车 更新
 */
export const shoppingCartUpdate = (config: any = {}) =>
  http.post<any>(WOC + '/order/shoppingCart/update', config)

/**
 * @name 购物车 列表
 */
export const shoppingCartList = (config: any = {}) =>
  http.post<any>(WOC + '/order/shoppingCart/list', config)

/**
 * @name 购物车 删除
 */
export const shoppingCartDelete = (config: any = {}) =>
  http.post<any>(WOC + '/order/shoppingCart/delete', config)

/**
 * @name 购物车 总数
 */
export const shoppingCartCount = (config: any = {}) =>
  http.post<any>(WOC + '/order/shoppingCart/count', config)

/**
 * @name 网段列表
 */
export const ipRangeList = (config: any) => http.get<any>(WOC + '/network/ip/list', config)

/**
 * @name 网段校验
 */
export const ipRangeCheck = (config: any = {}) => http.post<any>(WOC + '/network/ip/check', config)

/**
 * @name 子网切割
 */
export const ipRangeSplit = (config: any = {}) => http.post<any>(WOC + '/network/ip/subnet', config)

/**
 * @name 网络创建
 */
export const networkCreate = (config: any = {}) =>
  http.post<any>(WOC + '/network/networkCreate', config)

export const networkCreateBatch = (config: any) =>
  http.post(WOC + '/network/networkCreateBatch', config)

export const addNetworkSubnetBatch = (config: any) =>
  http.post(WOC + '/network/createSubnetWork', config)

/**
 * @name 更新网络状态
 */
export const updateNetworkStatus = (config: any = {}) =>
  http.post<any>(WOC + '/network/ip/updateStatus', config)

/**
 * @name vlan列表
 */
export const vlanList = (config: any) => http.get<any>(WOC + '/network/vlan/list', config)

/**
 * @name 更新vlan状态
 */
export const updateVlanStatus = (config: any = {}) =>
  http.post<any>(WOC + '/network/vlan/updateStatus', config)

/*
 * @name 资源导出
 */
export const resourceExport = (config: any = {}) =>
  http.post<any>(WOC + '/goods/resource/export', config, { responseType: 'blob' })

/**
 * @name network详情
 */
export const getNetworkDetail = (config: any) =>
  http.get<any>(WOC + '/network/getNetworkDetail', config)

/**
 * @name vpc详情
 */
export const getVpcDetail = (config: any) => http.get<any>(WOC + '/vpc/getVpcDetail', config)

/**
 * @name 草稿工单
 */
export const addDraftsOrder = (config: any = {}) =>
  http.post<any>(WOC + '/standardWorkorder/draft', config)

/**
 * @name 获取规格树
 */
export const flavorModelTree = (config: any = {}) =>
  http.post<any>(WOC + '/flavorModel/listTree', config)

/**
 * @name 获取镜像树
 */
export const imageTree = (config: any = {}) => http.post<any>(WOC + '/images/listTree', config)

/**
 *
 * @name 资源回收
 */
export const executeResourceOperation = (config: any = {}) =>
  http.post<any>(WOC + '/resource/vm/operate', config)

/**
 * @name 查询云主机挂载资源
 */
export const queryVmMountResource = (config: any = {}) =>
  http.post<any>(WOC + '/resource/getMountResource', config)

/**
 * @name 查询子网列表
 * @param config
 * @returns
 */
export const getSubnetList = (config: any = {}) =>
  http.post<any>(WOC + '/resource/getNetWorkDetail', config)

export const cycleBinCreate = (config: any = {}) =>
  http.post<any>(WOC + '/recovery/shoppingCart/create', config)

export const cycleBinUpdate = (config: any = {}) =>
  http.post<any>(WOC + '/recovery/shoppingCart/update', config)

export const cycleBinList = (config: any = {}) =>
  http.post<any>(WOC + '/recovery/shoppingCart/list', config)

export const cycleBinDelete = (config: any = {}) =>
  http.post<any>(WOC + '/recovery/shoppingCart/delete', config)

export const cycleBinCount = (config: any = {}) =>
  http.post<any>(WOC + '/recovery/shoppingCart/count', config)

export const cycleBinListProduct = (config: any = {}) =>
  http.post<any>(WOC + '/recovery/shoppingCart/listProduct', config)

export const recoveryDetail = (config: any = {}) =>
  http.post<any>(WOC + '/recovery/workOrder/detail', config)

export const submitRecoveryOrder = (config: any) =>
  http.post(WOC + '/recovery/workOrder/create', config)

export const addDraftsRecoveryOrder = (config: any = {}) =>
  http.post<any>(WOC + '/recovery/workOrder/draft', config)

/**
 * @name 变更区 更新
 */
export const propertyChangeUpdate = (config: any = {}) =>
  http.post<any>(WOC + '/change/shoppingCart/update', config)

/**
 * @name 变更区 列表
 */
export const propertyChangeList = (config: any = {}) =>
  http.post<any>(WOC + '/change/shoppingCart/list', config)

/**
 * @name 变更区 总数
 */
export const propertyChangeCount = (config: any = {}) =>
  http.post<any>(WOC + '/change/shoppingCart/count', config)

/**
 * @name 变更区 提交
 */
export const changeWorkOrderCreate = (config: any) =>
  http.post(WOC + '/changeWorkOrder/create', config)

/**
 * @name 变更区 草稿
 */
export const changeWorkOrderDraft = (config: any) =>
  http.post(WOC + '/changeWorkOrder/draft', config)

/**
 * @name 变更区 详情
 */
export const changeWorkOrderDetail = (config: any) =>
  http.post<any>(WOC + '/changeWorkOrder/detail', config)

/**
 * @name EIP绑定设备
 */
export const eipBind = (config: { id: string; deviceId: string; deviceType: string }) =>
  http.post<any>(WOC + '/eip/bind', config)

/**
 * @name EIP解绑设备
 */
export const eipUnbind = (config: { id: string }) => http.post<any>(WOC + '/eip/unbind', config)

/**
 * @name 云硬盘绑定设备
 */
export const evsBind = (config: { volumeResourceDetailId: string; vmResourceDetailId: string }) =>
  http.post<any>(WOC + '/evs/bind', config)

/**
 * @name 云硬盘解绑设备
 */
export const evsUnbind = (config: { volumeResourceDetailId: string }) =>
  http.post<any>(WOC + '/evs/unbind', config)

/**
 * @name 获取镜像列表
 */
export const getImageList = (config: any = {}) => http.post<any>(WOC + '/images/list', config)

/**
 * @name 获取镜像增加
 */
export const imageAdd = (config: any = {}) => http.post<any>(WOC + '/images/add', config)

/**
 * @name 安全组列表查询
 */
export const getSecurityGroupList = (config: any) =>
  http.post<any>(WOC + '/securityGroup/page', changeDateFormat(config, ['createTime']))

/**
 * @name 安全组创建
 */
export const createSecurityGroup = (config: any = {}) =>
  http.post<any>(WOC + '/securityGroup/create', config)

/**
 * @name 安全组删除
 */
export const deleteSecurityGroup = (config: any = {}) =>
  http.post<any>(WOC + '/securityGroup/delete', config)

/**
 * @name 安全组操作（绑定/解绑）
 */
export const securityGroupOperate = (config: any = {}) =>
  http.post<any>(WOC + '/securityGroup/operate', config)

/**
 * @name 安全组详情
 */
export const getSecurityGroupDetail = (config: { id: string }) =>
  http.get<any>(WOC + '/securityGroup/detail', config)

/**
 * @name 安全组规则添加
 */
export const addSecurityGroupRule = (config: any = {}) =>
  http.post<any>(WOC + '/securityGroup/createRule', config)

/**
 * @name 安全组规则编辑
 */
export const updateSecurityGroupRule = (config: any = {}) =>
  http.post<any>(WOC + '/securityGroup/updateRule', config)

/**
 * @name 安全组规则删除
 */
export const deleteSecurityGroupRule = (config: { id: string; ruleIds: string[] }) =>
  http.post<any>(WOC + '/securityGroup/deleteRule', config)

/**
 * @name 虚拟网卡列表查询
 */
export const getVirtualNicList = (config: any) =>
  http.post<any>(WOC + '/vnic/page', changeDateFormat(config, ['createTime']))

/**
 * @name 虚拟网卡创建
 */
export const createVirtualNic = (config: any = {}) => http.post<any>(WOC + '/vnic/save', config)

/**
 * @name 虚拟网卡删除
 */
export const deleteVirtualNic = (config: any = {}) => http.get<any>(WOC + '/vnic/delete', config)

/**
 * @name 虚拟网卡操作（绑定/解绑）
 */
export const virtualNicOperate = (config: any = {}) => http.post<any>(WOC + '/vnic/operate', config)

/**
 * @name 虚拟网卡详情
 */
export const getVirtualNicDetail = (config: { id: string }) =>
  http.get<any>(WOC + '/vnic/detail', config)

/**
 * @name 虚拟网卡编辑
 */
export const updateVirtualNic = (config: any = {}) => http.post<any>(WOC + '/vnic/update', config)

/**
 * @name 获取VPC子网列表
 */
export const getVpcSubnetsList = (config: any = {}) =>
  http.get<any>(WOC + '/vpc/getSubnetsByVpcId', config)

/**
 * @name 获取网络子网列表
 */
export const getNetworkSubnetsList = (config: any = {}) =>
  http.get<any>(WOC + '/network/getSubnetsByNetworkId', config)

/**
 * @name 虚拟IP列表查询
 */
export const getVirtualIpList = (config: any) =>
  http.post<any>(WOC + '/virtualIp/page', changeDateFormat(config, ['createdTime']))

/**
 * @name 虚拟IP创建
 */
export const createVirtualIp = (config: any = {}) => http.post<any>(WOC + '/virtualIp/add', config)

/**
 * @name 虚拟IP删除
 */
export const deleteVirtualIp = (config: any = {}) =>
  http.post<any>(WOC + '/virtualIp/delete', config)

/**
 * @name 虚拟IP详情
 */
export const getVirtualIpDetail = (config: { id: string }) =>
  http.get<any>(WOC + '/virtualIp/detail', config)

/**
 * @name 虚拟IP编辑
 */
export const updateVirtualIp = (config: any = {}) =>
  http.post<any>(WOC + '/virtualIp/update', config)

/**
 * @name 获取未使用的虚拟IP列表
 */
export const getUnusedVirtualIp = (config: any = {}) =>
  http.post<any>(WOC + '/virtualIp/getUnusedIpList', config)

/**
 * @name 获取SLB详情
 */
export const getResourceDetail = (config: any = {}) => {
  return http.post<any>(WOC + `/resource/detail`, config)
}

/**
 * @name 获取负载均衡监听列表
 */
export const getSlbListenerList = (config: any = {}) =>
  http.post<any>(WOC + '/slbListener/page', config)

/**
 * @name 创建负载均衡监听
 */
export const createSlbListener = (data: any) => http.post(WOC + '/slbListener/create', data)

/**
 * @name 删除负载均衡监听
 */
export const deleteSlbListener = (data: any) => http.post(WOC + `/slbListener/delete`, data)

/**
 * @name 获取负载均衡监听详情
 */
export const getSlbListenerDetail = (data: any) =>
  http.post<any>(WOC + '/slbListener/getById', data)

/**
 * @name 获取负载均衡服务器组列表
 */
export const getSlbServerGroupList = (config: any = {}) =>
  http.post<any>(WOC + '/slbListenerServerGroup/page', config)

/**
 * @name 获取负载均衡服务器组详情
 */
export const getSlbServerGroupDetail = (data: any) =>
  http.post<any>(WOC + '/slbListenerServerGroup/detail', data)

/**
 * @name 获取容器配额商品列表
 */
export const containerPageApi = (config: any) =>
  http.post<any>(WOC + '/container/quota/page', changeDateFormat(config, ['createTime']))

/**
 * @name 证书管理列表查询
 */
export const getCertificateList = (config: any) =>
  http.post<any>(WOC + '/certificate/page', changeDateFormat(config, ['createdTime']))

/**
 * @name 创建证书
 */
export const createCertificate = (config: any = {}) =>
  http.post<any>(WOC + '/certificate/add', config)

/**
 * @name 删除证书
 */
export const deleteCertificate = (config: any = {}) =>
  http.post<any>(WOC + '/certificate/delete', config)

/**
 * @name 证书详情
 */
export const getCertificateDetail = (config: { id: string }) =>
  http.get<any>(WOC + '/certificate/detail', config)
