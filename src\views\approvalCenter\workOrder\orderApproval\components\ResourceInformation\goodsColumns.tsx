import { useGlobalDicStore } from '@/stores/modules/dic'
import type { goodsColumnsType } from '@/views/approvalCenter/workOrder/interface/type'
const globalDic = useGlobalDicStore()
const { getDic } = globalDic

const whethers = [
  { label: '是', value: true },
  { label: '否', value: false },
]
// 云服务器
const ecsColumns: goodsColumnsType['ecs'] = [
  {
    prop: 'originName',
    label: '主机名称',
    minWidth: '180px',
  },
  {
    label: '功能模块',
    prop: 'functionalModule',
    minWidth: '100px',
  },
  {
    prop: 'flavorName',
    label: '实例规格',
    minWidth: 180,
    render(scope) {
      return (
        <div>
          {scope.row.flavorType ?? '--'} / {scope.row.flavorName ?? '--'}
        </div>
      )
    },
  },
  {
    prop: 'sysDiskInfo',
    label: '系统盘',
    minWidth: '150px',
    render(scope) {
      return (
        <div>
          {scope.row?.sysDiskType ?? '--'} / {scope.row?.sysDiskSize ?? '--'}
          GB
        </div>
      )
    },
  },
  {
    prop: 'evsInfo',
    label: '数据盘',
    minWidth: '150px',
    render(scope) {
      return (
        <div>
          {scope.row.mountDataDisk
            ? scope.row.mountDataDiskList
                ?.map((item: any) => {
                  return `${item.sysDiskType ?? '--'} / ${item.sysDiskSize ?? '--'} GB`
                })
                .join(',')
            : '--'}
        </div>
      )
    },
  },
  {
    prop: 'imageName',
    label: '镜像',
    minWidth: '180px',
    render(scope) {
      return (
        <div>
          {scope.row.imageVersion ?? '--'} / {scope.row.imageOs ?? '--'}
        </div>
      )
    },
  },
  {
    label: '带宽',
    prop: 'bandWiDthNumbers',
    minWidth: 100,
    render(scope) {
      return (
        <div>
          {scope.row.bindPublicIp
            ? scope.row.eipModelList?.map((item: any) => item.bandwidth).join(',')
            : '--'}
        </div>
      )
    },
  },
  {
    prop: 'disasterRecovery',
    label: '是否容灾',
    minWidth: '150px',
    headerRender,

    enum: whethers,
  },
  { label: '申请时长', prop: 'applyTime', minWidth: 100, enum: getDic('time') },

  // 云硬盘配置
]
// 云硬盘
const evsColumns: goodsColumnsType['evs'] = [
  {
    label: '数据盘',
    prop: 'evsInfo',
    minWidth: 100,
    render(scope) {
      return (
        <div>
          {scope.row.sysDiskType ?? '--'} / {scope.row.sysDiskSize ?? '--'} GB
        </div>
      )
    },
  },
  { label: '功能模块', prop: 'functionalModule', minWidth: 120 },
  {
    label: '是否挂载云主机',
    prop: 'mountDataDisk',
    minWidth: 130,
    render(scope) {
      return <div>{scope.row.vmName ? '是' : '否'}</div>
    },
  },
  {
    label: '云主机名称(IP)',
    prop: 'vmName',
    minWidth: 150,
    render(scope) {
      return <div>{scope.row.vmName?.length ? scope.row.vmName : '--'}</div>
    },
  },
  { label: '申请时长', prop: 'applyTime', minWidth: 100, enum: getDic('time') },
]
// 对象存储
const obsColumns: goodsColumnsType['obs'] = [
  { label: '对象存储名称', prop: 'originName', minWidth: 150 },
  { label: '功能模块', prop: 'functionalModule', minWidth: 120 },
  {
    label: '存储类型',
    prop: 'storageDiskType',
    minWidth: 120,
  },
  {
    label: '大小',
    prop: 'storageDiskSize',
    minWidth: 100,
    render(scope) {
      return <div>{scope.row.storageDiskSize ?? '--'} GB</div>
    },
  },
  { label: '申请时长', prop: 'applyTime', minWidth: 100, enum: getDic('time') },
]

// 负载均衡
const slbColumns: goodsColumnsType['slb'] = [
  { label: '负载均衡名称', prop: 'originName', minWidth: 150 },
  { label: '功能模块', prop: 'functionalModule', minWidth: 120 },
  {
    label: '实例规格',
    prop: 'flavorName',
    minWidth: 180,
  },
  { label: '是否绑定公网', prop: 'bindPublicIp', enum: whethers, minWidth: 120 },
  {
    label: '带宽',
    prop: 'bandWiDthNumbers',
    minWidth: 100,
    render(scope) {
      return (
        <div>
          {scope.row.bindPublicIp
            ? scope.row.eipModelList?.map((item: any) => item.bandwidth).join(',')
            : '--'}
        </div>
      )
    },
  },
  { label: '申请时长', prop: 'applyTime', minWidth: 100, enum: getDic('time') },
]
// NAT网关
const natColumns: goodsColumnsType['nat'] = [
  { label: '网关名称', prop: 'originName', minWidth: 150 },
  { label: '功能模块', prop: 'functionalModule', minWidth: 120 },
  {
    label: '实例规格',
    prop: 'flavorName',
    minWidth: 180,
  },
  { label: '是否绑定公网', prop: 'bindPublicIp', enum: whethers, minWidth: 120 },
  {
    label: '带宽',
    prop: 'bandWiDthNumbers',
    minWidth: 100,
    render(scope) {
      return (
        <div>
          {scope.row.bindPublicIp
            ? scope.row.eipModelList?.map((item: any) => item.bandwidth).join(',')
            : '--'}
        </div>
      )
    },
  },
  { label: '申请时长', prop: 'applyTime', minWidth: 100, enum: getDic('time') },
]

// 弹性IP
const eipColumns: goodsColumnsType['eip'] = [
  { label: '弹性公网名称', prop: 'originName', minWidth: 150 },
  { label: '功能模块', prop: 'functionalModule', minWidth: 120 },
  {
    label: '云主机名称(IP)',
    prop: 'vmName',
    minWidth: 150,
    render(scope) {
      return <div>{scope.row.vmName?.length ? scope.row.vmName : '--'}</div>
    },
  },
  {
    label: '带宽',
    prop: 'bandwidth',
    minWidth: 100,
  },
  { label: '申请时长', prop: 'applyTime', minWidth: 100, enum: getDic('time') },
]

// 容器配额
const cqColumns: goodsColumnsType['cq'] = [
  { label: '容器配额名称', prop: 'originName', minWidth: 200 },
  { label: 'vCPU(核)', prop: 'vCpus', minWidth: 100 },
  {
    label: '内存(GB)',
    prop: 'ram',
    minWidth: 100,
  },
  {
    label: 'GPU算力',
    prop: 'gpuRatio',
    minWidth: 100,
  },
  {
    label: 'GPU显存(GB)',
    prop: 'gpuVirtualMemory',
    minWidth: 130,
  },
  {
    label: 'GPU卡数量(个)',
    prop: 'gpuCore',
    minWidth: 130,
  },
  { label: '申请时长', prop: 'applyTime', minWidth: 100, enum: getDic('time') },
]

// MySQL数据库
const mysqlColumns: goodsColumnsType['mysql'] = [
  { label: 'MySQL实例名称', prop: 'originName', minWidth: 180 },
  { label: '功能模块', prop: 'functionalModule', minWidth: 120 },
  {
    label: '实例规格',
    prop: 'flavorName',
    minWidth: 180,
    render(scope) {
      return (
        <div>
          {scope.row.flavorType ?? '--'} / {scope.row.flavorName ?? '--'}
        </div>
      )
    },
  },
  {
    label: '存储类型',
    prop: 'storageDiskType',
    minWidth: 120,
  },
  {
    label: '存储大小',
    prop: 'storageDiskSize',
    minWidth: 120,
    render(scope) {
      return <div>{scope.row.storageDiskSize ?? '--'} GB</div>
    },
  },
  {
    label: '数据库版本',
    prop: 'dbVersion',
    minWidth: 120,
  },
  { label: '是否容灾', prop: 'disasterRecovery', minWidth: 120, enum: whethers },
  { label: '申请时长', prop: 'applyTime', minWidth: 100, enum: getDic('time') },
]

// Redis缓存
const redisColumns: goodsColumnsType['redis'] = [
  { label: 'Redis实例名称', prop: 'originName', minWidth: 180 },
  { label: '功能模块', prop: 'functionalModule', minWidth: 120 },
  {
    label: '实例规格',
    prop: 'flavorName',
    minWidth: 180,
    render(scope) {
      return (
        <div>
          {scope.row.flavorType ?? '--'} / {scope.row.flavorName ?? '--'}
        </div>
      )
    },
  },
  {
    label: '内存大小',
    prop: 'memorySize',
    minWidth: 120,
    render(scope) {
      return <div>{scope.row.memorySize ?? '--'} GB</div>
    },
  },
  {
    label: 'Redis版本',
    prop: 'redisVersion',
    minWidth: 120,
  },
  {
    label: '部署模式',
    prop: 'deployMode',
    minWidth: 120,
  },
  { label: '是否容灾', prop: 'disasterRecovery', minWidth: 120, enum: whethers },
  { label: '申请时长', prop: 'applyTime', minWidth: 100, enum: getDic('time') },
]

const goodsAllColumns: goodsColumnsType = {
  ecs: ecsColumns,
  gcs: ecsColumns,
  evs: evsColumns,
  obs: obsColumns,
  slb: slbColumns,
  nat: natColumns,
  eip: eipColumns,
  cq: cqColumns,
  mysql: mysqlColumns,
  redis: redisColumns,
  other: [],
  unknown: [],
}

function headerRender({ column }: any) {
  return (
    <>
      <div>{column.label}</div>
      <div>
        <span style="color: red; font-size: 12px">容灾选“否”可能导致业务系统高可用缺失</span>
      </div>
    </>
  )
}
export default goodsAllColumns

export const goodsNameKey = {
  ecs: 'vmName',
  gcs: 'vmName',
  evs: 'evsName',
  obs: 'obsName',
  slb: 'slbName',
  nat: 'natName',
  eip: 'eipName',
  cq: 'cqName',
  mysql: 'mysqlName',
  redis: 'redisName',
}
