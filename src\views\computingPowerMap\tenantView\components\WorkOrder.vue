<template>
  <div id="WorkOrder">
    <div
      v-for="orderType in ['workOrder', 'recycleOrder', 'changeOrder'] as OrderType[]"
      :key="orderType"
      v-permission="orderType"
    >
      <div class="header">
        <div class="header-title">{{ orderTypeMsg[orderType] }}情况</div>
        <el-button class="header-btn" type="primary" @click="handleRouterGo(orderType)">
          查看{{ orderTypeMsg[orderType] }}
        </el-button>
      </div>
      <div class="body">
        <div class="row">
          <div class="row-item" @click="handleRouterGo(orderType, 'pending')">
            <div class="row-item__title">
              <span>待审批</span>
              <img
                class="icon-top-right"
                src="@/assets/images/overview/icon_top_right.png"
                alt=""
              />
            </div>
            <div class="row-item__value">
              {{ orderData[orderType].pendingCount }}<span class="unit">个</span>
            </div>
          </div>
          <div class="row-item" @click="handleRouterGo(orderType, 'approved')">
            <div class="row-item__title">
              <span>已通过</span>
              <img
                class="icon-top-right"
                src="@/assets/images/overview/icon_top_right.png"
                alt=""
              />
            </div>
            <div class="row-item__value color-blue">
              {{ orderData[orderType].approvedCount }}<span class="unit">个</span>
            </div>
          </div>
          <div class="row-item" @click="handleRouterGo(orderType, 'rejected')">
            <div class="row-item__title">
              <span>驳回</span>
              <img
                class="icon-top-right"
                src="@/assets/images/overview/icon_top_right.png"
                alt=""
              />
            </div>
            <div class="row-item__value color-orange">
              {{ orderData[orderType].rejectedCount }}<span class="unit">个</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup name="WorkOrder">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useWorkOrderStore } from '@/stores/modules/workOrder'

// 定义工单类型
type OrderType = 'workOrder' | 'recycleOrder' | 'changeOrder'

const router = useRouter()
const handleRouterGo = (orderType: OrderType, type?: string) => {
  const orderType2Path: Record<OrderType, string> = {
    workOrder: '/workOrder',
    recycleOrder: '/recycleWorkOrder',
    changeOrder: '/changeWorkOrder',
  }
  const path = type
    ? `${orderType2Path[orderType]}?activeTab=${type}`
    : `${orderType2Path[orderType]}`
  router.push(path)
}

// 使用全局工单store
const workOrderStore = useWorkOrderStore()

// 使用计算属性获取工单数据
const orderData = computed(() => workOrderStore.orderData)

const orderTypeMsg = ref<Record<OrderType, string>>({
  workOrder: '开通工单',
  recycleOrder: '回收工单',
  changeOrder: '变更工单',
})
</script>
<style lang="scss" scoped>
#WorkOrder {
  padding-left: 20px;
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    .header-title {
      font-size: 16px;
      font-weight: bold;
    }
  }
  .row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .row-item {
      width: 100%;
      margin-right: 20px;
      margin-bottom: 20px;
      padding: 20px;
      background-color: #f8f9fd;
      cursor: pointer;
      &__title {
        margin-bottom: 5px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 14px;
        color: #999;
        .icon-top-right {
          width: 20px;
          height: 20px;
        }
      }
      &__value {
        font-size: 28px;
        .unit {
          font-size: 14px;
          margin-left: 2px;
        }
        &.color-blue {
          color: var(--el-color-primary);
        }
        &.color-orange {
          color: #de7001;
        }
      }
      &__line {
        margin: 10px 0;
        border-bottom: 1px solid #eee;
      }
      &__footer {
        font-size: 14px;
        color: #999;
      }
    }
  }
}
</style>
