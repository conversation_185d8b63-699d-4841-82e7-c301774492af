import type { RouteRecordRaw } from 'vue-router'

const overviewRouter: RouteRecordRaw[] = [
  {
    path: '/operationsOverview',
    component: () => import('@/views/computingPowerMap/operationsOverview/index.vue'),
    name: 'operationsOverview',
    // name: 'VPC',
    meta: {
      title: '运营总览',
      icon: 'operationsOverview',
    },
  },
  {
    path: '/tenantView',
    component: () => import('@/views/computingPowerMap/tenantView/index.vue'),
    name: 'tenantView',
    meta: {
      title: '租户视图',
      icon: 'icon-gongdan',
      activeMenu: 'tenantView',
    },
  },
  {
    path: '/viewOfPublicTenants',
    component: () => import('@/views/computingPowerMap/viewOfPublicTenants/index.vue'),
    name: 'viewOfPublicTenants',
    meta: {
      title: '租户视图',
      icon: 'icon-gongdan',
      activeMenu: 'operationsView',
    },
  },
]

export default overviewRouter
