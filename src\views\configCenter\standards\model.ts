function useModel() {
  return {
    // @ productType 对应的产品名称
    goodsName: '规格',
    operationName: '新增',
    domainCode: '', //
    regionId: [], //
    serviceName: '', //
    specCode: '', //
    specInfo: '', //
    flavorType: '', //
    cateGoryCode: '', //
    cateGoryName: '', //
    vcpus: '', //
    ram: '', //
    disk: '', //
    shares: '', //
    description: '',

    name: '', // flavor名
    stackCode: '', //
    type: '', //类型
    resourceId: '', //底层资源id
    vgpus: '', // vgpu规格
    pgpus: '', //pgpu规格
    fpgas: '', //fpga规格
    flavorFamily: '', //规格族
    flavorMetadataId: '', //规格元数据id
    GpuType: '', //gpu类型
  }
}

export default useModel
