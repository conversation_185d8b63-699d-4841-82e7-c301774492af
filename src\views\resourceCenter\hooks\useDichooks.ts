import { getCloudPlatformDic, getCloudTypeDic, getResourcePoolsDic } from '@/api/modules/dic'
import { ref } from 'vue'

/**
 * 公用的资源管理下拉框公用方法
 */
export const useDichooks = () => {
  const cloudTypeDic = ref<FormDataType[]>([]) //云类型字典
  const cloudPlatformDic = ref<FormDataType[]>([]) //云平台字典
  const resourcePoolsDic = ref<FormDataType[]>([]) // 资源池字典

  // 获取云平台字典
  const getDomainCode = async () => {
    const { entity } = await getCloudPlatformDic({
      parentCode: '',
    })
    cloudPlatformDic.value = entity
  }
  // 获取安全域字典 和 资源池字典
  const getSecurityDomain = async () => {
    const { entity } = await getResourcePoolsDic({
      domainCode: '',
    })
    resourcePoolsDic.value = entity
  }

  // 获取云类型字典
  const getCloudType = async () => {
    const { entity } = await getCloudTypeDic({})
    cloudTypeDic.value = entity
  }
  getDomainCode()
  getSecurityDomain()
  getCloudType()
  return {
    cloudTypeDic, // 云类型字典
    cloudPlatformDic, // 云平台字典
    resourcePoolsDic, // 资源池字典
  }
}
