function useModel() {
  return {
    name: '', // 云区域名称
    code: '', // 云区域编码
    description: '', // 描述
    platformName: '', // 云平台名称
    cloudPlatformId: '', // 云平台ID
    type: '', // 类型
    endpoint: '', // 连接地址
    cityCode: '', //
    cityName: '', //
    resourceCode: '', //底层资源code
    realmType: '', //底层资源code
    azList: [], //底层资源code

    workOrdertype: '开通申请',
    operationName: '新增',
    // @工单id
    id: '',
    // @产品类型
    productType: '资源池',
    // @ productType 对应的产品名称
    goodsName: '资源池',
  }
}

export default useModel
