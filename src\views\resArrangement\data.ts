import { type Nodes, type Edges, type Layouts, defineConfigs } from 'v-network-graph'
import { ref, reactive } from 'vue'

export const useData = () => {
  const draggable = ref(true)
  const nodes = reactive<Nodes>({
    // node1: { name: '云主机', face: 'ecs.png', type: 'ecs' },
    // node2: { name: 'GPU', face: 'gcs.png', type: 'gcs' },
    // node3: { name: '弹性公网', face: 'eip.png', type: 'eip' },
    // node4: { name: '云硬盘', face: 'evs.png', type: 'evs' },
    // node5: { name: 'NAT网关', face: 'nat.png', type: 'nat' },
    // node6: { name: '负载均衡', face: 'slb.png', type: 'slb' },
  })
  const edges = reactive<Edges>({
    // edge1: { source: 'node1', target: 'node2' },
    // edge2: { source: 'node2', target: 'node3' },
    // edge3: { source: 'node2', target: 'node4' },
    // edge4: { source: 'node4', target: 'node5' },
    // edge5: { source: 'node4', target: 'node6' },
  })
  const layouts = reactive<Layouts>({
    nodes: {
      // node1: { x: 0, y: 0 },
      // node2: { x: 80, y: 80 },
      // node3: { x: 0, y: 160 },
      // node4: { x: 240, y: 80 },
      // node5: { x: 320, y: 0 },
      // node6: { x: 320, y: 160 },
    },
  })
  const configs = reactive(
    defineConfigs({
      node: {
        normal: {
          type: 'rect',
          width: 56,
          height: 56,
          color: '#fff',
          strokeWidth: 3,
          strokeColor: '#eee',
          borderRadius: 6,
        },
        hover: {
          strokeWidth: 3,
          color: '#666',
        },
        selected: {
          type: 'rect',
          width: 56,
          height: 56,
          color: '#666',
          strokeWidth: 3,
          strokeColor: '#eee',
          borderRadius: 6,
        },
        draggable: true,
        selectable: true,
        label: {
          fontSize: 14,
          fontFamily: 'Arial, sans-serif',
          margin: 8,
          direction: 'south',
        },
        focusring: {
          visible: true,
          width: 3,
          padding: 0,
          color: '#de8802',
          dasharray: '0',
        },
      },
      edge: {
        normal: {
          width: 2,
          color: '#999',
        },
        hover: {
          color: '#666666',
        },
        selectable: true,
        gap: 5,
        type: 'straight',
        margin: 4,
        marker: {
          source: {
            type: 'none',
            width: 4,
            height: 4,
            margin: -1,
            offset: 0,
            units: 'strokeWidth',
            color: null,
          },
          target: {
            type: 'arrow',
            width: 4,
            height: 4,
            margin: -1,
            offset: 0,
            units: 'strokeWidth',
            color: null,
          },
        },
      },
      view: {
        minZoomLevel: 0.1,
        maxZoomLevel: 2.0,
        panEnabled: true,
        zoomEnabled: true,
        fitContentMargin: '8%',
        mouseWheelZoomEnabled: true,
        doubleClickZoomEnabled: true,
        autoPanOnResize: true,
        grid: {
          visible: true,
          interval: 50,
          thickIncrements: 2,
          line: {
            color: '#eee',
            width: 1,
            dasharray: 1,
          },
          thick: {
            color: '#ededed',
            width: 1,
            dasharray: 0,
          },
        },
      },
    }),
  )

  return { nodes, edges, layouts, configs, draggable }
}
