<template>
  <div>
    <div class="tab-item">
      <template v-if="vpcForms.length">
        <div v-for="(form, index) in vpcForms" :key="index" class="network-form-container">
          <SinglePrivateVpcForm
            :ref="(ref) => setRefs(ref, form)"
            v-if="plane.includes('私网')"
            :plane="plane"
          />
          <SingleNonPrivateVpcForm :ref="(ref) => setRefs(ref, form)" v-else :plane="plane" />
          <el-icon
            v-if="['create', 'createWithOrder'].includes(opType)"
            @click="removeNetwork(index)"
            class="delete-icon"
            :size="24"
          >
            <CircleCloseFilled />
          </el-icon>
        </div>
      </template>
      <el-empty v-else description="暂无网络配置" :image-size="200">
        <el-button v-if="opType !== 'view'" type="primary" @click="addNetwork">
          添加网络配置
        </el-button>
      </el-empty>
    </div>
    <div style="padding: 8px" v-if="vpcForms.length">
      <el-button
        v-if="['create', 'createWithOrder'].includes(opType)"
        :icon="CirclePlus"
        style="width: 100%"
        type="primary"
        @click="addNetwork"
      >
        增加网络
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import SinglePrivateVpcForm from './singlePrivateVpcForm.vue'
import SingleNonPrivateVpcForm from './singleNonPrivateVpcForm.vue'
import { CirclePlus, CircleCloseFilled } from '@element-plus/icons-vue'
import { ref, inject, type Ref, watch } from 'vue'
import type { IBaseFormProvider } from './types'

const setRefs = (ref: any, form: any) => {
  form.ref = ref
}

const props = defineProps<{
  plane: string
  tabItem: any
  vpcId: string
}>()
const baseFormProvider = inject<Ref<IBaseFormProvider>>('baseFormProvider')!
const opType = baseFormProvider.value.opType
const vpcForms = ref<{ id: number; ref: null }[]>([])
function setTabCount(tabItem: any, count: number) {
  tabItem.count = count
}
baseFormProvider?.value.collectSubmits(vpcForms)
const addNetwork = () => {
  vpcForms.value.push({ id: vpcForms.value.length + 1, ref: null })
}
if (props.vpcId) addNetwork()

watch(
  () => vpcForms.value.length,
  (newVal) => {
    setTabCount(props.tabItem, newVal)
  },
  { immediate: true },
)

const removeNetwork = (index: number) => {
  vpcForms.value.splice(index, 1)
}
</script>

<style scoped>
.network-form-container {
  position: relative;
  margin-bottom: 16px;
}

.network-form-container:hover {
  position: relative;
  margin-bottom: 16px;
  .delete-icon {
    display: block;
  }
}

.delete-icon {
  position: absolute;
  top: 2px;
  right: 2px;
  font-size: 20px;
  cursor: pointer;
  z-index: 1;
  color: var(--el-color-danger);
  display: none;
}

.delete-icon:hover {
  opacity: 0.8;
}

:deep(.el-empty) {
  padding: 40px 0;
}
</style>
