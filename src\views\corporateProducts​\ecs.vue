<template>
  <div>
    <div>
      <sl-page-header title="云主机"></sl-page-header>
    </div>
    <sl-form
      class="corporate-products​"
      label-position="left"
      :options="formOptions"
      :model-value="formModel"
      :label-width="120"
      ref="formRef"
    >
      <template #image-slot="{ form, item }">
        <el-row>
          <el-col style="margin-bottom: 16px" :span="12">
            <el-input
              clearable
              v-model="imageQueryModel"
              placeholder="输入镜像查询"
              class="image-search-input"
            >
              <template #append>
                <el-button @click="handleImageQuery" :icon="Search" />
              </template>
            </el-input>
          </el-col>
          <el-col style="margin-bottom: 16px" :span="24">
            <CustomRadio :item="item" :form="form">
              <template #append>
                <div class="expand-container">
                  <el-button
                    link
                    type="primary"
                    class="expand-button"
                    @click="isExpanded = !isExpanded"
                  >
                    <el-icon :class="{ rotated: isExpanded }" class="expand-icon">
                      <ArrowDown />
                    </el-icon>
                    <span v-if="imageOptionsAll.length > 5" class="expand-text">
                      {{ isExpanded ? '收起其他镜像' : '展开其他镜像' }}
                    </span>
                  </el-button>
                  <div class="expand-line"></div>
                </div>
              </template>
            </CustomRadio>
          </el-col>
          <el-col style="margin-bottom: 16px" :span="12">
            <el-select clearable v-model="form.imageVersion" placeholder="输选择版本">
              <el-option
                v-for="item in versionOptions"
                :key="item.value"
                :label="item.label"
                :value="item"
                value-key="id"
              />
            </el-select>
          </el-col>
        </el-row>
      </template>
      <template #is-bind-slot="{ form, item }">
        <div class="eip-config">
          <el-checkbox @change="handleEipChange" v-model="form[item.key]">
            分配公网IPv4地址
          </el-checkbox>
        </div>
      </template>
      <template #spec-slot="{ form, item }">
        <EcsSpec
          v-if="form.resourcePool"
          :form="form"
          :item="item"
          @validate="validateSpec"
          type="ecs"
        />
      </template>
    </sl-form>

    <!-- 右下角价格面板 -->
    <div class="price-panel">
      <div class="con">
        <div class="price-section">
          <!-- <div class="price-info">
            <div class="discount-info">
              <span class="label">折扣价格:</span>
              <span class="discount-price">¥ 300.00</span>
              <span class="discount-badge">(3折)</span>
            </div>
            <div class="original-info">
              <span class="original-label">原价:</span>
              <span class="original-price">¥ 1000</span>
              <el-button link type="primary" class="detail-link">查看明细</el-button>
            </div>
          </div>
          <div class="warning-text">当前价格不代表真实产品，最终价格以BOSS侧为准！</div> -->
        </div>
        <div class="action-section">
          <el-button class="cancel-btn" @click="handleCancel">取消</el-button>
          <el-button type="primary" plain class="cart-btn" @click="handleAddToCart">
            加入清单
          </el-button>
          <el-button type="primary" class="order-btn" @click="handleDirectOrder">
            直接开通
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="tsx">
import { markRaw, ref, watch } from 'vue'
import CustomRadio from './components/CustomRadio.vue'
import RegionSelect from './components/RegionSelect.vue'
import VpcSelect from './components/VpcSelect.vue'
import SlBlockTitle from '@/components/base/SlBlockTitle.vue'
import EcsSpec from './components/EcsSpec.vue'
import CustomTable from './components/CustomTable.vue'
import Bandwidth from './components/Bandwidth.vue'
import { Search, ArrowDown } from '@element-plus/icons-vue'
import { useTenant } from './hooks/useTenant'
import {
  corporateOrderApi,
  corporateOrderTemSaveApi,
  colrporateListTreeApi as imageTreeApi,
} from '@/api/modules/resourecenter'
import { useGlobalDicStore } from '@/stores/modules/dic'
import useGetTheParameters from './hooks/useGetTheParameters'
import { useRouter } from 'vue-router'
import SlForm from '@/components/form/SlForm.vue'
import { ElMessage } from 'element-plus'
import { validateGoodsName } from '../resourceCenter/utils'
import eventBus from '@/utils/eventBus'

const globalDic = useGlobalDicStore()
const { getDic } = globalDic
const { tenantList: tenantListOptions } = useTenant()
const imageOptionsAll = ref([])
const imageOptions = ref([])
const versionOptions = ref<any[]>([])
const isExpanded = ref(false)
const getImageTree = async (config: any = {}) => {
  const { entity } = await imageTreeApi(config)
  imageOptionsAll.value = entity.map((e: any) => ({
    label: e.name,
    value: e,
  }))
}

watch(
  [isExpanded, imageOptionsAll],
  ([isExpanded, imageOptionsAll]) => {
    if (isExpanded) {
      imageOptions.value = imageOptionsAll
    } else {
      imageOptions.value = imageOptionsAll.slice(0, 5)
    }
  },
  { immediate: true },
)

const imageQueryModel = ref('')
watch(imageQueryModel, () => {
  imageOptions.value = imageOptionsAll.value.filter((e: any) =>
    e.label.includes(imageQueryModel.value),
  )
})

const handleImageQuery = () => {
  imageOptions.value = imageOptionsAll.value.filter((e: any) =>
    e.label.includes(imageQueryModel.value),
  )
}

const formModel = ref<FormDataType>({
  isBindEip: false,
  catalogueDomainCode: 'cloudst_group_moc',
  catalogueDomainName: '移动云',
  domain: '',
  resourcePool: null,
  az: '',
  bandwidth: 1,
  instanceName: '',
  loginName: 'root',
  loginPassword: '',
  confirmPassword: '',
  tenant: '',
  number: 1,
  paymentType: 'month',
  region: 'placeholder', // 占位 无意义
  vpc: null,
  subnet: null,
  spec: null,
  image: null,
  imageVersion: null,
  systemDisk: '',
  dataDisk: '',
})

watch(
  () => formModel.value.resourcePool?.id,
  (regionId: any) => {
    formModel.value.image = null
    formModel.value.imageVersion = null
    if (regionId) {
      getImageTree({
        regionId,
      })
    }
  },
)

watch(
  () => formModel.value.image,
  (image: any) => {
    formModel.value.imageVersion = null
    if (image) {
      versionOptions.value = image.children.map((e: any) => ({
        label: e.name,
        value: e.id,
      }))
    } else {
      versionOptions.value = []
    }
  },
)

const bandwidthOptions = ref([1, 2, 3, 5, 10, 50, 100, 200])
const formOptions = ref([
  {
    style: 'margin:10px;padding:15px 10px;',
    gutter: 40,
    groupItems: [
      {
        label: '付费类型',
        type: 'component',
        key: 'paymentType',
        span: 24,
        required: true,
        options: [
          {
            label: '按日付费',
            value: 'day',
          },
          {
            label: '按月付费',
            value: 'month',
          },
          {
            label: '按年付费',
            value: 'year',
          },
        ],
        component: markRaw(CustomRadio),
      },
      {
        label: '区域',
        type: 'component',
        key: 'region',
        span: 24,
        required: true,
        rules: [
          {
            validator: (rule: any, value: any, callback: any) => {
              if (!formModel.value.domain) {
                callback(new Error('请选择云平台'))
              }
              if (!formModel.value.resourcePool) {
                callback(new Error('请选择资源池'))
              }
              if (!formModel.value.az) {
                callback(new Error('请选择可用区'))
              }
              callback()
            },
            trigger: ['blur', 'change'],
          },
        ],
        component: markRaw(RegionSelect),
        props: {
          maxlength: 64,
        },
      },
      {
        label: '租户',
        type: 'select',
        key: 'tenant',
        span: 13,
        required: true,
        rules: [{ required: true, message: '请选择租户', trigger: ['blur', 'change'] }],
        options: tenantListOptions,
        props: {
          select: {
            valueKey: 'id',
          },
        },
      },
      {
        label: '网络',
        type: 'component',
        key: 'vpc',
        span: 24,
        required: true,
        rules: [
          { required: true, message: '请选择VPC' },
          {
            validator: (rule: any, value: any, callback: any) => {
              if (!formModel.value.subnet) {
                callback(new Error('请选择子网'))
              }
              callback()
            },
          },
        ],
        component: markRaw(VpcSelect),
      },
    ],
  },
  {
    style: 'margin:10px;padding:15px 10px;',
    gutter: 40,
    groupItems: [
      {
        span: 24,
        render() {
          return (
            <SlBlockTitle size={16} style="margin-bottom:20px">
              实例与镜像
            </SlBlockTitle>
          )
        },
      },
      {
        label: '实例',
        type: 'slot',
        slotName: 'spec-slot',
        key: 'spec',
        span: 24,
        required: true,
        rules: [
          {
            validator: (rule: any, value: any, callback: any) => {
              if (!formModel.value.spec) {
                callback(new Error('请选择实例规格'))
              }
              callback()
            },
            trigger: ['blur', 'change'],
          },
        ],
        totalWidth: '900px',
        // component: EcsSpec,
      },
      {
        label: '镜像',
        type: 'slot',
        key: 'image',
        options: imageOptions,
        slotName: 'image-slot',
        span: 24,
        required: true,
        rules: [
          { required: true, message: '请选择镜像', trigger: ['blur', 'change'] },
          {
            validator: (rule: any, value: any, callback: any) => {
              if (!formModel.value.imageVersion) {
                callback(new Error('请选择镜像版本'))
              }
              callback()
            },
            trigger: ['blur', 'change'],
          },
        ],
      },
    ],
  },
  {
    style: 'margin:10px;padding:15px 10px;',
    gutter: 40,
    groupItems: [
      {
        span: 24,
        render() {
          return (
            <SlBlockTitle size={16} style="margin-bottom:20px">
              存储
            </SlBlockTitle>
          )
        },
      },
      {
        label: '系统盘',
        type: 'component',
        key: 'systemDisk',
        span: 24,
        component: markRaw(CustomTable),
        columns: [
          {
            prop: 'type',
            label: '类型',
            width: 180,
            type: 'select',
            placeholder: '请选择类型',
            options: getDic('sysDisk'),
          },
          {
            prop: 'capacity',
            label: '容量',
            width: 200,
            type: 'number',
            min: 40,
            max: 500,
            unit: 'GB',
            inputWidth: '120px',
          },
          {
            prop: 'quantity',
            label: '数量',
            width: 180,
            type: 'text',
            min: 1,
            max: 100,
            inputWidth: '100px',
          },
        ],
        canAdd: false,
        showEmptyState: false,
        defaultRow: {
          type: 'SSD',
          capacity: 40,
          quantity: 1,
        },
      },
      {
        label: '数据盘',
        type: 'component',
        key: 'dataDisk',
        span: 24,
        component: markRaw(CustomTable),
        columns: [
          {
            prop: 'type',
            label: '类型',
            width: 180,
            type: 'select',
            placeholder: '请选择类型',
            options: getDic('evs'),
          },
          {
            prop: 'capacity',
            label: '容量',
            width: 200,
            type: 'number',
            min: 20,
            max: 2048,
            unit: 'GB',
            inputWidth: '120px',
          },
          {
            prop: 'quantity',
            label: '数量',
            width: 180,
            type: 'number',
            min: 1,
            max: 10,
            inputWidth: '100px',
          },
          {
            prop: 'action',
            label: '操作',
            width: 60,
            type: 'action',
            action: 'delete',
          },
        ],
        canAdd: true,
        maxRows: 16,
        addButtonText: '添加数据盘',
        emptyText: '暂无数据盘配置',
        showEmptyState: true,
        defaultRow: {
          type: 'SSD',
          capacity: 20,
          quantity: 1,
        },
      },
    ],
  },
  {
    style: 'margin:10px;padding:15px 10px;',
    gutter: 40,
    groupItems: [
      {
        span: 24,
        render() {
          return (
            <SlBlockTitle size={16} style="margin-bottom:20px">
              带宽
            </SlBlockTitle>
          )
        },
      },
      {
        label: '公网IP',
        type: 'slot',
        key: 'isBindEip',
        span: 24,
        slotName: 'is-bind-slot',
        columns: [
          {
            prop: 'ip',
            label: 'IP',
          },
        ],
      },
      {
        label: '带宽值',
        type: 'component',
        key: 'bandwidth',
        span: 24,
        component: markRaw(Bandwidth),
        options: bandwidthOptions,
        get disabled() {
          return !formModel.value.isBindEip
        },
      },
    ],
  },
  {
    style: 'margin:10px;padding:15px 10px;',
    gutter: 40,
    groupItems: [
      {
        span: 24,
        render() {
          return (
            <SlBlockTitle size={16} style="margin-bottom:20px">
              管理设置
            </SlBlockTitle>
          )
        },
      },
      {
        label: '实例名称',
        type: 'input',
        key: 'instanceName',
        required: true,
        rules: [
          { required: true, message: '请输入实例名称', trigger: ['blur', 'change'] },
          { validator: validateGoodsName, trigger: ['blur', 'change'] },
        ],
        span: 13,
      },
      {
        label: '登录名  ',
        type: 'input',
        key: 'loginName',
        required: true,
        rules: [{ required: true, message: '请输入登录名', trigger: ['blur', 'change'] }],
        span: 13,

        props: {
          disabled: true,
        },
      },
      {
        label: '登录密码',
        type: 'input',
        key: 'loginPassword',
        required: true,
        rules: [{ required: true, message: '请输入登录密码', trigger: ['blur', 'change'] }],
        props: {
          showPassword: true,
        },
        span: 13,
      },
      {
        label: '确认密码',
        type: 'input',
        key: 'confirmPassword',
        required: true,
        rules: [
          { required: true, message: '请输入确认密码', trigger: ['blur', 'change'] },
          {
            validator: (rule: any, value: any, callback: any) => {
              if (value !== formModel.value.loginPassword) {
                callback(new Error('两次密码不一致'))
              }
              callback()
            },
            trigger: ['blur', 'change'],
          },
        ],
        props: {
          showPassword: true,
        },
        span: 13,
      },
      {
        label: '购买数量',
        type: 'inputNumber',
        key: 'number',
        span: 13,
        min: 1,
        max: 10,
        inputWidth: '100px',
      },
    ],
  },
  {
    style: 'margin:10px;padding:15px 10px;',
    gutter: 40,
    groupItems: [
      {
        span: 24,
        render() {
          return (
            <SlBlockTitle size={16} style="margin-bottom:20px">
              配置概要
            </SlBlockTitle>
          )
        },
      },
      {
        label: '付费类型',
        type: 'text',
        getter: (form: any) =>
          form.paymentType === 'day'
            ? '按日付费'
            : form.paymentType === 'month'
              ? '按月付费'
              : '按年付费',
        span: 8,
      },
      {
        label: '区域',
        type: 'text',
        getter: (form: any) =>
          form.az?.name ? `${form.domain?.name} , ${form.resourcePool.name} , ${form.az.name}` : '',
        span: 8,
      },
      {
        label: '网络',
        type: 'text',
        getter: (form: any) =>
          form.subnet?.subnetName ? `${form.vpc.vpcName} , ${form.subnet?.subnetName}` : '',
        span: 8,
      },
      {
        label: '实例规格',
        type: 'text',
        getter: (form: any) => form.spec?.name || '',
        span: 8,
      },
      {
        label: '可用区',
        type: 'text',
        getter: (form: any) => form.az?.name || '',
        span: 8,
      },
      {
        label: '镜像',
        type: 'text',
        getter: (form: any) =>
          form.imageVersion?.label ? `${form.image?.name} / ${form.imageVersion.label} ` : '',
        span: 8,
      },
      {
        label: '系统盘',
        type: 'text',
        getter: (form: any) =>
          form.systemDisk?.[0]?.capacity
            ? `${form.systemDisk?.[0].type} / ${form.systemDisk?.[0]?.capacity}G / ${form.systemDisk?.[0]?.quantity}`
            : '',
        span: 8,
      },
      {
        label: '数据盘',
        type: 'text',
        getter: (form: any) =>
          form.dataDisk?.length > 0
            ? form.dataDisk
                ?.map((item: any) => `${item.type} / ${item.capacity}G / ${item.quantity}`)
                .join(',')
            : '',
        span: 8,
      },
      {
        label: '公网带宽',
        type: 'text',
        getter: (form: any) => (form.isBindEip ? form.bandwidth : ''),
        span: 8,
      },
      {
        label: '实例名称',
        type: 'text',
        getter: (form: any) => form.instanceName || '',
        span: 8,
      },
      {
        label: '所属租户',
        type: 'text',
        getter: (form: any) => form.tenant.name || '',
        span: 8,
      },
    ],
  },
])

// 处理公网IP绑定变化
const handleEipChange = () => {
  formModel.value.bandwidth = 0
  // 这里可以添加其他逻辑，比如当取消绑定时清空相关配置
}

const formRef = ref<InstanceType<typeof SlForm>>()

// 校验规格
const validateSpec = () => {
  formRef.value?.elFormRef?.validateField('spec')
}

const router = useRouter()
// 处理取消操作
const handleCancel = () => {
  // 可以添加取消逻辑，比如清空表单或返回上一页
  router.go(-1)
  eventBus.emit('corporateShoppingList:updateCount')
}

// 处理加入清单操作
const handleAddToCart = async () => {
  try {
    // 1. 校验表单
    const valid = await formRef.value?.validate()
    if (!valid) return

    // 2. 构建参数
    const params = useGetTheParameters(formModel.value, 'ecs', false)

    // 3. 调用API
    await corporateOrderTemSaveApi(params)
    ElMessage.success('加入清单成功')

    handleCancel()
  } catch (error: any) {
    // console.error(error)
    console.error(error)
  }
}

// 处理直接开通操作
const handleDirectOrder = async () => {
  try {
    // 1. 校验表单
    const valid = await formRef.value?.validate()
    if (!valid) return

    // 2. 构建参数
    const params = useGetTheParameters(formModel.value, 'ecs')

    // 3. 调用API
    await corporateOrderApi(params)
    ElMessage.success('发起开通成功')
    handleCancel()
  } catch (error: any) {
    console.error(error)
  }
}
</script>

<style scoped>
/* 镜像搜索框样式 - 防止校验时变红 */
.image-search-input :deep(.el-input__wrapper) {
  border-color: #dcdfe6 !important;
  box-shadow: 0 0 0 1px #dcdfe6 inset !important;
}

.image-search-input :deep(.el-input__wrapper):hover {
  border-color: #c0c4cc !important;
  box-shadow: 0 0 0 1px #c0c4cc inset !important;
}

.image-search-input :deep(.el-input__wrapper.is-focus) {
  border-color: #409eff !important;
  box-shadow: 0 0 0 1px #409eff inset !important;
}

/* 通用的form label加粗样式 */
.corporate-products​ :deep(.el-form-item__label) {
  font-weight: bold;
}
.corporate-products​ :deep(.table-main) {
  border: 1px solid #e4e7ed;
  box-shadow: none;
}

/* EIP配置样式 */
.eip-config {
  margin-bottom: 16px;
}

.eip-config :deep(.el-checkbox) {
  font-size: 14px;
}

.eip-config :deep(.el-checkbox__label) {
  font-weight: normal;
  color: #606266;
}

/* 展开按钮容器样式 */
.expand-container {
  display: flex;
  align-items: center;
  position: relative;
}
/* 图标样式 */
.expand-icon {
  transition: transform 0.3s ease;
  font-size: 14px;
}

.expand-icon.rotated {
  transform: rotate(180deg);
}

/* 价格面板样式 */
.price-panel {
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  background: #ffffff;
  border-top: 1px solid #e4e7ed;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  padding: 0 10px;

  .con {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.price-section {
  padding: 16px;
  border-bottom: 1px solid #f0f2f5;
}

.price-info {
  display: block;
}

.discount-info {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.label {
  font-size: 14px;
  color: #606266;
  margin-right: 8px;
}

.discount-price {
  font-size: 24px;
  font-weight: bold;
  color: #ff6b35;
  margin-right: 8px;
}

.discount-badge {
  font-size: 12px;
  color: #ff6b35;
  background: #fff2e8;
  padding: 2px 6px;
  border-radius: 4px;
}

.original-info {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.original-label {
  font-size: 12px;
  color: #909399;
  margin-right: 8px;
}

.original-price {
  font-size: 12px;
  color: #909399;
  text-decoration: line-through;
  margin-right: 8px;
}

.detail-link {
  font-size: 12px;
  padding: 0;
  height: auto;
}

.warning-text {
  font-size: 11px;
  color: #f56c6c;
  line-height: 1.4;
}

.action-section {
  padding: 12px 16px;
  display: flex;
  gap: 8px;
  align-self: end;
}

.cancel-btn {
  flex: 1;
  height: 36px;
  font-size: 14px;
  color: #606266;
  border-color: #dcdfe6;
}

.cart-btn {
  flex: 1;
  height: 36px;
  font-size: 14px;
}

.order-btn {
  flex: 1;
  height: 36px;
  font-size: 14px;
}
</style>
