<template>
  <div class="slPhrases">
    <el-button v-if="visibleBtn" type="primary" @click="openListDialog" :icon="DocumentCopy" link>
      常用语
    </el-button>

    <!-- 常用语列表弹窗 -->
    <el-dialog
      width="600px"
      v-model="listDialogVisible"
      title="常用语"
      :close-on-click-modal="false"
    >
      <div class="phrasePlus">
        <el-button type="primary" @click="openAddEditDialog()" :icon="EditPen" link>添加</el-button>
      </div>
      <div class="mt10 phraseList">
        <template v-for="(phrase, index) in phrasesList" :key="index">
          <div class="phraseItem flx-justify-between">
            <div
              @click="phraseClick(phrase)"
              class="ml10 pl3 phrasesTxt break-word"
              :class="{ 'phrasesTxt-first': index === 0 }"
            >
              {{ phrase }}
            </div>
            <div class="phrasesBtns">
              <el-button type="warning" @click="openAddEditDialog(phrase, index)" :icon="Edit" link>
              </el-button>
              <el-button type="danger" @click="removePhrase(index)" :icon="Delete" link>
              </el-button>
            </div>
          </div>
        </template>
      </div>

      <!-- <template #footer>
        <el-button @click="listDialogVisible = false">关闭</el-button>
      </template> -->
    </el-dialog>
    <!-- 添加/修改常用语弹窗 -->
    <el-dialog
      width="500px"
      v-model="addEditDialogVisible"
      :show-close="false"
      :close-on-click-modal="false"
      top="250px"
    >
      <el-input
        v-model="currentPhrase"
        type="textarea"
        show-word-limit
        maxlength="200"
        :rows="4"
        placeholder="请输入常用语"
      />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="addEditDialogVisible = false">取 消</el-button>
          <sl-button type="primary" :api-function="savePhrase">保 存</sl-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="tsx" setup>
import { getPropertyConfigApi, submitPropertyConfigApi } from '@/api/modules/computingPowerMap'
import { useVModel } from '@vueuse/core'
import { Delete, EditPen, DocumentCopy, Edit } from '@element-plus/icons-vue'
import { ref } from 'vue'
import SlMessage from '@/components/base/SlMessage'

type PropsType = {
  phrasesVlaue?: string
  visibleBtn?: boolean
}
const props = withDefaults(defineProps<PropsType>(), {
  phrasesVlaue: '',
  visibleBtn: true,
})

const emit = defineEmits<{
  (e: 'update:phrasesVlaue', value: string): void
}>()

const phrasesVlaueRef = useVModel(props, 'phrasesVlaue', emit)
const phrasesList = ref<string[]>([])
const currentPhrase = ref<string>('') // 用于添加或修改常用语
const listDialogVisible = ref<boolean>(false) // 列表弹窗可见性
const addEditDialogVisible = ref<boolean>(false) // 添加/修改弹窗可见性
const isEditing = ref<boolean>(false) // 标识当前是添加还是修改
const editingIndex = ref<number | null>(null)

const getInitData = async () => {
  if (phrasesList.value.length) return null
  const { entity } = await getPropertyConfigApi({
    type: 'PHRASES',
  })
  phrasesList.value = entity?.configJson ? JSON.parse(entity.configJson) : []
}

const openListDialog = async () => {
  await getInitData()
  listDialogVisible.value = true
}

const openAddEditDialog = (phrase?: string, index?: number) => {
  if (phrase && index !== undefined) {
    currentPhrase.value = phrase
    editingIndex.value = index
    isEditing.value = true
  } else {
    currentPhrase.value = ''
    editingIndex.value = null
    isEditing.value = false
  }
  addEditDialogVisible.value = true
}

const savePhrase = async () => {
  if (!currentPhrase.value.trim()) {
    SlMessage.warning('请输入有效的常用语')
    return
  }

  if (isEditing.value) {
    if (editingIndex.value !== null) {
      phrasesList.value[editingIndex.value] = currentPhrase.value.trim()
    }
  } else {
    phrasesList.value.push(currentPhrase.value.trim())
  }

  await submitPropertyConfigApi({
    type: 'PHRASES',
    configJson: JSON.stringify(phrasesList.value),
  })

  SlMessage.success(isEditing.value ? '修改成功' : '添加成功')
  addEditDialogVisible.value = false
  currentPhrase.value = ''
  editingIndex.value = null
  await getInitData()
}

const removePhrase = async (index: number) => {
  await ElMessageBox.confirm('确定要删除该常用语吗？', '确认删除', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
  })

  phrasesList.value.splice(index, 1)
  await submitPropertyConfigApi({
    type: 'PHRASES',
    configJson: JSON.stringify(phrasesList.value),
  })
  SlMessage.success('删除成功')
}

const phraseClick = (phrase: string) => {
  phrasesVlaueRef.value = phrase
  listDialogVisible.value = false
}

defineExpose({
  phrasesVlaueRef,

  openListDialog,
})
</script>

<style lang="scss" scoped>
.slPhrases {
  display: inline-block;
}
.phraseList {
  width: 100%;

  .phraseItem {
    .phrasesTxt {
      flex: 1;
      border: 1px solid #ccc;
      border-top: none;
      font-size: 16px;
      line-height: 40px;
      overflow: hidden;
      &-first {
        border-top: 1px solid #ccc !important;
      }
    }
  }
}
.phrasePlus {
  padding-right: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
