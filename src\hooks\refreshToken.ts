import { refreshToken } from '@/api/modules'
import { useUserStore } from '@/stores/modules/user'
import { useKeepAliveStore } from '@/stores/modules/keepAlive'
import { useAuthStore } from '@/stores/modules/auth'
export const useRefreshToken = () => {
  const keepAliveStore = useKeepAliveStore()
  const authStore = useAuthStore()
  const userStore = useUserStore()
  const refresh = async () => {
    try {
      const { entity } = await refreshToken()
      userStore.setUser(entity)
      userStore.setToken(entity.token)
      await authStore.getAuthMenuList()
      keepAliveStore.setKeepAliveName([])
    } catch (e) {
      console.error(e)
      throw new Error('刷新token失败')
    }
  }
  return {
    refresh,
  }
}
