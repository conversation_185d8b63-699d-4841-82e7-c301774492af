// import SlLayout from '@/views/SlLayout.vue'
import type { RouteRecordRaw } from 'vue-router'

const approvalCenterRouter: RouteRecordRaw[] = [
  {
    path: '/workOrder',
    component: () => import('@/views/approvalCenter/workOrder/index.vue'),
    name: 'workOrder',
    meta: {
      title: '开通工单管理',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/orderApproval',
    component: () => import('@/views/approvalCenter/workOrder/orderApproval/index.vue'),
    name: 'orderApproval',
    meta: {
      title: '开通工单审批',
      icon: 'icon-gongdan',
      activeMenu: 'workOrder',
    },
  },
  {
    path: '/recycleWorkOrder',
    component: () => import('@/views/approvalCenter/recyclingWorkOrders/index.vue'),
    name: 'recycleWorkOrder',
    meta: {
      title: '回收工单管理',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/recyclingPage',
    component: () =>
      import('@/views/approvalCenter/recyclingWorkOrders/approvalDetailsPage/index.vue'),
    name: 'recyclingPage',
    meta: {
      title: '回收审批',
      icon: 'icon-gongdan',
      activeMenu: 'recycleWorkOrder',
    },
  },
  {
    path: '/changeWorkOrder',
    component: () => import('@/views/approvalCenter/changeWorkOrders/index.vue'),
    name: 'changeWorkOrder',
    meta: {
      title: '变更工单管理',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/changePage',
    component: () =>
      import('@/views/approvalCenter/changeWorkOrders/approvalDetailsPage/index.vue'),
    name: 'changePage',
    meta: {
      title: '变更审批',
      icon: 'icon-gongdan',
      activeMenu: 'changeWorkOrder',
    },
  },
]

export default approvalCenterRouter
