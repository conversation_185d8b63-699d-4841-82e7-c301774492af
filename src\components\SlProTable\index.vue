<template>
  <!-- 表头搜索框 -->
  <div v-if="$slots.search" class="table-search-slot">
    <slot name="search"> </slot>
  </div>
  <!-- 查询表单 -->
  <SlSearchForm
    v-if="searchColumns.length && isShowSearch"
    :search="_search"
    :reset="_reset"
    :columns="searchColumns"
    :search-param="searchParam"
    :search-col="searchCol"
    :serach-form-props="searchFormProps"
  />

  <!-- 表格主体 -->
  <div class="card table-main">
    <!-- 表格头部 操作按钮 -->
    <div v-if="$slots.tableHeader" class="table-header">
      <slot
        name="tableHeader"
        :selected-list="selectedList"
        :selected-list-ids="selectedListIds"
        :is-selected="isSelected"
      />
    </div>

    <el-table
      ref="tableRef"
      v-bind="$attrs"
      :id="uuid"
      :data="processTableData || []"
      :border="border"
      :row-key="rowKey"
      @selection-change="selectionChange"
      @current-change="currentChange"
      v-loading="loading"
    >
      <!-- 默认插槽 -->
      <slot />
      <template v-for="item in tableColumns" :key="item">
        <!-- selection || radio || index || expand || sort -->
        <el-table-column
          v-if="item.type && columnTypes.includes(item.type)"
          v-bind="item"
          :align="item.align ?? 'center'"
          :reserve-selection="item.type == 'selection'"
          :class-name="item.className"
        >
          <template #default="scope">
            <!-- expand -->
            <template v-if="item.type == 'expand'">
              <component :is="item.render" v-bind="scope" v-if="item.render" />
              <slot v-else :name="item.type" v-bind="scope" />
            </template>
            <!-- radio -->
            <el-radio v-if="item.type == 'radio'" v-model="radio" :label="scope.row[rowKey]">
              <i></i>
            </el-radio>
            <!-- sort -->
            <el-tag v-if="item.type == 'sort'" class="move">
              <el-icon> <DCaret /></el-icon>
            </el-tag>
          </template>
        </el-table-column>
        <!-- other -->
        <TableColumn v-else :column="item">
          <template v-for="slot in slotKeys" :key="slot" #[slot]="scope">
            <slot :name="slot" v-bind="scope" />
          </template>
        </TableColumn>
      </template>
      <!-- 插入表格最后一行之后的插槽 -->
      <template #append>
        <slot name="append" />
      </template>
      <!-- 无数据 -->
      <template #empty>
        <div class="table-empty">
          <slot name="empty">
            <div>暂无数据</div>
          </slot>
        </div>
      </template>
    </el-table>
    <!-- 分页组件 -->
    <slot name="pagination">
      <Pagination
        v-if="pagination && isShowPagination"
        :pageable="pageable"
        :handle-size-change="handleSizeChange"
        :handle-current-change="handleCurrentChange"
      />
    </slot>
  </div>
</template>

<script setup lang="tsx" name="SlProTable">
import { useSelection } from '@/hooks/useSelection'
import { useTable } from '@/hooks/useTable'
import { ElTable, useId } from 'element-plus'
import { DCaret } from '@element-plus/icons-vue'
import { computed, onMounted, provide, reactive, ref, unref, useSlots, watch } from 'vue'
import type { BreakPoint, ColumnProps, TypeProps } from './interface'
import TableColumn from './components/TableColumn'
import Pagination from './components/Pagination.vue'
import SlSearchForm from '@/components/SlSearchForm/index.vue'

export interface ProTableProps {
  columns: ColumnProps[] // 列配置项  ==> 必传
  data?: any[] // 静态 table data 数据，若存在则不会使用 requestApi 返回的 data ==> 非必传
  requestApi?: (params: any) => Promise<any> // 请求表格数据的 api ==> 非必传
  requestAuto?: boolean // 是否自动执行请求 api ==> 非必传（默认为true）
  requestError?: (params: any) => void // 表格 api 请求错误监听 ==> 非必传
  dataCallback?: (data: any) => any // 返回数据的回调函数，可以对数据进行处理 ==> 非必传
  pagination?: boolean // 是否需要分页组件 ==> 非必传（默认为true）
  isShowPagination?: boolean // 是否展示分页组件 ===> 非必传，默认为true
  initParam?: any // 初始化请求参数 ==> 非必传（默认为{}）
  rowKey?: string // 行数据的 Key，用来优化 Table 的渲染，当表格数据多选时，所指定的 id ==> 非必传（默认为 id）
  border?: boolean // 是否带有纵向边框 ==> 非必传（默认为true）
  isShowSearch?: boolean // 是否显示搜索 ==> 非必传（默认为true）
  currentChange?: (currentRow: any, oldCurrentRow: any) => void // 当表格的当前行发生变化的时候会触发该事件
  searchCol?: number | Record<BreakPoint, number> // 表格搜索项 每列占比配置 ==> 非必传 { xs: 1, sm: 2, md: 2, lg: 3, xl: 4 }
  searchFormProps?: any // 表格搜索项配置 ==> 非必传
}

// 接受父组件参数，配置默认值
const props = withDefaults(defineProps<ProTableProps>(), {
  columns: () => [],
  requestAuto: true,
  pagination: true,
  isShowPagination: true,
  initParam: {},
  rowKey: 'id',
  border: true,
  isShowSearch: true,
  searchCol: () => ({ xs: 1, sm: 2, md: 2, lg: 3, xl: 3 }),
})

// table 实例
const tableRef = ref<InstanceType<typeof ElTable>>()

// 接收 columns 并设置为响应式
const tableColumns = reactive<ColumnProps[]>(props.columns)

// 生成组件唯一id
const uuid = useId()

// column 列类型
const columnTypes: TypeProps[] = ['selection', 'radio', 'index', 'expand', 'sort']

// 获取插槽
const slots = useSlots()
const slotKeys = ref<string[]>([])

// 单选值
const radio = ref<string | number | boolean | undefined>('')

// 表格多选 Hooks
const { selectionChange, selectedList, selectedListIds, isSelected } = useSelection(props.rowKey)

// 表格操作 Hooks
const {
  tableData,
  pageable,
  searchParam,
  searchInitParam,
  loading,
  getTableList,
  search,
  reset,
  handleSizeChange,
  handleCurrentChange,
} = useTable(
  props.requestApi,
  props.initParam,
  props.pagination,
  props.dataCallback,
  props.requestError,
)

// 清空选中数据列表
const clearSelection = () => tableRef.value!.clearSelection()

// 处理表格数据
const processTableData = computed(() => {
  if (!props.data) return tableData.value
  if (!props.pagination) return props.data
  return props.data.slice(
    (pageable.value.pageNum - 1) * pageable.value.pageSize,
    pageable.value.pageSize * pageable.value.pageNum,
  )
})

// 扁平化 columns
const flatColumns = computed(() => flatColumnsFunc(tableColumns))

// 监听页面 initParam 改化，重新获取表格数据
watch(
  () => props.initParam,
  (newValue) => getTableList(newValue),
  { deep: true },
)

// 扁平化 columns 的方法
const flatColumnsFunc = (columns: ColumnProps[], flatArr: ColumnProps[] = []) => {
  columns.forEach(async (col) => {
    if (col._children?.length) flatArr.push(...flatColumnsFunc(col._children))
    flatArr.push(col)

    // column 添加默认 isShow  && isFilterEnum 属性值
    col.isShow = col.isShow ?? true
    col.isFilterEnum = col.isFilterEnum ?? true

    // 设置 enumMap
    await setEnumMap(col)
  })
  return flatArr.filter((item) => !item._children?.length)
}

// 定义 enumMap 存储 enum 值（避免异步请求无法格式化单元格内容 || 无法填充搜索下拉选择）
const enumMap = ref(new Map<string, { [key: string]: any }[]>())
const setEnumMap = async ({ prop, enum: enumValue }: ColumnProps) => {
  if (!enumValue) return
  // 如果当前 enumMap 存在相同的值 return
  if (
    enumMap.value.has(prop!) &&
    (typeof enumValue === 'function' || enumMap.value.get(prop!) === enumValue)
  )
    return

  // 当前 enum 为静态数据，则直接存储到 enumMap
  if (typeof enumValue !== 'function') return enumMap.value.set(prop!, unref(enumValue!))

  // 为了防止接口执行慢，而存储慢，导致重复请求，所以预先存储为[]，接口返回后再二次存储
  enumMap.value.set(prop!, [])

  // 当前 enum 为后台数据需要请求数据，则调用该请求接口，并存储到 enumMap
  const { entity } = await enumValue()
  enumMap.value.set(prop!, entity)
}

// 注入 enumMap
provide('enumMap', enumMap)
// 定义 emit 事件
const emit = defineEmits<{
  search: []
  reset: []
  dragSort: [{ newIndex?: number; oldIndex?: number }]
}>()

const _search = () => {
  search()
  emit('search')
}

const _reset = () => {
  reset()
  emit('reset')
}

// 过滤需要搜索的配置项 && 排序
const searchColumns = computed(() => {
  return flatColumns.value
    ?.filter((item) => {
      if (item.search?.el || item.search?.render) {
        item.search.checked = item.search.checked ?? false
        return true
      }
      return false
    })
    .sort((a, b) => {
      if (a.search!.order === undefined && b.search!.order === undefined) return 0 // 都缺少 search!.order
      if (a.search!.order === undefined) return 1 // a 缺少 search!.order，放在后面
      if (b.search!.order === undefined) return -1 // b 缺少 search!.order，放在后面
      return a.search!.order - b.search!.order // 正常比较
    })
})

// 初始化表格数据
onMounted(() => {
  slotKeys.value = Object.keys(slots)
  if (props.requestAuto) getTableList()
  if (props.data) pageable.value.total = props.data.length
})

// 暴露给父组件的参数和方法 (外部需要什么，都可以从这里暴露出去)
defineExpose({
  element: tableRef, // 表格实例
  tableData: processTableData, // 表格数据
  radio, // 单选
  pageable, // 分页
  searchParam, // 搜索参数
  searchInitParam, // 搜索参数初始值
  isSelected, // 是否有选中项
  selectedList, // 选中项列表
  selectedListIds, // 选中项 id 列表

  // 下面为 function
  getTableList, // 获取表格数据
  search, // 搜索
  reset, // 重置
  handleSizeChange, // 分页 size 改变
  handleCurrentChange, // 分页 current 改变
  clearSelection, // 清空选中项
  // enumMap, // 枚举值
})
</script>

<style lang="scss" scoped></style>
