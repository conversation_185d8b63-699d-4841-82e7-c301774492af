<template>
  <div>
    <sl-form
      class="goods-info-form"
      size="small"
      ref="slFormRef"
      :options="goodsInfoOptions"
      :model-value="formModel"
      label-position="top"
    >
      <template #evsSlot="{ form, item }">
        <el-form-item
          class="evs-item"
          v-for="(evs, evsIndex) in form[item.key]"
          :key="evsIndex"
          :prop="item.key + '.' + evsIndex"
          :rules="evsRules"
        >
          <div class="evs-item-content">
            <el-select
              style="flex: 1"
              clearable
              :disabled="item.disabled"
              v-model="form[item.key][evsIndex][0]"
            >
              <el-option
                :key="option.value"
                v-for="option in item.options"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
            <el-input-number
              :disabled="item.disabled"
              v-bind="item.props"
              v-model="form[item.key][evsIndex][1]"
              style="margin: 0 4px; min-width: 90px"
            />
            <span>GB</span>
          </div>
        </el-form-item>
      </template>
      <template #isMountEcsSlot="{ form, item }">
        <div style="flex-grow: 0.12">
          <el-switch v-model="form[item.key]" active-value="1" inactive-value="0" />
        </div>
        <div class="sle" style="width: 120px">
          <el-input disabled :value="form[item.ecsName] || ''"></el-input>
        </div>
      </template>
    </sl-form>
  </div>
</template>
<script setup lang="ts">
import { reactive, ref } from 'vue'

import { useGlobalDicStore } from '@/stores/modules/dic'
import slForm from '@/components/form/SlForm.vue'
import { type IEvsModel, useEvsModel } from '../model'

const globalDic = useGlobalDicStore()
const { getDic } = globalDic

const props = defineProps<{
  goods: IEvsModel
}>()
const formModel = reactive(Object.assign(useEvsModel(), props.goods))
const slFormRef = ref()

const validateevsTypeevsSize = (rule: any, value: any, callback: any) => {
  let error = ''
  if (!value[0] && !value[1]) {
    error = '请选择数据盘类型并输入数据盘大小'
  } else if (!value[0] && value[1]) {
    error = '请选择数据盘类型'
  } else if (value[0] && !value[1]) {
    error = '请输入数据盘大小'
  }
  error ? callback(new Error(error)) : callback()
}

const evsRules = [
  {
    validator: validateevsTypeevsSize,
    trigger: 'change',
  },
]

const goodsInfoOptions = reactive([
  {
    gutter: 40,
    style: 'margin:0;padding:0;padding-top: 18px;',
    groupItems: [
      {
        label: '数据盘',
        type: 'slot',
        slotName: 'evsSlot',
        key: 'evs',
        options: getDic('evs'),
        span: 24,
        required: true,
        props: {
          min: 20,
          max: 2048,
          step: 1,
        },
      },
      {
        label: '申请时长',
        type: 'select',
        key: 'time',
        span: 24,
        options: getDic('time'),
        rules: [{ required: true, message: '请选择申请时长', trigger: ['blur', 'change'] }],
      },
    ],
  },
])

const submitForm = async () => {
  const goods = props.goods
  Object.assign(goods, formModel)
}
const validateForm = async () => {
  const res = await slFormRef.value.validate()
  return res
}
defineExpose({
  submitForm,
  validateForm,
})
</script>
<style scoped>
.goods-del-btn {
  position: absolute;
  top: -7px;
  right: -6px;
  z-index: 10;
  color: var(--el-color-danger);
  font-size: 1.2rem;
  cursor: pointer;
  display: none;
}
.goods-info-form:hover .goods-del-btn {
  display: block;
}

.evs-item {
  width: 100%;
  margin-bottom: 15px;
  .evs-item-content {
    width: 100%;
    display: flex;
    align-items: center;
  }
  &:last-child {
    margin-bottom: 0;
  }
  .evs-icons {
    display: flex;
    align-items: center;
    margin-left: 10px;
    .el-icon {
      cursor: pointer;
    }
  }
}
</style>
