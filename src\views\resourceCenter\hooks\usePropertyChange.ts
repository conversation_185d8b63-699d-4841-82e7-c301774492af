import { propertyChangeUpdate, propertyChangeList } from '@/api/modules/resourecenter'
import SlMessage from '@/components/base/SlMessage'
import { reactive, onUnmounted, type Reactive, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import eventBus from '@/utils/eventBus'
import { useBaseModel, type IBaseModel, type IPropsListItem } from './usePropertyChangeModels'
import { changeWorkOrderDetail } from '@/api/modules/resourecenter'

type GoodsType = 'ecs' | 'gcs' | 'obs' | 'slb' | 'evs' | 'nat' | 'eip' | 'base' | 'mysql' | 'redis'

export interface IGoodsItem<T> {
  id: number
  goodsType: GoodsType
  orderJson: T
  ref?: Reactive<any>
}

export interface ICartsModel {
  ecsPropertyList: IPropsListItem<'ecs'>[]
  mysqlPropertyList: IPropsListItem<'mysql'>[]
  redisPropertyList: IPropsListItem<'redis'>[]
  gcsPropertyList: IPropsListItem<'gcs'>[]
  obsPropertyList: IPropsListItem<'obs'>[]
  slbPropertyList: IPropsListItem<'slb'>[]
  evsPropertyList: IPropsListItem<'evs'>[]
  natPropertyList: IPropsListItem<'nat'>[]
  eipPropertyList: IPropsListItem<'eip'>[]
  basePropertyList: IBaseModel[]
}
type CartListKey = keyof Omit<ICartsModel, 'basePropertyList'>

const transform = {
  resourceType: {
    ecs: '云主机',
    mysql: '云数据库',
    redis: '云中间件',
    gcs: 'GPU云主机',
    obs: '对象存储',
    slb: '负载均衡',
    evs: '云硬盘',
    nat: 'NAT网关',
    eip: '弹性公网',
  },
  changeType: {
    instance_spec_change: '实例规格变更',
    storage_expand: '存储扩容',
    bandwidth_expand: '带宽扩容',
    delay: '延期',
  },
}

export async function updateRecord(
  cartsModel: ICartsModel,
  showMessage: boolean = false,
  refresh: boolean = false,
): Promise<boolean> {
  const keys = [
    'ecsPropertyList',
    'mysqlPropertyList',
    'redisPropertyList',
    'evsPropertyList',
    'gcsPropertyList',
    'obsPropertyList',
    'slbPropertyList',
    'natPropertyList',
    'eipPropertyList',
    'basePropertyList',
  ] as const
  const params: any = {}
  let isEmpty = true
  keys.forEach((key) => {
    if (cartsModel[key].length) {
      if (key !== 'basePropertyList') {
        isEmpty = false
      }
      params[key] = cartsModel[key].map((ele) => ({
        ...ele,
        ref: null,
      }))
    }
  })
  if (isEmpty) {
    params.basePropertyList = []
  }
  try {
    console.log(11)

    await propertyChangeUpdate(params)
    eventBus.emit('propertyChange:updateCount')
    refresh && eventBus.emit('propertyChange:refresh')
    showMessage && SlMessage.success('暂存成功')
    return true
  } catch (error) {
    eventBus.emit('propertyChange:refresh')
    console.error(error)
    return false
  }
}

function transformData(data: any) {
  data.forEach((item: any) => {
    item.resourceTypeCn =
      transform.resourceType[item.resourceType as keyof typeof transform.resourceType]
    item.props.forEach((prop: any) => {
      prop.changeTypeCn = transform.changeType[prop.changeType as keyof typeof transform.changeType]
      if (prop.changeType === 'storage_expand' && Array.isArray(prop.after) && !prop.after.length) {
        prop.after = JSON.parse(JSON.stringify(prop.before))
      }
      if (prop.changeType === 'bandwidth_expand') {
        prop.before = Number((String(prop.before) || '').replace('Mbps', ''))
        prop.after = Number(prop.after)
      }
      if (prop.changeType === 'instance_spec_change') {
        if (!prop.after.length) {
          prop.after = []
          if (prop.resourceType === 'slb') {
            prop.after = ''
          }
        }
      }
    })
  })
}

function assignPropertyList<T extends keyof ICartsModel>(target: ICartsModel, source: any, key: T) {
  if (source[key]?.length) {
    target[key] = source[key]
    transformData(target[key])
  }
}
function setCartModelFromList(entity: ICartsModel, cartsModel: ICartsModel) {
  const keys: CartListKey[] = [
    'ecsPropertyList',
    'mysqlPropertyList',
    'redisPropertyList',
    'evsPropertyList',
    'gcsPropertyList',
    'obsPropertyList',
    'slbPropertyList',
    'natPropertyList',
    'eipPropertyList',
  ]
  keys.forEach((key) => {
    assignPropertyList(cartsModel, entity, key)
  })
  if (entity.basePropertyList?.length) {
    Object.assign(cartsModel.basePropertyList[0], entity.basePropertyList[0])
  }
  nextTick(() => {
    cartsModel.basePropertyList[0].ref?.clearValidate()
  })
}
function setCartModelFromDetail(entity: any, cartsModel: ICartsModel) {
  Object.assign(cartsModel.basePropertyList[0], {
    busiSystemId: entity.businessSystemId,
    orderTitle: entity.orderTitle,
    busiDepartLeaderId: entity.businessDepartLeaderId,
    levelThreeLeaderId: entity.levelThreeLeaderId,
    orderDesc: entity.orderDesc,
    manufacturer: entity.manufacturer,
    manufacturerContacts: entity.manufacturerContacts,
    manufacturerMobile: entity.manufacturerMobile,
    moduleId: entity.moduleId,
    applyUserName: entity.bureauUserName,
    department: entity.departmentName,
    moduleName: entity.moduleName,
    busiDepartLeaderLabel: entity.businessDepartLeaderName,
    levelThreeLeaderLabel: entity.levelThreeLeaderName,
    busiSystemName: entity.businessSystemName,
    files: entity.resourceApplyFiles.map((e: any) => {
      return {
        orderFileType: e.orderFileType,
        id: e.fileId,
        ...e,
      }
    }),
  })
  if (entity.ecsChangeList?.length) {
    entity.ecsChangeList.forEach((e: any) => {
      cartsModel.ecsPropertyList.push({
        resourceDetailId: e.resourceDetailId,
        deviceName: e.vmName,
        resourceType: 'ecs',
        resourceTypeCn: transform.resourceType.ecs,
        domainCode: e.domainCode,
        cloudPlatform: e.domainName,
        azId: e.azId,
        regionId: e.regionId,
        resourcePoolName: e.regionName,
        props: e.changeType.map((changeType: keyof typeof transform.changeType) => {
          const props: any = {
            resourceDetailId: e.resourceDetailId,
            resourceType: e.productType,
            templateCode: e.templateCode,
            resourceTypeCn:
              transform.resourceType[e.productType as keyof typeof transform.resourceType],
            changeType: changeType,
            changeTypeCn: transform.changeType[changeType],
            eipId: e.eipModel?.eipId,
          }
          if (changeType === 'instance_spec_change') {
            props.before = e.vmSpec
            props.after = [e.changeFlavorType, e.changeFlavorName]
            props.templateCode = e.templateCode
          }
          if (changeType === 'storage_expand') {
            props.before = e.evsModelList?.map((evs: any) => {
              const [type, size] = evs.spec.split(' ')
              return [type, Number(size.replace('GB', ''))]
            })
            props.after = e.evsModelList?.map((evs: any) => {
              return [evs.spec.split(' ')[0], evs.changeVolumeSize, evs.evsId]
            })
          }
          if (changeType === 'bandwidth_expand') {
            props.before = Number(e.eipModel?.eipBandwidth || 0)
            props.after = Number(e.eipModel?.changeBandwidth || 0)
            props.eipId = e.eipModel?.eipId
          }
          if (changeType === 'delay') {
            props.before = e.expireTime
            props.after = e.changeTime
          }
          return props
        }),
      })
    })
  }
  if (entity.mysqlChangeList?.length) {
    entity.mysqlChangeList.forEach((e: any) => {
      cartsModel.mysqlPropertyList.push({
        resourceDetailId: e.resourceDetailId,
        deviceName: e.vmName,
        resourceType: 'mysql',
        resourceTypeCn: transform.resourceType.mysql,
        domainCode: e.domainCode,
        cloudPlatform: e.domainName,
        azId: e.azId,
        regionId: e.regionId,
        resourcePoolName: e.regionName,
        props: e.changeType.map((changeType: keyof typeof transform.changeType) => {
          const props: any = {
            resourceDetailId: e.resourceDetailId,
            resourceType: e.productType,
            templateCode: e.templateCode,
            resourceTypeCn:
              transform.resourceType[e.productType as keyof typeof transform.resourceType],
            changeType: changeType,
            changeTypeCn: transform.changeType[changeType],
            eipId: e.eipModel?.eipId,
          }
          if (changeType === 'instance_spec_change') {
            props.before = e.vmSpec
            props.after = [e.changeFlavorType, e.changeFlavorName]
            props.templateCode = e.templateCode
          }
          if (changeType === 'storage_expand') {
            props.before = e.evsModelList?.map((evs: any) => {
              const [type, size] = evs.spec.split(' ')
              return [type, Number(size.replace('GB', ''))]
            })
            props.after = e.evsModelList?.map((evs: any) => {
              return [evs.spec.split(' ')[0], evs.changeVolumeSize, evs.evsId]
            })
          }
          if (changeType === 'bandwidth_expand') {
            props.before = Number(e.eipModel?.eipBandwidth || 0)
            props.after = Number(e.eipModel?.changeBandwidth || 0)
            props.eipId = e.eipModel?.eipId
          }
          if (changeType === 'delay') {
            props.before = e.expireTime
            props.after = e.changeTime
          }
          return props
        }),
      })
    })
  }
  if (entity.redisChangeList?.length) {
    entity.redisChangeList.forEach((e: any) => {
      cartsModel.redisPropertyList.push({
        resourceDetailId: e.resourceDetailId,
        deviceName: e.vmName,
        resourceType: 'redis',
        resourceTypeCn: transform.resourceType.redis,
        domainCode: e.domainCode,
        cloudPlatform: e.domainName,
        azId: e.azId,
        regionId: e.regionId,
        resourcePoolName: e.regionName,
        props: e.changeType.map((changeType: keyof typeof transform.changeType) => {
          const props: any = {
            resourceDetailId: e.resourceDetailId,
            resourceType: e.productType,
            templateCode: e.templateCode,
            resourceTypeCn:
              transform.resourceType[e.productType as keyof typeof transform.resourceType],
            changeType: changeType,
            changeTypeCn: transform.changeType[changeType],
            eipId: e.eipModel?.eipId,
          }
          if (changeType === 'instance_spec_change') {
            props.before = e.vmSpec
            props.after = [e.changeFlavorType, e.changeFlavorName]
            props.templateCode = e.templateCode
          }
          if (changeType === 'storage_expand') {
            props.before = e.evsModelList?.map((evs: any) => {
              const [type, size] = evs.spec.split(' ')
              return [type, Number(size.replace('GB', ''))]
            })
            props.after = e.evsModelList?.map((evs: any) => {
              return [evs.spec.split(' ')[0], evs.changeVolumeSize, evs.evsId]
            })
          }
          if (changeType === 'bandwidth_expand') {
            props.before = Number(e.eipModel?.eipBandwidth || 0)
            props.after = Number(e.eipModel?.changeBandwidth || 0)
            props.eipId = e.eipModel?.eipId
          }
          if (changeType === 'delay') {
            props.before = e.expireTime
            props.after = e.changeTime
          }
          return props
        }),
      })
    })
  }
  if (entity.gcsChangeList?.length) {
    entity.gcsChangeList.forEach((e: any) => {
      cartsModel.gcsPropertyList.push({
        resourceDetailId: e.resourceDetailId,
        deviceName: e.vmName,
        resourceType: 'gcs',
        resourceTypeCn: transform.resourceType.gcs,
        cloudPlatform: e.domainName,
        resourcePoolName: e.regionName,
        props: e.changeType.map((changeType: keyof typeof transform.changeType) => {
          const props: any = {
            resourceDetailId: e.resourceDetailId,
            resourceType: e.productType,
            resourceTypeCn:
              transform.resourceType[e.productType as keyof typeof transform.resourceType],
            changeType: changeType,
            changeTypeCn: transform.changeType[changeType],
            eipId: e.eipModel?.eipId,
          }
          if (changeType === 'instance_spec_change') {
            props.before = e.vmSpec
            props.after = [e.changeFlavorType, e.changeFlavorName]
          }
          if (changeType === 'storage_expand') {
            props.before = e.evsModelList?.map((evs: any) => {
              const [type, size] = evs.spec.split(' ')
              return [type, Number(size.replace('GB', ''))]
            })
            props.after = e.evsModelList?.map((evs: any) => {
              return [evs.spec.split(' ')[0], evs.changeVolumeSize, evs.evsId]
            })
          }
          if (changeType === 'bandwidth_expand') {
            props.before = Number(e.eipModel?.eipBandwidth || 0)
            props.after = Number(e.eipModel?.changeBandwidth || 0)
            props.eipId = e.eipModel?.eipId
          }
          if (changeType === 'delay') {
            props.before = e.expireTime
            props.after = e.changeTime
          }
          return props
        }),
      })
    })
  }
  if (entity.slbChangeList?.length) {
    entity.slbChangeList.forEach((e: any) => {
      cartsModel.slbPropertyList.push({
        resourceDetailId: e.resourceDetailId,
        deviceName: e.slbName,
        resourceType: 'slb',
        resourceTypeCn: transform.resourceType.slb,
        cloudPlatform: e.domainName,
        resourcePoolName: e.regionName,
        props: e.changeType.map((changeType: keyof typeof transform.changeType) => {
          const props: any = {
            resourceDetailId: e.resourceDetailId,
            resourceType: e.productType,
            resourceTypeCn:
              transform.resourceType[e.productType as keyof typeof transform.resourceType],
            changeType: changeType,
            changeTypeCn: transform.changeType[changeType],
            eipId: e.eipModel?.eipId,
          }
          if (changeType === 'instance_spec_change') {
            props.before = e.slbSpec
            props.after = e.changeFlavorName
          }
          if (changeType === 'bandwidth_expand') {
            props.before = Number(e.eipModel?.eipBandwidth || 0)
            props.after = Number(e.eipModel?.changeBandwidth || 0)
          }
          if (changeType === 'delay') {
            props.before = e.expireTime
            props.after = e.changeTime
          }
          return props
        }),
      })
    })
  }
  if (entity.natChangeList?.length) {
    entity.natChangeList.forEach((e: any) => {
      cartsModel.natPropertyList.push({
        resourceDetailId: e.resourceDetailId,
        deviceName: e.natName,
        resourceType: 'nat',
        resourceTypeCn: transform.resourceType.nat,
        cloudPlatform: e.domainName,
        resourcePoolName: e.regionName,
        props: e.changeType.map((changeType: keyof typeof transform.changeType) => {
          const props: any = {
            resourceDetailId: e.resourceDetailId,
            resourceType: e.productType,
            resourceTypeCn:
              transform.resourceType[e.productType as keyof typeof transform.resourceType],
            changeType: changeType,
            changeTypeCn: transform.changeType[changeType],
            eipId: e.eipModel?.eipId,
          }
          if (changeType === 'delay') {
            props.before = e.expireTime
            props.after = e.changeTime
          }
          return props
        }),
      })
    })
  }
  if (entity.evsChangeList?.length) {
    entity.evsChangeList.forEach((e: any) => {
      cartsModel.evsPropertyList.push({
        resourceDetailId: e.resourceDetailId,
        deviceName: '',
        resourceType: 'evs',
        resourceTypeCn: transform.resourceType.evs,
        cloudPlatform: e.domainName,
        resourcePoolName: e.regionName,
        props: e.changeType.map((changeType: keyof typeof transform.changeType) => {
          const props: any = {
            resourceDetailId: e.resourceDetailId,
            resourceType: e.productType,
            resourceTypeCn:
              transform.resourceType[e.productType as keyof typeof transform.resourceType],
            changeType: changeType,
            changeTypeCn: transform.changeType[changeType],
            eipId: e.eipModel?.eipId,
          }
          if (changeType === 'storage_expand') {
            const [type, size] = e.spec.split(' ')
            props.before = [[type, Number(size.replace('GB', ''))]]
            props.after = [[type, e.changeVolumeSize]]
          }
          if (changeType === 'delay') {
            props.before = e.expireTime
            props.after = e.changeTime
          }
          return props
        }),
      })
    })
  }
  if (entity.eipChangeList?.length) {
    entity.eipChangeList.forEach((e: any) => {
      cartsModel.eipPropertyList.push({
        resourceDetailId: e.resourceDetailId,
        deviceName: e.eipName,
        resourceType: 'eip',
        resourceTypeCn: transform.resourceType.eip,
        cloudPlatform: e.domainName,
        resourcePoolName: e.regionName,
        props: e.changeType.map((changeType: keyof typeof transform.changeType) => {
          const props: any = {
            resourceDetailId: e.resourceDetailId,
            resourceType: e.productType,
            resourceTypeCn:
              transform.resourceType[e.productType as keyof typeof transform.resourceType],
            changeType: changeType,
            changeTypeCn: transform.changeType[changeType],
            eipId: e.eipModel?.eipId,
          }
          if (changeType === 'bandwidth_expand') {
            props.before = Number(e.eipModel?.eipBandwidth || 0)
            props.after = Number(e.eipModel?.changeBandwidth || 0)
          }
          if (changeType === 'delay') {
            props.before = e.expireTime
            props.after = e.changeTime
          }
          return props
        }),
      })
    })
  }
  if (entity.obsChangeList?.length) {
    entity.obsChangeList.forEach((e: any) => {
      cartsModel.obsPropertyList.push({
        resourceDetailId: e.resourceDetailId,
        deviceName: e.obsName,
        resourceType: 'obs',
        resourceTypeCn: transform.resourceType.obs,
        cloudPlatform: e.domainName,
        resourcePoolName: e.regionName,
        props: e.changeType.map((changeType: keyof typeof transform.changeType) => {
          const props: any = {
            resourceDetailId: e.resourceDetailId,
            resourceType: e.productType,
            resourceTypeCn:
              transform.resourceType[e.productType as keyof typeof transform.resourceType],
            changeType: changeType,
            changeTypeCn: transform.changeType[changeType],
            eipId: e.eipModel?.eipId,
          }
          if (changeType === 'storage_expand') {
            const [type, size] = e.obsSpec.split(' ')
            props.before = [[type, Number(size.replace('GB', ''))]]
            props.after = [[type, e.changeVolumeSize]]
          }
          if (changeType === 'delay') {
            props.before = e.expireTime
            props.after = e.changeTime
          }
          return props
        }),
      })
    })
  }
}
async function loadDatafromOrderEcho(cartsModel: ICartsModel, orderId: string): Promise<void> {
  const { entity } = await changeWorkOrderDetail({ workOrderId: orderId, aggregation: true })
  if (!entity) return

  setCartModelFromDetail(entity, cartsModel)
}
async function loadDatafrompropertyChange(cartsModel: ICartsModel): Promise<void> {
  const { entity } = await propertyChangeList()
  if (!entity) return
  setCartModelFromList(entity, cartsModel)
}
export function usePropertyChange() {
  const route = useRoute()
  const cartsModel: Reactive<ICartsModel> = reactive({
    ecsPropertyList: [],
    evsPropertyList: [],
    gcsPropertyList: [],
    obsPropertyList: [],
    slbPropertyList: [],
    natPropertyList: [],
    eipPropertyList: [],
    mysqlPropertyList: [],
    redisPropertyList: [],
    basePropertyList: [useBaseModel()],
  })
  if (route.query.orderId) {
    // 从工单回显数据
    loadDatafromOrderEcho(cartsModel, route.query.orderId as string)
  } else {
    // 从暂存回显数据
    loadDatafrompropertyChange(cartsModel)
  }

  eventBus.on('propertyChange:updateGoods', handleUpdateGoods as any)
  eventBus.on('propertyChange:refresh', handleRefresh)

  onUnmounted(() => {
    eventBus.off('propertyChange:updateGoods')
    eventBus.off('propertyChange:refresh')
  })
  function handleRefresh() {
    eventBus.emit('propertyChange:updateCount')
    if (route.query.orderId) {
      loadDatafromOrderEcho(cartsModel, route.query.orderId as string)
    } else {
      loadDatafrompropertyChange(cartsModel)
    }
  }

  function handleUpdateGoods({
    showMessage = false,
    refresh = false,
  }: {
    showMessage?: boolean
    refresh?: boolean
  } = {}) {
    updateRecord(cartsModel, showMessage, refresh)
  }
  return cartsModel
}
