// stores/user.js
import { defineStore } from 'pinia'
import type { Entity } from '../../types/apis/login'
export const useUserStore = defineStore('user', {
  state: () => ({
    username: '',
    token: '',
    cmpToken: '',
    userInfo: {} as Entity,
  }),
  getters: {
    getUserInfo: (state) => state.userInfo,
    // 是否租户
    isTenant(state) {
      return state.userInfo.oacRoles.some((element) => {
        if (element.roleCode === 'user_task') {
          return true
        }
      })
    },
    isOperate(state) {
      return state.userInfo.oacRoles.some((element) => {
        if (element.roleCode === 'operation_administrator') {
          return true
        }
      })
    },
    isMaintenanceOrLeader(state) {
      return state.userInfo.oacRoles.some((element) => {
        if (element.roleCode === 'maintenance_administrator') {
          return true
        }
      })
    },
    // 判断是否为业务部门领导
    isBusinessDepartLeader(state) {
      return state.userInfo.oacRoles.some((element) => {
        if (element.roleCode === 'business_depart_leader') {
          return true
        }
      })
    },
    // 判断是否为云中心领导
    isCloudLeader(state) {
      return state.userInfo.oacRoles.some((element) => {
        if (element.roleCode === 'cloud_leader') {
          return true
        }
      })
    },
  },
  actions: {
    setUser(userInfo: Entity) {
      this.userInfo = userInfo
    },
    setToken(token: string) {
      this.token = token
    },
    setCmpToken(cmpToken: string) {
      this.cmpToken = cmpToken
    },
    logout() {
      this.userInfo = {} as Entity
    },
  },
  persist: {
    key: 'user',
    storage: localStorage,
  },
})
