<template>
  <div class="resourcerequest">
    <sl-page-header
      :title="`${formModel.operationName}${formModel.goodsName}`"
      :icon="{
        Vnode: Platform,
        color: '#0052D9',
        size: '20px',
      }"
      :show-back="true"
      :back="{
        title: '返回列表',
        function: goBack,
      }"
    >
    </sl-page-header>
    <div class="resourcerequestbox">
      <div class="onebox">
        <div class="oneboxleft">
          <sl-form
            ref="slFormRef"
            show-block-title
            :options="formOptions"
            :model-value="formModel"
            style="overflow: hidden"
          >
          </sl-form>
          <div class="sl-form-container">
            <div class="sl-form">
              <div class="group-con bg-fff sl-layout">
                <div class="block-title-container pb20" style="font-size: 18px">
                  <div class="block-title">可用区</div>
                </div>

                <div class="btn op" style="margin-top: 8px; margin-bottom: 8px">
                  <el-button @click="addAzItem" type="primary"> 新增可用区 </el-button>
                  <el-button @click="batchDeleteItem" type="primary"> 批量删除 </el-button>
                </div>
                <div class="card table-main resourceAddTable">
                  <el-table
                    ref="tableRef"
                    style="min-height: 300px"
                    :data="azTableData"
                    hidden-table-header
                    border
                    @selection-change="handleSelectionChange"
                  >
                    <el-table-column type="selection" width="55" />
                    <template v-for="item in tableColumns" :key="item">
                      <el-table-column :label="item.label" :prop="item.prop">
                        <template #default="scope">
                          <el-input v-model="scope.row[item.prop]" placeholder="请输入" />
                        </template>
                      </el-table-column>
                    </template>
                    <el-table-column label="操作">
                      <template #default="scope">
                        <el-button @click="deleteItem(scope.row)" type="primary" link>
                          删除
                        </el-button>
                      </template>
                    </el-table-column>
                    <template #append>
                      <slot name="append" />
                    </template>
                    <!-- 无数据 -->
                    <template #empty>
                      <div class="table-empty">
                        <slot name="empty">
                          <div>暂无数据</div>
                        </slot>
                      </div>
                    </template>
                  </el-table>
                  <!-- 分页组件 -->
                </div>
              </div>
            </div>
          </div>
          <!--          </el-scrollbar>-->
        </div>
      </div>
      <div class="onefooter">
        <el-button @click="goBack">取消</el-button>
        <el-button v-if="!readOnly" type="primary" @click="submitfromdata">提交申请</el-button>
      </div>
    </div>
  </div>
</template>
<script setup lang="tsx">
import { Platform } from '@element-plus/icons-vue'
import slForm from '@/components/form/SlForm.vue'
import { reactive, ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import SlMessage from '@/components/base/SlMessage'
import { getVpcDetail } from '@/api/modules/resourecenter'
import { addResourcePoolApi, getPlatformListApi } from '@/api/modules/configCenter'
import useModel from './model'
import { ElTable } from 'element-plus'
// import Pagination from '@/components/SlProTable/components/Pagination.vue'
// import { V1CLOUD } from '@/api/config/servicePort'
// import type { UploadFile } from 'element-plus'
import { validateNameNoSpecialChars } from '@/utils/validate'

const model = useModel()
const formModel = reactive<{ [key: string]: any }>({})
Object.assign(formModel, model)

const slFormRef = ref()
const router = useRouter()
const route = useRoute()
const vpcId = route.query.vpcId as string
const readOnly = !!vpcId

const cloudOptionsList = ref([])

async function getPlatformList() {
  const res: any = await getPlatformListApi({})
  cloudOptionsList.value = res.entity
  console.log(cloudOptionsList.value)
}
getPlatformList()

const childNetFormModel = reactive([
  { azCode: '', subnetName: '', startIp: '', netmask: '', azLabel: '', ref: null },
])

//删除可用区信息
const deleteItem = async (row: any) => {
  console.log(row.id)
  azTableData.value = azTableData.value.filter((item: any) => item.id !== row.id)
}
//删除可用区信息
const batchDeleteItem = async () => {
  azTableData.value = azTableData.value.filter(
    (item: any) => !selectedIdArr.value.includes(item.id),
  )
}
//添加可用区信息
function addAzItem() {
  azTableData.value.push({
    id: generateUUID32Compat(),
    name: '',
    code: '',
    resourceId: '',
    description: '',
  })
}
interface User {
  date: string
  name: string
  address: string
}
const multipleSelection = ref<User[]>([])
const selectedIdArr = ref<User[]>([])
const handleSelectionChange = (val: User[]) => {
  multipleSelection.value = val
  selectedIdArr.value = multipleSelection.value.map((item: any) => item.id)
  console.log(val)
}

// 类型定义
interface azType {
  id: string
  name: string
  code: string
  resourceId: string
  description: string
}
// 表格数据
const azTableData = ref<azType[]>([
  {
    id: generateUUID32Compat(),
    name: '',
    code: '',
    resourceId: '',
    description: '',
  },
])

const tableColumns = ref<any>([
  {
    label: '可用区名称',
    prop: 'name',
  },
  {
    label: '可用区编码',
    prop: 'code',
  },
  {
    label: '可用区ID',
    prop: 'resourceId',
  },
  {
    label: '可用区描述',
    prop: 'description',
  },
])

if (vpcId) {
  displayBackDetail(vpcId)
}

async function displayBackDetail(vpcId: string) {
  const { entity } = await getVpcDetail({ vpcId })
  const obj = JSON.parse(entity)
  Object.assign(formModel, obj.formModel)
  childNetFormModel.length = 0
  childNetFormModel.push(...obj.childNetFormModel)
}

const formOptions = reactive([
  {
    style: 'margin-top: 0;margin-bottom: 0;',
    groupName: '基础信息',
    groupItems: [
      {
        label: '云平台',
        type: 'select',
        key: 'type',
        options: cloudOptionsList,
        labelField: 'domainName',
        valueField: 'domainCode',
        onChange(option: any, formModel: any) {
          console.log(option)
          formModel.platformName = option.domainName
          formModel.cloudPlatformId = option.domainCode
        },
        span: 12,
        rules: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }],
      },
      {
        label: '资源池名称',
        type: 'input',
        key: 'name',
        props: {
          rows: 1,
          maxlength: 100,
        },
        span: 12,
        rules: [
          { required: true, message: '请输入资源池名称', trigger: ['blur', 'change'] },
          { validator: validateNameNoSpecialChars, trigger: ['blur'] },
        ],
      },
      {
        label: '资源池代码',
        type: 'input',
        key: 'code',
        props: {
          rows: 1,
          maxlength: 200,
        },
        span: 12,
        rules: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }],
      },
      {
        label: '资源池ID',
        type: 'input',
        key: 'resourceCode',
        props: {
          rows: 1,
          maxlength: 200,
        },
        span: 12,
        rules: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }],
      },
      {
        label: '连接地址',
        type: 'input',
        key: 'endpoint',
        props: {
          rows: 1,
          maxlength: 100,
        },
        span: 12,
        rules: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }],
      },
      {
        label: '城市编码',
        type: 'input',
        key: 'cityCode',
        props: {
          rows: 1,
          maxlength: 100,
        },
        span: 12,
      },
      {
        label: '城市名',
        type: 'input',
        key: 'cityName',
        props: {
          rows: 1,
          maxlength: 100,
        },
        span: 12,
      },
      {
        label: '领域类型',
        type: 'select',
        key: 'realmType',
        options: [
          {
            label: 'IAAS',
            value: 'IAAS',
          },
          {
            label: 'PAAS',
            value: 'PAAS',
          },
        ],
        span: 12,
        rules: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
      },
      {
        label: '描述',
        type: 'input',
        key: 'description',
        props: {
          type: 'textarea',
          rows: 4,
          maxlength: 250,
          showWordLimit: true,
        },
        span: 24,
      },
    ],
  },
])
function generateUUID32Compat() {
  // 生成 32 位十六进制字符串（无连字符）
  return 'xxxxxxxxxxxx4xxxyxxxxxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0 // 0-15 的随机整数
    const v = c === 'x' ? r : (r & 0x3) | 0x8 // 指定第 13 位为 4，第 17 位为 8/9/a/b
    return v.toString(16)
  })
}
// 定义类型接口
interface UserData {
  userInfo?: {
    id: string
    userName: string
  }
}

// 安全获取数据
const userStorage = localStorage.getItem('user')
let userInfo: UserData['userInfo'] | null = null

if (userStorage) {
  try {
    const parsedData: UserData = JSON.parse(userStorage)
    userInfo = parsedData.userInfo || null
  } catch (error) {
    console.error('JSON 解析失败:', error)
  }
}

// let userInfo:any = JSON.parse(localStorage.getItem('user')).userInfo || null
const doSubmit = async () => {
  const validate = await slFormRef.value.validate()
  if (!validate) return

  let arr: any = []
  azTableData.value.forEach((item: any) => {
    arr.push({
      name: item.name,
      labelName: item.name,
      code: item.code,
      resourceId: item.resourceId,
      description: item.description,
    })
  })
  const submitdata = {
    name: formModel.name,
    code: formModel.code,
    description: formModel.description,
    platformName: formModel.platformName,
    cloudPlatformId: formModel.cloudPlatformId,
    type: formModel.type,
    endpoint: formModel.endpoint,
    cityCode: formModel.cityCode,
    cityName: formModel.cityName,
    resourceCode: formModel.resourceCode,
    realmType: formModel.realmType || '1',
    createdBy: (userInfo && userInfo.id) || '',
    createdByName: (userInfo && userInfo.userName) || '',
    azList: arr,
  }
  // : await updateResourcePoolApi(submitdata)
  try {
    const entity = await addResourcePoolApi(submitdata)
    if (entity.code == 200) {
      SlMessage.success('提交成功')
      goBack()
    } else {
      SlMessage.error('提交失败')
    }
  } finally {
  }
}
const submitfromdata = async () => {
  if (!slFormRef.value) return
  doSubmit()
}
function goBack() {
  router.go(-1)
}
</script>
<style scoped>
.scroll-view {
  height: calc(100vh - 188px);
}

.resourcerequest {
  .resourcerequestbox {
    .onebox {
      display: flex;

      .oneboxleft {
        flex-grow: 3;
        margin: 8px 0 8px 0;
      }
    }

    .onefooter {
      height: 48px;
      margin: 0 8px;
      background: #ffffff;
      border-radius: 8px 8px 8px 8px;
      display: flex;
      align-items: center;
      justify-content: right;
      padding: 0 14px;
    }
  }
}

.block-title {
  flex: 1;
}
.block-title-container::before {
  content: '';
  width: 4px;
  background-color: var(--el-color-primary);
  margin-right: 10px;
  height: 0.78em;
}

.block-title-container {
  display: flex;
  padding: 10px 0;
  font-weight: bold;
  font-size: 18px;
  color: #000c1f;
  display: flex;
  align-items: center;
}
</style>
<style>
.card.table-main.resourceAddTable
  .el-table
  .el-table__inner-wrapper
  .el-scrollbar
  .el-scrollbar__view {
  vertical-align: baseline !important;
}
</style>
