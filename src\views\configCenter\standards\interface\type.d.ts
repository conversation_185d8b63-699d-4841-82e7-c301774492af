/**
 * @name 规格管理接口类型
 */
export interface flavorType {
  id?: number // 角色ID
  domainCode?: string //
  regionId?: string //
  serviceName?: string // 规格名称
  specCode?: string //
  specInfo?: string //
  flavorType?: string // flavorType
  vcpus?: number // 租户id
  ram?: number // 镜像类型ID
  disk?: string // 镜像类型ID
  shares?: string // 镜像类型ID
  description?: string // 镜像类型ID
  vgpus?: string // 镜像类型ID
  cateGoryCode?: string // 镜像大小
  cateGoryName?: string // 最小cpu
  flavorList?: Array // 最小cpu
  flavorResourceType?: string // 资源类型
}

export interface flavorListType extends flavorType {
  createdUserName?: string // 创建人
  createTime?: string // 创建时间，格式为 'YYYY-MM-DD HH:mm:ss'
  [key: string]: any
}
