/* Menu */
declare namespace Menu {
  interface MenuOptions {
    path: string
    name: string
    component?: string | (() => Promise<unknown>)
    redirect?: string
    meta: MetaProps
    children?: MenuOptions[]
  }
  interface MetaProps {
    icon: string
    title: string
    activeMenu?: string
    isLink?: string
    isHide: boolean
    isFull: boolean
    isAffix: boolean
    isDisabled: boolean
    isKeepAlive: boolean
  }
}

/* FileType */
declare namespace File {
  type ImageMimeType =
    | 'image/apng'
    | 'image/bmp'
    | 'image/gif'
    | 'image/jpeg'
    | 'image/pjpeg'
    | 'image/png'
    | 'image/svg+xml'
    | 'image/tiff'
    | 'image/webp'
    | 'image/x-icon'

  type ExcelMimeType =
    | 'application/vnd.ms-excel'
    | 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'

  type PdfMimeType = 'application/pdf'

  type TextMimeType = 'text/plain'

  type MimeType = ImageMimeType | ExcelMimeType | PdfMimeType | TextMimeType
  type MimeTypeMap = {
    [key in MimeType]: FileType
  }
}
declare enum FileType {
  // 图片类型
  IMAGE = 'image/*',
  JPG = '.jpg',
  JPEG = '.jpeg',
  PNG = '.png',
  GIF = '.gif',
  BMP = '.bmp',
  TIFF = '.tiff',
  SVG = '.svg',

  // 文档类型
  PDF = 'application/pdf',
  WORD = 'application/msword',
  DOCX = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  XLS = 'application/vnd.ms-excel',
  XLSX = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  PPT = 'application/vnd.ms-powerpoint',
  PPTX = 'application/vnd.openxmlformats-officedocument.presentationml.presentation',

  // 音频和视频类型
  AUDIO = 'audio/*',
  VIDEO = 'video/*',
  MP3 = '.mp3',
  WAV = '.wav',
  MP4 = '.mp4',
  AVI = '.avi',
  MOV = '.mov',
  WEBM = '.webm',

  // 压缩文件类型
  ZIP = 'application/zip',
  TAR = 'application/x-tar',
  GZIP = 'application/gzip',
  RAR = '.rar',
  SEVEN_Z = '.7z',

  // 文本文件类型
  TEXT = 'text/*',
  TXT = '.txt',
  CSV = '.csv',
  XML = '.xml',
  JSON = '.json',
}

/**
 * @name 表单数据的整体结构，这里使用了一个通用的定义
 */
declare interface FormDataType {
  [key: string]: any // 表单键值对，键名由 FieldConfig 中的 model 定义，值的类型可以根据实际表单字段决定
}
