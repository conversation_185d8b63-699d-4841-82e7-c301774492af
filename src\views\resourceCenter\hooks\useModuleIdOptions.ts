import { watchEffect, ref } from 'vue'
import { belongingBusiness, busisystemDetail } from '@/api/modules/resourecenter'
export default function useModuleIdOptions(formModel: any) {
  const moduleIdOptions = ref<any>([]) //所属业务模块
  watchEffect(() => {
    if (formModel.busiSystemId) {
      getBusisystemDetail(formModel.busiSystemId, formModel)
      getmoduleIdOptions(formModel.busiSystemId, moduleIdOptions)
    } else {
      formModel.manufacturer = ''
      formModel.manufacturerContacts = ''
      formModel.manufacturerMobile = ''
      formModel.applyUserName = ''
      moduleIdOptions.value = []
    }
  })
  return {
    moduleIdOptions,
  }
}

// 获取所属业务模块
async function getmoduleIdOptions(businessSystemId: string, moduleIdOptions: any) {
  const { entity } = await belongingBusiness({ businessSystemId })
  if (entity && Array.isArray(entity)) {
    moduleIdOptions.value = entity.map((e: any) => ({
      value: e.id,
      label: e.moduleName,
      businessSystemId: e.businessSystemId,
      systemName: e.systemName,
    }))
  }
}
// 获取厂家负责人
async function getBusisystemDetail(id: string, formModel: any) {
  const { entity } = await busisystemDetail({ id })
  formModel.manufacturer = entity.manufacturer
  formModel.manufacturerContacts = entity.manufacturerContacts
  formModel.manufacturerMobile = entity.manufacturerMobile
  formModel.applyUserName = entity.applyUserName
  formModel.departmentName = entity.departmentName
  formModel.tenantId = entity.tenantId
  formModel.tenantName = entity.tenantName
}
