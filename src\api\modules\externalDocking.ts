import http from '@/api'
import { CLOUD, V1CLOUD } from '../config/servicePort'
/**
 * @name 云类型字典
 */
export const getPlatformTypesDicAPi = () =>
  http.get<FormDataType[]>(CLOUD + '/platform_types', {}, { RemoteUser: 'BusinessCenter' })

/**
 * @name 云平台->资源池字典
 */
export const getRegionsDicAPi = (platTypeId: number) =>
  http.post<FormDataType[]>(CLOUD + '/regions', { platTypeId }, { RemoteUser: 'BusinessCenter' })

/**
 * @name 资源容量概览
 */
export const getLatestAPi = (config: any) =>
  http.post<FormDataType>(CLOUD + '/latest', config, {
    RemoteUser: 'BusinessCenter',
    loading: true,
  })

/**
 * @name 云平台->所有资源池字典-分页
 */
export const getRegionsSelectAPi = (config: any) =>
  http.get<any>(V1CLOUD + '/region/regionsSelect', config)
// --------------------  --------------------
/**
 * @name 云平台->所有项目列表-分页
 */
export const getProjectListAPi = (config: any) =>
  http.get<FormDataType[]>(V1CLOUD + '/project/list', config)
// --------------------  --------------------
