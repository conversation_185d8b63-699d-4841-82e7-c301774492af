<template>
  <div class="item">
    <el-icon style="font-size: 18px">
      <Collection />
    </el-icon>
    <a
      class="operation-manual text"
      href="./protected-download/操作手册.docx"
      download="操作手册.docx"
    >
      操作手册
    </a>
  </div>
</template>
<script setup lang="ts">
import { Collection } from '@element-plus/icons-vue'
</script>
<style scoped lang="scss">
.operation-manual {
  outline: none;
  text-decoration: none;
  font-size: 14px;
  color: var(--el-text-primary);
}
.item {
  display: flex;
  color: var(--el-color-primary);
  align-items: center;
  justify-content: center;
  cursor: pointer;
  margin-right: 8px;
  color: var(--el-text-primary) !important;
  &:hover {
    color: var(--el-color-primary) !important;
  }
  .text {
    font-size: 14px;
    margin-left: 4px;
  }
}
</style>
