<template>
  <div :class="[ns.b(), ns.m(simple ? 'simple' : direction)]">
    <slot />
  </div>
</template>

<script lang="ts" setup>
import { getCurrentInstance, inject, provide, watch, ref } from 'vue'
import { CHANGE_EVENT } from 'element-plus'
import { useNamespace, useOrderedChildren } from 'element-plus'
import { stepsEmits, stepsProps } from './steps'

import type { StepItemState } from './item.vue'

defineOptions({
  name: 'SlBaseSteps',
})

const props = defineProps(stepsProps)
const emit = defineEmits(stepsEmits)

const ns = useNamespace('steps')
const {
  children: steps,
  addChild: addStep,
  removeChild: removeStep,
} = useOrderedChildren<StepItemState>(getCurrentInstance()!, 'SlBaseStep')

watch(steps, () => {
  steps.value.forEach((instance: StepItemState, index: number) => {
    instance.setIndex(index)
  })
})

provide('SlBaseSteps', { props, steps, addStep, removeStep })

const parent = inject('parentStep', { index: ref(-1) })

watch(
  () => parent.index.value,
  () => {
    steps.value.forEach((instance: StepItemState) => {
      instance.setOffset(parent.index.value + 1)
    })
  },
)

watch(
  () => props.active,
  (newVal: number | string, oldVal: number | string) => {
    emit(CHANGE_EVENT, newVal, oldVal)
  },
)
</script>
