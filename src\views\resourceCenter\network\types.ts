import type { Ref } from 'vue'

interface IBaseFormProvider {
  regionCode: string
  collectSubmits: ICollectSubmits
  planes?: string[]
  jsonStr?: string
  isCxc: boolean
  opType: 'createWithOrder' | 'addSubnet' | 'view' | 'create'
  uuids: string[]
  catalogueDomainCode: string
}
type ICollectSubmits = (fn: IFormRefs) => void

type IFormRefs = Ref<{ id: number; ref: any }[]>

interface ISubmitData {
  detail: string
  name: string
  networkType: string
  vlanId: string
  vlan: string
  subnets: {
    cidr: string
    ipVersion: string
    instanceId: string
    level2InstanceId: string
  }[]
}

export type { IBaseFormProvider, ICollectSubmits, IFormRefs, ISubmitData }
