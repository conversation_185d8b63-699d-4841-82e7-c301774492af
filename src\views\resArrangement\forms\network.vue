<template>
  <div class="resourcerequest">
    <div class="resourcerequestbox">
      <div class="onebox">
        <div class="oneboxleft">
          <sl-form
            ref="slFormRef"
            size="small"
            show-block-title
            label-position="top"
            :options="formOptions"
            :model-value="formModel"
            style="overflow: hidden"
          >
          </sl-form>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="tsx">
import slForm from '@/components/form/SlForm.vue'
import { reactive, ref } from 'vue'
import { useRoute } from 'vue-router'
import SlBlockTitle from '@/components/base/SlBlockTitle.vue'
import { useGlobalDicStore } from '@/stores/modules/dic'
import { CirclePlus, Delete } from '@element-plus/icons-vue'
import {
  useNetworkModel,
  type INetworkModel,
  type INetworkChildNetModel,
  useNetworkChildNetModel,
} from '@/views/resArrangement/model'
import { validateGoodsName } from '@/views/resourceCenter/utils'
import { uuid } from '@/utils'

const props = defineProps<{
  goods: INetworkModel
}>()

// 创建自定义深拷贝函数，排除ref属性
function deepCloneExcludeRef(obj: any): any {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }

  if (Array.isArray(obj)) {
    return obj.map((item) => deepCloneExcludeRef(item))
  }

  const cloned: any = {}
  for (const key in obj) {
    if (key !== 'ref' && obj.hasOwnProperty(key)) {
      cloned[key] = deepCloneExcludeRef(obj[key])
    }
  }
  return cloned
}

const formModel = reactive(Object.assign(useNetworkModel(), deepCloneExcludeRef(props.goods)))

const childNetFormModel: INetworkChildNetModel[] = formModel.childNet

const globalDic = useGlobalDicStore()

const slFormRef = ref()
const { getDic } = globalDic
const route = useRoute()
const orderId = route.query.orderId as string

const childNetFormOptions = function (model: INetworkChildNetModel) {
  console.log(model)
  if (model.ipVersion === 'ipv6') {
    return [
      {
        style: 'margin:0;padding:0;padding-top: 18px;',
        gutter: 40,
        groupItems: [
          {
            label: 'IPv6地址',
            type: 'input',
            key: 'cidr',
            rules: [{ required: true, message: '请输入IPv6地址', trigger: ['blur', 'change'] }],
            span: 24,
          },
        ],
      },
    ]
  }
  return [
    {
      style: 'margin:0;padding:0;padding-top: 18px;',
      gutter: 40,
      groupItems: [
        {
          label: 'IPv4地址',
          type: 'input',
          key: 'cidr',
          rules: [{ required: true, message: '请输入IPv4地址', trigger: ['blur', 'change'] }],
          span: 24,
        },
      ],
    },
  ]
}
const formOptions = reactive([
  {
    style: 'margin:0;padding:0;padding-top: 18px;',
    gutter: 0,
    hideBlockTitle: true,
    groupItems: [
      {
        label: '网络名称',
        type: 'input',
        key: 'instanceName',
        span: 24,
        props: {
          maxlength: 48,
          showWordLimit: true,
        },
        rules: [
          { required: true, message: '请输入网络名称', trigger: ['blur', 'change'] },
          { validator: validateGoodsName, trigger: ['blur', 'change'] },
        ],
      },
      {
        label: '网络平面',
        type: 'select',
        key: 'plane',
        span: 24,
        props: {
          select: {
            collapseTags: true,
            collapseTagsTooltip: true,
          },
        },
        options: getDic('plane'),
        rules: [{ required: true, message: '请选择网络平面', trigger: ['blur', 'change'] }],
      },
      {
        label: '网络类型',
        type: 'text',
        key: 'networkType',
        span: 24,
      },
      {
        span: 24,
        render() {
          return (
            <SlBlockTitle size={14} style="margin-bottom:20px" no-bar>
              子网信息
            </SlBlockTitle>
          )
        },
      },
      {
        span: 24,
        render() {
          return childNetFormModel.map((model, index) => (
            <el-row>
              <el-col span={23}>
                <sl-form
                  size="small"
                  key={model}
                  ref={(ref: any) => setChildNetRef(ref, model)}
                  options={childNetFormOptions(model)}
                  modelValue={model}
                ></sl-form>
              </el-col>
              <el-col
                style="display: flex;align-items: center !important;margin-bottom: 0px;justify-content: center;cursor: pointer;"
                span={1}
                onClick={() => removeChildNet(index)}
              >
                <el-icon color="red">
                  <Delete />
                </el-icon>
              </el-col>
            </el-row>
          ))
        },
      },
      {
        span: 24,
        render() {
          return childNetFormModel.length < 2 ? (
            <el-button
              onClick={() => addChildNet()}
              icon={<CirclePlus />}
              style="width:100%"
              type="primary"
              plain
            >
              增加子网
            </el-button>
          ) : null
        },
      },
    ],
  },
])
function addChildNet() {
  if (childNetFormModel.length && childNetFormModel[0].ipVersion === 'ipv4') {
    childNetFormModel.push(useNetworkChildNetModel('ipv6'))
  } else {
    childNetFormModel.push(useNetworkChildNetModel('ipv4'))
  }
}
function setChildNetRef(ref: any, model: any) {
  // vue3 先创建再销毁，所以需要判断
  if (ref) model.ref = ref
}
function removeChildNet(index: number) {
  childNetFormModel.splice(index, 1)
}

const doSubmit = async () => {
  const tempChildNetFormModel = childNetFormModel.map((item: any) => {
    return {
      ...item,
      uuid: item.uuid ? item.uuid : item.old ? '' : uuid(16),
    }
  })
  const couldSubmitChildNetFormModel = tempChildNetFormModel.filter((item: any) => !item.old)
  if (couldSubmitChildNetFormModel.length === 0) {
    return
  }
  const submitdata = {
    vpcName: formModel.instanceName,
    cidr: formModel.netRange,
    plane: formModel.plane,
    subnetDTOList: couldSubmitChildNetFormModel,
    detail: JSON.stringify({
      formModel: { ...formModel, operationName: '查看资源申请单' },
      childNetFormModel: tempChildNetFormModel.map((ele) => ({
        ...ele,
        ref: null,
      })),
      orderId,
    }),
  }
  return {
    submitdata,
    updateIps: childNetFormModel.map((item: any) => item.instanceId),
  }
}
const submit = async () => {
  if (!slFormRef.value) return
  const ChildNetPromises: Promise<any>[] = childNetFormModel.map((model: any) =>
    model.ref.validate(),
  )
  ChildNetPromises.push(slFormRef.value.validate())
  const validate = await Promise.all(ChildNetPromises)
  if (!(validate.length > 0 && validate.every(Boolean))) return
  return doSubmit()
}
const validateForm = async () => {
  const res = await slFormRef.value.validate()
  return res
}
const submitForm = async () => {
  const goods = props.goods
  Object.assign(goods, formModel)
}
defineExpose({
  validateForm,
  submit,
  submitForm,
})
</script>
<style scoped>
.scroll-view {
  height: calc(100vh - 188px);
}

.resourcerequest {
  .resourcerequestbox {
    .onebox {
      display: flex;

      .oneboxleft {
        flex-grow: 3;
        margin: 8px 0px 8px 0;
      }

      .oneboxright {
        flex-grow: 1;
        flex-basis: 0;
        background: #ffffff;
        margin: 8px 8px 8px 0;
        border-radius: 6px;
        min-width: 300px;
      }
    }

    .onefooter {
      height: 48px;
      margin: 0 8px;
      background: #ffffff;
      border-radius: 8px 8px 8px 8px;
      display: flex;
      align-items: center;
      justify-content: right;
      padding: 0 14px;
    }
  }
}
</style>
