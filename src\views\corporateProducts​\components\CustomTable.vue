<template>
  <div class="custom-table">
    <!-- 添加按钮 (如果配置了可添加) -->
    <div v-if="canAdd" class="table-header">
      <el-button
        type="primary"
        link
        @click="addRow"
        :disabled="tableData.length >= maxRows"
        class="add-button"
      >
        + {{ addButtonText }} ({{ tableData.length }} / {{ maxRows }})
      </el-button>
    </div>

    <!-- 表格 -->
    <el-table
      :data="tableData"
      :style="{ width: totalWidth + 2 + 'px' }"
      :show-header="true"
      v-if="tableData.length > 0"
    >
      <!-- 动态渲染列 -->
      <el-table-column
        v-for="column in columns"
        :key="column.prop"
        :prop="column.prop"
        :label="column.label"
        :width="column.width"
        :fixed="column.fixed"
        :align="column.align || 'center'"
      >
        <template #default="{ row, $index }">
          <component
            :is="column.render"
            v-if="column.render"
            :row="row"
            :column="column"
            :index="$index"
          />
          <!-- 输入框类型 -->
          <el-input
            v-else-if="column.type === 'input'"
            v-model="row[column.prop]"
            :placeholder="column.placeholder || '请输入'"
            @change="handleDataChange"
          />
          <!-- 选择器类型 -->
          <el-select
            v-else-if="column.type === 'select'"
            v-model="row[column.prop]"
            :placeholder="column.placeholder || '请选择'"
            v-bind="column?.props?.select || {}"
            @change="handleDataChange"
          >
            <el-option
              v-for="option in column.options"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>

          <!-- 数字输入框类型 -->
          <div v-else-if="column.type === 'number'" class="number-input">
            <el-input-number
              v-model="row[column.prop]"
              :min="column.min || 1"
              :max="column.max || 1000"
              @change="handleDataChange"
              :style="{ width: column.inputWidth || '120px' }"
            />
            <span v-if="column.unit" class="unit">{{ column.unit }}</span>
          </div>

          <!-- 操作列 -->
          <el-button
            v-else-if="column.type === 'action' && column.action === 'delete'"
            type="danger"
            link
            @click="removeRow($index)"
            class="remove-button"
          >
            <el-icon><Delete /></el-icon>
          </el-button>

          <!-- 普通文本 -->
          <span v-else>{{ row[column.prop] }}</span>
        </template>
      </el-table-column>
    </el-table>

    <!-- 空状态提示 -->
    <div v-else-if="showEmptyState" class="empty-state">
      <p>{{ emptyText }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed, onMounted, type VNode } from 'vue'
import { Delete } from '@element-plus/icons-vue'

interface TableColumn {
  props: any
  prop: string
  label: string
  width?: string | number
  type: 'select' | 'number' | 'text' | 'input' | 'action'
  placeholder?: string
  options?: Array<{ label: string; value: any }>
  min?: number
  max?: number
  unit?: string
  inputWidth?: string
  action?: 'delete'
  render?: () => VNode
  fixed?: 'left' | 'right'
  align?: 'left' | 'center' | 'right'
}

interface TableConfig {
  columns: TableColumn[]
  canAdd?: boolean
  maxRows?: number
  addButtonText?: string
  emptyText?: string
  showEmptyState?: boolean
  defaultRow?: Record<string, any>
  totalWidth?: number
}

const props = defineProps<{
  item: TableConfig & { key: string }
  form: any
}>()

const model = props.form

// 计算属性
const columns = computed(() => props.item.columns || [])
const canAdd = computed(() => props.item.canAdd ?? false)
const maxRows = computed(() => props.item.maxRows ?? 16)
const addButtonText = computed(() => props.item.addButtonText ?? '添加行')
const emptyText = computed(() => props.item.emptyText ?? '暂无数据')
const showEmptyState = computed(() => props.item.showEmptyState ?? true)
const defaultRow = computed(() => props.item.defaultRow || {})
// col width 求和
const totalWidth = computed(() =>
  props.item.totalWidth
    ? props.item.totalWidth
    : columns.value.reduce((acc, column) => acc + Number(column.width || 150), 0),
)

// 表格数据
const tableData = ref<Record<string, any>[]>(model[props.item.key] || [])

// 初始化数据
const initData = () => {
  // 如果有默认行配置，使用默认行；否则创建空对象
  const newRow = Object.keys(defaultRow.value).length > 0 ? { ...defaultRow.value } : {}

  // 如果没有默认行配置，根据列配置生成默认值
  if (Object.keys(newRow).length === 0) {
    columns.value.forEach((column) => {
      if (column.type === 'select' && column.options && column.options.length > 0) {
        newRow[column.prop] = column.options[0].value
      } else if (column.type === 'number') {
        newRow[column.prop] = column.min || 1
      } else if (column.type !== 'action') {
        newRow[column.prop] = ''
      }
    })
  }

  tableData.value = [newRow]
  model[props.item.key] = tableData.value
}

// 添加行
const addRow = () => {
  if (tableData.value.length < maxRows.value) {
    const newRow = Object.keys(defaultRow.value).length > 0 ? { ...defaultRow.value } : {}

    // 如果没有默认行配置，根据列配置生成默认值
    if (Object.keys(newRow).length === 0) {
      columns.value.forEach((column) => {
        if (column.type === 'select' && column.options && column.options.length > 0) {
          newRow[column.prop] = column.options[0].value
        } else if (column.type === 'number') {
          newRow[column.prop] = column.min || 1
        } else if (column.type !== 'action') {
          newRow[column.prop] = ''
        }
      })
    }

    tableData.value.push(newRow)
    handleDataChange()
  }
}

// 删除行
const removeRow = (index: number) => {
  tableData.value.splice(index, 1)
  handleDataChange()
}

// 数据变化处理
const handleDataChange = () => {
  model[props.item.key] = [...tableData.value]
}

// 监听外部数据变化
watch(
  () => model[props.item.key],
  (newVal) => {
    if (newVal && Array.isArray(newVal) && newVal.length > 0) {
      tableData.value = [...newVal]
    }
  },
  { deep: true },
)

// 组件挂载时初始化
onMounted(() => {
  if (
    !model[props.item.key] ||
    !Array.isArray(model[props.item.key]) ||
    model[props.item.key].length === 0
  ) {
    initData()
  } else {
    tableData.value = [...model[props.item.key]]
  }
})
</script>

<style scoped>
.custom-table {
  width: auto;
}

.table-header {
  margin-bottom: 12px;
}

.add-button {
  color: #409eff;
  font-size: 14px;
}

.add-button:hover {
  color: #66b1ff;
}

.add-button:disabled {
  color: #c0c4cc;
  cursor: not-allowed;
}

.remove-button {
  color: #f56c6c;
}

.remove-button:hover {
  color: #f78989;
}

.number-input {
  display: flex;
  align-items: center;
  gap: 8px;
}

.unit {
  color: #606266;
  font-size: 14px;
}

.empty-state {
  text-align: center;
  padding: 40px 0;
  color: #909399;
  background: #fafafa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

/* 表格样式调整 */
:deep(.el-table) {
  border: 1px solid #e4e7ed;
}

:deep(.el-table__header-wrapper) {
  background: #fafafa;
}

:deep(.el-table th) {
  background: #fafafa;
  font-weight: 600;
  color: #303133;
}

:deep(.el-table td) {
  padding: 8px 0;
}

:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-select) {
  width: 100%;
}
</style>
