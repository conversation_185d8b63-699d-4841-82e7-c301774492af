import { useUserStore } from '@/stores/modules/user'
import { reactive } from 'vue'

export default function useModel() {
  const userStore = useUserStore()
  const formModel = reactive({
    applicant: userStore.userInfo.userName,
    department: userStore.userInfo.sysDeptName,
    orderTitle: '',
    // @工单id
    id: '',
    // @业务系统id
    busiSystemId: '',
    // 局方负责人
    applyUserName: '',
    // @厂家
    manufacturer: '',
    // @厂家负责人
    manufacturerContacts: '',
    // @厂家电话
    manufacturerMobile: '',
    // @所属业务模块
    moduleId: '',
    // @二级部门领导
    busiDepartLeaderId: '',
    // @三级部门领导
    levelThreeLeaderId: '',
    // @资源申请说明
    orderDesc: '',
    // @资源上云说明书
    files: [],
  })

  return formModel
}
