<template>
  <div>
    <el-scrollbar class="scrollbar">
      <div class="menu">
        <div class="menu-inner">
          <sl-group-menu-item
            :active-index="activeIndex"
            :key="item.id"
            v-for="item in menuData"
            :menu-item="item"
            :router="router"
            @menuItemClick="handleMenuItemClick"
          />
        </div>
      </div>
    </el-scrollbar>
  </div>
</template>

<script setup lang="ts">
import SlGroupMenuItem from './SlGroupMenuItem.vue'
import { ref } from 'vue'

const props = defineProps({
  menuData: {
    type: Array<any>,
    required: true,
  },
  defaultActive: {
    type: String,
    required: false,
  },
  router: {
    type: Boolean,
    required: false,
    default: false,
  },
})
const activeIndex = ref(props.defaultActive)
const handleMenuItemClick = (index: string) => {
  console.log(1234)

  activeIndex.value = index
}
</script>

<style scoped>
.menu {
  width: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: #fff;
  height: 100vh;
  box-shadow: inset -1px 0 0 0 #eee;
  .menu-inner {
    padding: 16px;
    display: block;
    width: 100%;
    box-sizing: border-box;
  }
}
</style>
