export type goodsDetailsType = { id: number; [k: string]: any }
export type NetworkSubnet = {
  subnetId: string
  subnetName: string
  subnetIp: string
  ipAddress?: string[] | null[]
}
export type NetworkModel = {
  id: string
  name: string
  plane: string
  type?: string
  subnets: NetworkSubnet[]
  sort: number
}

export type FormDataStringArrayType = { [key: string]: string[] | null[] }

const useModelList = (getkeys: () => string[]) => {
  // 插入数组逻辑
  function insertAtIndexAndClear(array: any[], index: number, items: any[]) {
    // 清除当前索引的值，并插入多条数据
    array.splice(index, 1, ...items) // 删除 1 个元素并插入新元素
  }

  // 生成子数据
  function createObjectsArray(row: FormDataType): goodsDetailsType[] {
    const ids = row.ids
    const messages = row.messages ?? []
    const goodsNames = row.goodsNames ?? []
    const newRow = JSON.parse(JSON.stringify(row))

    const ipAddressMap = row.ipAddressMap ?? {}

    delete newRow.ipAddressMap
    delete newRow.planeNetworkModel
    delete newRow.ids
    delete newRow.id
    delete newRow.message
    delete newRow.messages
    delete newRow.goodsName
    delete newRow.goodsNames
    return ids.map((item: any, index: number) => ({
      ...newRow,
      id: item,
      openNum: 1,
      message: messages[index] ?? '',
      goodsName: goodsNames[index] ?? '',
      planeNetworkModel: assignIpToNetworkModel(row as any, ipAddressMap, index),
    }))
  }

  // 去重 计数
  function uniqueByPropertiesWithCustomKey<
    T extends { id: any; message?: string; planeNetworkModel?: any } & FormDataType,
  >(
    arr: T[],
    keys: (keyof T)[],
    countKey: string, // 自定义计数的键名
  ): (FormDataType & { ids: any[] })[] {
    const seen = new Map<string, FormDataType & { ids: any[] }>()

    arr.forEach((item) => {
      let values = keys.map((key) => item[key]).join('|') // 将多个属性值组合成一个字符串

      let ipAddressMap: FormDataStringArrayType = {}
      // 这里云主机 和GPU的参数不一样所以要加一个参数校验
      if (item?.planeNetworkModel && Array.isArray(item.planeNetworkModel)) {
        const { ipAddressMap: map, planeNetworkModel } = extractIpAddresses(
          item?.planeNetworkModel,
          item.idHash,
        )
        values += JSON.stringify(planeNetworkModel)
        ipAddressMap = map
      } else {
        values += JSON.stringify(item?.planeNetworkModel)
      }

      const id = item.id // 获取 id
      const message = item.message ?? '' //获取 message
      const goodsName = item.goodsName ?? '' //获取 goodsName
      delete item.id // 删除 id
      if (seen.has(values)) {
        // 如果已经存在，增加计数并添加 id
        const existingItem = seen.get(values)!
        existingItem[countKey] += 1
        existingItem.ids.push(id) // 添加到 ids

        Object.keys(ipAddressMap).forEach((key) => {
          if (!existingItem.ipAddressMap[key]) {
            existingItem.ipAddressMap[key] = []
          }
          existingItem.ipAddressMap[key].push(...ipAddressMap[key])
        })

        // 目前失败的不让分区了 所以这样写
        if (message) {
          existingItem.messages.push(message) // 添加到 errorMassages
        }
        if (goodsName) {
          existingItem.goodsNames.push(goodsName) // 添加到 goodsNames
        }
      } else {
        // 否则，添加到 Map 中并初始化计数和 ids
        seen.set(values, {
          ...(item as FormDataType),
          [countKey]: 1,
          ids: [id],
          messages: [message],
          goodsNames: [goodsName],
          ipAddressMap,
        })
      }
    })

    return Array.from(seen.values())
  }

  // 父级别的去重和计数函数
  function mergeParent(goods: any[]) {
    const newGoods = JSON.parse(JSON.stringify(goods))

    const arrall: any[] = []
    newGoods.map((row: any) => {
      arrall.push(...createObjectsArray(row))
    })

    return uniqueByPropertiesWithCustomKey(arrall, getkeys(), 'openNum')
  }

  /**
   * 提取网络模型中的IP地址
   * @param networkModels 网络模型数组
   * @returns IP地址数组
   */
  function extractIpAddresses(networkModels: NetworkModel[], idHash: string) {
    if (!networkModels || !networkModels.length) return { ipAddressMap: {}, planeNetworkModel: [] }

    const planeNetworkModel: NetworkModel[] = JSON.parse(JSON.stringify(networkModels))
    const ipAddressMap: FormDataStringArrayType = {}

    // 遍历所有网络模型
    planeNetworkModel.forEach((network) => {
      if (network.subnets && network.subnets.length) {
        // 遍历每个子网
        network.subnets.forEach((subnet) => {
          // 如果子网有IP地址，添加到结果中
          if (subnet.ipAddress && subnet.ipAddress.length) {
            ipAddressMap[idHash + subnet.subnetId] = subnet.ipAddress
            delete subnet.ipAddress
          }
        })
      }
    })

    return { ipAddressMap, planeNetworkModel }
  }

  /**
   * 为网络模型分配IP地址
   * @param networkModels 网络模型数组
   * @param ipAddress IP地址
   * @returns 更新后的网络模型数组
   */
  function assignIpToNetworkModel(
    row: { planeNetworkModel: NetworkModel[] | NetworkModel | null } & FormDataType,
    ipAddressMap: FormDataStringArrayType,
    index: number,
  ) {
    // 不为数组
    if (!row.planeNetworkModel || !Array.isArray(row.planeNetworkModel)) {
      return row.planeNetworkModel
    }

    if (!row.planeNetworkModel.length) return []

    if (!ipAddressMap || !Object.keys(ipAddressMap).length) return row.planeNetworkModel

    const clonedModels = JSON.parse(JSON.stringify(row.planeNetworkModel))
    const idHash = row.idHash

    clonedModels.forEach((network: NetworkModel) => {
      if (network.subnets && network.subnets.length) {
        network.subnets.forEach((subnet: NetworkSubnet) => {
          const subnetId = subnet.subnetId
          const key = idHash + subnetId

          if (ipAddressMap[key]) {
            let ip = ipAddressMap[key][index]
            subnet.ipAddress = ip ? [ip] : [null]
          } else {
            subnet.ipAddress = [null]
          }
        })
      }
    })

    return clonedModels
  }

  return {
    mergeParent, // 父级别的去重和计数函数
    insertAtIndexAndClear, // 插入数组逻辑
    uniqueByPropertiesWithCustomKey, // 去重 计数
    createObjectsArray, // 生成子数据
    extractIpAddresses, // 提取IP地址
  }
}

export default useModelList
