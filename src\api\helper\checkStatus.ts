/*
 * @LastEditTime: 2023-12-29 10:29:53
 * @FilePath: \4.0测试\src\api\helper\checkStatus.ts
 */
import SlMessage from '@/components/base/SlMessage'

/**
 * @description: 校验网络请求状态码
 * @param {string} status
 * @return void
 */
export const checkStatus = (status: string) => {
  switch (status) {
    case '400':
      SlMessage.error(' 参数列表错误（缺少，格式不匹配）!!')
      break
    case '401':
      SlMessage.error('登录失效！请您重新登录')
      break
    case '403':
      SlMessage.error('当前账号无权限访问！')
      break
    case '404':
      SlMessage.error('你所访问的资源不存在！')
      break
    case '405':
      SlMessage.error('请求方式错误！请您稍后重试')
      break
    case '408':
      SlMessage.error('请求超时！请您稍后重试')
      break
    case '409':
      SlMessage.error('资源冲突，或者资源被锁!')
      break
    case '415':
      SlMessage.error('不支持的数据，媒体类型!')
      break
    case '500':
      SlMessage.error('服务异常！')
      break
    case '501':
      SlMessage.error('接口未实现')
      break
    case '502':
      SlMessage.warning('网络波动！')
      break
    case '503':
      SlMessage.error('服务不可用！')
      break
    case '504':
      SlMessage.error('网关超时！')
      break
    case '601':
      SlMessage.error('系统警告！')
      break
    default:
      SlMessage.error('请求失败！')
  }
}

/**
 * @description: 校验网络请求状态码
 * @param {string} status
 * @return void
 */
const errorMessages: any = {}
export const checkCode = (data: any) => {
  const message = errorMessages[data.code as string] ?? data.msg ?? '"请求失败！"'
  SlMessage.error(message)
}
