<template>
  <div class="tool-bar-ri">
    <div class="header-icon">
      <!-- 后面加功能再加组件 -->
      <!-- <SearchMenu id="searchMenu" /> -->
      <template v-if="vifShoppingCats"> <ShoppingCart id="shoppingCart" />| </template>
      <template v-if="vifRecycleBin"> <RecycleBin id="recycleBin" />| </template>
      <template v-if="vifPropertyChange"> <PropertyChange id="propertyChange" />| </template>
      <OperationManual />
      |
      <Fullscreen id="fullscreen" />
    </div>
    <Avatar />
  </div>
</template>

<script setup lang="ts">
import Fullscreen from './components/Fullscreen.vue'
// import AssemblySize from './components/AssemblySize.vue'
import Avatar from './components/Avatar.vue'
import ShoppingCart from './components/shoppingCart.vue'
import RecycleBin from './components/recycleBin.vue'
import PropertyChange from './components/propertyChange.vue'
import { computed } from 'vue'
import { useAuthStore } from '@/stores/modules/auth'
import OperationManual from './components/operationManual.vue'
const authStore = useAuthStore()
const vifShoppingCats = computed(() => !!authStore.authButtonListGet.shoppingCarts)
const vifRecycleBin = computed(() => !!authStore.authButtonListGet.recycleBin)
const vifPropertyChange = computed(() => !!authStore.authButtonListGet.propertyChange)
</script>

<style scoped lang="scss">
.tool-bar-ri {
  display: flex;
  align-items: center;
  justify-content: center;
  .header-icon {
    display: flex;
    align-items: center;
    gap: 10px;
    & > * {
      color: var(--el-header-text-color);
    }
  }
  .username {
    margin: 0 20px;
    font-size: 15px;
    color: var(--el-header-text-color);
  }
}
</style>
