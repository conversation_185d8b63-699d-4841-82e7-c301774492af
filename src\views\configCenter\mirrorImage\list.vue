<template>
  <div class="table-box">
    <sl-page-header
      title="镜像"
      title-line="镜像(Image)提供了完整系统环境复刻服务、便捷应用部署基础支撑，确保系统搭建快速精准。帮助用户构建一致、稳定、易拓展的软件运行框架，提升整体系统构建与运维效率。"
      :icon="{
        Vnode: Platform,
        color: '#0052D9',
        size: '50px',
      }"
    >
    </sl-page-header>
    <div class="btn op" style="margin-top: 8px">
      <el-button @click="openDialog" type="primary"> 新增镜像 </el-button>
      <el-button @click="openUploadDialog" type="primary"> 批量导入 </el-button>
      <el-button @click="handleBatchRecycle" type="primary"> 批量删除 </el-button>
    </div>

    <div class="filter-form-con">
      <sl-form
        class="filter-form"
        :class="{ collapsed: collapsed }"
        ref="formRef"
        :options="formOptions"
        v-model="formModel"
      >
      </sl-form>
    </div>
    <div class="table-layout">
      <dataList ref="dataListRef" :query-params="queryParams"></dataList>
    </div>

    <!-- 弹窗 -->
    <el-dialog
      v-model="uploadDialogVisible"
      title="批量导入"
      width="640px"
      :close-on-click-modal="false"
      @close="close"
    >
      <div class="sl-form-container">
        <div class="sl-form">
          <el-form>
            <div class="group-con bg-fff sl-layout">
              <el-row class="sl-layout-inner">
                <el-col :span="24">
                  <el-form-item
                    label="镜像文件:"
                    :rules="[
                      { required: true, message: '请上传镜像文件', trigger: ['blur', 'change'] },
                    ]"
                    prop="_fileList"
                  >
                    <!--                   -->
                    <div class="upload-box" style="width: 100%">
                      <el-upload
                        ref="uploadRef"
                        :limit="1"
                        :drag="true"
                        accept=".xlsx,.xls"
                        :auto-upload="false"
                        :on-change="handleFileChange"
                        :file-list="fileList"
                        :http-request="handleHttpRequest"
                      >
                        <!-- :class="['upload', self_disabled ? 'disabled' : '', drag ? 'no-border' : '']" -->

                        <div v-if="true" class="el-upload__text">
                          点击上传 <em>/拖拽到此区域 </em>
                        </div>
                      </el-upload>
                      <div class="el-upload__tip">
                        <span style="color: red">请上传xlsx格式文件，大小在 100M 以内</span>
                        <el-button class="ml5" type="primary" @click="handleDownload()" link>
                          {{ '模板下载' }}
                        </el-button>
                      </div>
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-form>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="uploadDialogVisible = false">取消</el-button>
          <!--          <el-button type="primary" @click="uploadSubmit">提交</el-button>-->
          <el-button type="primary" @click="handleUpload">提交</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="tsx" setup>
import { reactive, ref, onMounted } from 'vue'
import type { UploadFile, UploadInstance, UploadFiles } from 'element-plus'
import { Delete, Search } from '@element-plus/icons-vue'
import { Platform } from '@element-plus/icons-vue'
import SlPageHeader from '@/components/SlPageHeader/index.vue'
import slForm from '@/components/form/SlForm.vue'
import dataList from './dataList.vue'
// import { useDownload } from '@/hooks/useDownload'
import {
  getImageOsTypes,
  getPlatformListApi,
  getRegionsListApi,
  uploadImageApi,
} from '@/api/modules/configCenter'
import SlMessage from '@/components/base/SlMessage'
// import { V1CLOUD } from '@/api/config/servicePort'
import { useRouter } from 'vue-router'
const router = useRouter()

// 初始化表格数据
onMounted(() => {
  init()
})

// 类型定义
type FileList = UploadFile[]
// 上传组件实例
const uploadRef = ref<UploadInstance>()
// 文件列表
const fileList = ref<FileList>([])
// 文件选择变化处理
const handleFileChange = (file: UploadFile, files: UploadFiles) => {
  fileList.value = files
}
// 手动上传触发
const handleUpload = () => {
  console.log(fileList.value)
  if (fileList.value.length === 0) {
    ElMessage.warning('请先选择文件')
    return
  }
  uploadRef.value?.submit()
}

// 自定义上传逻辑
const handleHttpRequest = async (options: any) => {
  const { file } = options
  console.log(file)
  try {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('fileType', 'RESOURCE_EXPLAIN')

    uploadImageApi(formData, {}).then((res: any) => {
      if (res.code === 200) {
        SlMessage.success('上传成功')
        dataListRef.value?.reloadTableList()
        uploadDialogVisible.value = false
      }
    })
    // 上传成功后从列表中移除
    fileList.value = fileList.value.filter((f) => f.uid !== file.uid)
  } catch (error) {
    ElMessage.error(`${file.name} 上传失败`)
    console.error('上传错误:', error)
  }
}

// 新增表单
const formRef = ref<any>(null)
// 上传文件
const queryParams = ref<any>(null)

const formModel = reactive({})
// 重置表单内容
function reset() {
  formRef.value!.resetFields()
  queryParams.value = { ...formModel }
}
function search() {
  queryParams.value = { ...formModel }
}
// const { getDic } = globalDic
// 是否默认折叠搜索项
const collapsed = ref(false)

const osTypeDataList = ref<Option[]>([]) //
// 定义类型/接口
interface Option {
  label: string
  value: string
}
const dominCodeList: any = ref([])
const regionList: any = ref([]) //  region列表

async function init() {
  const res1: any = await getPlatformListApi() //查询domincode
  dominCodeList.value = res1.entity

  getImageOsTypes({}).then((res: any) => {
    res &&
      res.entity.forEach((item: string) => {
        osTypeDataList.value.push({
          label: item,
          value: item,
        })
      })
  })
}

const formOptions = reactive([
  {
    style: 'padding: 0',
    groupItems: [
      {
        label: '云平台',
        type: 'select',
        key: 'domainCode',
        labelField: 'domainName',
        valueField: 'domainCode',
        options: dominCodeList,
        span: 8,
        disabled: true,
        hidden: false,
        defaultSelect: true,
        onChange(option: any) {
          getRegionsListApi({
            dominCode: option.domainCode,
            pageSize: 999,
          }).then((res: any) => {
            regionList.value = res.entity.records
          })
        },
      },
      {
        label: '资源池',
        type: 'select',
        key: 'regionId',
        options: regionList,
        labelField: 'name',
        valueField: 'id',
        span: 8,
        disabled: true,
        hidden: false,
        defaultSelect: true,
      },
      {
        label: '镜像名称',
        type: 'input',
        key: 'name',
        span: 8,
        disabled: true,
        hidden: false,
        defaultSelect: true,
      },
      {
        label: '操作系统类型',
        type: 'select',
        key: 'osType',
        options: osTypeDataList,
        span: 8,
        disabled: true,
        hidden: false,
        defaultSelect: true,
      },
      {
        span: 8,
        render() {
          return <div style="display: flex;justify-content: flex-end;"></div>
        },
      },
      {
        span: 8,
        render() {
          return (
            <div style="display: flex;justify-content: flex-end;">
              <el-button onClick={reset} icon={<Delete />}>
                重置
              </el-button>
              <el-button onClick={search} icon={<Search />} type="primary">
                搜索
              </el-button>
            </div>
          )
        },
      },
    ],
  },
])

const uploadDialogVisible = ref(false)

//打开上传文件弹出
const openUploadDialog = () => {
  uploadDialogVisible.value = true
}
//新增弹窗
const openDialog = () => {
  router.push({
    path: '/mirrorImageAdd',
    query: {},
  })
}

// 批量回收
const dataListRef = ref()
const handleBatchRecycle = () => {
  dataListRef.value?.handleBatchRecycle()
}

/**
 * @description 下载文件模板
 * */
const handleDownload = async () => {
  let url: any = window.location.protocol + '//' + window.location.host
  window.location.href = url + '/imageTemplate.xlsx'
}

//关闭弹窗
const close = () => {
  uploadDialogVisible.value = false
  fileList.value = []
}
</script>
<style lang="scss" scoped>
.sl-table-content {
  overflow: auto !important;
}
.sl-page-content {
  // 减去头部的标题高度 - 不减去会超出屏幕
  overflow-x: hidden;
  height: calc(100% - 90px);
}
.header-search-input {
  width: 350px;
}
.table-layout {
  margin: 2px 8px 8px 8px;
  flex: 1;
  overflow: hidden;
}
.btn.op {
  margin: 8px 8px 0 8px;
}
.filter-form-con {
  margin: 8px 8px 0 8px;
  background: #fff;
  border-radius: 8px;
}
.filter-form.collapsed {
  height: 50px;
}
.filter-form {
  height: 110px;
  overflow: hidden;
  transition: height 0.2s;
}
</style>
<style scoped lang="scss">
.upload-box-disabled {
  :deep(.el-upload) {
    display: none;
  }
  :deep(.el-upload-list) {
    margin-top: -2px;
  }
}
.el-upload__tip {
  text-align: center;
}
</style>
