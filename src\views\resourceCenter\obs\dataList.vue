<template>
  <SlProTable
    ref="proTable"
    highlight-current-row
    :columns="columns"
    style="min-height: 300px"
    :request-api="getResourceList"
    :init-param="queryParams"
    :current-change="currentChange"
    @selection-change="handleSelectionChange"
    hidden-table-header
    row-key="goodsOrderId"
  >
  </SlProTable>

  <!-- 资源变更弹窗 -->
  <ResourceChangeDialog
    v-model:visible="changeDialogVisible"
    resource-type="obs"
    :selected-resources="selectedResources"
    :allowed-change-types="allowedChangeTypes"
    @confirm="handleConfirm"
  />

  <!-- 资源延期弹窗 -->
  <ResourceChangeDialog
    v-model:visible="delayDialogVisible"
    resource-type="obs"
    :selected-resources="selectedResources"
    :is-delay="true"
    @confirm="handleConfirm"
  />
</template>
<script setup lang="tsx" name="dataList">
import { ref, reactive } from 'vue'
import type { ColumnProps, ProTableInstance } from '@/components/SlProTable/interface'
import { getResourceList, cycleBinCreate } from '@/api/modules/resourecenter'
import eventBus from '@/utils/eventBus'
import ResourceChangeDialog from '../components/ResourceChangeDialog.vue'
import type { ResourceChangeType } from '../types'
import { ElMessage } from 'element-plus'
import { useResourceChange } from '../hooks/useResourceChange'
import { useRecycleValidation } from '../hooks/useRecycleValidation'

const { queryParams } = defineProps<{
  queryParams: Record<string, any>
}>()

const { validateResources } = useResourceChange()

const emit = defineEmits(['currentChange'])
const currentChange = (currentRow: any, oldCurrentRow: any) => {
  emit('currentChange', currentRow, oldCurrentRow)
}
// 表格配置项
const columns = reactive<ColumnProps<any>[]>([
  { type: 'selection', width: 55 },
  { type: 'index', label: '序号', width: 55, fixed: 'left' },
  { prop: 'deviceName', label: '对象存储名称', minWidth: 150, fixed: 'left' },
  { prop: 'deviceId', label: '资源ID', width: 200 },
  { prop: 'storeType', label: '存储类型', minWidth: 150 },
  { prop: 'capacity', label: '容量', minWidth: 150 },
  { prop: 'accessKey', label: '公钥', minWidth: 150 },
  { prop: 'secretKey', label: '私钥', minWidth: 150 },
  { prop: 'publicAddress', label: '公网访问地址', minWidth: 200 },
  { prop: 'internalAddress', label: '私网访问地址', minWidth: 200 },
  { prop: 'applyTime', label: '申请时长', minWidth: 100 },
  { prop: 'tenantName', label: '租户', minWidth: 150 },
  { prop: 'businessSysName', label: '业务系统', minWidth: 150 },
  { prop: 'cloudPlatform', label: '所属云', minWidth: 150 },
  { prop: 'resourcePoolName', label: '资源池', minWidth: 200 },
  { prop: 'orderCode', label: '工单编号', minWidth: 150 },
  { prop: 'effectiveTime', label: '开通时间', minWidth: 150 },
  { prop: 'expireTime', label: '到期时间', minWidth: 150 },
  { prop: 'billId', label: '计费号', width: 150 },
  { prop: 'deviceStatusCn', label: '状态', width: 100 },
  { prop: 'recoveryStatusCn', label: '回收状态', width: 100 },
  { prop: 'changeStatusCn', label: '变更状态', width: 100 },
  { prop: 'applyUserName', label: '申请人', minWidth: 100 },
])

const proTable = ref<ProTableInstance>()

// 回收功能
const currentRecycleIdsList = ref<any[]>([])

// 使用回收校验钩子函数
const { validateRecycle, validateChange } = useRecycleValidation()

const handleCreateRecycle = async (goodsItems: any[]) => {
  const res = await cycleBinCreate({
    goodsType: 'obs',
    goodsItems,
  })
  if (res.code != 200) {
    return ElMessage.error(res.message || '接口请求失败')
  }
  ElMessage.success('已加入回收站')
  proTable.value?.getTableList()
  proTable.value?.clearSelection()
  eventBus.emit('cycleBins:updateCount')
}

// 批量回收功能
const handleBatchRecycle = async () => {
  const selectedList = proTable.value?.selectedList || []

  // 使用新的验证逻辑
  if (validateRecycle(selectedList, 'obs')) {
    currentRecycleIdsList.value = selectedList.map((i) => ({ goodsId: i.id.trim() }))
    handleCreateRecycle(currentRecycleIdsList.value)
  }
}

// 多选数据
const multipleSelection = ref<any[]>([])
const changeDialogVisible = ref(false)
const delayDialogVisible = ref(false)
const selectedResources = ref<any[]>([])

// OBS允许的变更类型
const allowedChangeTypes: ResourceChangeType[] = ['storage_expand']

// 处理资源变更
const handleResourceChange = async () => {
  if (!multipleSelection.value.length) {
    ElMessage.warning('请选择要变更的资源')
    return
  }
  selectedResources.value = multipleSelection.value

  // 使用新的校验逻辑
  if (validateChange(selectedResources.value)) {
    // 使用钩子函数中的validateResources进行业务系统一致性校验
    const isValid = await validateResources(selectedResources.value)
    if (isValid) {
      // 校验通过，才显示弹窗
      changeDialogVisible.value = true
    }
  }
}

// 处理资源延期
const handleResourceDelay = async () => {
  if (!multipleSelection.value.length) {
    ElMessage.warning('请选择要延期的资源')
    return
  }
  selectedResources.value = multipleSelection.value

  // 使用新的校验逻辑
  if (validateChange(selectedResources.value)) {
    // 使用钩子函数中的validateResources进行业务系统一致性校验
    const isValid = await validateResources(selectedResources.value)
    if (isValid) {
      // 校验通过，才显示弹窗
      delayDialogVisible.value = true
    }
  }
}

// 处理确认
const handleConfirm = () => {
  proTable.value?.getTableList()
}

// 选择变化事件
const handleSelectionChange = (selection: any[]) => {
  multipleSelection.value = selection
}

defineExpose({
  handleBatchRecycle,
  handleResourceChange,
  handleResourceDelay,
})
</script>
