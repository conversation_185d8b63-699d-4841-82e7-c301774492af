<!-- Grid.vue -->
<template>
  <div :style="gapStyle">
    <slot></slot>
    <!-- 插槽，用于显示 GridItem -->
  </div>
</template>

<script setup lang="ts">
import { computed, provide, ref, useSlots, type VNode, type VNodeArrayChildren, watch } from 'vue'
import type { BreakPoint } from '../SlProTable/interface'
import { useWindowSize } from '@vueuse/core'

const props = withDefaults(
  defineProps<{
    cols?: number | Record<BreakPoint, number> // 列数
    collapsed?: boolean // 控制网格是否折叠
    gap?: [number, number] | number // 网格项之间的间距
    collapsedRows?: number // 折叠后显示的行数
  }>(),
  {
    cols: () => ({ xs: 1, sm: 2, md: 2, lg: 3, xl: 4 }),
    collapsed: false,
    collapsedRows: 1,
    gap: 0,
  },
)

const { width } = useWindowSize()

const breakPoint = computed<BreakPoint>(() => {
  if (width.value < 576) return 'xs'
  if (width.value < 768) return 'sm'
  if (width.value < 992) return 'md'
  if (width.value < 1200) return 'lg'
  return 'xl'
})
provide('breakPoint', breakPoint)

const gridCols = computed(() => {
  if (typeof props.cols === 'object') return props.cols[breakPoint.value] ?? props.cols
  return props.cols
})
provide('cols', gridCols)

const hiddenIndex = ref(-1)
provide('shouldHiddenIndex', hiddenIndex)

const slots = useSlots().default?.('_') ?? []

const findIndex = () => {
  let fields: VNodeArrayChildren = []
  let suffix: VNode | null = null
  slots?.forEach((slot: any) => {
    // suffix
    if (
      typeof slot.type === 'object' &&
      slot.type.__name === 'GridItem' &&
      slot.props?.suffix !== undefined
    )
      suffix = slot

    // slot children
    if (typeof slot.type === 'symbol' && Array.isArray(slot.children)) fields.push(...slot.children)
  })

  // 计算 suffix 所占用的列
  let suffixCols = 0
  if (suffix) {
    suffixCols =
      ((suffix as VNode).props![breakPoint.value]?.span ?? (suffix as VNode).props?.span ?? 1) +
      ((suffix as VNode).props![breakPoint.value]?.offset ?? (suffix as VNode).props?.offset ?? 0)
  }
  try {
    let find = false
    fields.reduce((prev = 0, current: any, index: number) => {
      prev +=
        ((current as VNode)!.props![breakPoint.value]?.span ??
          (current as VNode)!.props?.span ??
          1) +
        ((current as VNode)!.props![breakPoint.value]?.offset ??
          (current as VNode)!.props?.offset ??
          0)
      if (Number(prev) > props.collapsedRows * gridCols.value - suffixCols) {
        hiddenIndex.value = index
        find = true
        throw 'find it'
      }
      return prev
    }, 0)
    if (!find) hiddenIndex.value = -1
  } catch {
    // console.warn(e)
  }
}

watch(
  () => props.collapsed,
  (value) => {
    if (value) return findIndex()
    hiddenIndex.value = -1
  },
  { immediate: true },
)

// 断点变化时执行 findIndex
watch(
  () => breakPoint.value,
  () => {
    if (props.collapsed) findIndex()
  },
)

const gridGap = computed(() => {
  if (typeof props.gap === 'number') return `${props.gap}px`
  if (Array.isArray(props.gap)) return `${props.gap[1]}px ${props.gap[0]}px`
  return 'unset'
})

// 设置 style
const gapStyle = computed(() => {
  return {
    display: 'grid',
    gridGap: gridGap.value,
    gridTemplateColumns: `repeat(${gridCols.value}, minmax(0, 1fr))`,
  }
})

defineExpose({ breakPoint })
</script>
