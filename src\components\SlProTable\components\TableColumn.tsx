import { inject, ref, toRefs, useSlots } from 'vue'
import type { ColumnProps, EleType, HeaderRenderScope, RenderScope } from '../interface'
import { ElTableColumn, ElTag, ElLink } from 'element-plus'
import { filterEnum, formatValue, handleProp, handleRowAccordingToProp } from '@/utils'
type propsType = {
  column: ColumnProps
}

// 返回表格列
const RenderTableColumn = (props: propsType) => {
  const { column } = toRefs(props)
  const slots = useSlots()
  const enumMap = inject('enumMap', ref(new Map()))

  // 渲染表格数据
  const renderCellData = (item: ColumnProps, scope: RenderScope<any>) => {
    return enumMap.value.get(item.prop) && item.isFilterEnum
      ? filterEnum(
          handleRowAccordingToProp(scope.row, item.prop!),
          enumMap.value.get(item.prop)!,
          item.fieldNames,
        )
      : formatValue(handleRowAccordingToProp(scope.row, item.prop!))
  }

  // 获取 tag 类型
  const getTagType = (item: ColumnProps, scope: RenderScope<any>, type?: 'tag' | 'link') => {
    return (
      filterEnum(
        handleRowAccordingToProp(scope.row, item.prop!),
        enumMap.value.get(item.prop),
        item.fieldNames,
        type,
      ) || 'primary'
    )
  }

  return (
    <>
      {column.value.isShow && (
        <ElTableColumn
          key={column.value.prop}
          {...column.value}
          align={column.value.align ?? 'center'}
          showOverflowTooltip={
            column.value.showOverflowTooltip ?? column.value.prop !== 'operation'
          }
        >
          {{
            default: (scope: RenderScope<any>) => {
              if (column.value._children)
                return column.value._children.map((child) => RenderTableColumn({ column: child }))
              if (column.value.render) return column.value.render(scope)
              if (column.value.prop && slots[handleProp(column.value.prop)])
                return slots[handleProp(column.value.prop)]!(scope)
              if (column.value.tag)
                return (
                  <ElTag type={getTagType(column.value, scope, 'tag') as EleType}>
                    {renderCellData(column.value, scope)}
                  </ElTag>
                )
              if (column.value.link)
                return (
                  <ElLink
                    type={getTagType(column.value, scope, 'link') as EleType}
                    underline={false}
                  >
                    {renderCellData(column.value, scope)}
                  </ElLink>
                )
              return renderCellData(column.value, scope)
            },
            header: (scope: HeaderRenderScope<any>) => {
              if (column.value.headerRender) return column.value.headerRender(scope)
              if (column.value.prop && slots[`${handleProp(column.value.prop)}Header`])
                return slots[`${handleProp(column.value.prop)}Header`]!(scope)
              return <>{column.value.label}</>
            },
          }}
          {/* {column.value.render && column.value.render} */}
        </ElTableColumn>
      )}
    </>
  )
}

export default RenderTableColumn
