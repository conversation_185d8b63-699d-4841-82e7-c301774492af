<template>
  <div class="form-container">
    <sl-form
      ref="slFormRef"
      size="small"
      :model-value="baseInfo[0].orderJson"
      show-block-title
      :options="formOptions"
    >
      <!-- 添加按钮 -->
      <template #addBtnSlot="{ scope }">
        <el-button @click="addGoods" style="margin-right: 16px" :icon="Plus" plain type="primary">
          {{ scope.text }}
        </el-button>
      </template>
      <!-- 配置信息 -->
      <template #goodsInfoSlot>
        <div style="padding-top: 6px">
          <template v-for="(goods, goodsIndex) in goodsList" :key="goods">
            <goodsItem
              :flavor-options="flavorOptions"
              :style="goodsIndex !== 0 ? 'margin-top:20px' : ''"
              :goods="goods"
            ></goodsItem>
          </template>
        </div>
      </template>
    </sl-form>
  </div>
</template>
<script setup lang="ts">
import { Plus } from '@element-plus/icons-vue'
import type { IGoodsItem } from '@/views/resourceCenter/hooks/useShoppingCarts'
import type { ICqModel, IBaseModel } from '@/views/resourceCenter/hooks/useGoodsModels'
import slForm from '@/components/form/SlForm.vue'
import { reactive, ref, computed } from 'vue'
import eventBus from '@/utils/eventBus'
import goodsItem from './goodsItem.vue'
import { useRoute } from 'vue-router'
import { useFlavorTree } from '@/views/resourceCenter/hooks/useFlavorTree'

const flavorOptions = useFlavorTree('cq')
const slFormRef = ref()
const rouete = useRoute()

const props = defineProps<{
  goodsList: IGoodsItem<ICqModel>[]
  baseInfo: IGoodsItem<IBaseModel>[]
}>()

setRef(props.baseInfo[0])
function setRef(baseInfo: IGoodsItem<IBaseModel>) {
  baseInfo.a4FormRef = slFormRef
}

function addGoods() {
  eventBus.emit('shoppingCarts:addGoods', {
    goodsType: 'cq',
    isEdit: rouete.query.orderId ? true : false,
  })
}

const hidden4AForm = computed(() => {
  return props.goodsList.length === 0
})

const formOptions = reactive([
  {
    blockTitleFilter: {
      slot: 'addBtnSlot',
      text: '增加容器配额',
    },
    blockTitleStyle: 'position: sticky;top:0;z-index:99;background: #fff;',
    gutter: 40,
    groupName: '配置信息',
    groupItems: [
      {
        label: '4A账号',
        type: 'input',
        key: 'a4Account',
        span: 8,
        props: {
          maxlength: 64,
          showWordLimit: true,
        },
        hidden: hidden4AForm,
        rules: [{ required: true, message: '请输入4A账号', trigger: ['blur', 'change'] }],
      },
      {
        label: '4A账号绑定的手机号',
        type: 'input',
        key: 'a4Phone',
        span: 8,
        hidden: hidden4AForm,
        rules: [
          {
            required: true,
            message: '请输入4A账号绑定的手机号',
            trigger: ['blur', 'change'],
            pattern: /^1[3-9]\d{9}$/,
          },
        ],
      },
      {
        span: 24,
        noFormItem: true,
        type: 'slot',
        slotName: 'goodsInfoSlot',
      },
    ],
  },
])
</script>
<style scoped>
.scroll-view {
  height: calc(100vh - 468px);
}
.goods-del-btn {
  position: absolute;
  top: -7px;
  right: -6px;
  z-index: 10;
  color: var(--el-color-danger);
  font-size: 1.2rem;
  cursor: pointer;
  display: none;
}
.goods-info-form:hover .goods-del-btn {
  display: block;
}
.form-container {
  display: flex;
  flex-direction: column;
}
</style>
