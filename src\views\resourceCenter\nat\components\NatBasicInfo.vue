<template>
  <div class="nat-basic-info">
    <sl-form
      ref="formRef"
      v-model="detailData"
      :options="formOptions"
      label-width="120px"
      :disabled="true"
    ></sl-form>
  </div>
</template>

<script setup lang="tsx" name="NatBasicInfo">
import { reactive, onMounted } from 'vue'
import { getResourceDetail } from '@/api/modules/resourecenter'

// 定义props
const props = defineProps<{
  id: string
}>()

// 详情数据
const detailData = reactive<any>({
  deviceName: '',
  deviceId: '',
  spec: '',
  vpcName: '',
  subnetName: '',
  subnetCidr: '',
  eip: '',
  bandWidth: '',
  applyTime: '',
  tenantName: '',
  businessSysName: '',
  cloudPlatform: '',
  resourcePoolName: '',
  orderCode: '',
  effectiveTime: '',
  expireTime: '',
  billId: '',
  deviceStatusCn: '',
  recoveryStatusCn: '',
  changeStatusCn: '',
  applyUserName: '',
  transformAddress: '',
})

// 表单配置
const formOptions = reactive<any[]>([
  {
    groupItems: [
      {
        label: '网关名称',
        type: 'text',
        key: 'deviceName',
        span: 8,
      },
      {
        label: '资源ID',
        type: 'text',
        key: 'deviceId',
        span: 8,
      },
      {
        label: '实例规格',
        type: 'text',
        key: 'spec',
        span: 8,
      },
      {
        label: 'VPC名称',
        type: 'text',
        key: 'vpcName',
        span: 8,
      },
      {
        label: '子网名称',
        type: 'text',
        key: 'subnetName',
        span: 8,
      },
      {
        label: '子网网段',
        type: 'text',
        key: 'subnetCidr',
        span: 8,
      },
      {
        label: '弹性公网IP',
        type: 'text',
        key: 'eip',
        span: 8,
      },
      {
        label: '带宽大小',
        type: 'text',
        key: 'bandWidth',
        span: 8,
      },
      {
        label: '申请时长',
        type: 'text',
        key: 'applyTime',
        span: 8,
      },
      {
        label: '租户',
        type: 'text',
        key: 'tenantName',
        span: 8,
      },
      {
        label: '业务系统',
        type: 'text',
        key: 'businessSysName',
        span: 8,
      },
      {
        label: '所属云',
        type: 'text',
        key: 'cloudPlatform',
        span: 8,
      },
      {
        label: '资源池',
        type: 'text',
        key: 'resourcePoolName',
        span: 8,
      },
      {
        label: '工单编号',
        type: 'text',
        key: 'orderCode',
        span: 8,
      },
      {
        label: '开通时间',
        type: 'text',
        key: 'effectiveTime',
        span: 8,
      },
      {
        label: '到期时间',
        type: 'text',
        key: 'expireTime',
        span: 8,
      },
      {
        label: '计费号',
        type: 'text',
        key: 'billId',
        span: 8,
      },
      {
        label: '状态',
        type: 'text',
        key: 'deviceStatusCn',
        span: 8,
      },
      {
        label: '回收状态',
        type: 'text',
        key: 'recoveryStatusCn',
        span: 8,
      },
      {
        label: '变更状态',
        type: 'text',
        key: 'changeStatusCn',
        span: 8,
      },
      {
        label: '申请人',
        type: 'text',
        key: 'applyUserName',
        span: 8,
      },
    ],
  },
])

// 获取NAT详情
const fetchNatDetail = async () => {
  if (!props.id) return

  try {
    const res = await getResourceDetail({
      id: props.id,
    })

    if (res && res.entity) {
      // 填充基本信息
      for (const key in detailData) {
        detailData[key] = res.entity[key] !== undefined ? res.entity[key] : ''
      }
    }
  } catch (error) {
    console.error('获取NAT详情失败', error)
  }
}

// 初始化
onMounted(async () => {
  await fetchNatDetail()
})

// 暴露刷新方法供父组件调用
defineExpose({
  refresh: fetchNatDetail,
})
</script>

<style lang="scss" scoped>
.nat-basic-info {
  padding: 16px;
  background-color: #fff;
  border-radius: 8px;
}
</style>
