<template>
  <div>
    <el-row :gutter="24">
      <el-col :span="4">
        <el-input disabled placeholder="云平台" :value="model.domain?.name"></el-input>
      </el-col>
      <el-col :span="4">
        <el-input disabled placeholder="资源池" :value="model.resourcePool?.name"></el-input>
      </el-col>
      <el-col :span="4">
        <el-input disabled placeholder="可用区" :value="model.az?.name"></el-input>
      </el-col>
    </el-row>
  </div>
</template>
<script setup lang="ts">
const props = defineProps<{
  item: any
  form: any
}>()
const model = props.form
</script>
