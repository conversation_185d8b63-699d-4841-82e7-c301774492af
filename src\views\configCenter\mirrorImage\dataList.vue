<template>
  <SlProTable
    ref="proTable"
    highlight-current-row
    :columns="columns"
    style="min-height: 300px"
    :request-api="imageList"
    :init-param="queryParams"
    :current-change="currentChange"
    hidden-table-header
    row-key="id"
  >
  </SlProTable>
</template>
<script setup lang="tsx" name="dataList">
import { ref, reactive } from 'vue'
import type { ColumnProps, ProTableInstance } from '@/components/SlProTable/interface'
import { imageList, deleteImageByIdApi, batchDeleteImageByIdApi } from '@/api/modules/configCenter'
import { Delete } from '@element-plus/icons-vue'
import SlMessage from '@/components/base/SlMessage'

const { queryParams } = defineProps<{
  queryParams: any
}>()

const emit = defineEmits(['currentChange'])
const currentChange = (currentRow: any, oldCurrentRow: any) => {
  emit('currentChange', currentRow, oldCurrentRow)
}
// 表格配置项
const columns = reactive<ColumnProps<any>[]>([
  { type: 'selection', width: 55 },
  { type: 'index', label: '序号', minWidth: 55 },
  {
    prop: 'domainName',
    label: '云平台',
    minWidth: 160,
  },
  {
    prop: 'name',
    label: '镜像名称',
    minWidth: 160,
  },
  { prop: 'regionName', label: '资源池', minWidth: 160 },
  // {
  //   label: '是否公有',
  //   width: 100,
  //   render: ({ row }) => <span> {row.shares == '0' ? '私有' : '公有'}</span>,
  // },
  { prop: 'osType', label: '操作系统类型', width: 120 },
  { prop: 'diskFormat', label: '磁盘格式', minWidth: 120 },
  { prop: 'imageSize', label: '镜像大小(GB)', minWidth: 120 },
  // { prop: 'minCpu', label: '最小CPU', width: 150 },
  // { prop: 'minRam', label: '最小内存(MB)', width: 140 },
  // { prop: 'minDisk', label: '最小磁盘(GB)', width: 140 },
  // { prop: 'password', label: '镜像密码', width: 100 },
  // { prop: 'resourceId', label: '镜像ID', width: 100 },
  // { prop: 'imageType', label: '镜像类型', width: 100 },
  { prop: 'azName', label: '可用区', width: 100 },
  { prop: 'version', label: '镜像版本', width: 100 },
  // { prop: 'description', label: '描述' },
  {
    prop: 'operation',
    label: '操作',
    width: '150',
    fixed: 'right',
    render: ({ row }) => {
      return (
        <>
          <el-button type="danger" link icon={Delete} onClick={() => deletePermission(row)}>
            删除
          </el-button>
        </>
      )
    },
  },
])

const proTable = ref<ProTableInstance>()

const reloadTableList = () => {
  proTable.value?.clearSelection()
  proTable.value?.getTableList()
}
/**
 * 删除
 * @param id
 */
const deletePermission = async (row: any) => {
  await ElMessageBox.confirm('确认删除镜像' + row.name + '吗？', '信息提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
  })
  await deleteImageByIdApi({
    id: row.id,
  })
  SlMessage.success('删除成功')
  proTable.value?.getTableList()
  proTable.value?.clearSelection()
}

// 批量删除功能
const handleBatchRecycle = async () => {
  const selectedList = proTable.value?.selectedList || []
  if (selectedList.length == 0) {
    return SlMessage.warning('请先选择镜像')
  }
  const ids = selectedList.map((i) => i.id.trim())
  await ElMessageBox.confirm('确认批量删除镜像吗？', '信息提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
  })
  await batchDeleteImageByIdApi({
    ids: ids,
  })
  SlMessage.success('删除成功')
  proTable.value?.clearSelection()
  proTable.value?.getTableList()
}

defineExpose({
  handleBatchRecycle,
  reloadTableList,
})
</script>
