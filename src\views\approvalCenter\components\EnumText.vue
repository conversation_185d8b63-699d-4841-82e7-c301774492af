<template>
  <div class="medium sle">
    <el-tooltip :content="content" placement="top">
      <span :style="fields.valueStyle" class="medium sle"> {{ content }} </span>
    </el-tooltip>
  </div>
</template>

<script setup lang="ts">
import { computed, toRefs } from 'vue'
import { filterEnum } from '@/utils'

// Props 定义
const props = defineProps<{
  formModel: FormDataType
  fields: FormDataType
  dicCollection: FormDataType
}>()

const { formModel, fields, dicCollection } = toRefs(props)

const content = computed(() => {
  if (!formModel.value || !fields.value) return ''

  if (fields.value.keyName) {
    return formModel.value[fields.value.keyName] || ''
  }

  const enumData = fields.value.dicKey
    ? (dicCollection.value[fields.value.dicKey] ?? [])
    : (fields.value.options ?? [])

  const fieldNames = {
    label: fields.value.labelField ?? 'label',
    value: fields.value.valueField ?? 'value',
  }
  const text = filterEnum(formModel.value[fields.value.key], enumData, fieldNames)
  return text || ''
})
</script>
