import type { ColumnProps } from '@/components/SlProTable/interface'

/**
 * @name 资源列表-工单
 */
export interface ResourceListType {
  [key: string]: any
  activityTask: GetResourceListResType | undefined
}

/**
 * @name 资源列表-参数
 */
export interface GetResourceListParamsType {
  activitiesKey?: string
  currentAuthMenuCode?: string
  domainCode?: string
  currentRole?: string
  searchType?: string
  keyword?: string
  endTime?: string
  // userId: number
  pageNum?: number
  pageSize?: number
  // type: string
  // isAgain: boolean
}

/**
 * @name 流程节点
 */
export interface GetResourceListResType {
  currentTask: string
  currentTaskName: string
  allTasks: ActiviteDetailVoListType[]
}

export interface ActiviteDetailVoListType {
  task: string
  taskName: string
}

/*

*/
export const goodsTypeEnum = {
  ecs: { code: 'ecs', desc: '云主机', params: ['azId', 'templateCode'] },
  gcs: { code: 'gcs', desc: 'GPU云主机', params: ['azId'] },
  evs: { code: 'evs', desc: '云硬盘', params: ['azId'] },
  eip: { code: 'eip', desc: '弹性公网', params: ['azId'] },
  obs: { code: 'obs', desc: '对象存储', params: ['azId'] },
  slb: { code: 'slb', desc: '负载均衡', params: ['azId', 'vpcId', 'subnetId'] },
  nat: { code: 'nat', desc: 'NAT网关', params: ['azId', 'vpcId', 'subnetId'] },
  cq: { code: 'cq', desc: '容器配额', params: [] },
  mysql: { code: 'mysql', desc: '云数据库', params: ['azId'] },
  other: { code: 'other', desc: '其他产品', params: ['azId'] },
  unknown: { code: 'unknown', desc: '_', params: ['azId'] },
}
/* {
  ecs: 'ecsModelList',
  gcs: 'gcsModelList',
  evs: 'evsModelList',
  obs: 'obsModelList',
  slb: 'slbModelList',
  nat: 'natModelList',
  eip: 'eipModelList',
  mysql: 'mysqlModelList',
} */
export const goodsValueEnum = [
  { code: 'ecs', desc: '云主机', goodsList: 'ecsModelList' },
  { code: 'gcs', desc: 'GPU云主机', goodsList: 'gcsModelList' },
  { code: 'evs', desc: '云硬盘', goodsList: 'evsModelList' },
  { code: 'obs', desc: '对象存储', goodsList: 'obsModelList' },
  { code: 'slb', desc: '负载均衡', goodsList: 'slbModelList' },
  { code: 'nat', desc: 'NAT网关', goodsList: 'natModelList' },
  { code: 'eip', desc: '弹性公网', goodsList: 'eipModelList' },
  { code: 'cq', desc: '容器配额', goodsList: 'cqModelList' },
  { code: 'mysql', desc: '云数据库', goodsList: 'mysqlModelList' },
  { code: 'other', desc: '其他产品', goodsList: 'otherModelList' },
]

export type goodsTypeCodeEnum = keyof typeof goodsTypeEnum

export type goodsColumnsType = {
  [key in goodsTypeCodeEnum]: ColumnProps[]
}

export type DetailGoodsType = {
  orderOverView: FormDataType & {
    showInfo: FormDataType
  }
  goodsList: FormDataType[]
  desc: string
  goodsType: goodsTypeCodeEnum
}
export type OrderDetailType = {
  goods: DetailGoodsType[]
} & FormDataType

// ----------------------回收资源 详情----------------
export type RecycleResourceDetailType = {
  goodsType: goodsTypeCodeEnum

  goodsList: FormDataType[] | null | undefined

  netWork: FormDataType[] | null | undefined
  vpcList: FormDataType[] | null | undefined
}
