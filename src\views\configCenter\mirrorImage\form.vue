<template>
  <div class="resourcerequest">
    <sl-page-header
      :title="`${formModel.operationName}${formModel.goodsName}`"
      :icon="{
        Vnode: Platform,
        color: '#0052D9',
        size: '20px',
      }"
      :show-back="true"
      :back="{
        title: '返回列表',
        function: goBack,
      }"
    >
    </sl-page-header>
    <div class="resourcerequestbox">
      <div class="onebox">
        <div class="oneboxleft">
          <el-scrollbar class="scroll-view">
            <sl-form
              ref="slFormRef"
              show-block-title
              :options="formOptions"
              :model-value="formModel"
              style="overflow: hidden"
            >
            </sl-form>
          </el-scrollbar>
        </div>
      </div>
      <div class="onefooter">
        <el-button @click="goBack">取消</el-button>
        <el-button type="primary" @click="submitfromdata">提交申请</el-button>
      </div>
    </div>
  </div>
</template>
<script setup lang="tsx">
// 资源配置详情申请
import { Platform } from '@element-plus/icons-vue'
import slForm from '@/components/form/SlForm.vue'
import { reactive, ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import SlMessage from '@/components/base/SlMessage'
import useModel from './model'
import {
  getImageOsTypes,
  addImageApi,
  getRegionsListApi,
  getAzsListApi,
  // getFlavorResTypesApi,
  getPlatformListApi,
} from '@/api/modules/configCenter'
import {
  validateNoSpecialChars,
  validateNumber,
  validatePassword,
  validateNameNoSpecialChars,
} from '@/utils/validate'

// 初始化表格数据
onMounted(() => {
  init()
})

const model = useModel()
const formModel = reactive<{ [key: string]: any }>({})
Object.assign(formModel, model)

// 表单
const slFormRef = ref()
const router = useRouter()
const route = useRoute()
const orderId = route.query.id as string

//查询资源池信息
const regionList = ref([])
//云平台信息
const dominCodeList = ref([])
// 定义类型/接口
interface Option {
  label: string
  value: string
}
const osTypeDataList = ref<Option[]>([]) // ✅ 推荐写法
// 可用区信息
const azList = ref([])
async function init() {
  // getRegionsListApi({
  //   pageSize:10000,
  // }).then((res: any) => {
  //   regionList.value = res.entity.records
  // })

  const res1: any = await getPlatformListApi() //查询domincode
  dominCodeList.value = res1.entity

  getImageOsTypes({}).then((res: any) => {
    res &&
      res.entity.forEach((item: string) => {
        osTypeDataList.value.push({
          label: item,
          value: item,
        })
      })
  })
}

// 提交表单数据
const submitfromdata = async () => {
  const validate = await slFormRef.value.validate()
  if (!validate) return

  const submitdata = {
    orderId,
    name: formModel.name,
    description: formModel.description,
    regionId: formModel.regionId,
    shares: formModel.shares,
    osType: formModel.osType,
    diskFormat: formModel.diskFormat,
    imageSize: formModel.imageSize,
    minCpu: formModel.minCpu,
    minRam: formModel.minRam,
    minDisk: formModel.minDisk,
    resourceId: formModel.resourceId,
    imageType: 'VM', //镜像类型，默认固定值VM
    azId: formModel.azId,
    version: formModel.version,
    domainCode: formModel.domainCode,
    osTypeSource: 'VM',
  }
  try {
    // const entity = orderId ? await addImageApi(submitdata) : await updateImageApi(submitdata)
    const entity = await addImageApi(submitdata)
    if (entity.code == 200) {
      SlMessage.success('提交成功')
      goBack()
    } else {
      SlMessage.error('提交失败')
    }
  } finally {
  }
}
const formOptions = reactive([
  {
    style: 'margin-top: 0;margin-bottom: 0;',
    groupName: '基本信息',
    groupItems: [
      {
        label: '云平台',
        type: 'select',
        key: 'domainCode',
        options: dominCodeList,
        labelField: 'domainName',
        valueField: 'domainCode',
        onChange(option: any) {
          console.log(option)
          formModel.domainCode = option.domainCode
          formModel.regionId = []
          formModel.flavorType = ''

          getRegionsListApi({
            dominCode: option.domainCode,
          }).then((res: any) => {
            regionList.value = res.entity.records
          })
        },
        span: 12,
        rules: {
          required: true,
          message: '请选择',
          trigger: ['blur', 'change'],
        },
      },
      {
        label: '资源池',
        type: 'select',
        key: 'regionId',
        options: regionList,
        labelField: 'name',
        valueField: 'id',
        onChange(option: any) {
          getAzsListApi({
            regionCode: option.resourceCode,
          })
            .then((res: any) => {
              azList.value = res.entity.records
            })
            .catch(() => {
              azList.value = []
            })
        },
        span: 12,
        rules: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
      },

      {
        label: '可用区',
        type: 'select',
        key: 'azId',
        options: azList,
        labelField: 'azName',
        valueField: 'id',
        span: 12,
        rules: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
      },
      {
        label: '镜像名称',
        type: 'input',
        key: 'name',
        props: {
          type: 'textarea',
          rows: 1,
          maxlength: 50,
          showWordLimit: true,
        },
        span: 12,
        rules: [
          { required: true, message: '请输入镜像名称', trigger: ['blur', 'change'] },
          { validator: validateNameNoSpecialChars, trigger: ['blur', 'change'] },
        ],
      },

      {
        label: '镜像ID',
        type: 'input',
        key: 'resourceId',
        props: {
          type: 'input',
          rows: 1,
          maxlength: 50,
          showWordLimit: false,
        },
        span: 12,
        rules: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }],
      },
      {
        label: '操作系统',
        type: 'select',
        key: 'osType',
        options: osTypeDataList,
        span: 12,
        rules: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
      },
      {
        label: '镜像版本',
        type: 'input',
        key: 'version',
        props: {
          rows: 1,
          maxlength: 50,
        },
        span: 12,
        rules: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
      },
      {
        label: '磁盘格式',
        type: 'select',
        key: 'diskFormat',
        options: [
          {
            label: 'qcow2',
            value: 'qcow2',
          },
          {
            label: 'vmdk',
            value: 'vmdk',
          },
          {
            label: 'raw',
            value: 'raw',
          },
          {
            label: 'vhd',
            value: 'vhd',
          },
          {
            label: 'ari',
            value: 'ari',
          },
          {
            label: 'aki',
            value: 'aki',
          },
          {
            label: 'iso',
            value: 'iso',
          },
        ],
        span: 12,
        rules: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
      },

      {
        label: '镜像大小(GB)',
        type: 'input',
        key: 'imageSize',
        props: {
          type: 'input',
          rows: 1,
          maxlength: 50,
          showWordLimit: false,
        },
        // suffix: '',
        span: 12,
        rules: [{ validator: validateNoSpecialChars, trigger: ['blur', 'change'] }],
      },
      {
        label: '最小CPU(核)',
        type: 'input',
        key: 'minCpu',
        props: {
          type: 'input',
          rows: 1,
          maxlength: 50,
          showWordLimit: false,
        },
        // suffix: '',
        span: 12,
        rules: [{ validator: validateNumber, trigger: ['blur', 'change'] }],
      },

      {
        label: '最小内存(MB)',
        type: 'input',
        key: 'minRam',
        props: {
          type: 'input',
          rows: 1,
          maxlength: 50,
          showWordLimit: false,
        },
        // suffix: '',
        span: 12,
        rules: [{ validator: validateNumber, trigger: ['blur', 'change'] }],
      },
      {
        label: '最小磁盘(GB)',
        type: 'input',
        key: 'minDisk',
        // suffix: '',
        props: {
          type: 'input',
          rows: 1,
          maxlength: 50,
          showWordLimit: false,
        },
        span: 12,
        rules: [{ validator: validateNumber, trigger: ['blur', 'change'] }],
      },
      {
        label: '是否公有',
        type: 'select',
        key: 'shares',
        options: [
          {
            label: '是',
            value: '1',
          },
          {
            label: '否',
            value: '0',
          },
        ],
        span: 12,
        rules: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
      },

      {
        label: '镜像密码',
        type: 'input',
        key: 'password',
        props: {
          type: 'input',
          rows: 1,
          maxlength: 100,
          showWordLimit: false,
        },
        span: 12,
        rules: [
          { required: true, message: '请输入镜像密码', trigger: ['blur', 'change'] },
          { validator: validatePassword, trigger: ['blur', 'change'] },
        ],
      },
      {
        label: '描述',
        type: 'input',
        key: 'description',
        props: {
          type: 'textarea',
          rows: 4,
          maxlength: 250,
          showWordLimit: true,
        },
        span: 24,
      },
    ],
  },
])
function goBack() {
  router.go(-1)
}
</script>
<style scoped>
.scroll-view {
  height: calc(100vh - 188px);
}

.resourcerequest {
  .resourcerequestbox {
    .onebox {
      display: flex;

      .oneboxleft {
        flex-grow: 3;
        margin: 8px 0 8px 0;
      }
    }

    .onefooter {
      height: 48px;
      margin: 0 8px;
      background: #ffffff;
      border-radius: 8px 8px 8px 8px;
      display: flex;
      align-items: center;
      justify-content: right;
      padding: 0 14px;
    }
  }
}
</style>
