<template>
  <el-dialog :lock-scroll="false" class="escDialog" @close="close" width="1000">
    <!-- 添加查询输入框和查询按钮 -->
    <div style="margin-bottom: 16px">
      <sl-form
        ref="slFormRef"
        :options="formOptions"
        :model-value="formModel"
        style="overflow: hidden"
      >
      </sl-form>
    </div>
    <vlanList :query-params="queryParams" @currentChange="currentChange" />
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="close">取消</el-button>
        <el-button type="primary" :disabled="confirmDisabled" @click="confirm"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="tsx" setup name="ecsDailog">
import { computed, reactive, ref } from 'vue'
import vlanList from './vlanList.vue'
// import SlDialog from '@/components/SlDialog/index.vue'
import SlForm from '@/components/form/SlForm.vue'

const props = defineProps({
  regionCode: {
    type: String,
    required: true,
  },
})

const formModel = reactive<{ [key: string]: any }>({
  type: '',
})

const formOptions = reactive([
  {
    style: 'margin-top: 0;margin-bottom: 0;padding:0',
    gutter: 10,
    groupItems: [
      {
        label: '类型',
        type: 'select',
        key: 'type',
        options: [
          {
            label: '管理面-硬管',
            value: '管理面-硬管',
          },
          {
            label: '管理面-基础设施',
            value: '管理面-基础设施',
          },
          {
            label: '管理面-业务',
            value: '管理面-业务',
          },
          {
            label: '管理面-存储',
            value: '管理面-存储',
          },
          {
            label: '业务面-业务',
            value: '业务面-业务',
          },
          {
            label: '业务面-QinQ外层',
            value: '业务面-QinQ外层',
          },
          {
            label: '业务面-互联',
            value: '业务面-互联',
          },
          {
            label: '存储面-前端',
            value: '存储面-前端',
          },
          {
            label: '存储面-后端',
            value: '存储面-后端',
          },
          {
            label: '未规划',
            value: '未规划',
          },
        ],
        span: 16,
      },
      {
        span: 2,
        render() {
          return (
            <el-button style="margin-top: 0px;" type="primary" onClick={handleSearch}>
              查 询
            </el-button>
          )
        },
      },
    ],
  },
])

let currentRow = ref(null)

const queryParams = ref<any>({
  regionCode: props.regionCode,
})
// 定义查询方法
const handleSearch = () => {
  // 这里可以添加查询逻辑，例如触发 dataList 的查询方法

  queryParams.value = {
    ...queryParams.value,
    ...formModel,
  }
}

const emit = defineEmits(['update:modelValue', 'confirm', 'dialogClose'])

const currentChange = (row: any) => {
  currentRow.value = row
}

const confirmDisabled = computed(() => !currentRow.value)

const close = () => {
  emit('update:modelValue', false)
  emit('dialogClose')
}

const confirm = () => {
  emit('update:modelValue', false)
  emit('confirm', { row: currentRow.value })
}
</script>

<style>
.escDialog .el-dialog__body {
  padding: 8px 0 0 0;
}
</style>
