<template>
  <SlProTable
    ref="proTable"
    highlight-current-row
    :columns="columns"
    style="min-height: 300px"
    :request-api="getCloudPortList"
    :init-param="queryParams"
    :current-change="currentChange"
    hidden-table-header
    row-key="id"
  >
  </SlProTable>
</template>
<script setup lang="tsx" name="DataList">
import { ref, type VNode, computed } from 'vue'
import type { ColumnProps, ProTableInstance } from '@/components/SlProTable/interface'
import { getCloudPortList, deleteCloudPort } from '@/api/modules/resourecenter'
import { ElMessageBox, ElMessage } from 'element-plus'
import { View } from '@element-plus/icons-vue'

defineProps({
  queryParams: {
    type: Object,
    default: () => ({}),
  },
})

const emit = defineEmits(['currentChange', 'selected'])

const currentChange = (currentRow: any, oldCurrentRow: any) => {
  emit('currentChange', currentRow, oldCurrentRow)
}
const handleShowPeerPassword = (row: any) => {
  row.showPeerPassword = !row.showPeerPassword
}

// 动态计算列配置
const columns = computed(() => {
  const baseColumns: ColumnProps<any>[] = [
    { type: 'index', label: '序号', width: 55, fixed: 'left' },
    { prop: 'cloudPortName', label: '云端口名称', width: 150, fixed: 'left' },
    { prop: 'businessSystemName', label: '业务系统', width: 150 },
    { prop: 'catalogueDomainName', label: '云类型', width: 150 },
    { prop: 'platformName', label: '云平台', width: 150 },
    { prop: 'regionName', label: '资源池', width: 150 },
    { prop: 'azName', label: '可用区', width: 150 },
    { prop: 'vpcName', label: 'VPC名称', width: 150 },
    { prop: 'vlanId', label: 'VLAN ID', width: 150 },
    { prop: 'srcIp', label: '本段地址', width: 150 },
    { prop: 'peerIp', label: 'GM2地址', width: 150 },
    {
      prop: 'peerPassword',
      label: 'BGP密钥',
      width: 250,
      render: ({ row }: any) => {
        // 小眼睛切换展示 ****
        const show = row.showPeerPassword
        return (
          <div style="display: flex; align-items: center; justify-content: center">
            <span style={{ height: show ? '' : '16px' }}>
              {show ? row.peerPassword : row.peerPassword.replace(/./g, '*')}
            </span>
            <el-icon
              style="cursor: pointer;margin-left: 10px"
              onClick={() => handleShowPeerPassword(row)}
            >
              <View />
            </el-icon>
          </div>
        )
      },
    },
    { prop: 'createTime', label: '创建时间', width: 150 },
    {
      prop: 'operation',
      label: '操作',
      width: 100,
      fixed: 'right',
      render: operationRender,
    },
  ]
  return baseColumns
})

// 普通操作列渲染函数
function operationRender({ row }: any): VNode {
  return (
    <el-button onClick={() => handleDelete(row)} type="danger" link>
      删除
    </el-button>
  )
}

const proTable = ref<ProTableInstance>()

// 删除虚拟网卡
const handleDelete = async (row: any) => {
  ElMessageBox.confirm(`确定要删除云端口 "${row.cloudPortName}" 吗？`, '删除确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      try {
        const res = await deleteCloudPort({
          id: row.id,
        })
        if (res.code == 200) {
          ElMessage.success('删除成功')
          proTable.value?.getTableList()
        }
      } catch (error) {
        console.error('删除云端口失败', error)
      }
    })
    .catch(() => {
      // 取消删除
    })
}

defineExpose({
  proTable,
})
</script>
