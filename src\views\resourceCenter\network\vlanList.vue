<template>
  <SlProTable
    highlight-current-row
    :columns="columns"
    style="min-height: 300px"
    :request-api="vlanList"
    :init-param="queryParams"
    :current-change="currentChange"
    hidden-table-header
    row-key="instanceId"
  >
  </SlProTable>
</template>
<script setup lang="tsx" name="dataList">
import { reactive, ref } from 'vue'
import type { ColumnProps } from '@/components/SlProTable/interface'
import { vlanList } from '@/api/modules/resourecenter'
const { queryParams } = defineProps<{
  queryParams?: any
}>()
const emit = defineEmits(['currentChange'])
const currentChange = (currentRow: any, oldCurrentRow: any) => {
  radioValue.value = currentRow.instanceId
  emit('currentChange', currentRow, oldCurrentRow)
}
const radioValue = ref()
// 表格配置项
const columns = reactive<ColumnProps<any>[]>([
  {
    render: ({ row }) => {
      return <el-radio modelValue={radioValue.value} value={row.instanceId} size="large"></el-radio>
    },
    label: '选择',
    width: 55,
  },
  { prop: 'vlan', label: 'vlan值', width: 120 },
  { prop: 'type', label: '类型', width: 120 },
  { prop: 'relatedPool', label: '资源池' },
])
</script>
