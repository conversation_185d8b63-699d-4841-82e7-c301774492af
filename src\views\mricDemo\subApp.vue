<template>
  <div class="sub-app-container">
    <h2>子应用示例</h2>
    <div class="props-display">
      <h3>从主应用获取的数据：</h3>
      <div class="prop-item">
        <span class="label">token:</span>
        <span class="value">{{ wujieProps.data.token }}</span>
      </div>
      <el-button @click="handleClick">工单审批</el-button>
      <el-button @click="handleClick2">登录失效</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

// 声明 window.$wujie 的类型
declare global {
  interface Window {
    $wujie: {
      props: {
        data: {
          token: string
          router: any
          logOut: () => void
        }
      }
    }
  }
}

// 使用 ref 存储 props 数据
const wujieProps = ref({
  data: {
    token: '',
    router: undefined as any,
    logOut: undefined as (() => void) | undefined,
  },
})
const handleClick = () => {
  console.log('window.$wujie', window.$wujie)
  // 示例1：跳转
  window.$wujie.props.data.router.push('/workOrder')
}
const handleClick2 = () => {
  // 示例2：登录失效
  window.$wujie.props.data.logOut()
}
onMounted(() => {
  console.log('window.$wujie', window.$wujie)
  // 在组件挂载时获取 wujie props
  wujieProps.value = {
    data: window.$wujie.props.data,
  }
  // 打印获取到的数据
  console.log('获取到的 props 数据:', window.$wujie.props)
})
</script>

<style scoped>
.sub-app-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.props-display {
  margin-top: 20px;
  padding: 20px;
  border: 1px solid #eee;
  border-radius: 8px;
  background-color: #f9f9f9;
}

.prop-item {
  margin: 10px 0;
  display: flex;
  gap: 10px;
}

.label {
  font-weight: bold;
  color: #666;
  min-width: 80px;
}

.value {
  color: #333;
}

h2,
h3 {
  color: #333;
  margin-bottom: 16px;
}

h3 {
  font-size: 1.1em;
  color: #666;
}
</style>
