<template>
  <div
    @click="() => handleClick(menuItem.id)"
    v-if="menuItem.isLeaf"
    class="leaf menu-item"
    :class="{ active: menuItem.id === activeIndex }"
  >
    <el-icon class="icon" v-if="menuItem.icon"><component :is="menuItem.icon" /></el-icon>
    <span class="title">{{ menuItem.title }}</span>
  </div>
  <template v-else>
    <div class="group border-box">
      <div class="group-header">
        <div class="medium menu-item">
          <el-icon class="icon" v-if="menuItem.icon"><component :is="menuItem.icon" /></el-icon>
          <span class="title">{{ menuItem.title }}</span>
        </div>
      </div>

      <div class="child">
        <sl-group-menu-item
          :key="subItem.id"
          :active-index="activeIndex"
          v-for="subItem in menuItem.children"
          :menu-item="subItem"
          :router="router"
          @menu-item-click="handleMenuItemClick"
        />
      </div>
    </div>
  </template>
</template>

<script setup lang="ts">
import SlGroupMenuItem from './SlGroupMenuItem.vue'
import { useRouter } from 'vue-router'
const routerIns = useRouter()

const props = defineProps({
  menuItem: {
    type: Object,
    required: true,
  },
  activeIndex: {
    type: String,
    required: false,
  },
  router: {
    type: Boolean,
    required: false,
    default: false,
  },
})
const emit = defineEmits(['menuItemClick'])

const handleClick = (id: string) => {
  console.log(props.router)

  if (props.router) {
    routerIns.push(`/${id}`)
  }
  emit('menuItemClick', id)
}

const handleMenuItemClick = (id: string) => {
  emit('menuItemClick', id)
}
</script>

<style scoped lang="scss">
.icon {
  margin-right: 12px;
  color: var(--el-color-primary);
}
.child {
  border-top: 0 !important;
  border-radius: 0 0 6px 6px;
  color: #000c1f;
}
.group-header {
  border-radius: 6px 6px 0 0;
  color: #000c1f;
  border-bottom: 1px solid rgba(50, 60, 76, 0.1);
}
.group {
  border-radius: 6px;
  border: 1px solid rgba(50, 60, 76, 0.1);
  margin-bottom: 16px;
}
.border-box {
  border: 1px solid rgba(50, 60, 76, 0.1);
}
.menu-item {
  padding: 6px 12px;
  margin: 8px;
  display: flex;
  align-items: center;
  font-size: 14px;
  &.leaf {
    cursor: pointer;
  }
  &.leaf:hover {
    background: #3361ff;
    color: #fff;
    border-radius: 4px;
    .icon {
      color: #fff;
    }
  }
  &.active {
    background: #3361ff;
    color: #fff;
    border-radius: 4px;
    .icon {
      color: #fff;
    }
  }
}
</style>
