<template>
  <div>
    <div class="tab-item">
      <template v-if="networkForms.length">
        <div v-for="(form, index) in networkForms" :key="index" class="network-form-container">
          <SingleNetworkForm :ref="(ref) => setRefs(ref, form)" :plane="plane" />
          <el-icon
            v-if="['create', 'createWithOrder'].includes(opType)"
            @click="removeNetwork(index)"
            class="delete-icon"
            :size="24"
          >
            <CircleCloseFilled />
          </el-icon>
        </div>
      </template>
      <el-empty v-else description="暂无网络配置" :image-size="200">
        <el-button v-if="opType !== 'view'" type="primary" @click="addNetwork">
          添加网络配置
        </el-button>
      </el-empty>
    </div>
    <div style="padding: 8px" v-if="networkForms.length">
      <el-button
        v-if="['create', 'createWithOrder'].includes(opType)"
        :icon="CirclePlus"
        style="width: 100%"
        type="primary"
        @click="addNetwork"
      >
        增加网络
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import SingleNetworkForm from './singleNetworkForm.vue'
import { CirclePlus, CircleCloseFilled } from '@element-plus/icons-vue'
import { ref, watch, inject, type Ref } from 'vue'
import type { IBaseFormProvider } from './types'

const baseFormProvider = inject<Ref<IBaseFormProvider>>('baseFormProvider')!
const opType = baseFormProvider.value.opType
const props = defineProps<{
  plane: string
  tabItem: any
  networkId: string
}>()
function setTabCount(tabItem: any, count: number) {
  tabItem.count = count
}
const setRefs = (ref: any, form: any) => {
  form.ref = ref
}
const networkForms = ref<{ id: number; ref: null }[]>([])
baseFormProvider?.value.collectSubmits(networkForms)
watch(
  () => networkForms.value.length,
  (newVal) => {
    setTabCount(props.tabItem, newVal)
  },
)

const addNetwork = () => {
  networkForms.value.push({ id: networkForms.value.length + 1, ref: null })
}

if (props.networkId) addNetwork()

const removeNetwork = (index: number) => {
  networkForms.value.splice(index, 1)
}
</script>

<style scoped>
.network-form-container {
  position: relative;
  margin-bottom: 16px;
}

.network-form-container:hover {
  position: relative;
  margin-bottom: 16px;
  .delete-icon {
    display: block;
  }
}

.delete-icon {
  position: absolute;
  top: 2px;
  right: 2px;
  font-size: 20px;
  cursor: pointer;
  z-index: 1;
  color: var(--el-color-danger);
  display: none;
}

.delete-icon:hover {
  opacity: 0.8;
}

:deep(.el-empty) {
  padding: 40px 0;
}
</style>
