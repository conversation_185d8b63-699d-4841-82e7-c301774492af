<template>
  <div id="SlDialog">
    <el-dialog
      v-model="visible"
      :title="title"
      :width="width"
      :top="top"
      :close-on-click-modal="closeOnClickOverlay"
      :show-close="showClose"
      :modal="showOverlay"
      :before-close="handleClose"
      v-bind="$attrs"
    >
      <template #header>
        <slot name="header">
          <span>{{ title }}</span>
        </slot>
      </template>
      <slot></slot>
      <template #footer>
        <slot name="footer">
          <el-button @click="close" v-if="showCancel">
            {{ cancelText }}
          </el-button>
          <el-button type="primary" @click="handleConfirm" v-if="showConfirm">
            {{ confirmText }}
          </el-button>
        </slot>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { ElMessageBox } from 'element-plus'

defineOptions({
  inheritAttrs: false,
})

interface Props {
  modelValue?: boolean
  title?: string
  width?: string
  top?: string
  showOverlay?: boolean
  showClose?: boolean
  closeOnClickOverlay?: boolean
  showCancel?: boolean
  cancelText?: string
  showConfirm?: boolean
  confirmText?: string
  closeConfirm?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  title: '弹窗标题',
  width: '50%',
  top: '15vh',
  showOverlay: true,
  showClose: true,
  closeOnClickOverlay: false,
  showCancel: true,
  cancelText: '取消',
  showConfirm: true,
  confirmText: '确定',
  closeConfirm: false,
})

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void
  (e: 'open'): void
  (e: 'close'): void
  (e: 'confirm'): void // 添加 confirm 事件
}>()

const visible = ref(props.modelValue)

watch(
  () => props.modelValue,
  (newValue) => {
    visible.value = newValue
  },
)

watch(visible, (newValue) => {
  emit('update:modelValue', newValue)
})

const close = () => {
  visible.value = false
  emit('close')
}

const handleClose = (done: () => void) => {
  if (props.closeConfirm === false) {
    close()
    return
  }
  ElMessageBox.confirm('请确认是否关闭弹窗').then(() => {
    done()
    close()
  })
}

const handleConfirm = () => {
  emit('confirm') // 触发 confirm 事件
}
</script>
