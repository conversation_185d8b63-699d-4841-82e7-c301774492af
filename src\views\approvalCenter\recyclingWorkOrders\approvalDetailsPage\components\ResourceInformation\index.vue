<template>
  <div>
    <div class="sl-card mb8 no-card" v-if="recyclingsTabs.length">
      <sl-block-title title="回收资源"> </sl-block-title>
      <div class="orderDetailTab">
        <sl-base-tabs
          class="mb10"
          show-count
          :tabs="recyclingsTabs"
          v-model="recyclingType"
        ></sl-base-tabs>
      </div>
      <div class="search-form mt-20 mb20 ml20">
        <template v-if="pageStatus">
          <template v-if="btnAuth?.operation_group">
            <el-button type="primary" @click="() => bulkRecycling()">
              <el-icon><RefreshRight /></el-icon>批量回收
            </el-button>
            <el-button
              v-if="recyclingType === 'ecs'"
              type="primary"
              @click="() => batchRejection()"
            >
              <el-icon><RefreshLeft /></el-icon> 批量退维
            </el-button>
          </template>
          <el-button v-if="btnAuth?.tenant_task" type="primary" @click="() => batchConfirmation()">
            <el-icon><Check /></el-icon>批量确认
          </el-button>
        </template>
        <el-button @click="funcexport" type="primary">
          <el-icon class="el-icon--left"><Upload /></el-icon>批量导出
        </el-button>
      </div>
      <div v-if="['vpc', 'network'].includes(recyclingType)" class="tip">
        根据当前选择的所需回收资源，以下网段下所有IP都将释放，其网段也会回收释放
      </div>
      <template v-for="item in recyclingsTabs" :key="item.label">
        <template v-if="item.name === recyclingType">
          <SlProTable
            :key="item.name"
            :ref="(el) => setProTableRef(el as unknown as ProTableInstance, item.name)"
            :data="item.list"
            :columns="resourcesColumns(item.name)"
            :pagination="false"
            row-key="id"
          >
          </SlProTable>
        </template>
      </template>
    </div>
    <div v-if="['vpc', 'network'].includes(recyclingType)" class="sl-card">
      <sl-block-title title="防火墙策略"> </sl-block-title>
      <div class="tip">
        以上回收网络所涉及到的防火墙策略也将删除
        <div class="tip-message">
          温馨提示:根据所需回收的资源，如网络下的IP全部释放则网络也将回收，
          网络回收后防火墙策略也会同步删除;如您不想删除某个网络或防火墙策略可移除资源的回收
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="tsx">
import { inject, nextTick, ref, type VNode } from 'vue'
import { RefreshRight, RefreshLeft, Check, Upload } from '@element-plus/icons-vue'
import type {
  RecyclingBtnsType,
  RecyclingProTableType,
  RecyclingTabsType,
  RecyclingType,
} from '../../../interface/type'
import recyclingAllColumns, {
  decommissionStatusList,
  indexColumn,
  messageColumn,
  recoveryStatusList,
  recyclingValueEnum,
} from './goodsColumns'
import type { ColumnProps, ProTableInstance } from '@/components/SlProTable/interface'
import { showTips } from '@/utils'
import {
  networkRecycleApi,
  recoveryCheckNetworkApi,
  recoveryHandoverApi,
  recoveryTentantConfirmrApi,
  recycleResourceApi,
  resourceExportApi,
} from '@/api/modules/approvalCenter'
import { useDownload } from '@/hooks/useDownload'

const pageStatus = inject('pageStatus', ref(true))
const btnAuth = inject('btnAuth', ref<RecyclingBtnsType>())
const workOrderId = inject('workOrderId', '')

const emit = defineEmits(['refresh', 'update:disabledBtn'])

const proTable = ref<RecyclingProTableType>({
  ecs: null,
  gcs: null,
  evs: null,
  obs: null,
  slb: null,
  nat: null,
  vpc: null,
  network: null,
  eip: null,
})

const setProTableRef = (el: ProTableInstance | null, name: RecyclingType) => {
  if (el) {
    proTable.value[name] = el
  }
}

const recyclingType = ref<RecyclingType>('ecs')
const recyclingsTabs = ref<RecyclingTabsType[]>([])

const initData = (data: any, flag: boolean = true) => {
  const tabs: RecyclingTabsType[] = []
  recyclingValueEnum.forEach((item) => {
    if (data[item.goodsList] && data[item.goodsList].length) {
      tabs.push({
        label: item.desc,
        name: item.code as RecyclingType,
        count: data[item.goodsList].length,
        list: data[item.goodsList],
      })
    }
  })
  recyclingsTabs.value = tabs
  flag && (recyclingType.value = tabs[0]?.name || 'ecs')
  nextTick(() => {
    btnAuth.value?.operation_group && disabledResource(tabs)
    btnAuth.value?.tenant_task && disabledTenant(tabs)
  })
}
// 判断是否禁用
const disabledResource = (tabs: RecyclingTabsType[]) => {
  if (!tabs.length) return true
  let falg = tabs.every((goodItem) => {
    return goodItem.list.every((item) => item.recoveryStatus == '3')
  })

  emit('update:disabledBtn', !falg)
}

// 判断是否禁用
const disabledTenant = (tabs: RecyclingTabsType[]) => {
  if (!tabs.length) return true
  let falg = tabs.every((goodItem) => {
    return goodItem.list.every((item) => item.tenantConfirm)
  })

  emit('update:disabledBtn', !falg)
}
//批量回收
/**
 * 回收函数 -资源的
 *
 */

const bulkRecycling = async () => {
  const isSelected = proTable.value?.[recyclingType.value]?.isSelected
  if (!isSelected) {
    return showTips('请先选择要回收的资源')
  }
  const ids = proTable.value?.[recyclingType.value]?.selectedListIds
  // 批量回收
  await recycleFn({ recoveryType: recyclingType.value, recoveryIdList: ids })
  proTable.value?.[recyclingType.value]?.clearSelection()
}

const ecsVerification = (params: any) => {
  return recyclingsTabs.value
    .find((itme) => itme.name === 'ecs')
    ?.list.filter((item) => params.recoveryIdList.includes(item.id))
    .every((item) => item.hcmStatus === 'success')
}

const recycleFn = async (params: any) => {
  // 云主机校验
  if (params.recoveryType === 'ecs' && !ecsVerification(params)) {
    return showTips('云主机回收前请先完成退维操作')
  }
  await ElMessageBox.confirm('确定回收选中的资源吗？', '提示', {
    type: 'warning',
    confirmButtonText: '确 认',
    cancelButtonText: '取 消',
  })
  // 网络的回收 --- 需要先校验网络是否可以回收
  if (['vpc', 'network'].includes(params.recoveryType)) {
    const { entity } = await recoveryCheckNetworkApi({ ...params, workOrderId })
    const ids = entity.filter((item: any) => !item.pass).map((item: any) => item.id)
    if (ids.length) {
      const names = recyclingsTabs.value
        ?.find((item) => item.name === params.recoveryType)
        ?.list.filter((item: any) => ids.includes(item.id))
        ?.map((item: any) => item.vpcName ?? item.networkName)
        .join(',')
      return showTips(names + '网络下存在未回收的资源，请先回收资源')
    }
  }

  // 发起回收
  ;['vpc', 'network'].includes(params.recoveryType)
    ? await networkRecycleApi(
        params.recoveryIdList.map((item: string) => ({ id: item, type: params.recoveryType })),
      )
    : await recycleResourceApi({ ...params, workOrderId })
  ElMessage.success('已提交回收')
  emit('refresh')
}

//批量退维
const batchRejection = async () => {
  const isSelected = proTable.value?.[recyclingType.value]?.isSelected
  if (!isSelected) {
    return showTips('请先选择要退维的资源')
  }
  const arr = proTable.value?.[recyclingType.value]?.selectedList
  const ids = arr
    ?.filter((item: any) => ['wait', 'fail'].includes(item.hcmStatus))
    .map((item: any) => item.id)
  if (ids?.length !== arr?.length) return showTips('只有退维状态为“失败,待退维”的资源才能退维')
  // 批量退维
  await recoveryHandover({ recoveryType: recyclingType.value, recoveryIdList: ids })
  proTable.value?.[recyclingType.value]?.clearSelection()
}

const recoveryHandover = async (params: any) => {
  await ElMessageBox.confirm('确定退维选中的资源吗？', '提示', {
    type: 'warning',
    confirmButtonText: '确 认',
    cancelButtonText: '取 消',
  })
  await recoveryHandoverApi({ ...params, workOrderId })
  ElMessage.success('已提交退维')
  emit('refresh')
}

//批量确认
const batchConfirmation = async () => {
  const isSelected = proTable.value?.[recyclingType.value]?.isSelected
  if (!isSelected) {
    return showTips('请先选择要确认的资源')
  }
  const ids = proTable.value?.[recyclingType.value]?.selectedListIds

  // 批量确认
  await tenantConfirmFn({ recoveryType: recyclingType.value, recoveryIdList: ids })
  proTable.value?.[recyclingType.value]?.clearSelection()
}

const tenantConfirmFn = async (params: any) => {
  await ElMessageBox.confirm('确定确认选中的资源吗？', '提示', {
    type: 'warning',
    confirmButtonText: '确 认',
    cancelButtonText: '取 消',
  })
  await recoveryTentantConfirmrApi({ ...params, workOrderId })
  ElMessage.success('确认成功')
  emit('refresh')
}

// 选择退回的选择
const selectionColumn: ColumnProps = {
  type: 'selection',
  width: 55,
  fixed: 'left',
  selectable: (row: any) => {
    if (btnAuth.value?.tenant_task) return !row.tenantConfirm
    return !['3', '2'].includes(row.recoveryStatus)
  },
}
// 退维状态
const disDimensionColumn: ColumnProps = {
  prop: 'hcmStatus',
  label: '退维状态',
  align: 'center',
  width: '100px',
  fixed: 'right',
  render: ({ row }: { row: any }): VNode => {
    let obj = {
      running: 'warning',
      success: 'success',
      fail: 'danger',
      wait: 'primary',
    }
    const type = obj[row.hcmStatus as keyof typeof obj] ?? 'primary'
    return (
      <el-text type={type} underline={false}>
        {decommissionStatusList.find((item) => item.value === row.hcmStatus)?.label ?? '--'}
      </el-text>
    )
  },
}
// 回收状态
const recoveryStatusColumn: ColumnProps = {
  prop: 'recoveryStatus',
  label: '回收状态',
  align: 'center',
  width: '100px',
  fixed: 'right',
  render: ({ row }: { row: any }): VNode => {
    let obj = {
      1: 'primary',
      2: 'warning',
      3: 'success',
      4: 'danger',
    }
    const type = obj[row.recoveryStatus as keyof typeof obj] ?? 'primary'
    return (
      <el-text o type={type} underline={false}>
        {recoveryStatusList.find((item) => item.value === row.recoveryStatus)?.label ?? '--'}
      </el-text>
    )
  },
}

// 回收按钮 @param flag 是否是资源 默认为资源 false 为网络
const operationColumns = (recyclingType: RecyclingType): ColumnProps => {
  return {
    prop: 'operation',
    label: '操作',
    align: 'center',
    fixed: 'right',
    width: recyclingType === 'ecs' ? '150px' : '100px',
    render: ({ row }: { row: any; $index: number }): VNode => {
      return (
        <>
          {recyclingType === 'ecs' && btnAuth.value?.operation_group && (
            <el-button
              onclick={() =>
                recoveryHandover({
                  recoveryType: recyclingType,
                  recoveryIdList: [row.id],
                })
              }
              disabled={['success', 'running'].includes(row.hcmStatus)}
              type="primary"
              link
            >
              退维
            </el-button>
          )}
          {btnAuth.value?.operation_group && (
            <el-button
              disabled={['3', '2'].includes(row.recoveryStatus)}
              onclick={() =>
                recycleFn({
                  recoveryType: recyclingType,
                  recoveryIdList: [row.id],
                })
              }
              type="primary"
              link
            >
              {row.recoveryStatus == '4' ? '重新回收' : '回收'}
            </el-button>
          )}
          {btnAuth.value?.tenant_task && (
            <el-button
              onclick={() =>
                tenantConfirmFn({
                  recoveryType: recyclingType,
                  recoveryIdList: [row.id],
                })
              }
              disabled={row.tenantConfirm}
              type="primary"
              link
            >
              {row.tenantConfirm ? '已确认' : '确认'}
            </el-button>
          )}
        </>
      )
    },
  }
}

const resourcesColumns = (name: RecyclingType) => {
  const columns: ColumnProps[] = [...recyclingAllColumns[`${name}`]]
  columns.unshift(indexColumn)
  if (btnAuth.value?.operation_group) {
    columns.push(messageColumn)
    name === 'ecs' && columns.push(disDimensionColumn)
    columns.push(recoveryStatusColumn)
    pageStatus.value && columns.push(operationColumns(name))
    pageStatus.value && columns.unshift(selectionColumn)
  }
  if (btnAuth.value?.tenant_task && pageStatus.value) {
    columns.push(operationColumns(name))
    columns.unshift(selectionColumn)
  }
  return columns
}
const validateForm = async () => {
  return true
}
const submitForm = async () => {
  return {}
}

/**
 * @description: 导出
 */
const funcexport = async () => {
  const params: any = {
    workOrderId: workOrderId,
    productType: recyclingType.value,
  }

  const temName =
    recyclingsTabs.value.find((item) => item.name === recyclingType.value)?.label + '数据.xlsx'
  useDownload(resourceExportApi, temName, params)
}

defineExpose({
  initData,
  validateForm,
  submitForm,
})
</script>

<style lang="scss" scoped>
.orderDetailTab {
  background-color: #f2f3f5;
  position: relative;
  &::before {
    content: '';
    display: inline-block;
    position: absolute;
    left: 5px;
    top: 11px;
    width: 4px;
    z-index: 10;
    background-color: var(--el-color-primary);
    margin-right: 10px;
    height: 0.78em;
    font-size: 18px;
  }
}
.tip {
  font-size: 14px;
  line-height: 30px;
  color: red;
}
.tip-message {
  color: #000;
}
</style>
