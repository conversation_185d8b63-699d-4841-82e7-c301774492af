import { ref } from 'vue'

// 定义 JSONP 请求的返回类型
interface JsonpResponse<T> {
  data: T
  success: boolean
}

// 自定义 Hook
export function useJsonp<T>(url: string, callbackName: string) {
  // 定义响应式变量
  const data = ref<T | null>(null)
  const loading = ref(false)
  const error = ref<Error | null>(null)

  // 执行 JSONP 请求
  const fetchJsonp = () => {
    loading.value = true
    error.value = null

    // 创建一个全局回调函数
    ;(window as any)[callbackName] = (response: JsonpResponse<T>) => {
      if (response.success) {
        data.value = response.data
      } else {
        error.value = new Error('JSONP request failed')
      }
      loading.value = false

      // 请求完成后移除 script 标签和回调函数
      delete (window as any)[callbackName]
      document.body.removeChild(script)
    }

    // 创建 script 标签
    const script = document.createElement('script')
    script.src = `${url}?callback=${callbackName}`
    script.async = true
    script.onerror = () => {
      error.value = new Error('JSONP script error')
      loading.value = false
    }

    // 将 script 标签添加到 body 中
    document.body.appendChild(script)
  }

  return {
    data,
    loading,
    error,
    fetchJsonp,
  }
}
