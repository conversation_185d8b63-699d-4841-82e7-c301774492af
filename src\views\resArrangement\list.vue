<template>
  <div class="table-box">
    <sl-page-header
      title="产品组合编排"
      title-line="产品组合编排"
      :icon="{
        Vnode: Platform,
        color: '#0052D9',
        size: '50px',
      }"
    >
    </sl-page-header>
    <div class="btn op" style="margin-top: 8px">
      <el-button @click="handleOpenDrawer" type="primary"> 新建产品组合编排模板 </el-button>
    </div>
    <div class="filter-form-con">
      <sl-form
        class="filter-form"
        :class="{ collapsed: collapsed }"
        ref="formRef"
        :options="formOptions"
        v-model="formModel"
      >
      </sl-form>
    </div>
    <div class="table-layout">
      <DataList ref="dataListRef" :query-params="queryParams"></DataList>
    </div>

    <!-- 证书创建抽屉 -->
    <el-drawer
      v-model="drawerVisible"
      :title="editMode ? '编辑产品组合编排模板' : '新建产品组合编排模板'"
      size="50%"
      :destroy-on-close="true"
      :before-close="handleCloseDrawer"
    >
      <sl-form
        ref="resArrangementFormRef"
        v-model="resArrangementForm"
        :options="resArrangementFormOptions"
        label-width="160px"
        class="resArrangement-form"
      >
      </sl-form>
      <div class="drawer-footer">
        <el-button @click="handleCloseDrawer">取消</el-button>
        <el-button type="primary" @click="handleSubmit">下一步</el-button>
      </div>
    </el-drawer>
  </div>
</template>

<script lang="tsx" setup>
declare global {
  interface Window {
    refreshParent: (() => void) | null
  }
}

import { onUnmounted, reactive, ref } from 'vue'
import { Delete, Search, ArrowDown, ArrowUp, Platform } from '@element-plus/icons-vue'
import SlPageHeader from '@/components/SlPageHeader/index.vue'
import slForm from '@/components/form/SlForm.vue'
import DataList from '@/views/resArrangement/components/DataList.vue'
import ConditionFilter from '@/views/resourceCenter/conditionFilter.vue'
import type { FormInstance } from 'element-plus'

const formRef = ref<any>(null)
const queryParams = ref<any>({})

const formModel = reactive({})

const dataListRef = ref<any>(null)

function reset() {
  formRef.value!.resetFields()
  queryParams.value = { ...formModel }
}

function search() {
  queryParams.value = { ...formModel }
}
window.refreshParent = () => {
  search()
}

onUnmounted(() => {
  window.refreshParent = null
})
// 是否默认折叠搜索项
const collapsed = ref(true)

// 抽屉相关
const drawerVisible = ref(false)
const resArrangementFormRef = ref<FormInstance>()
const editMode = ref(false) // 是否为编辑模式

// 表单数据
const resArrangementForm = reactive({
  id: '', // 产品组合编排ID，编辑模式下使用
  name: '', // 产品组合编排名称
  description: '', // 产品组合编排描述
})

// 表单配置
const resArrangementFormOptions = reactive([
  {
    groupItems: [
      {
        label: '产品组合编排名称',
        type: 'input',
        key: 'name',
        span: 24,
        rules: [{ required: true, message: '请输入产品组合编排名称', trigger: 'blur' }],
      },
      {
        label: '产品组合编排描述',
        type: 'input',
        key: 'description',
        span: 24,
        rules: [{ required: true, message: '请输入产品组合编排描述', trigger: 'blur' }],
        props: {
          type: 'textarea',
          rows: 4,
        },
      },
    ],
  },
])

// 表单配置
const formOptions = reactive([
  {
    style: 'padding: 0',
    groupItems: [
      {
        label: '模板名称',
        type: 'input',
        key: 'name',
        span: 8,
        disabled: true,
        hidden: false,
        defaultSelect: true,
      },
      {
        label: '创建人',
        type: 'input',
        key: 'creator',
        span: 8,
        disabled: true,
        hidden: false,
        defaultSelect: true,
      },
      {
        span: 8,
        render() {
          return (
            <div style="display: flex;justify-content: flex-end;">
              <el-button type="primary" link>
                <ConditionFilter
                  formModel={formModel}
                  resourceList={formOptions[0].groupItems}
                ></ConditionFilter>
              </el-button>
              <el-button onClick={reset} icon={<Delete />}>
                重置
              </el-button>
              <el-button onClick={search} icon={<Search />} type="primary">
                搜索
              </el-button>
              <el-button
                type="primary"
                link
                class="search-isOpen"
                onClick={() => (collapsed.value = !collapsed.value)}
              >
                {collapsed.value ? '展开' : '折叠'}
                <el-icon class="el-icon--right">
                  {collapsed.value ? <ArrowDown /> : <ArrowUp />}
                </el-icon>
              </el-button>
            </div>
          )
        },
      },
      {
        label: '创建时间',
        type: 'date',
        key: 'createTime',
        span: 8,
        disabled: false,
        hidden: true,
        props: {
          type: 'datetimerange',
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          rangeSeparator: '至',
          startPlaceholder: '开始时间',
          endPlaceholder: '结束时间',
        },
      },
    ],
  },
])

// 打开抽屉 - 新建模式
const handleOpenDrawer = () => {
  editMode.value = false
  resArrangementForm.id = ''
  resArrangementForm.name = ''
  resArrangementForm.description = ''
  drawerVisible.value = true
}

// 打开抽屉 - 编辑模式
const handleOpenEditDrawer = (row: any) => {
  editMode.value = true
  resArrangementForm.id = row.id
  resArrangementForm.name = row.name
  resArrangementForm.description = row.description
  drawerVisible.value = true
}

// 关闭抽屉
const handleCloseDrawer = () => {
  drawerVisible.value = false
  // 重置表单
  resArrangementForm.id = ''
  resArrangementForm.name = ''
  resArrangementForm.description = ''
  // 重置表单校验状态
  resArrangementFormRef.value?.resetFields()
}

// 提交表单
const handleSubmit = async () => {
  if (!resArrangementFormRef.value) return

  const valid = await resArrangementFormRef.value?.validate()
  if (valid) {
    // 构建查询参数
    const queryParams = new URLSearchParams()
    if (editMode.value) {
      queryParams.append('id', resArrangementForm.id)
    }
    queryParams.append('name', resArrangementForm.name)
    queryParams.append('description', resArrangementForm.description)
    // 使用window.open打开新页面
    window.open(`resArrangement?${queryParams.toString()}`, '_blank')
    drawerVisible.value = false
  }
}

// 暴露方法给子组件使用
defineExpose({
  handleOpenEditDrawer,
})
</script>

<style lang="scss" scoped>
.sl-table-content {
  overflow: auto !important;
}
.sl-page-content {
  // 减去头部的标题高度 - 不减去会超出屏幕
  overflow-x: hidden;
  height: calc(100% - 90px);
}
.header-search-input {
  width: 350px;
}
.table-layout {
  margin: 2px 8px 8px 8px;
  flex: 1;
  overflow: hidden;
}
.btn.op {
  margin: 8px 8px 0 8px;
}
.filter-form-con {
  margin: 8px 8px 0 8px;
  background: #fff;
  border-radius: 8px;
}
.filter-form.collapsed {
  height: 50px;
}
.filter-form {
  height: 110px;
  overflow: hidden;
  transition: height 0.2s;
}
.certificate-form {
  padding: 20px;
}
.drawer-footer {
  padding: 0 20px 20px 20px;
  text-align: right;
}
</style>
