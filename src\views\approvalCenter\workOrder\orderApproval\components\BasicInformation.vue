<template>
  <div>
    <div class="sl-card mb8">
      <sl-block-title>工单信息</sl-block-title>
      <sl-form
        :show-block-title="false"
        :label-width="140"
        ref="slFormRef"
        v-model="form"
        :options="options"
      >
      </sl-form>
    </div>
    <div class="sl-card mb8">
      <sl-block-title>工单附件信息</sl-block-title>
      <sl-form
        :show-block-title="false"
        :label-width="140"
        ref="slFormRef"
        v-model="form"
        :options="options2"
      >
      </sl-form>
    </div>
  </div>
</template>

<script setup lang="ts" name="BasicForm">
import { maskPhoneNumber } from '@/utils'
import { ref, watch, nextTick } from 'vue'

const props = defineProps({
  orderDesc: {
    type: Object,
    default: () => ({}),
  },
})

const rawForm = {
  departmentName: '',
  orderType: '',
  orderTitle: '',
  businessSystemName: '',
  bureauUserName: '',
  manufacturer: '',
  manufacturerContacts: '',
  manufacturerMobile: '',
  moduleName: '',
  orderDesc: '',
  businessDepartLeaderName: '',
  levelThreeLeaderName: '',
  createdUserName: '',
  resourceApplyFiles: [],
}
const form = ref(rawForm)

watch(
  () => props.orderDesc, // 监视 props.orderDesc.data 的变化
  (newData) => {
    nextTick(() => {
      Object.keys(form.value).forEach((key) => {
        // 只有当 `orderDesc.data` 有值时才进行赋值
        if (newData && newData[key] !== undefined) {
          form.value[key as keyof typeof rawForm] =
            key !== 'manufacturerMobile'
              ? newData[key]
              : newData[key]
                ? maskPhoneNumber(newData[key])
                : ''
        }
      })
    })
  },
  { immediate: true }, // 立即触发一次
)

const options = [
  {
    groupName: '工单信息',
    groupItems: [
      {
        label: '申请人',
        type: 'text',
        key: 'createdUserName',
        span: 8,
      },
      {
        label: '所属部门',
        type: 'text',
        key: 'departmentName',
        span: 8,
      },
      {
        label: '工单类型',
        type: 'text',
        key: 'orderType',
        span: 8,
      },
      {
        label: '标题',
        type: 'text',
        key: 'orderTitle',
        span: 8,
      },
      {
        label: '业务系统',
        type: 'text',
        key: 'businessSystemName',
        span: 8,
      },
      {
        label: '所属业务模块',
        type: 'text',
        key: 'moduleName',
        span: 8,
      },
      {
        label: '局方负责人',
        type: 'text',
        key: 'bureauUserName',
        span: 8,
      },
      {
        label: '厂家',
        type: 'text',
        key: 'manufacturer',
        span: 8,
      },
      {
        label: '厂家负责人',
        type: 'text',
        key: 'manufacturerContacts',
        span: 8,
      },
      {
        label: '电话',
        type: 'text',
        key: 'manufacturerMobile',
        span: 8,
      },

      {
        label: '二级业务部门领导',
        type: 'text',
        key: 'businessDepartLeaderName',
        span: 8,
      },
      {
        label: '三级业务部门领导',
        type: 'text',
        key: 'levelThreeLeaderName',

        span: 8,
      },
      {
        label: '资源申请说明',
        type: 'text',
        key: 'orderDesc',
        span: 16,
      },
    ],
  },
]

const options2 = [
  {
    groupName: '工单附件信息',
    groupItems: [
      {
        label: '资源上云说明书',
        type: 'upload',
        key: 'resourceApplyFiles',
        span: 24,
        props: {
          upload: {
            drag: false,
            fileType: 'RESOURCE_EXPLAIN',
            disabled: true,
            style: {
              'max-width': '500px',
            },
          },
        },
      },
    ],
  },
]
</script>
