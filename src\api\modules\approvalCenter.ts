import type {
  GetResourceListParamsType,
  GetResourceListResType,
  ResourceListType,
} from '@/views/approvalCenter/workOrder/interface/type'
import http from '@/api'
import { WOC } from '../config/servicePort'
import type { ResPage } from '../interface'
import type { RecyclingWorkorderType } from '@/views/approvalCenter/recyclingWorkOrders/interface/type'

// ------------------------工单管理-------------------------
/**
 * @name 统计工单批量导出
 */
export const batchExport = (config: any) =>
  http.get(WOC + '/order/excel/export', config, { responseType: 'blob', loading: true })

/**
 * @name 统计工单待审核、已审核数量
 */
export const getAuditCountApi = (config: any) =>
  http.post<any>(WOC + '/standardWorkorder/auditCount', config)

/**
 * @name 资源列表-工单查询
 */

export const getStandardWorkorderListApi = (config: GetResourceListParamsType) =>
  http.post<ResPage<ResourceListType>>(WOC + '/standardWorkorder/page', config, { loading: true })

/**
 * @name 资源列表-撤销
 */
export const standardWorkorderCancelApi = (workOrderId: string) =>
  http.post(WOC + '/standardWorkorder/cancel', { workOrderId }, { loading: true })

/**
 * @name 工单详情
 */
export const getStandardWorkorderDetailApi = (config: { workOrderId: string }) =>
  http.post<ResourceListType>(WOC + '/standardWorkorder/detail', config, { loading: true })

/**
 * @name 获取当前节点信息 和所有步骤点信息
 */
export const getTaskNodesApi = (config: { workOrderId: string; requestType: string }) =>
  http.get<GetResourceListResType>(WOC + '/activiti/getTaskNodes', config)

/**
 * @name 审批提交
 */
export const standardWorkorderAuditApi = (config: any) =>
  http.post(WOC + '/standardWorkorder/audit', config, { loading: true })

/**
 * @name 工单管理页面-网络开通状态前置 - vpc
 */
export const getVpcStatusApi = (orderId: string) =>
  http.get(WOC + '/vpc/getVpcStatus', { orderId }, { loading: true })

/**
 * @name 工单管理页面-网络开通状态前置 - 网络信息
 */
export const getNetworkStatusApi = (orderId: string) =>
  http.get(WOC + '/network/getNetworkStatus', { orderId }, { loading: true })

/**
 * @name 网络资源开通--实列规格字典
 */
export const getListFlavorApi = (config: any) =>
  http.post<FormDataType[]>(WOC + '/flavorModel/listFlavor', config)

/**
 * @name 网络资源开通 -- 获取可用区
 */
export const getQueryListApi = (config: any) =>
  http.get<FormDataType[]>(WOC + '/product/az/queryList', config)

/**
 * @name 获取网络资源开通 - -vpc网络信息
 */
export const getvpcTreeApi = (config: any) =>
  http.post<any>(WOC + '/vpc/vpcTree', config, { loading: true })

/**
 * @name 对公VPC
 */
export const getCorporateVpcTreeApi = (config: any) =>
  http.post<any>(WOC + '/vpc/corporateVpcTree', config, { loading: true })

/**
 * @name 获取网络资源开通 - network网络信息
 */
export const getNetworkTreeApi = (config: any) =>
  http.post<any>(WOC + '/network/networkTree', config, { loading: true })

/**
 * @name 网络资源开通
 */
export const standardWorkOrderResOpenApi = (config: any) =>
  http.post(WOC + '/standardWorkOrderResOpen/open', config, { loading: true })

/**
 * @name 检验资源池是否可以使用
 */
export const standardWorkOrderCapacityCheckApi = (config: any) =>
  http.post<string[]>(WOC + '/standardWorkorder/capacityCheck', config, { loading: true })

/**
 * @name 根据子网ID获取IP地址列表
 * @param config 包含子网ID和请求数量的配置对象
 * @returns 返回IP地址列表
 */
export const getAvailableIp = (config: {
  subnetId: string
  count: number
  filterIps?: string[]
}) => {
  return http.post<any>(WOC + '/network/getAvailableIp', config)
}

/**
 * @name 校验IP地址是否可用
 * @returns 返回校验结果
 */
export const validateIpAddress = (config: any) => {
  return http.post<any>(WOC + '/network/checkIpAvailable', config)
}

// ------------------------end------------------------

// ------------------------回收工单管理-------------------------
/**
 * @name 回收列表-导出
 */
export const recoveryExportApi = (config: any) =>
  http.post(WOC + '/recovery/workOrder/export', config, { responseType: 'blob', loading: true })

/**
 * @name 统计回收待审核、已审核数量
 */
export const getRecoveryWorkOrdersAuditCountApi = (config: any) =>
  http.post<any>(WOC + '/recovery/workOrder/auditCount', config)

/**
 * @name 回收列表-回收工单查询
 */

export const getRecoveryWorkOrdersListApi = (config: GetResourceListParamsType) =>
  http.post<ResPage<RecyclingWorkorderType>>(WOC + '/recovery/workOrder/page', config, {
    loading: true,
  })

/**
 * @name 回收列表-撤销
 */
export const recoveryWorkOrdersCancelApi = (workOrderId: string) =>
  http.post(WOC + '/recovery/workOrder/cancel', { workOrderId }, { loading: true })

/**
 * @name 回收工单详情
 */
export const getRecoveryWorkOrdersDetailApi = (config: { workOrderId: string }) =>
  http.post<RecyclingWorkorderType>(WOC + '/recovery/workOrder/detail', config, { loading: true })

/**
 * @name 回收审批提交
 */
export const recoveryWorkOrdersAuditApi = (config: any) =>
  http.post(WOC + '/recovery/workOrder/audit', config, { loading: true })

/**
 * @name 回收工单管理页面-回收资源
 */
export const recycleResourceApi = (config: any) =>
  http.post(WOC + '/recovery/recovery', config, { loading: true })

/**
 * @name 回收工单管理页面-回收网络资源
 */
export const networkRecycleApi = (config: any) =>
  http.post(WOC + '/network/networkRecycle', config, { loading: true })

/**
 * @name 回收工单管理页面-退维
 */
export const recoveryHandoverApi = (config: any) =>
  http.post(WOC + '/recovery/handover', config, { loading: true })

/**
 * @name 回收工单管理页面-确认
 */
export const recoveryTentantConfirmrApi = (config: any) =>
  http.post(WOC + '/recovery/tentantConfirm', config, { loading: true })

/**
 * @name 回收工单管理页面-网络信息是否可回收的校验
 */
export const recoveryCheckNetworkApi = (config: any) =>
  http.post<{ id: string; pass: boolean }[]>(WOC + '/recovery/checkNetwork', config, {
    loading: true,
  })

/**
 * @name 回收工单管理页面-导出资源信息
 */
export const resourceExportApi = (config: { workOrderId: string; productType: string }) =>
  http.get(WOC + '/recovery/workOrder/resourceExport', config, {
    loading: true,
    responseType: 'blob',
  })

// ------------------------end------------------------

// ------------------------变更工单管理-------------------------
/**
 * @name 变更列表-导出
 */
export const changeExportApi = (config: any) =>
  http.post(WOC + '/changeWorkOrder/export', config, { responseType: 'blob', loading: true })

/**
 * @name 统计变更待审核、已审核数量
 */
export const getChangeWorkOrdersAuditCountApi = (config: any) =>
  http.post<any>(WOC + '/changeWorkOrder/auditCount', config)

/**
 * @name 变更列表-变更工单查询
 */

export const getChangeWorkOrdersListApi = (config: any) =>
  http.post<ResPage<RecyclingWorkorderType>>(WOC + '/changeWorkOrder/page', config, {
    loading: true,
  })

/**
 * @name 变更列表-撤销
 */
export const changeWorkOrdersCancelApi = (workOrderId: string) =>
  http.post(WOC + '/changeWorkOrder/cancel', { workOrderId }, { loading: true })

/**
 * @name 变更工单详情
 */
export const getChangeWorkOrdersDetailApi = (config: { workOrderId: string }) =>
  http.post<RecyclingWorkorderType>(WOC + '/changeWorkOrder/detail', config, { loading: true })

/**
 * @name 变更审批提交
 */
export const changeWorkOrdersAuditApi = (config: any) =>
  http.post(WOC + '/changeWorkOrder/audit', config, { loading: true })

/**
 * @name 变更工单管理页面-确认
 */
export const changeTentantConfirmrApi = (config: any) =>
  http.post(WOC + '/changeWorkOrder/tenantConfirm', config, { loading: true })

/**
 * @name 变更工单管理页面-屏蔽
 */
export const changeAlarmSuppressApi = (config: any) =>
  http.post(WOC + '/changeWorkOrder/alarmSuppress', config, { loading: true })
// ------------------------end------------------------
