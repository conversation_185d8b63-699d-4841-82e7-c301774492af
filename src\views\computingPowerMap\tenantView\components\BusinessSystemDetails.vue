<template>
  <div id="BusinessSystemDetails" class="bussiness-system-details">
    <div class="body">
      <div class="group" v-for="(group, groupIndex) in options" :key="groupIndex">
        <div class="group-item" v-for="item in group.groupItems" :key="item.key">
          <div class="group-item__label">{{ item.label }}</div>
          <div class="group-item__content">{{ formData[item.key] }}</div>
        </div>
      </div>
    </div>
    <div class="footer">
      <el-button @click="handleClose">关闭</el-button>
    </div>
  </div>
</template>
<script setup lang="ts" name="BusinessSystemDetails">
import { ref, reactive, onMounted } from 'vue'
import { busisystemDetail } from '@/api/modules/resourecenter'

const props = defineProps({
  businessSystemId: Number, // 当前业务系统 ID
})

const formData = ref<Record<string, any>>({
  busiSystemName: '', // 业务系统名称
  secondFieldNames: '', // 业务模块名称集合
  importanceLevelShortName: '', // 业务系统等级名称
  firstFieldName: '', // 业务归属类别名称
  createdTime: '', // 入网时间/创建时间
  lifeCycleName: '', // 生命周期状态名称
  applyUserName: '', // 业务负责人
  departmentName: '', // 所属部门
  applyUserMobile: '', // 业务负责人联系电话
  userEmail: '', // 业务负责人邮箱
  manufacturer: '', // 厂家名称
  manufacturerShortName: '', // 业务厂家简称
  manufacturerContacts: '', // 厂家负责人名称
  manufacturerEmail: '', // 厂家负责人邮箱
  manufacturerMobile: '', // 厂家负责人联系方式
})

const getBusinessSystemDetails = async () => {
  const { entity } = await busisystemDetail({ id: props.businessSystemId })
  formData.value = entity
  formData.value.secondFieldNames = entity.oacBusinessSystemModuleList
    .map((i: { moduleName: string }) => i.moduleName)
    .join('，')
}

const options = reactive([
  {
    groupItems: [
      {
        label: '业务名称',
        key: 'systemName',
      },
      {
        label: '业务模块名称',
        key: 'secondFieldNames',
      },
      {
        label: '业务系统等级',
        key: 'importanceLevelShortName',
      },
      {
        label: '业务归属类别',
        key: 'firstFieldName',
      },
      {
        label: '入网时间',
        key: 'createdTime',
      },
      {
        label: '生命周期状态',
        key: 'lifeCycleName',
      },
    ],
  },
  {
    groupItems: [
      {
        label: '业务负责人',
        key: 'applyUserName',
      },
      {
        label: '所属部门',
        key: 'departmentName',
      },
      {
        label: '联系电话',
        key: 'applyUserMobile',
      },
      {
        label: '邮箱',
        key: 'userEmail',
      },
    ],
  },
  {
    groupItems: [
      {
        label: '业务厂家',
        key: 'manufacturer',
      },
      {
        label: '业务厂家简称',
        key: 'manufacturerShortName',
      },
      {
        label: '厂家负责人',
        key: 'manufacturerContacts',
      },
      {
        label: '厂家负责人邮箱',
        key: 'manufacturerEmail',
      },
      {
        label: '联系方式',
        key: 'manufacturerMobile',
      },
    ],
  },
])

const emit = defineEmits(['close'])

const handleClose = () => {
  emit('close')
}

onMounted(() => {
  getBusinessSystemDetails()
})
</script>
<style lang="scss" scoped>
.bussiness-system-details {
  font-size: 14px;
  .body {
    padding: 0 20px;
    .group-item {
      display: flex;
      margin-block: 20px;
      &__label {
        width: 150px;
      }
      &__content {
        font-weight: 600;
      }
    }
  }
  .footer {
    height: 48px;
    margin: 0 8px;
    background: #ffffff;
    border-radius: 8px 8px 8px 8px;
    display: flex;
    align-items: center;
    justify-content: right;
    padding: 0 14px;
  }
}
</style>
