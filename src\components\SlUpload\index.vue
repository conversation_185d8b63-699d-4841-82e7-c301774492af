<template>
  <div class="upload-box" :class="{ 'upload-box-disabled': self_disabled }">
    <el-upload
      v-model:file-list="_fileList"
      action="#"
      :disabled="self_disabled"
      :limit="limit"
      :http-request="handleHttpUpload"
      :before-upload="beforeUpload"
      :on-success="uploadSuccess"
      :on-error="uploadError"
      :on-remove="deleteFile"
      :on-preview="handlePreview"
      :drag="drag"
      :accept="fileMimeType.join(',')"
    >
      <!-- :class="['upload', self_disabled ? 'disabled' : '', drag ? 'no-border' : '']" -->

      <template v-if="!self_disabled">
        <div v-if="drag" class="el-upload__text">点击上传 <em>/拖拽到此区域 </em></div>
        <div v-else>
          <el-button type="primary" plain>点击上传</el-button>
        </div>
      </template>
    </el-upload>
    <div class="el-upload__tip">
      <span style="color: red" v-if="tip">{{ tip }}</span>
      <el-button class="ml5" type="primary" v-if="fileTemplate" @click="handleDownload()" link>
        {{ fileTemplate.name ?? '模板下载' }}
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts" name="UploadFiles">
import { computed, inject, ref } from 'vue'
import { ElNotification, formContextKey, formItemContextKey } from 'element-plus'
import type { UploadFile, UploadProps, UploadRequestOptions, UploadUserFile } from 'element-plus'
import { uploadApi, downloadApi, downloadTemplateApi } from '@/api/modules'
import { useDownload } from '@/hooks/useDownload'
import { watchEffect } from 'vue'
import SlMessage from '@/components/base/SlMessage'

interface UploadFileProps {
  fileList: any[] // 文件地址 ==> 必传
  fileType: string // 文件地址标识 ==> 必传
  api?: (params: any) => Promise<any> // 上传文件的 api 方法，一般项目上传都是同一个 api 方法，在组件里直接引入即可 ==> 非必传
  drag?: boolean // 是否支持拖拽上传 ==> 非必传（默认为 true）
  disabled?: boolean // 是否禁用上传组件 ==> 非必传（默认为 false）
  fileSize?: number // 文件大小限制 ==> 非必传（默认为 50M）
  limit?: number // 最大图片上传数 ==> 非必传（默认为 5张）
  fileMimeType?: File.MimeType[] // 文件类型限制 ==> 非必传（默认为 不校验）
  height?: string // 组件高度 ==> 非必传（默认为 150px）
  width?: string // 组件宽度 ==> 非必传（默认为 150px）
  borderRadius?: string // 组件边框圆角 ==> 非必传（默认为 8px）
  tip?: string // 提示信息 ==> 非必传
  fileTemplate?: {
    // 文件模板 ==> 非必传（）
    name?: string //文件名字
    uid: any //参数
    api?: (params: any) => Promise<any> //下载文件的 api 方法，一般项目下载都是同一个 api 方法，在组件里直接引入即可 ==> 非必传,
    fileType?: string // 文件类型
    fileName?: string // 文件名字
  }
}

// 接受父组件参数
const props = withDefaults(defineProps<UploadFileProps>(), {
  fileList: () => [],
  drag: true,
  disabled: false,
  limit: 5,
  fileSize: 50,
  fileMimeType: () => [],
  height: '150px',
  width: '150px',
  borderRadius: '8px',
})

const _fileList = ref<UploadUserFile[]>([])

watchEffect(() => {
  _fileList.value = props.fileList.map((item: any) => {
    return {
      name: item.fileName,
      url: item.filePath,
      uid: item.fileId,
      ...item,
    }
  })
})

// 获取 el-form 组件上下文
const formContext = inject(formContextKey, void 0)
// 获取 el-form-item 组件上下文
const formItemContext = inject(formItemContextKey, void 0)
// 判断是否禁用上传和删除
const self_disabled = computed(() => {
  return props.disabled || formContext?.disabled
})

/**
 * @description 文件上传
 * @param options upload 所有配置项
 * */
const emit = defineEmits<{
  'update:fileList': any[]
}>()
const handleHttpUpload = async (options: UploadRequestOptions) => {
  let formData = new FormData()
  formData.append('file', options.file)
  formData.append('fileType', props.fileType)

  try {
    let interval
    const api = props.api ?? uploadApi
    const { entity } = await api(formData, {
      onUploadProgress: () => {
        // 更新对应文件的进度
        const file = _fileList.value.find((f) => f.uid === options.file.uid)
        if (file) {
          file.percentage = 0
          file.status = 'uploading'
          setInterval(() => {
            if (file && file.percentage! < 95) {
              file.percentage = Math.min(file.percentage! + Math.floor(Math.random() * 2) + 1, 95)
            }
          }, 200)
        }
      },
    })
    clearInterval(interval)
    const file = entity
    emit('update:fileList', [
      ...props.fileList,
      { fileName: file.fileName, fileType: file.fileType, fileId: file.id, ...file },
    ])
    // 调用 el-form 内部的校验方法（可自动校验）
    if (formItemContext?.prop) formContext?.validateField([formItemContext.prop as string])
  } catch (error) {
    options.onError(error as any)
  }
}

/**
 * @description 删除文件
 * */
const deleteFile = (file: UploadFile) => {
  emit(
    'update:fileList',
    props.fileList.filter((item) => item.filePath !== file.url || item.fileName !== file.name),
  )
}

/**
 * @description 文件上传之前判断
 * @param rawFile 选择的文件
 * */
const beforeUpload: UploadProps['beforeUpload'] = (rawFile) => {
  const imgSize = rawFile.size / 1024 / 1024 < props.fileSize
  const rawFileSuffix = '.' + rawFile.name.split('.').pop()
  const imgType = props.fileMimeType.includes(rawFileSuffix as File.ImageMimeType)
  if (!imgType && props.fileMimeType.length > 0) {
    ElNotification({
      title: '温馨提示',
      message: '上传文件不符合所需的格式！',
      type: 'warning',
    })
    return false
  }

  if (!imgSize) {
    setTimeout(() => {
      ElNotification({
        title: '温馨提示',
        message: `上传文件大小不能超过 ${props.fileSize}M！`,
        type: 'warning',
      })
    }, 0)
    return false
  }
  return imgSize
}

/**
 * @description 文件上传成功
 * */
const uploadSuccess = () => {
  ElNotification({
    title: '温馨提示',
    message: '文件上传成功！',
    type: 'success',
  })
}

/**
 * @description 文件上传错误
 * */
const uploadError = () => {
  ElNotification({
    title: '温馨提示',
    message: '文件上传失败，请您重新上传！',
    type: 'error',
  })
}

/**
 * @description 下载文件
 * */

const handlePreview = async (options: any) => {
  useDownload(downloadApi, options.name, options.fileId)
}

/**
 * @description 下载文件模板
 * */

const handleDownload = async () => {
  if (!props.fileTemplate) return SlMessage.error('未配置文件模板！')
  const { api, name, uid, fileType, fileName } = props.fileTemplate
  const tmeApi = api ?? downloadTemplateApi
  let temName = fileName ?? name ?? '模板'
  if (fileType) temName = `${temName}.${fileType}`
  useDownload(tmeApi, temName, uid)
}
</script>

<style scoped lang="scss">
.upload-box-disabled {
  :deep(.el-upload) {
    display: none;
  }
  :deep(.el-upload-list) {
    margin-top: -2px;
  }
}
.el-upload__tip {
  text-align: center;
}
</style>
