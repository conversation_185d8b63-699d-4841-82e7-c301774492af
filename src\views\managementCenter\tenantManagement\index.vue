<template>
  <div class="table-box">
    <sl-page-header
      title="租户管理"
      title-line="提供平台用户模型管理视图化操作能力。"
      :icon="{
        Vnode: Platform,
        color: '#0052D9',
        size: '50px',
      }"
    >
    </sl-page-header>
    <div class="filter-form-con">
      <sl-form
        class="filter-form"
        :class="{ collapsed: collapsed }"
        ref="formRef"
        :options="formOptions"
        v-model="formModel"
      >
      </sl-form>
    </div>
    <div class="table-layout">
      <TenantDataList :query-params="queryParams" @showDialog="showUserListDialog"></TenantDataList>
    </div>
    <SlDialog
      v-model="dialogVisible"
      title="用户列表"
      width="1000px"
      :show-confirm="false"
      cancel-text="关闭"
      destroy-on-close
      @close="handleClose"
    >
      <UserDataList :tenant-id="tenantId"></UserDataList>
    </SlDialog>
  </div>
</template>

<script lang="tsx" setup>
import { reactive, ref } from 'vue'
import { Delete, Search, ArrowDown, ArrowUp } from '@element-plus/icons-vue'
import { Platform } from '@element-plus/icons-vue'
import TenantDataList from './components/TenantDataList.vue'
import UserDataList from './components/UserDataList.vue'
import ConditionFilter from '../../resourceCenter/conditionFilter.vue'

const formRef = ref<any>(null)
const queryParams = ref<any>({})

const formModel = reactive({
  name: '',
  tenantType: '',
  tenantLevel: '',
  specName: '',
  orgName: '',
  departmentName: '',
  ownerName: '',
  createdTime: '',
})
function reset() {
  formRef.value!.resetFields()
  queryParams.value = { ...formModel }
}
function search() {
  queryParams.value = { ...formModel }
}

// 是否默认折叠搜索项
const collapsed = ref(true)

const formOptions = reactive([
  {
    style: 'padding: 0',
    groupItems: [
      {
        label: '租户名称',
        type: 'input',
        key: 'name',
        span: 8,
        disabled: true,
        hidden: false,
        defaultSelect: true,
      },
      {
        label: '租户类型',
        type: 'select',
        key: 'tenantType',
        span: 8,
        options: [
          { label: '内部', value: '0' },
          { label: '外部', value: '1' },
        ],
        disabled: true,
        hidden: false,
        defaultSelect: true,
      },
      {
        span: 8,
        render() {
          return (
            <div style="display: flex;justify-content: flex-end;">
              <el-button type="primary" link>
                <ConditionFilter
                  formModel={formModel}
                  resourceList={formOptions[0].groupItems}
                ></ConditionFilter>
              </el-button>
              <el-button onClick={reset} icon={<Delete />}>
                重置
              </el-button>
              <el-button onClick={search} icon={<Search />} type="primary">
                搜索
              </el-button>
              <el-button
                type="primary"
                link
                class="search-isOpen"
                onClick={() => (collapsed.value = !collapsed.value)}
              >
                {collapsed.value ? '展开' : '折叠'}
                <el-icon class="el-icon--right">
                  {collapsed.value ? <ArrowDown /> : <ArrowUp />}
                </el-icon>
              </el-button>
            </div>
          )
        },
      },
      {
        label: '租户等级',
        type: 'select',
        key: 'tenantLevel',
        options: [
          { label: '核心', value: '核心' },
          { label: '重要', value: '重要' },
          { label: '一般', value: '一般' },
        ],
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '所属公司',
        type: 'input',
        key: 'companyName',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '所属部门',
        type: 'input',
        key: 'orgName',
        valueField: 'lable',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '所属科室',
        type: 'input',
        key: 'departmentName',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '租户所属人',
        type: 'input',
        key: 'ownerName',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '租户所属账号',
        type: 'input',
        key: 'ownerId',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '创建时间',
        type: 'date',
        key: 'createdTime',
        span: 8,
        disabled: false,
        hidden: true,
        props: {
          type: 'datetimerange',
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          rangeSeparator: '至',
          startPlaceholder: '开始时间',
          endPlaceholder: '结束时间',
        },
      },
    ],
  },
])

const dialogVisible = ref(false)
const tenantId = ref<number>(0)
const showUserListDialog = (item: any) => {
  tenantId.value = item.id
  dialogVisible.value = true
}

const handleClose = () => {
  dialogVisible.value = false
}
</script>
<style lang="scss" scoped>
.sl-table-content {
  overflow: auto !important;
}
.sl-page-content {
  // 减去头部的标题高度 - 不减去会超出屏幕
  overflow-x: hidden;
  height: calc(100% - 90px);
}
.header-search-input {
  width: 350px;
}
.table-layout {
  margin: 2px 8px 8px 8px;
  flex: 1;
  overflow: hidden;
}
.btn.op {
  margin: 8px 8px 0 8px;
}
.filter-form-con {
  margin: 8px 8px 0 8px;
  background: #fff;
  border-radius: 8px;
}
.filter-form.collapsed {
  height: 50px;
}
.filter-form {
  height: 110px;
  overflow: hidden;
  transition: height 0.2s;
}
</style>
