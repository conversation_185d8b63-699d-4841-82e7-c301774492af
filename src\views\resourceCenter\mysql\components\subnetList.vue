<template>
  <SlProTable
    ref="proTable"
    highlight-current-row
    :columns="columns"
    style="min-height: 300px"
    :data="props.subnetList"
    hidden-table-header
    :pagination="false"
  >
  </SlProTable>
</template>

<script setup lang="tsx" name="subnetList">
import { ref, computed } from 'vue'
import type { ColumnProps, ProTableInstance } from '@/components/SlProTable/interface'

const props = defineProps<{
  subnetList: any[]
  hideNetworkPlane?: boolean // 是否隐藏网络平面字段，默认false（显示）
}>()

const proTable = ref<ProTableInstance>()

// 动态表格配置项
const columns = computed<ColumnProps<any>[]>(() => {
  const baseColumns: ColumnProps<any>[] = [{ type: 'index', label: '序号', width: 55 }]

  // 如果不隐藏网络平面字段，则添加该列
  if (!props.hideNetworkPlane) {
    baseColumns.push({ prop: 'plan', label: '网络平面', minWidth: 100 })
  }

  // 添加其他列
  baseColumns.push(
    { prop: 'type', label: '类型', minWidth: 80 },
    { prop: 'name', label: '名称', minWidth: 150 },
    { prop: 'subnetName', label: '子网名称', minWidth: 150 },
    { prop: 'cidr', label: '子网网段', minWidth: 150 },
    { prop: 'ipv4', label: 'ipv4', minWidth: 150 },
    { prop: 'ipv6', label: 'ipv6', minWidth: 150 },
  )

  return baseColumns
})
</script>
