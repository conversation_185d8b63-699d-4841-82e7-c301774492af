# SlTabs 简洁样式效果

## 实现的视觉效果

根据您的要求，我已经实现了简洁的标签页样式：

### 选中状态样式
- 🔘 **灰色背景**: `#909399` 背景色
- ⚪ **白色文字**: `#fff` 文字颜色
- 📝 **字体加粗**: `font-weight: 500`
- 🔄 **简单圆角**: `border-radius: 4px`
- ❌ **无两侧弯角**: 移除了复杂的圆角过渡效果

### 未选中状态样式
- 🔘 **灰色文字**: `#909399` 颜色
- 🔍 **透明背景**: 无背景色
- 🖱️ **悬停效果**: 鼠标悬停时变为蓝色

### 数量标签样式
- **未选中**: 蓝色背景 + 蓝色文字
- **选中**: 半透明白色背景 + 白色文字

### 整体布局
- 🎨 **透明背景**: 容器无背景色
- 📄 **白色内容区**: 简洁的内容区域
- ❌ **无阴影**: 移除了所有阴影效果
- ❌ **无复杂过渡**: 移除了弯角过渡效果

## 代码实现

### 选中状态样式
```scss
&.is-active {
  color: #fff;                // 白色文字
  font-weight: 500;           // 字体加粗
  background: #909399;        // 灰色背景
  border-radius: 4px;         // 简单圆角
}
```

### 数量标签样式
```scss
.tab-count {
  background: rgba(72, 127, 239, 0.1);
  color: var(--el-color-primary);
}

// 选中状态下的数量标签
:deep(.el-tabs__item.is-active) .tab-count {
  background: rgba(255, 255, 255, 0.2);
  color: #fff;
}
```

## 使用示例

### 基础用法
```vue
<template>
  <sl-tabs v-model="activeTab" :tabs="tabs">
    <el-tab-pane name="pending">
      <div>待审批内容</div>
    </el-tab-pane>
    <el-tab-pane name="approved">
      <div>已审批内容</div>
    </el-tab-pane>
    <el-tab-pane name="rejected">
      <div>驳回内容</div>
    </el-tab-pane>
  </sl-tabs>
</template>

<script setup>
const activeTab = ref('approved') // 默认选中"已审批"

const tabs = [
  { name: 'pending', label: '待审批' },
  { name: 'approved', label: '已审批' },
  { name: 'rejected', label: '驳回' },
]
</script>
```

### 带数量显示
```vue
<sl-tabs v-model="activeTab" :tabs="tabsWithCount" show-count>
  <!-- 标签页内容 -->
</sl-tabs>

<script setup>
const tabsWithCount = [
  { name: 'pending', label: '待审批', count: 15 },
  { name: 'approved', label: '已审批', count: 128 },
  { name: 'rejected', label: '驳回', count: 3 },
]
</script>
```

## 样式特点

### 简洁设计
- ✅ **无复杂效果**: 移除了弯角过渡、阴影等复杂效果
- ✅ **简单圆角**: 使用 4px 简单圆角
- ✅ **清晰对比**: 选中状态有明显的颜色对比
- ✅ **一致性**: 数量标签在不同状态下保持一致的设计

### 保留功能
- ✅ **Element Plus 功能**: 保留所有原生功能
- ✅ **响应式**: 支持各种屏幕尺寸
- ✅ **无障碍**: 保留键盘导航等无障碍功能
- ✅ **动画**: 保留平滑的过渡动画

### 技术优势
- ✅ **性能优秀**: 移除了复杂的 CSS 效果，性能更好
- ✅ **易于维护**: 样式简洁，易于理解和修改
- ✅ **兼容性好**: 不依赖现代 CSS 特性，兼容性更好
- ✅ **可扩展**: 可以轻松添加新的样式效果

## 视觉效果对比

### 之前的复杂样式
- 🔄 复杂的弯角过渡效果
- 🌟 阴影和立体效果
- 🎨 多层背景和渐变

### 现在的简洁样式
- 🔘 简单的灰色背景
- ⚪ 清晰的白色文字
- 🔄 简单的 4px 圆角
- ❌ 无复杂的视觉效果

## 总结

重构后的 SlTabs 组件实现了简洁、清晰的视觉效果：
- **选中状态**: 灰色背景 + 白色文字
- **未选中状态**: 灰色文字 + 透明背景
- **无复杂效果**: 移除了弯角过渡和阴影
- **保留功能**: 完整的 Element Plus 功能

这种设计更加简洁明了，符合现代 UI 设计的简约风格，同时保持了良好的可用性和可访问性。
