<template>
  <div class="table-box">
    <sl-page-header
      :icon="{
        Vnode: Platform,
        color: '#0052D9',
        size: '50px',
      }"
      title="告警中心"
      title-line="告警中心实时监测业务中各个设备的运行状态，分类统计告警数据，帮助快速定位和解决问题，保障系统稳定运行，"
    >
    </sl-page-header>

    <div class="filter-form-con">
      <!--      :class="{ collapsed: collapsed }"-->
      <div ref="formRef" class="filter-form">
        <div class="sl-form-container">
          <div class="sl-form">
            <div class="sl-layout">
              <el-form ref="elFormRef" :inline="true" :model="formModel">
                <div style="display: flex">
                  <!--                  <el-row>-->
                  <!--                    <el-col :span="11">-->
                  <div class="searchBtnBox" style="line-height: 40px; align-items: center">
                    <el-button
                      :class="{ active: activeBtnType == '1' }"
                      style="padding: 3px 7px; margin: 0 3px 0 0"
                      @click="changeDateType('1')"
                    >
                      最近1小时
                    </el-button>
                    <el-button
                      :class="{ active: activeBtnType == '2' }"
                      style="padding: 3px 7px; margin: 0 3px 0 0"
                      @click="changeDateType('2')"
                    >
                      最近3小时
                    </el-button>
                    <el-button
                      :class="{ active: activeBtnType == '3' }"
                      style="padding: 3px 7px; margin: 0 3px 0 0"
                      @click="changeDateType('3')"
                    >
                      最近12小时
                    </el-button>
                    <el-button
                      :class="{ active: activeBtnType == '4' }"
                      style="padding: 3px 7px; margin: 0 3px 0 0"
                      @click="changeDateType('4')"
                    >
                      最近24小时
                    </el-button>
                    <el-button
                      :class="{ active: activeBtnType == '5' }"
                      style="margin: 0 3px 0 0"
                      @click="changeDateType('5')"
                    >
                      自定义
                    </el-button>

                    <el-date-picker
                      v-model="formModel.date"
                      :disabled="activeBtnType != '5'"
                      end-placeholder="结束日期"
                      format="YYYY-MM-DD HH:mm:ss"
                      value-format="YYYY-MM-DD HH:mm:ss"
                      range-separator="-"
                      start-placeholder="开始日期"
                      style="width: 340px; margin-left: 10px"
                      type="datetimerange"
                    />

                    <!--                      </div>-->
                    <!--&lt;!&ndash;                    </el-col>&ndash;&gt;-->
                    <!--&lt;!&ndash;                    <el-col :span="13">&ndash;&gt;-->
                    <!--                      <div style="display: flex;  justify-content: flex-end">-->

                    <el-form-item label="业务系统" style="margin: 0 10px 0 50px">
                      <el-select
                        v-model="formModel.bizs"
                        clearable
                        class="alarmSelect"
                        placeholder="请选择"
                      >
                        <el-option
                          :key="item?.id"
                          v-for="item in businessList"
                          :label="item.systemName"
                          :value="item.id"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                    <el-button :icon="Delete" @click="reset"> 重置</el-button>
                    <el-button :icon="Search" type="primary" @click="search"> 搜索</el-button>
                    <el-button :icon="Refresh" type="primary" @click="reloadData"> 刷新</el-button>

                    <el-button
                      class="search-isOpen"
                      link
                      style=""
                      type="primary"
                      @click="changeCollapsed"
                    >
                      {{ collapsed ? '展开' : '折叠' }}
                      <el-icon class="el-icon--right">
                        <templated v-if="collapsed">
                          <ArrowDown />
                        </templated>
                        <templated v-if="!collapsed">
                          <ArrowUp />
                        </templated>
                      </el-icon>
                    </el-button>
                    <div v-if="!collapsed" style="padding: 10px 0">
                      <el-row>
                        <el-col :span="8">
                          <el-form-item label="" style="width: 45%; margin: 0 10px 0 0">
                            <el-select
                              v-model="formModel.key1"
                              class="alarmSelect"
                              clearable
                              placeholder="请选择"
                            >
                              <el-option
                                v-for="item in paramsList"
                                :key="item.key"
                                :label="item.description"
                                :value="item.key"
                              ></el-option>
                            </el-select>
                          </el-form-item>
                          <el-form-item label="" style="width: 45%; margin: 0 10px 0 0">
                            <el-input v-model="formModel.value1" placeholder="请输入" />
                          </el-form-item>
                        </el-col>
                        <el-col :span="8">
                          <el-form-item label="" style="width: 45%; margin: 0 10px 0 0">
                            <el-select
                              v-model="formModel.key2"
                              class="alarmSelect"
                              clearable
                              placeholder="请选择"
                            >
                              <el-option
                                v-for="item in paramsList"
                                :key="item.key"
                                :label="item.description"
                                :value="item.key"
                              ></el-option>
                            </el-select>
                          </el-form-item>
                          <el-form-item label="" style="width: 45%; margin: 0 10px 0 0">
                            <el-input v-model="formModel.value2" placeholder="请输入" />
                          </el-form-item>
                        </el-col>
                        <el-col :span="8">
                          <el-form-item label="" style="width: 45%; margin: 0 10px 0 0">
                            <el-select
                              v-model="formModel.key3"
                              class="alarmSelect"
                              clearable
                              placeholder="请选择"
                            >
                              <el-option
                                v-for="item in paramsList"
                                :key="item.key"
                                :label="item.description"
                                :value="item.key"
                              ></el-option>
                            </el-select>
                          </el-form-item>
                          <el-form-item label="" style="width: 45%; margin: 0 10px 0 0">
                            <el-input v-model="formModel.value3" placeholder="请输入" />
                          </el-form-item>
                        </el-col>
                      </el-row>
                    </div>
                  </div>

                  <!--                    </el-col>-->
                  <!--                  </el-row>-->
                </div>
              </el-form>
            </div>
          </div>
        </div>
      </div>
      <!--      <sl-form-->
      <!--        class="filter-form"-->
      <!--       -->

      <!--        :options="formOptions"-->
      <!--        v-model="formModel"-->
      <!--      >-->
      <!--      </sl-form>-->
    </div>
    <div class="table-layout chartBox">
      <div class="card" style="height: 100%">
        <div class="leftPieChart" style="">
          <Echarts ref="reportEcharts" :option="option" :is-empty="totalCount == 0" />
        </div>
        <div class="rightLineChart" style="">
          <div style="width: 90%; margin: 0 0 0 8%; height: 100%">
            <Echarts ref="" :option="rightOption" :is-empty="false" />
          </div>
        </div>
      </div>
    </div>
    <div class="table-layout">
      <el-tabs v-model="tabActiveName" class="demo-tabs" type="card" @tab-click="handleClick">
        <el-tab-pane label="未清除" name="noClear">
          <el-table :data="tableData" border class="alarmTable" style="width: 100%; height: 240px">
            <el-table-column fixed prop="appId" label="业务系统" width="150">
              <template #default="scope">
                {{ scope.row.appId || '--' }}
              </template>
            </el-table-column>
            <el-table-column label="告警级别" prop="alarmSeverity" width="120">
              <template #default="scope">
                <span v-if="scope.row.alarmSeverity == 0">未知</span>
                <span v-if="scope.row.alarmSeverity == 1">严重</span>
                <span v-if="scope.row.alarmSeverity == 2">主要</span>
                <span v-if="scope.row.alarmSeverity == 3">次要</span>
                <span v-if="scope.row.alarmSeverity == 4">警告</span>
                <span v-if="scope.row.alarmSeverity == 5">提示</span>
              </template>
            </el-table-column>
            <el-table-column label="告警标题" prop="alarmTitle" show-overflow-tooltip width="220" />
            <el-table-column label="定位信息" prop="locateInfo" show-overflow-tooltip width="120" />
            <el-table-column label="告警时间" prop="eventTime" width="300" />
            <el-table-column label="设备类型" prop="equipmentType" width="120" />
            <el-table-column label="告警状态" prop="alarmStatus" width="120">
              <template #default="scope">
                <span v-if="scope.row.alarmStatus == 0">已清除</span>
                <span v-if="scope.row.alarmStatus == 1">未清除</span>
              </template>
            </el-table-column>
            <el-table-column prop="alarmContent" label="告警内容" width="480" />
            <!--            <el-table-column label="告警内容" prop="alarmText" show-overflow-tooltip width="480" />-->
            <!--            <el-table-column prop="zip" label="设备厂家名称" width="120" />-->
            <el-table-column
              label="告警来源"
              prop="alarmSource"
              show-overflow-tooltip
              width="160"
            />
            <el-table-column
              label="告警可能原因"
              prop="probableCauseTxt"
              show-overflow-tooltip
              width="150"
            />
            <el-table-column fixed="right" label="操作" min-width="120">
              <template #default="scope">
                <el-button link size="small" type="primary" @click="openDetailDialog(scope.row)">
                  详情</el-button>
              </template>
            </el-table-column>
          </el-table>

          <el-pagination
            v-model:current-page="page.current"
            v-model:page-size="page.size"
            :background="true"
            :page-sizes="[10, 20, 30, 40, 50, 100]"
            :total="page.total"
            layout="total,->, prev, pager, next"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </el-tab-pane>
        <el-tab-pane label="已清除" name="clear">
          <el-table :data="tableData" border class="alarmTable" style="width: 100%; height: 240px">
            <el-table-column fixed prop="date" label="业务系统" width="150">
              <template #default="scope">
                {{ scope.row.appId || '--' }}
              </template>
            </el-table-column>
            <el-table-column label="告警级别" prop="alarmSeverity" width="120">
              <template #default="scope">
                <span v-if="scope.row.alarmSeverity == 0">未知</span>
                <span v-if="scope.row.alarmSeverity == 1">严重</span>
                <span v-if="scope.row.alarmSeverity == 2">主要</span>
                <span v-if="scope.row.alarmSeverity == 3">次要</span>
                <span v-if="scope.row.alarmSeverity == 4">警告</span>
                <span v-if="scope.row.alarmSeverity == 5">提示</span>
              </template>
            </el-table-column>
            <el-table-column label="告警标题" prop="alarmTitle" show-overflow-tooltip width="220" />
            <el-table-column label="定位信息" prop="locateInfo" show-overflow-tooltip width="120" />
            <el-table-column label="告警时间" prop="eventTime" width="300" />
            <el-table-column label="设备类型" prop="equipmentType" width="120" />
            <el-table-column label="告警状态" prop="alarmStatus" width="120">
              <template #default="scope">
                <span v-if="scope.row.alarmStatus == 0">已清除</span>
                <span v-if="scope.row.alarmStatus == 1">未清除</span>
              </template>
            </el-table-column>
            <el-table-column prop="alarmContent" label="告警内容" width="480" />
            <!--            <el-table-column label="告警内容" prop="alarmText" show-overflow-tooltip width="480" />-->
            <!--            <el-table-column prop="zip" label="设备厂家名称" width="120" />-->
            <el-table-column
              label="告警来源"
              prop="alarmSource"
              show-overflow-tooltip
              width="160"
            />
            <el-table-column
              label="告警可能原因"
              prop="probableCauseTxt"
              show-overflow-tooltip
              width="150"
            />
            <el-table-column fixed="right" label="操作" min-width="120">
              <template #default>
                <el-button link size="small" type="primary" @click="openDetailDialog">
                  详情</el-button>
              </template>
            </el-table-column>
          </el-table>

          <el-pagination
            v-model:current-page="page.current"
            v-model:page-size="page.size"
            :background="true"
            :page-sizes="[10, 20, 30, 40, 50, 100]"
            :total="page.total"
            layout="total,->, prev, pager, next"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </el-tab-pane>
      </el-tabs>
    </div>
    <el-dialog
      v-model="detailDialogVisible"
      :before-close="close"
      :close-on-click-modal="false"
      style="height: 750px"
      title="详情"
      width="900px"
    >
      <div style="height: 635px; overflow: auto">
        <div class="info-container">
          <!-- 左侧信息列 -->
          <div class="info-column">
            <div v-for="(item, index) in leftData" :key="index" class="info-row">
              <span class="label">{{ item.label }} :</span>
              <span class="value" :title="item.value">{{ item.value || '--' }}</span>
            </div>
          </div>

          <!-- 右侧信息列 -->
          <div class="info-column">
            <div v-for="(item, index) in rightData" :key="index" class="info-row">
              <span class="label">{{ item.label }} :</span>
              <span class="value" :title="item.value">{{ item.value || '--' }}</span>
            </div>
          </div>
        </div>
        <div class="info-container" style="margin: 10px 0">
          <div class="info-column">
            <div class="info-row">
              <span class="label">告警正文 :</span>
              <span class="value2">{{ alarmText || '暂无数据' }}</span>
            </div>
          </div>
        </div>
        <div v-if="showMoreVisible" class="info-container">
          <!-- 左侧信息列 -->
          <div class="info-column">
            <div v-for="(item, index) in leftData2" :key="index" class="info-row">
              <span class="label">{{ item.label }} :</span>
              <span class="value" :title="item.value">{{ item.value || '--' }}</span>
            </div>
          </div>

          <!-- 右侧信息列 -->
          <div class="info-column">
            <div v-for="(item, index) in rightData2" :key="index" class="info-row">
              <span class="label">{{ item.label }} :</span>
              <span class="value" :title="item.value">{{ item.value || '--' }}</span>
            </div>
          </div>
        </div>
        <div>
          <el-button type="text" @click="showMoreVisible = !showMoreVisible">
            {{ showMoreVisible ? '显示基础信息' : '显示完整信息' }}
          </el-button>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="detailDialogVisible = false">确定</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 弹窗 -->
  </div>
</template>

<script lang="tsx" setup>
import { reactive, ref, onMounted } from 'vue'
import { Delete, Search, Refresh, ArrowDown, ArrowUp } from '@element-plus/icons-vue'
import { Platform } from '@element-plus/icons-vue'
import SlPageHeader from '@/components/SlPageHeader/index.vue'
import Echarts from './ReEcharts.vue'

import {
  getAlarmLevelsListApi,
  getAlarmListDataApi,
  getBusinessListDataApi,
  getParmsListDataApi,
  getAlarmDetailApi,
} from '@/api/modules/operationsOverview'

const tabActiveName = ref('noClear')
const showAlarmDataType = ref(1)
const handleClick = (e: any) => {
  if (tabActiveName.value == 'noClear') {
    showAlarmDataType.value = 1
  } else {
    showAlarmDataType.value = 0
  }
  page.current = 1
  initAlarmList()
  // detailDialogVisible.value = true
  console.log(e)
}
// let myChartsRef = ref<HTMLDivElement>()
// let myChart: ECharts
let option = reactive({})
let rightOption = reactive({})
// 告警数量总计
const totalCount = ref(0)
// 严重级别告警数量
const criticalCount = ref(0)
// 主要级别告警数量
const majorCount = ref(0)
// 次要级别告警数量
const minorCount = ref(0)
// 警告级别告警数量
const warningCount = ref(0)
// 提示级别告警数量
const infoCount = ref(0)
// 未知级别告警数量
const indeterminateCount = ref(0)
const alarmText = ref('')

function initChart() {
  Object.assign(option, {
    color: ['#4991ff', '#00dca8', '#63799b', '#fc7d02', '#fab700', '#93ce07'],
    tooltip: {
      trigger: 'item',
      valueFormatter: function (value: any) {
        if (value == 0) {
          return '0%'
        } else {
          return formatPercentage(value, totalCount.value, '')
        }
      },
    },
    title: {
      text: `告警总量\n${totalCount.value}`, // 使用变量动态设置
      left: 'center',
      top: 'center',
      textStyle: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#333',
        lineHeight: 26,
        rich: {
          // 单独设置数值样式
          value: {
            fontSize: 24,
            color: '#e4393c',
            fontWeight: 'bold',
            padding: [5, 0], // 上下间距
          },
        },
      },
      // left:'38%',
      // top:'center',
      // text:'告警总量',
      // subtext: totalCount.value || '44',
      // subtextStyle:{
      //   align:'right'
      // }
    },
    legend: {
      selectedMode: false,
      top: 'center',
      // left: 'center',
      orient: 'vertical',
      right: 0,
    },
    series: [
      {
        name: '',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          // borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2,
        },
        label: {
          show: true,
          position: 'inner',
          formatter: function (params: any) {
            if (params.value == 0) {
              return ''
            } else {
              return formatPercentage(params.value, totalCount.value, '')
            }
          },
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 20,
            fontWeight: 'bold',
          },
        },
        labelLine: {
          show: false,
        },
        data: [
          { value: criticalCount.value, name: '严重' },
          { value: majorCount.value, name: '主要' },
          { value: minorCount.value, name: '次要' },
          { value: warningCount.value, name: '警告' },
          { value: infoCount.value, name: '提示' },
          { value: indeterminateCount.value, name: '未知' },
        ],
      },
    ],
  })

  Object.assign(rightOption, {
    color: ['#4991ff', '#00dca8', '#63799b', '#fc7d02', '#fab700', '#93ce07'],
    title: {
      text: 'Stacked Line',
      show: false,
    },
    tooltip: {
      trigger: 'axis',
    },
    legend: {
      left: '4%',
      top: 10,
      icon: 'roundRect',
      itemGap: 20,
      textStyle: {
        padding: [5, 0, 0, 0],
      },
      data: alarmTypeList.value,
    },
    grid: {
      top: 50,
      left: '5%',
      right: '5%',
      bottom: '40',
      containLabel: true,
    },
    dataZoom: [
      {
        startValue: dateDateList.value[0],
      },
      {
        type: 'inside',
      },
    ],
    // toolbox: {
    //   feature: {
    //     saveAsImage: {}
    //   }
    // },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: dateDateList.value,
      axisLabel: {
        formatter: function (value: any) {
          return formatDate(value, activeBtnType.value)
        },
      },
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        name: '严重',
        type: 'line',
        stack: 'Total',
        data: yanzhongData.value,
      },
      {
        name: '主要',
        type: 'line',
        stack: 'Total',
        data: zhuyaoData.value,
      },
      {
        name: '次要',
        type: 'line',
        stack: 'Total',
        data: ciyaoData.value,
      },
      {
        name: '警告',
        type: 'line',
        stack: 'Total',
        data: jinggaoData.value,
      },
      {
        name: '提示',
        type: 'line',
        stack: 'Total',
        data: tishiData.value,
      },
      {
        name: '未知',
        type: 'line',
        stack: 'Total',
        data: weizhiData.value,
      },
    ],
  })
  // if (myChart !== undefined) {
  //   myChart.dispose()
  // }
  // myChart = init(myChartsRef.value as HTMLDivElement)
}

const formRef = ref<any>(null)

const page = reactive({
  current: 1,
  size: 5,
  total: 10,
})

const handleSizeChange = (val: number) => {
  console.log(`${val} items per page`)
}
const handleCurrentChange = (val: number) => {
  console.log(`current page: ${val}`)
}
const formModel = reactive({
  bizs: '',
  region: '',
  date: '',
  value: '',
  key1: '',
  value1: '',
  key2: '',
  value2: '',
  key3: '',
  value3: '',
})
const searchForm = reactive({
  type: '3',
  appId: '',
  key1: '',
  value1: '',
  key2: '',
  value2: '',
  key3: '',
  value3: '',
  startTime: '',
  endTime: '',
})
//查询数据
const search = () => {
  searchForm.type = activeBtnType.value
  searchForm.appId = formModel.bizs
  searchForm.startTime = formModel.date[0]
  searchForm.endTime = formModel.date[1]
  searchForm.key1 = formModel.key1
  searchForm.value1 = formModel.value1
  searchForm.key2 = formModel.key2
  searchForm.value2 = formModel.value2
  searchForm.key3 = formModel.key3
  searchForm.value3 = formModel.value3
  initChartData()
  // 查询告警列表
  initAlarmList()
}
//刷新数据
const reloadData = () => {
  initChartData()
  initAlarmList()
}

function reset() {
  formModel.bizs = ''
  activeBtnType.value = '3'
  formModel.key1 = ''
  formModel.key2 = ''
  formModel.key3 = ''
  formModel.value1 = ''
  formModel.value2 = ''
  formModel.value3 = ''
  search()
  // formRef.value!.resetFields()
  // queryParams.value = { ...formModel }
}
// 底部列表数据
const tableData = ref([])
const openDetailDialog = (row: any) => {
  getAlarmDetailApi({
    alarmId: row.id,
  }).then((res: any) => {
    // 使用示例
    // 假设您已经有了leftData和response
    mergeAlarmData(leftData.value, res)
    mergeAlarmData(rightData.value, res)
    mergeAlarmData(leftData2.value, res)
    mergeAlarmData(rightData2.value, res)
    alarmText.value = res.entity.alarmText
    detailDialogVisible.value = true
    showMoreVisible.value = false
  })
}

// 是否默认折叠搜索项
const collapsed = ref(true)
//
const businessList = ref<any>([])
//
const paramsList = ref<any>([])

const alarmTypeList = ref(['严重', '主要', '次要', '警告', '提示', '未知'])
const dateDateList = ref<any>([])
//严重告警数据
const yanzhongData = ref<any>([])
const zhuyaoData = ref<any>([])
const ciyaoData = ref<any>([])
const jinggaoData = ref<any>([])
const tishiData = ref<any>([])
const weizhiData = ref<any>([])
// 加载图表数据信息
const initChartData = () => {
  const now = new Date()
  let str2 = formatDateTime(now)

  let hoursToSubtract: number = 0
  let type = searchForm.type
  if (type == '1') {
    hoursToSubtract = 1
  } else if (type == '2') {
    hoursToSubtract = 3
  } else if (type == '3') {
    hoursToSubtract = 12
  } else if (type == '4') {
    hoursToSubtract = 24
  }
  const earlierTime = new Date(now.getTime() - hoursToSubtract * 60 * 60 * 1000)
  let str1 = formatDateTime(earlierTime)

  if (type == '5') {
    str1 = searchForm.startTime
    str2 = searchForm.endTime
  }

  let params: any = {
    appId: searchForm.appId,
    startTime: str1,
    // startTime: '2025-06-10 09:20:50',
    endTime: str2,
    // endTime: '2025-06-10 09:49:59',
  }
  if (searchForm.key1 && searchForm.value1) {
    params[searchForm.key1] = searchForm.value1
  }
  if (searchForm.key2 && searchForm.value2) {
    params[searchForm.key2] = searchForm.value2
  }
  if (searchForm.key3 && searchForm.value3) {
    params[searchForm.key3] = searchForm.value3
  }
  // const alarmLineChartData = ref<any>([])
  getAlarmLevelsListApi(params).then((res1: any) => {
    criticalCount.value = res1.entity.alarmCountDto.criticalCount || 0
    majorCount.value = res1.entity.alarmCountDto.majorCount || 0
    minorCount.value = res1.entity.alarmCountDto.minorCount || 0
    warningCount.value = res1.entity.alarmCountDto.warningCount || 0
    infoCount.value = res1.entity.alarmCountDto.infoCount || 0
    indeterminateCount.value = res1.entity.alarmCountDto.indeterminateCount || 0
    totalCount.value = res1.entity.alarmCountDto.totalCount || 0
    // alarmLineChartData.value = res1.entity.alarmCountDtoList || []
    dateDateList.value = []
    yanzhongData.value = []
    zhuyaoData.value = []
    ciyaoData.value = []
    jinggaoData.value = []
    tishiData.value = []
    weizhiData.value = []
    res1.entity.alarmCountDtoList.forEach((item: any) => {
      dateDateList.value.push(item.date.slice(0, -3))
      yanzhongData.value.push(item.criticalCount)
      zhuyaoData.value.push(item.majorCount)
      ciyaoData.value.push(item.minorCount)
      jinggaoData.value.push(item.warningCount)
      tishiData.value.push(item.infoCount)
      weizhiData.value.push(item.indeterminateCount)
    })

    initChart()
  })
}
async function init() {
  // 获取业务系统信息
  const res2: any = await getBusinessListDataApi({})
  businessList.value = res2.entity || []
  //获取查询告警属性参数
  const res3: any = await getParmsListDataApi({})
  paramsList.value = res3.entity
    .filter((item: any) => !item.description.includes('时间'))
    .filter((item: any) => !item.description.includes('业务系统'))
}

// 格式化日期时间
const formatDateTime = (date: any) => {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

const str1 = ref('')
const str2 = ref('')

//加载告警数据列表
const initAlarmList = () => {
  const now = new Date()
  str2.value = formatDateTime(now)

  let hoursToSubtract: number = 0
  let type = searchForm.type
  if (type == '1') {
    hoursToSubtract = 1
  } else if (type == '2') {
    hoursToSubtract = 3
  } else if (type == '3') {
    hoursToSubtract = 12
  } else if (type == '4') {
    hoursToSubtract = 24
  }
  const earlierTime = new Date(now.getTime() - hoursToSubtract * 60 * 60 * 1000)
  str1.value = formatDateTime(earlierTime)

  if (type == '5') {
    str1.value = searchForm.startTime
    str2.value = searchForm.endTime
  }
  let params: any = {
    appId: searchForm.appId,
    pageNum: page.current,
    pageSize: page.size,
    startTime: str1.value,
    endTime: str2.value,
    alarmStatus: showAlarmDataType.value, // 1-活动告警未清除 0-清除告警
  }
  if (searchForm.key1 && searchForm.value1) {
    params[searchForm.key1] = searchForm.value1
  }
  if (searchForm.key2 && searchForm.value2) {
    params[searchForm.key2] = searchForm.value2
  }
  if (searchForm.key3 && searchForm.value3) {
    params[searchForm.key3] = searchForm.value3
  }
  getAlarmListDataApi(params).then((res: any) => {
    tableData.value = res.entity.records || []
    page.total = res.entity.total || 0
  })
}
// 初始化表格数据
onMounted(() => {
  init()
  search()
})
//默认选择的时间信息
const activeBtnType = ref('3')
//切换选择日期类型
const changeDateType = (type: string) => {
  console.log(type)
  activeBtnType.value = type
}

const detailDialogVisible = ref(false)

//关闭弹窗
const close = () => {
  detailDialogVisible.value = false
}

const changeCollapsed = () => {
  collapsed.value = !collapsed.value
}

function formatPercentage(partial: any, total: any, fallback = '0%') {
  // 处理无效输入
  if (typeof partial !== 'number' || typeof total !== 'number') {
    return fallback
  }

  // 处理分母为零的情况
  if (total === 0) {
    return partial === 0 ? '0%' : 'Infinity%'
  }

  // 计算百分比
  const percentage = (partial / total) * 100

  // 格式化为两位小数
  const fixed = percentage.toFixed(2)

  // 检查小数部分是否为".00"
  return fixed.endsWith('.00')
    ? `${Math.floor(percentage)}%` // 去掉小数部分
    : `${parseFloat(fixed)}%` // 保留有效小数位
}

// 增强版日期格式化函数
const formatDate = (dateStr: any, formatType: any) => {
  try {
    // 基本格式验证
    if (!dateStr || typeof dateStr !== 'string') {
      console.error('Invalid date string')
      return ''
    }

    // 正则验证格式 (YYYY-MM-DD HH:mm:ss)
    // const dateRegex = /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/
    const dateRegex = /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}$/
    if (!dateRegex.test(dateStr)) {
      console.error('Date format mismatch')
      return dateStr // 返回原字符串
    }

    // 提取时间部分
    const timePart = dateStr.split(' ')[1]

    // 根据类型返回
    if (['1', '2', '3', '4'].includes(formatType)) {
      return timePart.substring(0, 5) // 取 HH:mm
    }

    if (formatType === '5') {
      return dateStr.substring(5, 10) // 取 MM-DD
    }

    return dateStr // 默认返回原字符串
  } catch (error) {
    console.error('Date formatting error:', error)
    return dateStr
  }
}

const showMoreVisible = ref(false)
const leftData = ref<any>([
  { label: '告警流水号', key: 'alarmId', value: '4248674383_463746928_1203750577_2886719285' },
  { label: '设备类型', key: 'equipmentType', value: '8104' },
  { label: '告警发生时间', key: 'eventTime', value: '2018-02-26 23:58:15' },
  { label: '厂家原始告警级别', key: 'vendorSeverity', value: '紧急告警' },
  { label: '告警状态', key: 'alarmStatus', value: '1' },
  { label: '厂家告警ID', key: 'probableCsuse', value: '198099803' },
  { label: '告警单元', key: 'alarmUnit', value: '' },
  { label: '告警子类型', key: 'subAlarmType', value: '68' },
  { label: '网元名称', key: 'neName', value: 'Z863544温岭泽国瑞二中学路口X' },
  { label: '地区', key: 'regionName', value: '台州市' },
  { label: '专业', key: 'professionalType', value: '1' },
  { label: '电路编号', key: 'circuitName', value: '' },
])
const leftData2 = ref<any>([
  { label: '基站编号', key: 'siteId', value: '863544' },
  { label: '网管告警标准名', key: 'standardName', value: '基站退服' },
  { label: '该事件对设备的影响', key: 'effectNe', value: '8' },
  { label: '告警价值', key: 'alarmValue', value: '' },
  { label: '业务覆盖区域', key: 'businessRegionIds', value: '' },
  { label: 'EOMs网络分类', key: 'eomsNetworkType', value: '' },
  { label: '集客客户', key: 'gcssClient', value: '' },
  { label: '集客客户级别', key: 'gcssClientLevel', value: '' },
  { label: '集客业务', key: 'gcssService', value: '' },
  { label: '集客业务数量', key: 'gcssServiceNum', value: '' },
  { label: '集客客户属性', key: 'gcssClientGrade', value: '' },
  { label: '厂家ID', key: 'vendorId', value: '7' },
  { label: '业务类型', key: 'bussnesType', value: '' },
  { label: '是否上报集团', key: 'sendJtFlag', value: '1' },
  { label: '告警对象工程状态', key: 'neStatus', value: '1' },
  { label: 'OMCID', key: 'omcId', value: '' },
  { label: '告警对象别名', key: 'eqpAlias', value: '' },
  { label: '告警对象管理状态', key: 'neAdminStatus', value: '' },
  { label: '告警对象设备类型', key: 'objectClass', value: '' },
  { label: '关联告警标志', key: 'alarmOrigin', value: '' },
  { label: '告警解释', key: 'extraString1', value: '' },
  { label: '告警可能原因', key: 'probableCauseTxt', value: '' },
  { label: '告警逻辑子类', key: 'logicSubAlarmType', value: '' },
  { label: '重定义告警级别', key: 'redefineSeverity', value: '' },
  { label: '告警类型', key: 'orgType', value: '' },
  { label: '乡镇', key: 'town', value: '' },
  { label: '告警RDN', key: 'alarmRdn', value: '' },
  { label: '业务信息', key: 'businessServiceInfo', value: '' },
  { label: 'ONU设备编号', key: 'onuentitycode', value: '' },
  { label: '所属客户名称', key: 'businessenterprise', value: '' },
  { label: '专网模式', key: 'customerstype', value: '' },
  { label: '站点标识', key: 'site', value: '' },
  { label: '资源池', key: 'resourcePool', value: '' },
  { label: '创单规则Id', key: 'ttruleid', value: '' },
])
const rightData = ref<any>([
  { label: '清除告警流水号', key: 'clearId', value: '4248674383_463746928_1203750577_2886719285' },
  { label: '告警标题', key: 'alarmTitle', value: 'LTE基站退服派生告警' },
  { label: '告警级别', key: 'alarmSeverity', value: '2' },
  { label: '厂家原始告警类别', key: 'vendorType', value: '1' },
  { label: '原始告警流水号', key: 'omcAlarmId', value: '1552891291183@1513881791764' },
  { label: '定位信息', key: 'locateInfo', value: '网元新链导数退服' },
  { label: '告警内容', key: 'alarmContent', value: '0' },
  { label: '设备厂家名称', key: 'vendorName', value: '中兴' },
  { label: '县市', key: 'county', value: '温岭市' },
  { label: '告警工程状态', key: 'alarmResouceStatus', value: '1300' },
  { label: '设备机房', key: 'siteNo', value: '438381' },
  { label: '适用的厂家版本号', key: 'vendorVersion', value: '' },
])
const rightData2 = ref<any>([
  { label: '告警来源', key: 'alarmSource', value: '' },
  { label: '网管告警ID', key: 'standardId', value: '68' },
  { label: '该事件对业务的影响', key: 'effectService', value: '52' },
  { label: '工程名', key: 'projName', value: '' },
  { label: '业务覆盖区域名', key: 'eomsNetworkType', value: '' },
  { label: '网元IP', key: 'neIp', value: '' },
  { label: '集客客户名称', key: 'gcssClientName', value: '' },
  { label: '集客客户数量', key: 'gcssClientNum', value: '' },
  { label: '集客业务级别', key: 'gcssServiceLevel', value: '' },
  { label: '集客产品', key: 'gcssServiceType', value: '' },
  { label: '集客客户管理属性', key: 'gcssClientGradeMgt', value: '' },
  { label: 'STANDARDFLAG', key: 'standardFlag', value: '2' },
  { label: '网管告警ID', key: 'standardAlarmId', value: '000-000-00-000000' },
  { label: '二级分类', key: 'networkType', value: '1003' },
  { label: 'EMSID', key: 'emsId', value: '' },
  { label: '告警定位对象ID', key: 'intId', value: '' },
  { label: '告警对象工程细分状态', key: 'alarmNeStatus', value: '' },
  { label: '告警对象名称', key: 'neLabel', value: '' },
  { label: '告警对象重要级别', key: 'objectLevel', value: '' },
  { label: '告警发现时间', key: 'timeStamp', value: '' },
  { label: '告警解释辅助字段', key: 'extraString2', value: '' },
  { label: '告警逻辑分类', key: 'logicAlarmType', value: '' },
  { label: '一级专业', key: 'networkTypeTop', value: '' },
  { label: '配置项编号', key: 'ciId', value: '' },
  { label: '网元端口', key: 'transPortName', value: '' },
  { label: '省', key: 'province', value: '' },
  { label: '关联业务设备', key: 'relatedDevice', value: '' },
  { label: '对端设备别名', key: 'attachedDeviceAlias', value: '' },
  { label: 'TOB业务', key: 'tobbussiness', value: '' },
  { label: '项目名称', key: 'businessindustry', value: '' },
  { label: '是否共享', key: 'sharingflag', value: '' },
  { label: '机房标识', key: 'room', value: '' },
  { label: '工单号ID', key: 'ticketid', value: '' },
  { label: 'VIP等级', key: 'isvipcustomer', value: '' },
])

// 处理数据合并的函数
function mergeAlarmData(leftData: any, responseData: any) {
  // 确保responseData和entity存在
  if (!responseData || !responseData.entity) return

  const entity = responseData.entity

  // 遍历leftData
  leftData.forEach((item: any) => {
    const key = item.key

    // 检查动态数据中是否存在相同key
    if (key && entity.hasOwnProperty(key)) {
      // 获取动态数据的值
      const dynamicValue = entity[key]

      // 特殊处理空值/null
      if (dynamicValue === null || dynamicValue === undefined) {
        item.value = ''
      } else {
        item.value = dynamicValue.toString()
      }
    }
  })
}
</script>
<style lang="scss" scoped>
.table-box {
  height: auto !important;
}

.sl-table-content {
  overflow: auto !important;
}

.sl-page-content {
  // 减去头部的标题高度 - 不减去会超出屏幕
  overflow-x: hidden;
  height: calc(100% - 90px);
}

.header-search-input {
  width: 350px;
}

.table-layout {
  margin: 2px 8px 8px 8px;
  flex: 1;
  overflow: hidden;
  padding: 5px;
  background: #fff;
}

.btn.op {
  margin: 8px 8px 0 8px;
}

.filter-form-con {
  margin: 8px 8px 0 8px;
  background: #fff;
  border-radius: 8px;
}

.filter-form.collapsed {
  //height: 50px;
}

.filter-form {
  //height: 110px;
  overflow: hidden;
  transition: height 0.2s;
}

.sl-layout {
  padding: 2px;
}

.sl-form-container .sl-form .suffix-item {
  flex: 0;
  margin-left: 4px;
  display: flex;
}

.sl-form-container {
  position: relative;

  :deep(.el-form-item__content > *) {
    flex: 1;
  }
}

.chartBox {
  .card > div {
    //display: inline-block;
    height: 340px;
    float: left;
  }

  div.leftPieChart {
    width: 34%;
  }

  div.rightLineChart {
    width: 64%;
    margin-left: 1%;
  }
}

.info-container {
  display: flex;
  gap: 30px; /* 两列之间的间距 */
}

.info-column {
  flex: 1; /* 两列等宽 */
  display: flex;
  flex-direction: column;
  gap: 10px; /* 行间距 */
}

.info-row {
  display: flex;
  width: 100%;
  min-height: 24px;
  line-height: 1.5;
}

.label {
  color: #606266;
  font-weight: 600;
  padding-right: 12px;
  flex-shrink: 0; /* 防止label被压缩 */
}

.value {
  color: #303133;
  flex: 1; /* 占据剩余空间 */
  word-break: break-word; /* 长文本自动换行 */
  text-align: left;
  display: -webkit-box; /* 必须结合WebKit浏览器使用 */
  -webkit-line-clamp: 2; /* 限制显示的行数为3 */
  -webkit-box-orient: vertical; /* 垂直排列盒子中的子元素 */
  overflow: hidden; /* 隐藏溢出的内容 */
}
.value2 {
  color: #303133;
  flex: 1; /* 占据剩余空间 */
  word-break: break-word; /* 长文本自动换行 */
  text-align: left;
}
.searchBtnBox .el-button {
  //padding: 3px 7px;
}
.searchBtnBox .el-button.active {
  border: 1px solid #409eff;
}

/* 响应式适配小屏幕 */
@media (max-width: 768px) {
  .info-container {
    flex-direction: column;
    gap: 10px;
  }
}
</style>
<style>
.alarmSelect .el-select__selection {
  width: 260px;
}
</style>
