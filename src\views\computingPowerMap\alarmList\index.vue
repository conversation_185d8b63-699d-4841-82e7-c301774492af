<template>
  <div class="table-box">
    <sl-page-header
      :icon="{
        Vnode: Platform,
        color: '#0052D9',
        size: '50px',
      }"
      title="告警中心"
      title-line="告警中心实时监测业务中各个设备的运行状态，分类统计告警数据，帮助快速定位和解决问题，保障系统稳定运行，"
    >
    </sl-page-header>

    <div class="btn op" style="margin-top: 8px">
      <el-button type="primary" @click="openDialog"> 新增规格</el-button>
      <el-button type="primary" @click="openUploadDialog"> 批量导入</el-button>
      <!--      <el-button @click="handleBatchRecycle" type="primary"> 批量删除 </el-button>-->
    </div>
    <div class="filter-form-con">
      <!--      :class="{ collapsed: collapsed }"-->
      <div ref="formRef" class="filter-form">
        <div class="sl-form-container">
          <div class="sl-form">
            <div class="sl-layout">
              <el-form ref="elFormRef" :inline="true" :model="formModel">
                <div style="display: flex">
                  <!--                  <el-row>-->
                  <!--                    <el-col :span="11">-->
                  <div style="line-height: 40px; align-items: center">
                    <el-button @click="reset" style="margin: 0 3px 0 0"> 最近1小时</el-button>
                    <el-button @click="reset" style="margin: 0 3px 0 0"> 最近3小时</el-button>
                    <el-button @click="reset" style="margin: 0 3px 0 0"> 最近12小时</el-button>
                    <el-button @click="reset" style="margin: 0 3px 0 0"> 最近24小时</el-button>

                    <el-date-picker
                      v-model="formModel.date"
                      end-placeholder="结束日期"
                      range-separator="-"
                      start-placeholder="开始日期"
                      style="width: 240px; margin-left: 10px"
                      type="daterange"
                    />

                    <!--                      </div>-->
                    <!--&lt;!&ndash;                    </el-col>&ndash;&gt;-->
                    <!--&lt;!&ndash;                    <el-col :span="13">&ndash;&gt;-->
                    <!--                      <div style="display: flex;  justify-content: flex-end">-->

                    <el-form-item label="业务系统" style="margin: 0 10px 0 50px">
                      <el-select v-model="formModel.bizs" class="alarmSelect" placeholder="请选择">
                        <el-option label="业务系统1" value="shanghai" />
                        <el-option label="业务系统2" value="beijing" />
                      </el-select>
                    </el-form-item>
                    <el-button :icon="Delete"> 重置</el-button>
                    <el-button :icon="Search" type="primary"> 搜索</el-button>
                    <el-button :icon="Refresh" type="primary"> 刷新</el-button>

                    <el-button
                      style=""
                      class="search-isOpen"
                      link
                      type="primary"
                      @click="changeCollapsed"
                    >
                      {{ collapsed ? '展开' : '折叠' }}
                      <el-icon class="el-icon--right">
                        <templated v-if="collapsed">
                          <ArrowDown />
                        </templated>
                        <templated v-if="!collapsed">
                          <ArrowUp />
                        </templated>
                      </el-icon>
                    </el-button>
                    <div v-if="!collapsed" style="padding: 10px 0">
                      <el-row>
                        <el-col :span="12">
                          <el-form-item label="" style="margin: 0 10px 0 0">
                            <el-select
                              class="alarmSelect"
                              v-model="formModel.region"
                              clearable
                              placeholder="请选择"
                            >
                              <el-option label="Zone one" value="shanghai" />
                              <el-option label="Zone two" value="beijing" />
                            </el-select>
                          </el-form-item>
                          <el-form-item label="" style="margin: 0 10px 0 0">
                            <el-input v-model="formModel.value" clearable placeholder="请输入" />
                          </el-form-item>
                        </el-col>
                      </el-row>
                    </div>
                  </div>

                  <!--                    </el-col>-->
                  <!--                  </el-row>-->
                </div>
              </el-form>
            </div>
          </div>
        </div>
      </div>
      <!--      <sl-form-->
      <!--        class="filter-form"-->
      <!--       -->

      <!--        :options="formOptions"-->
      <!--        v-model="formModel"-->
      <!--      >-->
      <!--      </sl-form>-->
    </div>
    <div class="table-layout chartBox">
      <div class="card" style="height: 100%">
        <div class="leftPieChart" style="width: 30%">
          <Echarts :option="option" ref="reportEcharts" />
        </div>
        <div class="rightLineChart" style="width: 70%">
          <div style="width: 80%; margin: 0 8%; height: 100%">
            <Echarts :option="rightOption" ref="" />
          </div>
        </div>
      </div>
    </div>
    <div class="table-layout">
      <el-tabs v-model="tabActiveName" type="card" class="demo-tabs" @tab-click="handleClick">
        <el-tab-pane label="已清除" name="first">
          <el-table class="alarmTable" :data="tableData" border style="width: 100%">
            <el-table-column fixed prop="date" label="业务系统" width="150" />
            <el-table-column prop="name" label="告警级别" width="220" />
            <el-table-column prop="state" label="告警标题" width="220" />
            <el-table-column prop="city" label="定位信息" width="120" />
            <el-table-column prop="address" label="告警时间" width="400" />
            <el-table-column prop="zip" label="设备类型" width="120" />
            <el-table-column prop="zip" label="告警状态" width="120" />
            <el-table-column prop="zip" label="告警内容" width="120" />
            <el-table-column prop="zip" label="告警内容" width="600" />
            <el-table-column prop="zip" label="设备厂家名称" width="120" />
            <el-table-column prop="zip" label="告警来源" width="120" />
            <el-table-column prop="zip" label="告警可能原因" width="120" />
            <el-table-column fixed="right" label="操作" min-width="120">
              <template #default>
                <el-button link type="primary" size="small" @click="handleClick"> 详情 </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="未清除" name="second">
          <dataList ref="dataListRef" :query-params="queryParams"></dataList>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 弹窗 -->
    <el-dialog
      v-model="uploadDialogVisible"
      :close-on-click-modal="false"
      title="批量导入"
      width="640px"
      @close="close"
    >
      <div class="sl-form-container">
        <div class="sl-form">
          <el-form>
            <div class="group-con bg-fff sl-layout">
              <el-row class="sl-layout-inner">
                <el-col :span="24">
                  <el-form-item
                    :rules="[
                      { required: true, message: '请上传镜像文件', trigger: ['blur', 'change'] },
                    ]"
                    label="镜像文件:"
                    prop="_fileList"
                  >
                    <!--                   -->
                    <div class="upload-box" style="width: 100%">
                      <el-upload
                        ref="uploadRef"
                        :auto-upload="false"
                        :drag="true"
                        :file-list="fileList"
                        :http-request="handleHttpRequest"
                        :limit="1"
                        :on-change="handleFileChange"
                        accept=".xlsx,.xls"
                      >
                        <!-- :class="['upload', self_disabled ? 'disabled' : '', drag ? 'no-border' : '']" -->

                        <div v-if="true" class="el-upload__text">
                          点击上传 <em>/拖拽到此区域 </em>
                        </div>
                      </el-upload>
                      <div class="el-upload__tip">
                        <span style="color: red">请上传xlsx格式文件，大小在 100M 以内</span>
                        <el-button class="ml5" link type="primary" @click="handleDownload">
                          {{ '模板下载' }}
                        </el-button>
                      </div>
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-form>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="uploadDialogVisible = false">取消</el-button>
          <!--          <el-button type="primary" @click="uploadSubmit">提交</el-button>-->
          <el-button type="primary" @click="handleUpload">提交</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="tsx" setup>
import { reactive, ref, onMounted } from 'vue'
import { Delete, Search, Refresh, ArrowDown, ArrowUp } from '@element-plus/icons-vue'
import { Platform } from '@element-plus/icons-vue'
import SlPageHeader from '@/components/SlPageHeader/index.vue'
// import slForm from '@/components/form/SlForm.vue'
import dataList from './dataList.vue'
import Echarts from './ReEcharts.vue'
// import { useGlobalDicStore } from '@/stores/modules/dic'
// import { useDichooks } from '../hooks/useDichooks'
// import { normalizeExportArgs } from '@/views/resourceCenter/utils'
// import { useDownload } from '@/hooks/useDownload'
// import { resourceExport } from '@/api/modules/resourecenter'
import { useRouter } from 'vue-router'
import {
  getFlavorResTypesApi,
  getPlatformListApi,
  uploadFlavorApi,
} from '@/api/modules/configCenter'
import SlMessage from '@/components/base/SlMessage'
import type { UploadFile, UploadFiles, UploadInstance } from 'element-plus'
const tabActiveName = ref('first')
const handleClick = (e: any) => {
  console.log(e)
}
// let myChartsRef = ref<HTMLDivElement>()
// let myChart: ECharts
let option = reactive({})
let rightOption = reactive({})
function initChart() {
  Object.assign(option, {
    color: ['#4991ff', '#00dca8', '#63799b', '#fab700'],
    tooltip: {
      trigger: 'item',
    },
    legend: {
      selectedMode: false,
      top: 'center',
      // left: 'center',
      orient: 'vertical',
      right: 0,
    },
    series: [
      {
        name: '告警数量',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['45%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          // borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2,
        },
        label: {
          show: false,
          position: 'center',
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 40,
            fontWeight: 'bold',
          },
        },
        labelLine: {
          show: false,
        },
        data: [
          { value: 1048, name: '严重' },
          { value: 735, name: '警告' },
          { value: 580, name: '轻微' },
          { value: 484, name: '提示' },
        ],
      },
    ],
  })
  Object.assign(rightOption, {
    color: ['#4991ff', '#00dca8', '#63799b', '#fab700'],
    title: {
      text: 'Stacked Line',
      show: false,
    },
    tooltip: {
      trigger: 'axis',
    },
    legend: {
      left: '4%',
      top: 10,
      icon: 'roundRect',
      itemGap: 20,
      textStyle: {
        padding: [5, 0, 0, 0],
      },
      data: ['严重', '警告', '轻微', '提示'],
    },
    grid: {
      top: 50,
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    // toolbox: {
    //   feature: {
    //     saveAsImage: {}
    //   }
    // },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['04-11', '04-12', '04-13', '04-14', '04-15', '04-16', '04-17'],
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        name: '严重',
        type: 'line',
        stack: 'Total',
        data: [120, 132, 101, 134, 90, 230, 210],
      },
      {
        name: '警告',
        type: 'line',
        stack: 'Total',
        data: [220, 182, 191, 234, 290, 330, 310],
      },
      {
        name: '轻微',
        type: 'line',
        stack: 'Total',
        data: [150, 232, 201, 154, 190, 330, 410],
      },
      {
        name: '提示',
        type: 'line',
        stack: 'Total',
        data: [320, 332, 301, 334, 390, 330, 320],
      },
    ],
  })
  // if (myChart !== undefined) {
  //   myChart.dispose()
  // }
  // myChart = init(myChartsRef.value as HTMLDivElement)
}
const router = useRouter()

const formRef = ref<any>(null)
const queryParams = ref<any>(null)

const formModel = reactive({
  bizs: '',
  region: '',
  date: '',
  value: '',
})

function reset() {
  formRef.value!.resetFields()
  queryParams.value = { ...formModel }
}
//
// function search() {
//   queryParams.value = { ...formModel }
// }
const tableData = ref([
  {
    date: '2016-05-03',
    name: 'Tom',
    state: 'California',
    city: 'Los Angeles',
    address: 'No. 189, Grove St, Los Angeles',
    zip: 'CA 90036',
    tag: 'Home',
  },
  {
    date: '2016-05-02',
    name: 'Tom',
    state: 'California',
    city: 'Los Angeles',
    address: 'No. 189, Grove St, Los Angeles',
    zip: 'CA 90036',
    tag: 'Office',
  },
  {
    date: '2016-05-04',
    name: 'Tom',
    state: 'California',
    city: 'Los Angeles',
    address: 'No. 189, Grove St, Los Angeles',
    zip: 'CA 90036',
    tag: 'Home',
  },
  {
    date: '2016-05-01',
    name: 'Tom',
    state: 'California',
    city: 'Los Angeles',
    address: 'No. 189, Grove St, Los Angeles',
    zip: 'CA 90036',
    tag: 'Office',
  },
])
// 类型定义
type FileList = UploadFile[]
// 上传组件实例
const uploadRef = ref<UploadInstance>()
// 文件列表
const fileList = ref<FileList>([])
// 文件选择变化处理
const handleFileChange = (file: UploadFile, files: UploadFiles) => {
  fileList.value = files
}
// 手动上传触发
const handleUpload = () => {
  if (fileList.value.length === 0) {
    ElMessage.warning('请先选择文件')
    return
  }
  uploadRef.value?.submit()
}

// 自定义上传逻辑
const handleHttpRequest = async (options: any) => {
  const { file } = options
  console.log(file)
  try {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('fileType', 'RESOURCE_EXPLAIN')
    uploadFlavorApi(formData).then((res: any) => {
      if (res.code === 200) {
        SlMessage.success('上传成功')
        dataListRef.value?.reloadTableList()
        uploadDialogVisible.value = false
      }
    })
    // 上传成功后从列表中移除
    fileList.value = fileList.value.filter((f) => f.uid !== file.uid)
  } catch (error) {
    ElMessage.error(`${file.name} 上传失败`)
    console.error('上传错误:', error)
  }
}

/**
 * @description 下载文件模板
 * */
const handleDownload = async () => {
  let url: any = window.location.protocol + '//' + window.location.host
  window.location.href = url + '/flavorTemplate.xlsx'
}

// const { resourcePoolsDic } = useDichooks()
// const globalDic = useGlobalDicStore()
// const { getDic } = globalDic
// 是否默认折叠搜索项
const collapsed = ref(true)

// 打开新增页面
const openDialog = () => {
  router.push({
    path: '/standardsAdd',
    query: {},
  })
}
const categoryCodeList = ref<Option[]>([])

// 定义类型/接口
interface Option {
  label: string
  value: string
}

async function init() {
  const res1: any = await getPlatformListApi({}) //查询domincode
  dominCodeList.value = res1.entity

  const res: any = await getFlavorResTypesApi({})

  res.entity.forEach((item: any) => {
    categoryCodeList.value.push({
      label: item.resName,
      // label: item.resType,
      value: item.resType,
    })
  })
}

// 初始化表格数据
onMounted(() => {
  init()
  initChart()
})

const uploadDialogVisible = ref(false)

//打开上传文件弹出
const openUploadDialog = () => {
  uploadDialogVisible.value = true
}

//关闭弹窗
const close = () => {
  uploadDialogVisible.value = false
  fileList.value = []
}
const dominCodeList: any = ref([])

const changeCollapsed = () => {
  collapsed.value = !collapsed.value
}

const dataListRef = ref()
</script>
<style lang="scss" scoped>
.table-box {
  height: auto !important;
}
.sl-table-content {
  overflow: auto !important;
}

.sl-page-content {
  // 减去头部的标题高度 - 不减去会超出屏幕
  overflow-x: hidden;
  height: calc(100% - 90px);
}

.header-search-input {
  width: 350px;
}

.table-layout {
  margin: 2px 8px 8px 8px;
  flex: 1;
  overflow: hidden;
  padding: 5px;
  background: #fff;
}

.btn.op {
  margin: 8px 8px 0 8px;
}

.filter-form-con {
  margin: 8px 8px 0 8px;
  background: #fff;
  border-radius: 8px;
}

.filter-form.collapsed {
  //height: 50px;
}

.filter-form {
  //height: 110px;
  overflow: hidden;
  transition: height 0.2s;
}

.sl-layout {
  padding: 2px;
}

.sl-form-container .sl-form .suffix-item {
  flex: 0;
  margin-left: 4px;
  display: flex;
}

.sl-form-container {
  position: relative;

  :deep(.el-form-item__content > *) {
    flex: 1;
  }
}
.chartBox {
  .card > div {
    display: inline-block;
    height: 300px;
  }
  div.leftPieChart {
    width: 30%;
  }
  div.rightLineChart {
    width: 70%;
  }
}
</style>
<style>
.alarmSelect .el-select__selection {
  width: 260px;
}
.el-table--border:before {
  background-color: #ff0000 !important;
}
</style>
