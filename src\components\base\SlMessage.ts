import {
  ElMessage,
  type MessageParams,
  type MessageOptions,
  type Message,
  type MessageParamsWithType,
} from 'element-plus'
import { isVNode, type AppContext } from 'vue'

const MessageType = ['success', 'warning', 'info', 'error'] as const

const customClass = 'qwertyuiopasdfghjklzxcvbnm-only-one-message'

const SlMessage = ((options?: MessageParams, appContext?: AppContext | null) => {
  if (document.getElementsByClassName(customClass).length > 0) {
    return null
  }
  if (typeof options === 'string' || isVNode(options) || typeof options === 'function') {
    return ElMessage(
      {
        message: options,
        customClass,
      },
      appContext,
    )
  }
  const reloadOptions = options as MessageOptions
  reloadOptions.showClose = reloadOptions.showClose ?? true
  reloadOptions.customClass = customClass
  return ElMessage(reloadOptions, appContext)
}) as Message

MessageType.forEach((type) => {
  SlMessage[type] = (options?: MessageParamsWithType, appContext?: AppContext | null) => {
    if (typeof options === 'string' || isVNode(options) || typeof options === 'function') {
      return SlMessage(
        {
          message: options,
          type,
        },
        appContext,
      )
    }
    return SlMessage({ ...options, type }, appContext)
  }
})

SlMessage.closeAll = ElMessage.closeAll

export default SlMessage
