p {
  margin-top: 0px;
  margin-bottom: 0px;
}

.topContentO {
  .topContentO_t {
    border: 2px solid #fff;
    background: linear-gradient(40deg, #e3fffcc4, #dbd5ffc9);
    & > p {
      width: 100%;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }

  .topContentO_b {
    border: 2px solid #fefeff;
    background: #fefeff;
    //height: calc(100% - 262px);
    //height: calc(100% - 120px);
    height: 100%;
  }

  img {
    // width: 50px;
    // height: 50px;
  }
}

.topContentT {
  .custom-progress {
    position: relative;
  }

  .circleS {
    width: 92px;
    height: 92px;
    border-radius: 50%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translateX(-50%) translateY(-50%);
    box-shadow: 0px 0px 14px 6px #ededf6;
  }

  .custom-progress :deep(svg path:first-child) {
    stroke: #d5e2fc;
  }

  .adhesiveDot {
    width: 8px !important;
    height: 8px !important;
    margin-right: 10px !important;
    margin-top: 8px !important;
    border-radius: 50%;
  }

  .bg-d5 {
    background-color: #d5e2fc !important;
  }
}

.topContentH {
  width: 40%;

  .storeAll {
    position: relative;
    border-radius: 4px;
    font-size: 16px;

    .img_ {
      border-radius: 4px;
      box-shadow: 1px 1px 4px 2px #cfe2f9;
    }

    .text {
      position: absolute;
      left: 10px;
      top: 20px;
      color: #fff;
      font-weight: 900;
    }

    .text_R {
      position: absolute;
      right: 10px !important;
      top: 20px !important;
      color: #fff;
      font-weight: 900;
    }
  }
  .storeAll_ {
    position: relative;
    border-radius: 4px;
    font-size: 16px;
    height: 80px;

    .img_ {
      border-radius: 4px;
      box-shadow: 1px 1px 4px 2px #cfe2f9;
      height: 100%;
    }

    .text {
      position: absolute;
      left: 10px;
      top: 50%;
      transform: translateY(-50%);
      color: #fff;
      font-weight: 900;
    }

    .text_R {
      position: absolute;
      right: 10px !important;
      top: 20px !important;
      color: #fff;
      font-weight: 900;
    }
  }

  .amountO {
    background: linear-gradient(#f7fff8, #feffff);

    .amountOx {
      position: relative;

      // border-top: 1px solid red;
      // border-top: 1px solid red;
      .c-c18 {
        color: #18be25;
      }
    }

    .amountOx::after {
      content: '';
      padding: 0px 10px;
      height: 2px;
      position: absolute;
      top: 0px;
      /* 位于边框线应该出现的位置 */
      left: 1px;
      right: 1px;
      z-index: 1;
      background: linear-gradient(to right, #ecfcee, #3fcc50, #ecfcee);
    }

    div:first-child {
      border-right: 2px solid #f1f5f3;
    }
  }

  .amountO:last-child {
    background: linear-gradient(#eff3fc, #feffff);

    .amountOx::after {
      content: '';
      padding: 0px 10px;
      height: 2px;
      position: absolute;
      top: 0px;
      /* 位于边框线应该出现的位置 */
      left: 1px;
      right: 1px;
      z-index: 1;
      background: linear-gradient(to right, #e2f5ff, #408bfe, #c8d9fa);
    }
  }

  .amountT {
    background: linear-gradient(#f2faff, #feffff);
  }
  .amountSummary {
    position: relative;
  }
  .amountSummaryT {
    .c-c18 {
      color: #18be25;
    }
    background: linear-gradient(40deg, #3fcc5003, #ecfcee20);
  }
  .amountSummaryB {
    background: linear-gradient(40deg, #408bfe03, #c8d9fa20);
  }
}

.topContentF {
  flex-wrap: wrap;

  .topContentF_b {
    // width: 48%;
    width: calc(50% - 8px);
    display: inline-block;
    // margin-bottom: 16px;
  }
  .topContentF_b_big {
    width: 100%;
  }
  .topContentF_x {
    // width: 48%;
    width: calc(25% - 12px);
    // margin-bottom: 16px;
  }

  .topContentF_b:nth-child(1) {
    margin-bottom: 16px;
  }

  .topContentF_b:nth-child(2) {
    margin-bottom: 16px;
  }

  .topContentF_b:nth-child(2) {
    margin-left: 16px;
  }

  .topContentF_b:nth-child(4) {
    margin-left: 16px;
  }
  .topContentF_x:nth-child(1) {
    margin-bottom: 16px;
  }
  .topContentF_x:nth-child(2) {
    margin-bottom: 16px;
  }
  .topContentF_x:nth-child(3) {
    margin-bottom: 16px;
  }
  .topContentF_x:nth-child(4) {
    margin-bottom: 16px;
  }
  .topContentF_x:nth-child(3) {
    margin-left: 16px;
  }

  .topContentF_i {
    width: 38px;
    height: 38px;
    border-radius: 50%;
    border: 6px solid #fff;
  }
  .topContentF_i_big {
    width: 65px;
    height: 65px;
    border-radius: 50%;
    border: 6px solid #fff;
  }

  .topContentF_l {
    padding: 6px 16px;
    border-radius: 10%;
    border-radius: 15px;
    font-size: 14px;
    background-color: #e9f8ecfd;
    color: #10bc1e;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .topContentF_c {
    padding: 6px 16px;
    border-radius: 15px;
    font-size: 14px;
    background-color: #edf2fd;
    color: #5e8ef1;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .topContentF_l.topContentF_l_xs,
  .topContentF_c.topContentF_c_xs {
    padding: 2px 8px;
  }

  .topContentF_t {
    padding: 6px 16px;
    border-radius: 15px;
    font-size: 14px;
    background-color: #e7e9ed;
    color: #999;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}

.bgfe {
  background: #fefeff;
}

.content {
  background-color: #eff5fe;
  width: 100%;
  height: 100vh;
  padding: 20px;
  box-sizing: border-box;
  background-image: url('../../../assets/images/img/bj.png');
  background-size: cover;
  background-position: top left;
  background-repeat: no-repeat;
}

.progress-warp {
  // 背景色
  :deep(.el-progress-bar__outer) {
    background: #dde7fc;
  }

  // 渐变进度条色
  :deep(.el-progress-bar__inner) {
    background: var(---, linear-gradient(104deg, #477eef 1.09%, #0c50da 97.26%));
  }
}

.progress-warpT {
  // 背景色
  :deep(.el-progress-bar__outer) {
    background: #dde7fc;
  }

  // 渐变进度条色
  :deep(.el-progress-bar__inner) {
    background: var(---, linear-gradient(104deg, #18be25 1.09%, #18be25 97.26%));
  }
}

.resourcePool {
  .btn {
    border: 4px solid #ebebf5;
    border-radius: 15px;
    line-height: 28px;
    height: 28px;

    & > p:nth-child(1) {
      border-right: 4px solid #ebebf5;
    }

    .checked_l {
      background-color: #487fef;
      color: #fff;
      border-radius: 15px 0px 0px 15px;
    }

    .unChecked_l {
      background-color: #ffffff;
      color: #646566;
      border-radius: 15px 0px 0px 15px;
    }

    .checked_r {
      background-color: #487fef;
      color: #fff;
      border-radius: 0px 15px 15px 0px;
    }

    .unChecked_r {
      background-color: #ffffff;
      color: #646566;
      border-radius: 0px 15px 15px 0px;
    }
  }
}

:deep(.resourcePoolO) {
  background-color: rgba(255, 255, 255, 0) !important;
  backdrop-filter: blur(15px);

  // padding: 20px 30px;
}

:deep(.el-overlay) {
  background-color: rgba(255, 255, 255, 0) !important;
  // padding: 20px 30px;
}

:deep(.selectCheck) {
  background-color: #759ff3;
  color: #fff;
}

.resourcePoolT {
  .search {
    width: 180px;
    border: 1px solid #eaedef;
    border-radius: 10px;
    color: #646566;
    background-color: #fff;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;

    p {
      border-right: 1px solid #eaedef;
    }
  }

  .searchT {
    width: 270px;

    span:nth-child(1) {
      width: 150px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .searchT_j {
      width: 30px;
      display: inline-block;
      background-color: rgb(243.9, 244.2, 244.8);
      border-radius: 4px;
      text-align: center;
      color: #909399;
    }
  }

  .‌continer‌ {
    border-bottom: 2px solid #f5f5f6;
  }
}

:deep(.resourcePoolT .el-tag) {
  padding: 8px 16px;
  font-size: 14px;
}

:deep(.untag) {
  color: #a1bcf5 !important;
  background-color: #f5f6f8;
}

:deep(.tag_) {
  color: #fff !important;
  background-color: #487fef;
  border-radius: 8px;
  text-align: center;
  span {
    display: block;
    width: 174px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

:deep(.untag_) {
  background-color: #f1f1f2;
  color: #000000;
  border: 1px solid #fff;
  border-radius: 8px;
  text-align: center;
  span {
    display: block;
    width: 174px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

:deep(.resourcePoolT .el-drawer__footer .el-button) {
  font-size: 16px !important;
  padding: 14px 30px !important;
}

:deep(.titleTopBtn) {
  padding: 19px 16px;
  height: 24px;
  font-size: 16px;
  margin-left: 0 !important;
  border-radius: 4px !important;
  border-radius: 10px !important;
}

.bgr {
  background-color: red;
}

:deep(.el-radio__inner) {
  display: none;
}

.radioBtn {
  font-size: 14px !important;

  .d {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    color: #ccc;
    background-color: #ccc;
  }
}

:deep(.button-1 .el-radio__input.is-checked + .el-radio__label) {
  color: #14be26;

  .d {
    background-color: #14be26;
  }
}

:deep(.button-2 .el-radio__input.is-checked + .el-radio__label) {
  color: #487dea;

  .d {
    background-color: #487dea;
  }
}

:deep(.button-3 .el-radio__input.is-checked + .el-radio__label) {
  color: #eec724;

  .d {
    background-color: #eec724;
  }
}

.button-2 {
  --el-radio-button-checked-bg-color: var(--el-color-primary);
  --el-radio-button-checked-text-color: var(--el-color-white);
  --el-radio-button-checked-border-color: var(--el-color-primary);
  --el-radio-button-disabled-checked-fill: var(--el-border-color-extra-light);
}

.button-3 {
  --el-radio-button-checked-bg-color: var(--el-color-danger);
  --el-radio-button-checked-text-color: var(--el-color-white);
  --el-radio-button-checked-border-color: var(--el-color-danger);
  --el-radio-button-disabled-checked-fill: var(--el-border-color-extra-light);
}

.storageBox {
  width: 100%;
  //height: 150px;
  position: relative;
  img {
    //width: 200px;
    width: 51.7%;
  }
  .storageBoxLeftImg {
    //position: absolute;
    //left: 0;
    //top: 0;
  }
  .storageBoxRightImg {
    //margin-top: 2px;
    //float: right;
    position: absolute;
    right: 0;
    top: 2px;
  }
}
.storageMiddleBg {
  .storageMiddleLeftBg {
    box-sizing: border-box;
    background-image: url('../../../assets/images/overview/icon_green_mini_bg.png');
    background-size: 100% 100%;
    //background-position: top left;
    background-repeat: no-repeat;
    padding: 20px 0;
  }
  .storageMiddleRightBg {
    box-sizing: border-box;
    background-image: url('../../../assets/images/overview/icon_blue_mini_bg.png');
    background-size: 100% 100%;
    //background-position: top left;
    background-repeat: no-repeat;
    padding: 20px 0;
  }
}
@media (max-width: 1670px) {
  .topContentF_b {
    // width: 48%;
    width: calc(50% - 8px) !important;
    // margin-bottom: 16px;
  }
  //.resourcePoolL.resourcePoolR {
  //  margin-top: 20px;
  //  margin-left: 0 !important;
  //}
  .topContentF_b_big {
    width: 100% !important;
  }
  .topContentF_l {
    font-size: 12px !important;
  }

  .topContentF_c {
    font-size: 12px !important;
  }

  .topContentF_b .title p:nth-child(1) {
    font-size: 14px !important;
  }

  .topContentF_b .title p:nth-child(2) {
    font-size: 12px !important;
  }

  .topContentO .title p:nth-child(1) {
    font-size: 12px !important;
  }

  .topContentO .title p:nth-child(2) {
    font-size: 12px !important;
  }

  .topContentF .topContentF_x:nth-child(3) {
    margin-left: 0;
  }
}
.gridGpuContainer {
  display: none !important;
}
.gridDcnContainer {
  display: none !important;
}
.eipContainer {
  width: 66.6% !important;
}
.gridStorageBox {
  .topContentF_b.topContentF_b_big {
    .topContentH {
      height: 100%;
    }
  }
}
@media (max-width: 1400px) {
  .gridGpuContainer {
    display: flex !important;
    margin-top: 20px;
    margin-bottom: 0 !important;
    .topContentH {
      margin-top: 0 !important;
    }
  }
  .gridDcnContainer {
    display: flex !important;
    height: auto !important;
    margin-top: 20px;
    //margin-bottom: 0!important;
  }
  .gpuContainer {
    display: none !important;
  }
  .dcnContainer {
    display: none !important;
  }

  .eipContainer {
    width: 100% !important;
    & > .f-r .f-1 {
      .topContentF_b.topContentF_b_big {
        margin-top: 0 !important;
      }
    }
  }
  .topContentF_l {
    font-size: 12px !important;
  }

  .topContentF_c {
    font-size: 12px !important;
  }

  .topContentF_b .title p:nth-child(1) {
    font-size: 14px !important;
  }

  .topContentF_b .title p:nth-child(2) {
    font-size: 12px !important;
  }

  .topContentO .title p:nth-child(1) {
    font-size: 12px !important;
  }

  .topContentO .title p:nth-child(2) {
    font-size: 12px !important;
  }

  .boxContiner {
    flex-direction: column;
    .boxContinerBox {
      width: 100% !important;
    }
    .ramBox {
      margin-left: 0 !important;
    }
    //& > div:nth-child(1) {
    //  margin-right: 0px !important;
    //}
    //
    //& > div:nth-child(2) {
    //  margin-left: 0px !important;
    //}
  }

  :deep(.topContentO) {
    width: calc(50% - 8px) !important;
    flex: none;
    margin-right: 8px !important;
  }
  :deep(.dcnBox.topContentO) {
    width: 100% !important;
  }
  :deep(.topContentT) {
    //width: 45.5% !important;
    //flex: none;
    //margin-left: 8px !important;
  }

  .topContentH {
    margin-top: 20px;
    box-sizing: border-box;
    //padding: 20px !important;
    width: calc(50% - 8px) !important;
    //margin-right: 8px !important;
  }

  .topContentF {
    margin-top: 20px;
    // width: calc(50% - 8px) !important;
    //margin-left: 8px !important;
  }
  .topContentF.ramBox {
    margin-top: 0;
  }
  .topContentF_b {
    //margin-top: 0!important;
    // width: calc(50% - 8px) !important;
    //margin-left: 8px !important;
  }

  // .topContentF_b {
  //   // width: 48%;
  //   width: calc(50% - 2.5%);
  //   // margin-bottom: 16px;
  // }

  // .topContentF_b:nth-child(2) {
  //   margin-left: 3.5% !important;
  // }

  // .topContentF_b:nth-child(4) {
  //   margin-left: 3.5% !important;
  // }

  //.resourcePoolL.resourcePoolR {
  //  margin-top: 20px;
  //  margin-left: 0 !important;
  //}
  .resourcePoolL {
    margin-right: 0px !important;
    .p-20.bs-b.f-r.f-j-b {
      display: block;
      .fz-18.fz-w {
        margin-bottom: 10px;
      }
    }
  }
  .resourcePoolL.resourcePoolR {
    margin-top: 20px;
    margin-left: 0 !important;
  }

  .topContentO.topContentF {
    width: 100% !important;
  }
  .topContentF .topContentF_x:nth-child(3) {
    margin-left: 0;
  }
  .topContentT {
    width: 100% !important;
  }
  .gridStorageBox {
    margin-left: 0 !important;
    display: flex;
    margin-bottom: 16px !important;
    .topContentF_b.topContentF_b_big {
      width: 50% !important;
      margin-bottom: 0 !important;
      .topContentH {
        width: 100% !important;
        height: auto !important;
      }
    }
  }
}
@media (min-width: 1681px) and (max-width: 1880px) {
  .content_Box {
    transform: scale(0.95);
    transform-origin: top left;
    width: calc(100% / 0.95);
    // height: calc(100% / 0.8);
  }
}
@media (min-width: 1500px) and (max-width: 1680px) {
  .content_Box {
    transform: scale(0.85);
    transform-origin: top left;
    width: calc(100% / 0.85);
    // height: calc(100% / 0.95);
  }
}
@media (min-width: 1400px) and (max-width: 1499px) {
  .content_Box {
    transform: scale(0.8);
    transform-origin: top left;
    width: calc(100% / 0.8);
    // height: calc(100% / 0.95);
  }
}

div {
  box-sizing: border-box !important;
  //font-size: 0;
}
