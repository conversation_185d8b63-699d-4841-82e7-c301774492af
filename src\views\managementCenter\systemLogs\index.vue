<template>
  <div class="table-box">
    <sl-page-header
      title="系统日志"
      title-line="系统日志是计算机系统在运行过程中自动生成的记录文件，用于追踪系统事件、操作行为、错误信息和性能指标。"
      :icon="{
        Vnode: Platform,
        color: '#0052D9',
        size: '50px',
      }"
    >
      <template #custom>
        <sl-base-tabs show-count :tabs="tabs" v-model="activeTab"></sl-base-tabs>
      </template>
    </sl-page-header>
    <div class="sl-page-content table-main">
      <SystemMaintenance v-show="activeTab === '0'" />
      <OperationLog v-show="activeTab === '1'" />
      <ProblemHandling v-show="activeTab === '2'" />
    </div>
  </div>
</template>

<script setup lang="ts" name="tenantManagement">
import { Platform } from '@element-plus/icons-vue'
import { ref } from 'vue'

import OperationLog from './OperationLog/index.vue'
import SystemMaintenance from './SystemMaintenance/index.vue'
import ProblemHandling from './ProblemHandling/index.vue'

const activeTab = ref('0')
// tab栏切换
const tabs: any = ref([
  { label: '系统维护', name: '0' },
  { label: '操作日志', name: '1' },
  { label: '问题处理', name: '2' },
])
</script>

<style lang="scss" scoped>
.sl-page-content {
  // 减去头部的标题高度 - 不减去会超出屏幕
  overflow: hidden;
}
</style>
