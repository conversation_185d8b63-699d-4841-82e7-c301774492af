<template>
  <el-dialog :lock-scroll="false" class="escDialog" @close="close" width="1000" title="上传">
    <div style="padding: 10px 20px">
      <sl-form
        ref="slFormRef"
        :options="formOptions"
        :model-value="formModel"
        style="margin-bottom: 16px"
      >
      </sl-form>

      <!-- 上传区域 -->
      <sl-minio-uploader @ready="handleReady" ref="uploaderRef" :max-file-size="30720" />

      <div style="color: #ff4d4f; margin-top: 8px; text-align: center">文件大小在30G以内。</div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="confirm">上传</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup name="uploadDialog">
import { reactive, ref } from 'vue'
import SlForm from '@/components/form/SlForm.vue'
import SlMinioUploader from '@/components/SlMinioUploader/index.vue'
import { imageAdd } from '@/api/modules/resourecenter'
defineProps({
  regionCode: {
    type: String,
    required: true,
  },
})
let uploader: any
const handleReady = (uploaderInstance: any) => {
  console.log('uploader', uploaderInstance)
  uploader = uploaderInstance
}

const formModel = reactive<{ [key: string]: any }>({
  imageName: '',
  osType: '',
  osVersion: '',
})

const formOptions = reactive([
  {
    style: 'margin-top: 0;margin-bottom: 0;padding:0',
    gutter: 10,
    groupItems: [
      {
        label: '镜像名称',
        type: 'input',
        key: 'imageName',
        required: true,
        placeholder: '请输入',
        rules: [{ required: true, message: '请输入镜像名称' }],
        span: 8,
      },
      {
        label: '操作系统',
        type: 'select',
        key: 'osType',
        required: true,
        placeholder: '请选择',
        rules: [{ required: true, message: '请选择操作系统' }],
        span: 8,
        options: [
          { label: 'Windows Server', value: 'Windows Server' },
          { label: 'Windows', value: 'Windows' },
          { label: 'CentOS', value: 'CentOS' },
          { label: 'Ubuntu', value: 'Ubuntu' },
          { label: 'Debian', value: 'Debian' },
          { label: 'Red Hat Enterprise Linux', value: 'RHEL' },
          { label: 'SUSE Linux Enterprise', value: 'SLES' },
          { label: 'Fedora', value: 'Fedora' },
          { label: 'AlmaLinux', value: 'AlmaLinux' },
          { label: 'Rocky Linux', value: 'Rocky Linux' },
          { label: 'Oracle Linux', value: 'Oracle Linux' },
          { label: 'FreeBSD', value: 'FreeBSD' },
          { label: 'OpenBSD', value: 'OpenBSD' },
          { label: 'macOS', value: 'macOS' },
          { label: 'Kali Linux', value: 'Kali Linux' },
          { label: 'Arch Linux', value: 'Arch Linux' },
          { label: 'Gentoo Linux', value: 'Gentoo Linux' },
          { label: 'openSUSE', value: 'openSUSE' },
        ],
      },
      {
        label: '操作系统版本',
        type: 'input',
        key: 'osVersion',
        required: true,
        placeholder: '请输入',
        rules: [{ required: true, message: '请输入操作系统版本' }],
        span: 8,
      },
    ],
  },
])

interface SlFormInstance {
  validate: () => Promise<boolean>
}

const slFormRef = ref<SlFormInstance | null>(null)
const uploaderRef = ref(null)

const emit = defineEmits(['update:modelValue', 'confirm', 'dialogClose'])

const close = () => {
  emit('update:modelValue', false)
  emit('dialogClose')
}

const submit = async () => {
  return await imageAdd({
    imageName: formModel.imageName,
    osType: formModel.osType,
    osVersion: formModel.osVersion,
    // 文件信息
    md5: uploader.md5,
    fileName: uploader.file.name,
    totalChunksCount: uploader.totalChunksCount,
    fileSize: uploader.file.size,
  })
}

const confirm = async () => {
  // 表单验证
  if (slFormRef.value) {
    const valid = await slFormRef.value.validate()
    if (!valid) return
    await submit()
    uploader.start()
    emit('update:modelValue', false)
    emit('confirm', { formModel, uploader })
  }
}
</script>

<style>
.escDialog .el-dialog__body {
  padding: 0;
}
</style>
