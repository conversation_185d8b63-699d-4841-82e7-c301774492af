<template>
  <div id="SystemUsageChart">
    <div class="header">
      <div class="header-title">云主机使用率</div>
      <div class="header-date">
        <el-radio-group v-model="activeDateType" @change="handleDateOrDateTypeChange">
          <el-radio-button :value="item.code" v-for="item in dateTypeList" :key="item.code">
            {{ item.name }}
          </el-radio-button>
        </el-radio-group>
        <el-date-picker
          v-model="date"
          type="date"
          placeholder="请选择日期"
          :disabled-date="disabledDate"
          @change="handleDateOrDateTypeChange"
        />
      </div>
    </div>
    <template v-if="showChart">
      <div ref="chartContainer" class="chart-container"></div>
      <div ref="chartContainerEvs" class="chart-container"></div>
    </template>
    <template v-else>
      <el-empty description="暂无数据" />
    </template>
  </div>
</template>

<script setup lang="ts" name="SystemUsageChart">
import { onMounted, ref, watch, type Ref, reactive } from 'vue'
import * as echarts from 'echarts/core'
import { LineChart } from 'echarts/charts'
import { GridComponent, TooltipComponent, LegendComponent } from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'
import { getCloudHostUsageListApi } from '@/api/modules/computingPowerMap'
// import type { CloudHostUsageListType } from '@/views/computingPowerMap/tenantView/interface/type'
import { getDateRange, getDateFromToday } from '@/utils/dateFormat'

echarts.use([LineChart, GridComponent, TooltipComponent, LegendComponent, CanvasRenderer])

const props = defineProps({
  systemList: {
    type: Array,
    default: () => [],
  },
  selectedSystemIds: {
    type: Array,
    default: () => [],
  },
})

const dateTypeList = ref([
  { code: 'monthly', name: '最近30天', days: 30 },
  { code: 'weekly', name: '最近一周', days: 7 },
])

const activeDateType = ref('weekly') // 初始化日期类型选项为最近一周
const date = ref(getDateFromToday({ days: -1 })) // 初始化日期为昨天
let dateRange = reactive(getDateRange({ endDate: date.value, days: 7 }))

const disabledDate = (time: Date) => {
  return time.getTime() > Date.now()
}

const handleDateOrDateTypeChange = () => {
  const _dateType = dateTypeList.value.find((i) => i.code === activeDateType.value)
  if (_dateType) {
    dateRange = { ...getDateRange({ endDate: date.value, days: _dateType.days }) }
  } else {
    console.error(`Invalid date type: ${activeDateType.value}`)
  }
  getCloudHostUsageList()
}

const showChart = ref(false)
const cloudHostUsageList = ref<any[]>([])
const getCloudHostUsageList = async () => {
  const params = {
    tenantIds: props.systemList
      .filter((item: any) => props.selectedSystemIds.includes(item.busiSystemId))
      .map((i: any) => i.tenantId)
      .join(','),
    startTime: dateRange.startDate,
    endTime: dateRange.endDate,
  }
  if (params.tenantIds.length === 0) {
    cloudHostUsageList.value = []
    showChart.value = false
    return
  }
  const { entity } = await getCloudHostUsageListApi(params)
  cloudHostUsageList.value = entity
  showChart.value = entity.length > 0
}

const chartContainer: Ref<HTMLElement | null> = ref(null)
const chartContainerEvs: Ref<HTMLElement | null> = ref(null)

let chart: echarts.ECharts | null = null
let chartEvs: echarts.ECharts | null = null

const initChart = (
  data: any[],
  type: string,
  container: HTMLElement | null,
  chartInstance: echarts.ECharts | null,
) => {
  const _legendData = type === 'cloud' ? ['CPU', '内存'] : ['IO读', 'IO写']
  if (container) {
    const _chartInstance = chartInstance || echarts.init(container)
    const option: echarts.EChartsCoreOption = {
      backgroundColor: '#fff',
      tooltip: {
        trigger: 'axis',
        formatter: (params: any) => {
          let tooltip = params[0].name + '<br>'
          params.forEach((param: any) => {
            tooltip += `${param.seriesName}: ${param.value}%<br>`
          })
          return tooltip
        },
      },
      legend: {
        data: _legendData,
        icon: 'rect',
        itemWidth: 14,
        itemHeight: 14,
        left: 120,
        itemGap: 50,
        textStyle: {
          fontSize: 14,
        },
      },
      xAxis: {
        type: 'category',
        data: data.map((item) => item.day),
        boundaryGap: false,
      },
      yAxis: {
        type: 'value',
        max: 100,
        axisLabel: {
          formatter: '{value}%', // 自定义标签格式
        },
      },
      series: [
        {
          name: _legendData[0],
          type: 'line',
          lineStyle: {
            color: 'rgba(255, 165, 0, 1)', // 设置磁盘的线条颜色
          },
          areaStyle: {
            color: 'rgba(255, 165, 0, 0.3)', // 设置磁盘的区域颜色为半透明纯色
          },
          data: data.map((item) => (type === 'cloud' ? item.avgCpuUtil : item.avgDiskReadIops)),
        },
        {
          name: _legendData[1],
          type: 'line',
          lineStyle: {
            color: 'rgba(102, 177, 255, 1)', // 设置CPU的线条颜色
          },
          areaStyle: {
            color: 'rgba(102, 177, 255, 0.3)', // 设置CPU的区域颜色为半透明纯色
          },
          data: data.map((item) => (type === 'cloud' ? item.avgMemUtil : item.avgDiskWriteIops)),
        },
      ],
    }
    _chartInstance.setOption(option)
    if (type === 'cloud') {
      chart = _chartInstance
    } else {
      chartEvs = _chartInstance
    }
  }
}

const resizeObserver = new ResizeObserver((entries) => {
  for (let entry of entries) {
    if (entry.contentBoxSize) {
      const contentBoxSize = Array.isArray(entry.contentBoxSize)
        ? entry.contentBoxSize[0]
        : entry.contentBoxSize
      if (entry.target.id === 'chartContainer') {
        chart?.resize({
          width: contentBoxSize.inlineSize,
          height: contentBoxSize.blockSize,
        })
      } else if (entry.target.id === 'chartContainerEvs') {
        chartEvs?.resize({
          width: contentBoxSize.inlineSize,
          height: contentBoxSize.blockSize,
        })
      }
    } else {
      if (entry.target.id === 'chartContainer') {
        chart?.resize({
          width: entry.contentRect.width,
          height: entry.contentRect.height,
        })
      } else if (entry.target.id === 'chartContainerEvs') {
        chartEvs?.resize({
          width: entry.contentRect.width,
          height: entry.contentRect.height,
        })
      }
    }
  }
})

watch(
  () => props.selectedSystemIds,
  () => {
    getCloudHostUsageList().then(() => {
      initChart(cloudHostUsageList.value, 'cloud', chartContainer.value, chart)
      initChart(cloudHostUsageList.value, 'evs', chartContainerEvs.value, chartEvs)
    })
  },
)

onMounted(() => {
  window.addEventListener('resize', () => {
    chart?.resize()
    chartEvs?.resize()
  })

  if (chartContainer.value) {
    chartContainer.value.id = 'chartContainer'
    resizeObserver.observe(chartContainer.value)
  }
  if (chartContainerEvs.value) {
    chartContainerEvs.value.id = 'chartContainerEvs'
    resizeObserver.observe(chartContainerEvs.value)
  }

  window.addEventListener('beforeunload', () => {
    resizeObserver.disconnect()
  })
})
</script>

<style lang="scss" scoped>
#SystemUsageChart {
  width: 100%;
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    .header-title {
      font-size: 16px;
      font-weight: bold;
    }
    .header-date {
      display: flex;
      align-items: center;
      .el-radio-group {
        margin-right: 10px;
      }
    }
  }
  .chart-container {
    width: 100%;
    height: 400px;
  }
}
</style>
