<template>
  <div class="sl-minio-uploader">
    <div :class="drag ? 'sl-minio-uploader__container' : 'sl-minio-uploader__container-drag'">
      <!-- 文件选择区域 -->
      <el-upload
        v-model:file-list="fileList"
        class="sl-minio-uploader__upload-area"
        :drag="drag"
        :auto-upload="false"
        :on-change="handleFileChange"
        :limit="1"
        ref="uploadRef"
        :accept="acceptFileTypes"
        :before-remove="handleBeforeRemove"
        :on-preview="handlePreview"
      >
        <template v-if="!drag" #trigger>
          <el-button link type="primary">{{ triggerText }}</el-button>
        </template>
        <template v-else #default>
          <div class="sl-minio-uploader__upload-content">
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="sl-minio-uploader__upload-text">点击或拖拽文件到此区域上传</div>
            <div class="sl-minio-uploader__upload-tip">
              只能选择一个文件进行上传，
              {{ maxFileSizeMB > 0 ? `大小不超过 ${maxFileSizeMB}MB` : '' }}
              {{ acceptFileTypes ? `，支持的类型: ${acceptFileTypes}` : '' }}
              <span v-if="props.sampleThreshold > 0" class="sl-minio-uploader__advanced-tip">
                (超过{{ props.sampleThreshold }}MB的文件将使用抽样哈希加速处理)
              </span>
            </div>
          </div>
        </template>
      </el-upload>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, computed } from 'vue'
import { type UploadProps, ElMessage } from 'element-plus'
import { UploadFilled } from '@element-plus/icons-vue'
import { uploaderManager } from './manage'

const emit = defineEmits(['ready'])
// 上传状态枚举
enum UploadStateEnum {
  Idle = 'idle', // 空闲状态
  Processing = 'processing', // 处理和上传中
  Success = 'success', // 上传成功
  Error = 'error', // 上传失败
  Cancelled = 'cancelled', // 上传取消
}
const fileList = ref<{ name: string; url: string }[]>([])
// 定义组件属性
const props = withDefaults(
  defineProps<{
    // 最大文件大小 (MB), 0表示不限制
    maxFileSize?: number
    // 接受的文件类型
    accept?: string
    // 最大并发上传数
    maxConcurrency?: number
    // 最大重试次数
    maxRetryCount?: number
    // 持久化上传状态
    persistState?: boolean
    // 组件ID，用于区分多个上传组件的存储数据
    uploaderId?: string
    // 使用抽样哈希的文件大小阈值(MB)，超过此大小使用抽样哈希而非完整哈希
    sampleThreshold?: number
    // 每个样本的大小(KB)
    sampleSize?: number
    // 中间取样数量
    middleSampleCount?: number
    // 是否显示上传进度
    drag?: boolean
    // 触发按钮文本
    triggerText?: string
  }>(),
  {
    maxFileSize: 0,
    accept: '',
    maxConcurrency: 3,
    maxRetryCount: 3,
    persistState: true,
    uploaderId: 'default',
    sampleThreshold: 10, // 默认10MB
    sampleSize: 1024, // 默认1MB
    middleSampleCount: 5, // 默认取5个中间样本
    drag: true,
    triggerText: '选择文件',
  },
)
/**
 * 文件块接口定义
 */
interface FileChunk {
  start: number
  end: number
  index: number
  hash: string
  blob: Blob
}
// 计算属性
const maxFileSizeMB = computed(() => props.maxFileSize)
const acceptFileTypes = computed(() => props.accept)

// 上传状态
const uploadState = reactive({
  file: null as File | null, // 当前选择的文件
  md5: '', // 文件的MD5值
  uploadProgress: 0, // 上传进度百分比
  status: UploadStateEnum.Idle, // 上传状态
  shouldCancel: false, // 是否应该取消上传
  uploadStatus: 'success' as 'success' | 'exception' | 'warning', // 上传状态样式
})

// 控制变量
const uploadRef = ref() // 上传组件引用
const fileChunks = ref<FileChunk[]>([]) // 文件分片数组

// 文件处理状态
const processedChunks = ref(0)
const totalChunks = ref(0)
const chunkQueue = ref<{ chunk: FileChunk; chunkNumber: number }[]>([])
const processingComplete = ref(false)
const autoUploadAfterProcess = ref(false)

// 计算属性：是否正在处理或上传
const isUploading = computed(() => uploadState.status === UploadStateEnum.Processing)

const handlePreview = () => {
  // a标签下载
  const a = document.createElement('a')
  a.href = fileList.value[0].url
  a.download = fileList.value[0].name
  a.click()
}
/**
 * 处理文件移除前
 */
const handleBeforeRemove = () => {
  // 如果正在上传，阻止移除
  if (isUploading.value) {
    ElMessage.warning('正在上传中，请先取消上传')
    return false
  }

  // 重置状态
  resetState()
  return true
}

/**
 * 重置状态
 */
const resetState = () => {
  uploadState.file = null
  uploadState.md5 = ''
  uploadState.uploadProgress = 0
  uploadState.status = UploadStateEnum.Idle
  uploadState.shouldCancel = false
  uploadState.uploadStatus = 'success'
  fileChunks.value = []
  processedChunks.value = 0
  totalChunks.value = 0
  chunkQueue.value = []
  processingComplete.value = false
  autoUploadAfterProcess.value = false
}
/**
 * 处理文件选择变更
 * @param uploadFile - 上传文件对象
 */
const handleFileChange: UploadProps['onChange'] = async (uploadFile) => {
  const file = uploadFile.raw as File
  const uploader = uploaderManager.createUploader({
    autoStart: false,
  })
  uploader.onProgress((progress: number) => {
    console.log('progress', progress)
  })
  await uploader.loadFile(file)
  emit('ready', uploader)
}
</script>

<style scoped>
.sl-minio-uploader {
  width: 100%;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei',
    Arial, sans-serif;
  cursor: pointer;
}

.sl-minio-uploader__container {
  width: 100%;
  margin: 0;
  padding: 10px;
  border-radius: 4px;
  background-color: #fff;
  border: 1px solid #dcdfe6;
}
.sl-minio-uploader__container-drag {
  :deep(.el-upload-list) {
    display: none;
  }
  :deep(.sl-minio-uploader__upload-area) {
    margin-bottom: 0px;
  }
  :deep(.el-upload--text) {
    vertical-align: middle !important;
  }
}

.sl-minio-uploader__upload-area {
  width: 100%;
  margin-bottom: 10px;
}

.sl-minio-uploader__upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 12px 0;
}

.sl-minio-uploader__upload-text {
  margin: 6px 0;
  font-size: 14px;
  color: #606266;
}

.sl-minio-uploader__upload-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 6px;
  text-align: center;
}

.sl-minio-uploader__actions {
  display: flex;
  justify-content: flex-end;
  margin: 8px 0;
}

.sl-minio-uploader__action-button {
  min-width: 90px;
}

.sl-minio-uploader__progress {
  margin: 10px 0 5px;
}

.sl-minio-uploader__saved {
  margin: 8px 0;
  padding: 8px 12px;
  background-color: #f0f9eb;
  border-radius: 4px;
  border: 1px solid #e1f3d8;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
}

.sl-minio-uploader__saved-info {
  display: flex;
  align-items: center;
  color: #67c23a;
  font-size: 13px;
}

.sl-minio-uploader__saved-info i {
  margin-right: 4px;
}

.sl-minio-uploader__saved-actions {
  display: flex;
  gap: 6px;
}

.sl-minio-uploader__status {
  margin: 8px 0;
  padding: 8px 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
}

.sl-minio-uploader__file-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.sl-minio-uploader__file-name {
  font-size: 13px;
  font-weight: normal;
  color: #303133;
  word-break: break-all;
}

.sl-minio-uploader__file-size {
  font-size: 12px;
  color: #909399;
}

.sl-minio-uploader__progress-area {
  margin: 8px 0;
}

.sl-minio-uploader__process-info {
  margin-bottom: 6px;
  font-size: 13px;
  color: #606266;
}

/* 响应式调整 */
@media screen and (max-width: 480px) {
  .sl-minio-uploader__saved {
    flex-direction: column;
    align-items: flex-start;
  }

  .sl-minio-uploader__saved-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .sl-minio-uploader__status {
    flex-direction: column;
    align-items: flex-start;
  }

  .sl-minio-uploader__actions {
    width: 100%;
    justify-content: flex-end;
  }
}

.sl-minio-uploader__advanced-tip {
  display: block;
  margin-top: 4px;
  color: #909399;
  font-size: 11px;
  font-style: italic;
}
</style>
