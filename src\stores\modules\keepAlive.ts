import { defineStore } from 'pinia'
/* KeepAliveState */
export interface KeepAliveState {
  keepAliveName: string[]
}

export const useKeepAliveStore = defineStore({
  id: 'sl-keepAlive',
  state: (): KeepAliveState => ({
    keepAliveName: [],
  }),
  actions: {
    // Add KeepAliveName
    async addKeepAliveName(name: string) {
      if (!this.keepAliveName.includes(name)) this.keepAliveName.push(name)
    },
    // Remove KeepAliveName
    async removeKeepAliveName(name: string) {
      this.keepAliveName = this.keepAliveName.filter((item) => item !== name)
    },
    // Set KeepAliveName
    async setKeepAliveName(keepAliveName: string[] = []) {
      this.keepAliveName = keepAliveName
    },
  },
  // 添加动态路由删除掉
  persist: {
    key: 'sl-keepAlive',
    storage: sessionStorage,
  },
})
