<template>
  <el-card :header="props.header" shadow="always">
    <template #header>
      <slot name="header"></slot>
    </template>
    <slot></slot>
  </el-card>
</template>

<script setup lang="ts">
import { ElCard } from 'element-plus'

// 定义组件的 props
const props = defineProps({
  header: {
    type: String,
    required: false,
  },
})
</script>

<style scoped>
:deep(.el-card__header) {
  padding: 10px 20px;
}
:deep(.el-card__body) {
  padding: 0;
}
</style>
