<template>
  <div v-if="searchColumns.length" class="card table-search">
    <el-form ref="formRef" v-bind="searchFormProps" :model="searchParam">
      <SlGrid
        :key="SlGridKey"
        ref="gridRef"
        :collapsed="collapsed"
        :gap="[20, 0]"
        :cols="searchCol"
      >
        <GridItem
          v-for="(item, index) in searchColumns"
          :key="item.prop"
          :index="index"
          v-bind="getResponsive(item)"
        >
          <el-form-item>
            <template #label>
              <el-space :size="4">
                <span>{{ `${item.search?.label ?? item.label}` }}</span>
                <el-tooltip
                  v-if="item.search?.tooltip"
                  effect="dark"
                  :content="item.search?.tooltip"
                  placement="top"
                >
                  <el-icon><QuestionFilled /></el-icon>
                </el-tooltip>
              </el-space>
              <span>&nbsp;:</span>
            </template>
            <SearchFormItem :column="item" :search-param="searchParam" />
          </el-form-item>
        </GridItem>
        <GridItem :suffix="true">
          <div class="operation">
            <filter-popover
              ref="filterPopoverRef"
              :columns="columns"
              @update:columns="setSearchColumns"
              :values="selectedValues"
            />

            <el-button type="primary" :icon="Search" @click="search"> 搜索 </el-button>
            <el-button ref="resetBtnRef" :icon="Delete" @click="reset"> 重置 </el-button>
            <el-button
              v-if="showCollapse"
              type="primary"
              link
              class="search-isOpen"
              @click="changeCollapsed"
            >
              {{ collapsed ? '展开' : '折叠' }}
              <el-icon class="el-icon--right">
                <component :is="collapsed ? ArrowDown : ArrowUp"></component>
              </el-icon>
            </el-button>
          </div>
        </GridItem>
      </SlGrid>
    </el-form>
  </div>
</template>
<script setup lang="ts" name="SearchForm">
import { computed, ref, watch, nextTick } from 'vue'
import type { BreakPoint, ColumnProps } from '@/components/SlProTable/interface'
import { Delete, Search, ArrowDown, ArrowUp, QuestionFilled } from '@element-plus/icons-vue'
import SearchFormItem from './components/SearchFormItem.vue'
import FilterPopover from './components/FilterPopover.vue'
import GridItem from '@//components/SlGrid/components/GridItem.vue'
import SlGrid from '@/components/SlGrid/index.vue'
interface ProTableProps {
  columns?: ColumnProps[] // 搜索配置列
  searchParam?: { [key: string]: any } // 搜索参数
  search: (params: any) => void // 搜索方法
  reset: (params: any) => void // 重置方法
  searchCol: number | Record<BreakPoint, number>
  searchFormProps?: any // 表格搜索项配置 ==> 非必传
}

// 默认值
const props = withDefaults(defineProps<ProTableProps>(), {
  columns: () => [],
  searchParam: () => ({}),
  searchFormProps: () => ({
    labelWidth: 'auto',
    labelPosition: 'right',
  }),
})

// 获取响应式设置
const getResponsive = (item: ColumnProps) => {
  return {
    span: item.search?.span,
    offset: item.search?.offset ?? 0,
    xs: item.search?.xs,
    sm: item.search?.sm,
    md: item.search?.md,
    lg: item.search?.lg,
    xl: item.search?.xl,
  }
}

// 是否默认折叠搜索项
const collapsed = ref(true)

const changeCollapsed = () => {
  collapsed.value = !collapsed.value
}
// 获取响应式断点

// 判断是否显示 展开/合并 按钮
// 获取响应式断点
const gridRef = ref()
const breakPoint = computed<BreakPoint>(() => gridRef.value?.breakPoint)

// 判断是否显示 展开/合并 按钮
const showCollapse = computed(() => {
  let show = false
  searchColumns.value.reduce((prev, current) => {
    prev +=
      (current.search![breakPoint.value]?.span ?? current.search?.span ?? 1) +
      (current.search![breakPoint.value]?.offset ?? current.search?.offset ?? 0)
    if (typeof props.searchCol !== 'number') {
      if (prev >= props.searchCol[breakPoint.value]) show = true
    } else {
      if (prev >= props.searchCol) show = true
    }
    return prev
  }, 0)
  return show
})

// 搜索控制

const searchColumns = ref<ColumnProps[]>([])
const selectedValues = ref<string[]>([])

const setSearchColumns = (vlaue: string[], flag = false) => {
  searchColumns.value = []
  searchColumns.value = props.columns.filter((item) => vlaue.includes(item.prop!))
  selectedValues.value = vlaue
  if (flag) props.reset({})
}

//
const SlGridKey = computed(() => searchColumns.value.map((item) => item.prop!)?.join('') ?? '')

type FilterPopoverType = InstanceType<typeof FilterPopover>
const filterPopoverRef = ref<FilterPopoverType | null>(null)

//设置默认值
watch(
  () => props.columns,
  (value) => {
    let values = value.filter((check) => check.search.checked)?.map((item) => item.prop!)
    setSearchColumns(values)
    nextTick(() => {
      filterPopoverRef.value?.addDefaultChecked(values)
      selectedValues.value = values
    })
  },
  { immediate: true },
)
</script>
<style lang="scss" scoped>
.list-title {
  font-weight: 600;
}
</style>
