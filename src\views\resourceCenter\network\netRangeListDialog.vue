<template>
  <el-dialog :lock-scroll="false" class="escDialog" @close="close" width="1000">
    <!-- 添加查询输入框和查询按钮 -->
    <div style="margin-bottom: 16px">
      <sl-form
        ref="slFormRef"
        :options="formOptions"
        :model-value="formModel"
        style="overflow: hidden"
      >
      </sl-form>
    </div>
    <netRangeList
      :mult-check="props.isSubNet"
      :query-params="queryParams"
      @currentChange="currentChange"
    />
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="close">取消</el-button>
        <el-button type="primary" :disabled="confirmDisabled" @click="confirm"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="tsx" setup name="ecsDailog">
import { computed, reactive, ref } from 'vue'
import netRangeList from './netRangeList.vue'
// import SlDialog from '@/components/SlDialog/index.vue'
import SlForm from '@/components/form/SlForm.vue'

const props = defineProps({
  ipVersion: {
    type: String,
    required: false,
  },
  formItem: {
    type: Object,
    required: true,
  },
  level: {
    type: Number,
    required: true,
  },
  regionCode: {
    type: String,
    required: true,
  },
  isSubNet: {
    type: Boolean,
    required: false,
  },
})

const formModel = reactive<{ [key: string]: any }>({
  type: '',
  vpn: '',
})

const formOptions = reactive([
  {
    style: 'margin-top: 0;margin-bottom: 0;padding:0',
    gutter: 10,
    groupItems: [
      {
        label: 'IP地址',
        type: 'input',
        key: 'prefix',
        span: 6,
      },
      {
        label: '掩码长度',
        type: 'input',
        key: 'mask',
        span: 5,
      },
      {
        label: '类型',
        type: 'select',
        key: 'type',
        options: [
          {
            label: 'DCN',
            value: 'DCN',
          },
          {
            label: '承载网',
            value: '承载网',
          },
          {
            label: 'CMNET',
            value: 'CMNET',
          },
          {
            label: '私网',
            value: '私网',
          },
        ],
        span: 5,
      },
      {
        label: 'VPN',
        type: 'input',
        key: 'vpn',
        span: 6,
      },
      {
        span: 2,
        render() {
          return (
            <el-button style="margin-top: 0px;" type="primary" onClick={handleSearch}>
              查 询
            </el-button>
          )
        },
      },
    ],
  },
])

let currentRow = ref<null | any[]>(null)

const queryParams = ref<any>({
  ipVersion: props.ipVersion,
  regionCode: props.regionCode,
  ipLevel: `IP${props.level}`,
  ...(props.level === 2 ? {} : { instanceId: props.formItem.instanceId2 }),
})
// 定义查询方法
const handleSearch = () => {
  // 这里可以添加查询逻辑，例如触发 dataList 的查询方法

  queryParams.value = {
    ...queryParams.value,
    ...formModel,
  }
}

const emit = defineEmits(['update:modelValue', 'confirm', 'dialogClose'])

const currentChange = (row: any) => {
  currentRow.value = row
}

const confirmDisabled = computed(() => {
  if (props.isSubNet) {
    return !currentRow?.value?.length
  }
  return !currentRow.value
})

const close = () => {
  emit('update:modelValue', false)
  emit('dialogClose')
}

const confirm = () => {
  emit('update:modelValue', false)
  emit('confirm', {
    row: currentRow.value,
    formItem: props.formItem,
    level: props.level,
    isSubNet: props.isSubNet,
  })
}
</script>

<style>
.escDialog .el-dialog__body {
  padding: 8px 0 0 0;
}
</style>
