<template>
  <div class="table-main">
    <SlProTable ref="proTable" :columns="columns" :request-api="getList" row-key="id"> </SlProTable>

    <!-- 弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      title="操作日志详情"
      width="940px"
      :close-on-click-modal="false"
      @close="() => (formData = {})"
    >
      <sl-form
        v-if="dialogVisible"
        ref="formRef"
        :show-block-title="false"
        v-model="formData"
        :options="options"
      >
        <template #enumText="{ item }">
          <EnumText :form-model="formData" :fields="item" :dic-collection="{}" />
        </template>
      </sl-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">关 闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="tsx">
import { operationLogPageApi } from '@/api/modules/managementCenter'
import type { ColumnProps } from '@/components/SlProTable/interface'
import { ref } from 'vue'
import EnumText from '@/views/approvalCenter/components/EnumText.vue'
import { changeDateFormat } from '@/utils'

const getList = async (data: any) => {
  const params = changeDateFormat(data, ['createTime'])

  return operationLogPageApi(params)
}

const columns: ColumnProps[] = [
  { type: 'index', label: '序号', width: 55 },
  {
    prop: 'userAccount',
    label: '用户账号',
    width: 180,
    search: { el: 'input', checked: true, defaultDisabled: true },
  },
  {
    prop: 'userName',
    label: '姓名',
    width: 130,
    search: { el: 'input', checked: true, defaultDisabled: true },
  },
  {
    prop: 'operationType',
    label: '操作类型',
    width: 100,
    enum: [
      { label: '创建', value: 'CREATE' },
      { label: '更新', value: 'UPDATE' },
      { label: '删除', value: 'DELETE' },
      { label: '查询', value: 'QUERY' },
      { label: '登录', value: 'LOGIN' },
    ],
    search: { el: 'select' },
  },
  {
    prop: 'description',
    label: '日志内容',
    minWidth: 230,
    render: ({ row }) => (
      <el-button
        type="primary"
        link
        onClick={() => {
          dialogVisible.value = true
          formData.value = row
        }}
      >
        {row.description}
      </el-button>
    ),
  },
  {
    prop: 'status',
    label: '状态',
    width: 80,
    enum: [
      { label: '成功', value: '成功' },
      { label: '失败', value: '失败' },
    ],
    search: { el: 'select' },
  },
  {
    prop: 'createTime',
    label: '操作时间',
    minWidth: 220,
    search: {
      el: 'date-picker',
      span: 1,
      props: {
        type: 'datetimerange',
        format: 'YYYY-MM-DD HH:mm:ss',
        valueFormat: 'YYYY-MM-DD HH:mm:ss',
        rangeSeparator: '至',
        startPlaceholder: '开始时间',
        endPlaceholder: '结束时间',
      },
    },
  },
]

const formData = ref<FormDataType>({})
const options = ref([
  {
    groupItems: [
      {
        label: '用户账号',
        type: 'slot',
        slotName: 'enumText',
        keyName: 'userAccount',
        span: 12,
      },
      {
        label: '姓名',
        type: 'slot',
        slotName: 'enumText',
        keyName: 'userName',
        span: 12,
      },
      {
        label: '操作类型',
        type: 'slot',
        slotName: 'enumText',
        key: 'operationType',
        options: [
          { label: '创建', value: 'CREATE' },
          { label: '更新', value: 'UPDATE' },
          { label: '删除', value: 'DELETE' },
          { label: '查询', value: 'QUERY' },
          { label: '登录', value: 'LOGIN' },
        ],
        span: 12,
      },
      {
        label: '日志内容',
        type: 'slot',
        slotName: 'enumText',
        keyName: 'description',
        span: 12,
      },
      {
        label: '状态',
        type: 'slot',
        slotName: 'enumText',
        keyName: 'status',
        span: 12,
      },
      {
        label: '操作时间',
        type: 'slot',
        slotName: 'enumText',
        keyName: 'createTime',
        span: 12,
      },
      {
        label: '接口地址',
        type: 'input',
        slotName: 'enumText',
        key: 'requestUrl',
        props: {
          type: 'textarea',
          showWordLimit: true,
          placeholder: '无接口地址',
          disabled: true,
        },
        span: 24,
      },
      {
        label: '接口入参',
        type: 'input',
        slotName: 'enumText',
        key: 'requestParams',
        props: {
          type: 'textarea',
          rows: 6,
          placeholder: '无入参信息',
          showWordLimit: true,
          disabled: true,
        },
        span: 24,
      },
      {
        label: '错误信息',
        type: 'input',
        slotName: 'enumText',
        key: 'errorMessage',
        props: {
          type: 'textarea',
          placeholder: '无错误信息',
          showWordLimit: true,
          disabled: true,
        },
        span: 24,
      },
    ],
  },
])

const dialogVisible = ref(false)
</script>

<style scoped lang="sass"></style>
