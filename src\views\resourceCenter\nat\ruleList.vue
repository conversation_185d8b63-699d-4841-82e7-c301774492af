<template>
  <div class="table-box">
    <sl-page-header
      title="NAT规则"
      title-line="NAT网关对应的规则维护"
      :icon="{
        Vnode: Platform,
        color: '#0052D9',
        size: '50px',
      }"
      :show-back="true"
      :back="{
        title: '返回列表',
        function: gotoList,
      }"
    >
    </sl-page-header>
    <div class="btn op" style="margin-top: 8px">
      <el-button :icon="Plus" @click="showCreateDialog" type="primary" v-permission="'Add'">
        添加规则
      </el-button>
    </div>
    <div class="filter-form-con">
      <sl-form
        class="filter-form"
        :class="{ collapsed: collapsed }"
        ref="formRef"
        :options="formOptions"
        v-model="formModel"
      >
      </sl-form>
    </div>
    <div class="table-layout">
      <RuleDataList
        ref="ruleDataList"
        :order-id="orderId"
        :query-params="queryParams"
      ></RuleDataList>
    </div>
    <SlDialog
      v-model="dialogVisible"
      title="NAT规则"
      width="600px"
      @close="handleClose"
      @confirm="handleConfirm"
    >
      <sl-form ref="slFormRef" :options="ruleFormOptions" v-model="ruleFormModel"></sl-form>
      <template #footer>
        <el-button @click="handleClose">取消</el-button>
        <sl-button type="primary" :api-function="handleConfirm">提交</sl-button>
      </template>
    </SlDialog>
  </div>
</template>

<script lang="tsx" setup>
import { reactive, ref } from 'vue'
import { Plus, Delete, Search, ArrowDown, ArrowUp, Platform } from '@element-plus/icons-vue'
import SlPageHeader from '@/components/SlPageHeader/index.vue'
import SlForm from '@/components/form/SlForm.vue'
import SlButton from '@/components/base/SlButton.vue'
import SlDialog from '@/components/SlDialog/index.vue'
import RuleDataList from './components/RuleDataList.vue'
import ConditionFilter from '../conditionFilter.vue'
import { useRouter, useRoute } from 'vue-router'
import { getNatDetails, natRuleCreate } from '@/api/modules/resourecenter'
import SlMessage from '@/components/base/SlMessage'

const route = useRoute()
const orderId = route.query.orderId as string

const formRef = ref<any>(null)
const queryParams = ref<any>({
  goodsOrderId: orderId,
})
const formModel = reactive({})
function reset() {
  formRef.value!.resetFields()
  queryParams.value = { ...formModel }
}
function search() {
  queryParams.value = { ...formModel }
}
// 是否默认折叠搜索项
const collapsed = ref(true)
const formOptions = reactive([
  {
    style: 'padding: 0',
    groupItems: [
      {
        label: '源地址',
        type: 'input',
        key: 'sourceAddress',
        span: 8,
        disabled: true,
        hidden: false,
        defaultSelect: true,
      },
      {
        label: '转换地址',
        type: 'input',
        key: 'transformAddress',
        span: 8,
        disabled: true,
        hidden: false,
        defaultSelect: true,
      },
      {
        span: 8,
        render() {
          return (
            <div style="display: flex;justify-content: flex-end;">
              <el-button type="primary" link>
                <ConditionFilter
                  formModel={formModel}
                  resourceList={formOptions[0].groupItems}
                ></ConditionFilter>
              </el-button>
              <el-button onClick={reset} icon={<Delete />}>
                重置
              </el-button>
              <el-button onClick={search} icon={<Search />} type="primary">
                搜索
              </el-button>
              <el-button
                type="primary"
                link
                class="search-isOpen"
                onClick={() => (collapsed.value = !collapsed.value)}
              >
                {collapsed.value ? '展开' : '折叠'}
                <el-icon class="el-icon--right">
                  {collapsed.value ? <ArrowDown /> : <ArrowUp />}
                </el-icon>
              </el-button>
            </div>
          )
        },
      },
      {
        label: '开通时间',
        type: 'date',
        key: 'createdTime',
        span: 8,
        disabled: false,
        hidden: true,
        props: {
          type: 'datetimerange',
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          rangeSeparator: '至',
          startPlaceholder: '开始时间',
          endPlaceholder: '结束时间',
        },
      },
      {
        label: '申请人',
        type: 'input',
        key: 'userName',
        span: 8,
        disabled: false,
        hidden: true,
      },
    ],
  },
])

const dialogVisible = ref(false)
const showCreateDialog = async () => {
  const { entity } = await getNatDetails({
    goodsOrderId: orderId,
  })
  ruleFormModel.transformAddress = entity.transformAddress
  dialogVisible.value = true
}
const ruleDataList = ref()
const handleClose = () => {
  dialogVisible.value = false
  ruleFormModel.sourceAddress = ''
  ruleFormModel.ruleType = ''
}

const slFormRef = ref()
const handleConfirm = async () => {
  if (!slFormRef.value) return
  await slFormRef.value.validate((valid: any, fields: any) => {
    if (valid) {
      handleSubmit()
    } else {
      console.log('error submit!', fields)
    }
  })
}

const handleSubmit = async () => {
  const res = await natRuleCreate({
    goodsOrderId: orderId,
    sourceAddress: ruleFormModel.sourceAddress,
    ruleType: ruleFormModel.ruleType,
  })
  if (res.code == 200) {
    ruleDataList.value.proTable.getTableList()
    SlMessage({
      message: res.entity,
      type: 'success',
    })
    dialogVisible.value = false
    ruleFormModel.sourceAddress = ''
    ruleFormModel.ruleType = ''
  }
}

const ruleFormModel = reactive({
  sourceAddress: '', // 源地址
  transformAddress: '', // 转换地址
  ruleType: '', // 规则类型，SNAT-SNAT
})

const ruleFormOptions = reactive([
  {
    groupName: '',
    groupItems: [
      {
        label: '源地址',
        type: 'input',
        key: 'sourceAddress',
        span: 24,
        props: {
          maxlength: 30,
          showWordLimit: true,
        },
        rules: [{ required: true, message: '请输入源地址', trigger: ['blur', 'change'] }],
      },
      {
        label: '转换地址',
        type: 'input',
        key: 'transformAddress',
        span: 24,
        props: {
          disabled: true,
        },
        rules: [{ required: true, message: '请输入源地址', trigger: ['blur', 'change'] }],
      },
      {
        label: '规则类型',
        type: 'select',
        key: 'ruleType',
        options: [{ label: 'SNAT', value: 'SNAT' }],
        span: 24,
        rules: {
          required: true,
          message: '请选择规则类型',
          trigger: ['blur', 'change'],
        },
      },
    ],
  },
])
const router = useRouter()
function gotoList() {
  router.push('/natList')
}
</script>
<style lang="scss" scoped>
.sl-table-content {
  overflow: auto !important;
}
.sl-page-content {
  // 减去头部的标题高度 - 不减去会超出屏幕
  overflow-x: hidden;
  height: calc(100% - 90px);
}
.header-search-input {
  width: 350px;
}
.table-layout {
  margin: 2px 8px 8px 8px;
  flex: 1;
  overflow: hidden;
}
.btn.op {
  margin: 8px 8px 0 8px;
}
.filter-form-con {
  margin: 8px 8px 0 8px;
  background: #fff;
  border-radius: 8px;
}
.filter-form.collapsed {
  height: 50px;
}
.filter-form {
  height: 110px;
  overflow: hidden;
  transition: height 0.2s;
}
</style>
