import { getTenantList } from '@/api/modules/resourecenter'
import { ref } from 'vue'
export const useTenant = () => {
  const tenantList = ref<any[]>([])
  const tenantListOptions = ref<any[]>([])
  const fetchTenantList = async () => {
    const { entity } = await getTenantList({
      type: 1,
    })
    tenantList.value = entity.map((item: any) => ({
      label: item.name,
      value: item,
    }))
    tenantListOptions.value = entity.map((item: any) => ({
      label: item.name,
      value: item.id,
    }))
  }
  fetchTenantList()
  return { tenantList, tenantListOptions }
}
