import { getTenantList } from '@/api/modules/resourecenter'
import { ref } from 'vue'
export const useTenant = () => {
  const tenantList = ref<any[]>([])
  const fetchTenantList = async () => {
    const { entity } = await getTenantList({
      type: 1,
    })
    tenantList.value = entity.map((item: any) => ({
      label: item.name,
      value: item,
    }))
  }
  fetchTenantList()
  return { tenantList }
}
