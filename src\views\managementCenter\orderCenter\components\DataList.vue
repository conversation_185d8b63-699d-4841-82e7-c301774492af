<template>
  <div class="table-main">
    <SlProTable
      ref="proTable"
      highlight-current-row
      :columns="columns"
      style="min-height: 300px"
      :request-api="corporateOrderPage"
      :init-param="queryParams"
      :current-change="currentChange"
      hidden-table-header
      row-key="goodsOrderId"
    >
    </SlProTable>
    <SlDialog
      v-model="dialogVisible"
      title="订单详情"
      width="1200px"
      :show-confirm="false"
      cancel-text="关闭"
      destroy-on-close
      @close="handleClose"
    >
      <ShoppingListTable
        :current-tenant="orderDetail"
        :all-tabs="allTabs"
        :get-columns-by-resource-type="getColumnsByResourceType"
      />
    </SlDialog>
  </div>
</template>
<script setup lang="tsx" name="dataList">
import { ref, reactive, type VNode } from 'vue'
import type { ColumnProps, ProTableInstance } from '@/components/SlProTable/interface'
import { corporateOrderPage, corporateOrderDetail } from '@/api/modules/resourecenter'
import ShoppingListTable from '@/views/corporateProducts​/components/ShoppingListTable.vue'
import { useShoppingListColumns } from '@/views/corporateProducts​/hooks/useShoppingListColumns'

const { queryParams } = defineProps<{
  queryParams: Record<string, any>
}>()

const emit = defineEmits(['currentChange'])
const currentChange = (currentRow: any, oldCurrentRow: any) => {
  emit('currentChange', currentRow, oldCurrentRow)
}

// 详情对话框相关
const dialogVisible = ref(false)
const orderDetail = ref<any>({})

// 打开详情对话框
const openDetailDialog = async (row: any) => {
  const res = await corporateOrderDetail({ id: row.id })
  const orderJson: any = {}
  for (let key in res.entity) {
    // 取res.entity中的xxxModelList为orderJson的xxxList，使用正则表达式
    orderJson[key.replace('ModelList', 'List')] = res.entity[key]
  }
  orderDetail.value = {
    orderJson,
  }
  dialogVisible.value = true
}

// 关闭详情对话框
const handleClose = () => {
  dialogVisible.value = false
}

// tab栏配置
const allTabs = ref([
  { label: '云主机', name: 'ecs', count: 0 },
  { label: 'GPU云主机', name: 'gcs', count: 0 },
  { label: '云硬盘', name: 'evs', count: 0 },
  { label: '对象存储', name: 'obs', count: 0 },
  { label: '负载均衡', name: 'slb', count: 0 },
  { label: 'NAT网关', name: 'nat', count: 0 },
  { label: 'VPN', name: 'vpn', count: 0 },
])

const { getColumnsByResourceType } = useShoppingListColumns({
  allowDelete: false,
  showStatusAndMessage: true,
})

// 表格配置项
const columns = reactive<ColumnProps<any>[]>([
  { type: 'selection', width: 55 },
  { type: 'index', label: '序号', width: 55, fixed: 'left' },
  {
    prop: 'orderCode',
    label: '订单编号',
    width: 300,
    fixed: 'left',
    render: ({ row }) => {
      return (
        <el-button onClick={() => openDetailDialog(row)} type="primary" link>
          {row.orderCode}
        </el-button>
      )
    },
  },
  { label: '租户', prop: 'tenantName', minWidth: 100 },
  { label: '订购人', prop: 'createByName', minWidth: 100 },
  { label: '订购时间', prop: 'createTime', minWidth: 100 },
  {
    prop: 'operation',
    label: '操作',
    width: 100,
    fixed: 'right',
    render: operationRender,
  },
])
function operationRender({ row }: any): VNode {
  return (
    <>
      <el-button onClick={() => openDetailDialog(row)} type="primary" link>
        详情
      </el-button>
    </>
  )
}

const proTable = ref<ProTableInstance>()

defineExpose({})
</script>
