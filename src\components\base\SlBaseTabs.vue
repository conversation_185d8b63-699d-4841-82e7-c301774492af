<template>
  <div class="tabs">
    <div class="tab-header">
      <div class="tab-item help frist">
        <div class="bg"></div>
      </div>
      <div
        v-for="tab in tabs"
        :key="tab.name"
        :class="['tab-item', { active: activeTab === tab.name }]"
        @click="selectTab(tab.name)"
      >
        <div class="bg"></div>
        <span class="title"> {{ tab.label }}</span>
        <span v-if="showCount" class="count">{{ tab.count }}</span>
      </div>
      <div class="tab-item help last">
        <div class="bg"></div>
      </div>
      <slot v-if="showActionBar" name="header-action-bar">
        <div class="header-search">
          <el-input
            clearable
            v-model="searchValue"
            style="max-width: 600px"
            :placeholder="searchPlaceholder"
            class="header-search-input"
          >
            <template #append>
              <el-button @click="handleSearch" :icon="Search" />
            </template>
          </el-input>
          <el-button @click="handleExport" class="header-export" type="primary">
            <el-icon class="el-icon--left"><Upload /></el-icon>批量导出
          </el-button>
        </div>
      </slot>
    </div>
    <div class="tab-content">
      <div v-for="tab in tabs" :key="tab.name" v-show="activeTab === tab.name">
        <slot :name="tab.name"></slot>
      </div>
      <slot></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { Search, Upload } from '@element-plus/icons-vue'

const searchValue = ref('')

interface Tab {
  name: string // 唯一标识
  label: string // 显示的标签
  count?: number
}

const props = defineProps({
  tabs: Array as () => Tab[], // Tabs 的数据列表
  modelValue: String, // 当前激活的 tab 名称（双向绑定）
  searchPlaceholder: {
    type: String,
    default: '工单类型/云平台/工单名称',
  },
  showCount: {
    type: Boolean,
    default: false,
  },
  showActionBar: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void
  (e: 'search', value: string): void
  (e: 'export'): void
}>()

const activeTab = ref(props.modelValue)

// 监听 props.modelValue 的变化
watch(
  () => props.modelValue,
  (newValue) => {
    activeTab.value = newValue
  },
)

// 选择标签时更新 activeTab 和触发双向绑定
const selectTab = (name: string) => {
  activeTab.value = name
  emit('update:modelValue', name)
}
const handleSearch = () => {
  emit('search', searchValue.value)
}
const handleExport = () => {
  emit('export')
}
</script>

<style scoped>
.header-search-input ::placeholder {
  color: #c4c7cc;
  font-size: 14px;
}
.header-search {
  background: #fff;
  padding: 0 8px 4px 0;
  width: 30%;
  display: flex;
  gap: 10px;
  flex: 1;
}
:v-deep(.tabs .el-input__wrapper) {
  border-radius: 10px 0 0 10px;
  height: 28px;
}
:v-deep(.header-search .el-input-group__append) {
  height: 30px;
}
:v-deep(.header-search .el-input, .header-export) {
  height: 30px;
  font-size: 14px;
  font-weight: normal;
}
.tab-item.help.last {
  flex-grow: 1;
}
.tab-item.help.frist {
  padding: 0px 8px;
}
.tabs {
  width: 100%;
  display: flex;
  flex-direction: column;
}

.tab-header {
  display: flex;
  font-size: 14px;
}

.tab-item {
  padding: 8px 20px;
  cursor: pointer;
  color: #606266;
  display: flex;
  align-items: center;
  position: relative;
}

.tab-item.active {
  color: var(--el-color-primary);
  font-weight: 400;
  /* background: linear-gradient(to bottom, 50% #fff, 50% transparent); */
  background: #fff;
}
.title {
  z-index: 1;
  position: relative;
}
.count {
  z-index: 1;
  background: rgba(72, 127, 239, 0.1);
  font-size: 12px;
  padding: 2px 4px;
  border-radius: 2px;
  margin-left: 2px;
}
.bg {
  position: absolute;
  top: 0px;
  left: 0px;
  right: 0px;
  bottom: 0px;
  /* border-radius: 0 0 6px 6px; */
  background-color: #fff;
  z-index: 0;
}
.tab-item:has(+ .active) .bg {
  border-bottom-right-radius: 12px;
}
.tab-item.active + .tab-item .bg {
  border-bottom-left-radius: 12px;
}
.tab-item.active .bg {
  background-color: #f2f3f5;
  border-radius: 8px 8px 0 0;
}

.tab-item:hover {
  color: #409eff;
}
</style>
