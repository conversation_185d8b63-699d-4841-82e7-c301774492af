<template>
  <div class="table-box">
    <sl-page-header
      title="容器配额"
      title-line="容器配额提供了对容器资源配额的管理能力。"
      :icon="{
        Vnode: Platform,
        color: '#0052D9',
        size: '50px',
      }"
    >
    </sl-page-header>
    <div class="btn op" style="margin-top: 8px" v-show="false">
      <el-button @click="handleBatchRecycle" type="primary" v-permission="'BatchRecycle'">
        批量回收
      </el-button>
      <el-button @click="handleBatchChange" type="primary" v-permission="'Change'">
        资源变更
      </el-button>
      <el-button @click="handleBatchDelay" type="primary" v-permission="'Delay'">
        资源延期
      </el-button>
    </div>
    <div class="filter-form-con">
      <sl-form
        class="filter-form"
        :class="{ collapsed: collapsed }"
        ref="formRef"
        :options="formOptions"
        v-model="formModel"
      >
      </sl-form>
    </div>
    <div class="table-layout">
      <DataList ref="dataListRef" :query-params="queryParams"></DataList>
    </div>
  </div>
</template>

<script lang="tsx" setup>
import { reactive, ref } from 'vue'
import { Delete, Search, ArrowDown, ArrowUp, Upload } from '@element-plus/icons-vue'
import { Platform } from '@element-plus/icons-vue'
import SlPageHeader from '@/components/SlPageHeader/index.vue'
import slForm from '@/components/form/SlForm.vue'
import DataList from './DataList.vue'
import ConditionFilter from '../conditionFilter.vue'
import useBusiSystemOptions from '../hooks/useBusiSystemOptions'
import { useGlobalDicStore } from '@/stores/modules/dic'
import { useDichooks } from '../hooks/useDichooks'
import { useDownload } from '@/hooks/useDownload'
import { containerExportApi } from '../../../api/modules/resourecenter'
import { changeDateFormat } from '@/utils'

const { busiSystemOptions } = useBusiSystemOptions()
const formRef = ref<any>(null)
const queryParams = ref<any>({})

const formModel = reactive({})
function reset() {
  formRef.value!.resetFields()
  queryParams.value = { ...formModel }
}
function search() {
  queryParams.value = { ...formModel }
}
const { resourcePoolsDic, cloudTypeDic, cloudPlatformDic } = useDichooks()
const globalDic = useGlobalDicStore()
const { getDic } = globalDic
// 是否默认折叠搜索项
const collapsed = ref(true)
const formOptions = reactive([
  {
    style: 'padding: 0',
    groupItems: [
      {
        label: '配额名称',
        type: 'input',
        key: 'cqName',
        span: 8,
        disabled: true,
        hidden: false,
        defaultSelect: true,
      },
      {
        label: '业务系统',
        type: 'select',
        key: 'businessSystemId',
        options: busiSystemOptions,
        span: 8,
        disabled: true,
        hidden: false,
        defaultSelect: true,
      },
      {
        span: 8,
        render() {
          return (
            <div style="display: flex;justify-content: flex-end;">
              <el-button type="primary" link>
                <ConditionFilter
                  formModel={formModel}
                  resourceList={formOptions[0].groupItems}
                ></ConditionFilter>
              </el-button>
              <el-button onClick={reset} icon={<Delete />}>
                重置
              </el-button>
              <el-button onClick={search} icon={<Search />} type="primary">
                搜索
              </el-button>
              <el-button onClick={funcexport} icon={<Upload />} type="primary">
                导出
              </el-button>
              <el-button
                type="primary"
                link
                class="search-isOpen"
                onClick={() => (collapsed.value = !collapsed.value)}
              >
                {collapsed.value ? '展开' : '折叠'}
                <el-icon class="el-icon--right">
                  {collapsed.value ? <ArrowDown /> : <ArrowUp />}
                </el-icon>
              </el-button>
            </div>
          )
        },
      },
      {
        label: 'vCPU',
        type: 'input',
        key: 'vCpus',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '内存',
        type: 'input',
        key: 'ram',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: 'GPU算力',
        type: 'input',
        key: 'gpuRatio',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: 'GPU显存',
        type: 'input',
        key: 'gpuVirtualMemory',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: 'GPU卡数量',
        type: 'input',
        key: 'gpuCore',
        span: 8,
        disabled: false,
        hidden: true,
      },

      {
        label: '云类型',
        type: 'select',
        key: 'catalogueDomainCode',
        options: cloudTypeDic,
        labelField: 'name',
        valueField: 'code',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '云平台',
        type: 'select',
        key: 'domainCode',
        options: cloudPlatformDic,
        labelField: 'name',
        valueField: 'code',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '资源池',
        type: 'select',
        key: 'regionId',
        options: resourcePoolsDic,
        labelField: 'name',
        valueField: 'id',
        span: 8,
        disabled: false,
        hidden: true,
      },

      {
        label: '申请人',
        type: 'input',
        key: 'applyUserName',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '申请时长',
        type: 'select',
        key: 'applyTime',
        options: getDic('time'),
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '4A账号',
        type: 'input',
        key: 'a4Account',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '4A账号绑定的手机号',
        type: 'input',
        key: 'a4Phone',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '创建时间',
        type: 'date',
        key: 'createTime',
        span: 8,
        disabled: false,
        hidden: true,
        props: {
          type: 'datetimerange',
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          rangeSeparator: '至',
          startPlaceholder: '开始时间',
          endPlaceholder: '结束时间',
        },
      },
    ],
  },
])

// 批量回收
const dataListRef = ref()
const handleBatchRecycle = () => {
  dataListRef.value?.handleBatchRecycle()
}

// 资源变更
const handleBatchChange = () => {
  dataListRef.value?.handleResourceChange()
}

// 资源延期
const handleBatchDelay = () => {
  dataListRef.value?.handleResourceDelay()
}

/**
 * @description: 批量导出
 */
const funcexport = async () => {
  const params: any = {
    ...formModel,
  }
  const newParams = changeDateFormat(params, ['createTime'])
  const temName = '容器配额数据.xlsx'
  useDownload(containerExportApi, temName, newParams)
}
</script>

<style lang="scss" scoped>
.sl-table-content {
  overflow: auto !important;
}
.sl-page-content {
  // 减去头部的标题高度 - 不减去会超出屏幕
  overflow-x: hidden;
  height: calc(100% - 90px);
}
.header-search-input {
  width: 350px;
}
.table-layout {
  margin: 2px 8px 8px 8px;
  flex: 1;
  overflow: hidden;
}
.btn.op {
  margin: 8px 8px 0 8px;
}
.filter-form-con {
  margin: 8px 8px 0 8px;
  background: #fff;
  border-radius: 8px;
}
.filter-form.collapsed {
  height: 50px;
}
.filter-form {
  height: 110px;
  overflow: hidden;
  transition: height 0.2s;
}
</style>
