<template>
  <div class="sl-minio-uploader">
    <div class="sl-minio-uploader__container">
      <!-- 文件选择区域 -->
      <el-upload
        v-model:file-list="fileList"
        class="sl-minio-uploader__upload-area"
        drag
        :auto-upload="false"
        :on-change="handleFileChange"
        :limit="1"
        ref="uploadRef"
        :accept="acceptFileTypes"
        :before-remove="handleBeforeRemove"
        :on-preview="handlePreview"
      >
        <div class="sl-minio-uploader__upload-content">
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="sl-minio-uploader__upload-text">点击或拖拽文件到此区域上传</div>
          <div class="sl-minio-uploader__upload-tip">
            只能选择一个文件进行上传，
            {{ maxFileSizeMB > 0 ? `大小不超过 ${maxFileSizeMB}MB` : '' }}
            {{ acceptFileTypes ? `，支持的类型: ${acceptFileTypes}` : '' }}
            <span v-if="props.sampleThreshold > 0" class="sl-minio-uploader__advanced-tip">
              (超过{{ props.sampleThreshold }}MB的文件将使用抽样哈希加速处理)
            </span>
          </div>
        </div>
      </el-upload>

      <!-- 已保存的上传信息显示 -->
      <div v-if="hasSavedUpload && !uploadState.file" class="sl-minio-uploader__saved">
        <div class="sl-minio-uploader__saved-info">
          <el-icon><i class="el-icon-info" /></el-icon>
          <span>检测到未完成的上传任务：{{ savedFileName }}</span>
        </div>
        <div class="sl-minio-uploader__saved-actions">
          <el-button type="primary" size="small" @click="resumeSavedUpload">继续上传</el-button>
          <el-button type="danger" size="small" @click="discardSavedUpload">放弃</el-button>
        </div>
      </div>

      <!-- 上传状态展示 -->
      <div
        class="sl-minio-uploader__status"
        v-if="uploadState.file && uploadState.uploadProgress < 100"
      >
        <div class="sl-minio-uploader__file-info">
          <span class="sl-minio-uploader__file-name">{{ uploadState.file.name }}</span>
          <span class="sl-minio-uploader__file-size">
            {{ formatFileSize(uploadState.file.size) }}{{ isUploading }}
          </span>
        </div>

        <!-- 上传按钮区域 -->
        <div class="sl-minio-uploader__actions">
          <el-button
            type="danger"
            @click="cancelUpload"
            v-if="isUploading"
            class="sl-minio-uploader__action-button"
          >
            暂停
          </el-button>
          <el-button
            type="success"
            @click="handleFileUpload"
            v-else
            :disabled="!uploadState.file || isUploading"
            class="sl-minio-uploader__action-button"
          >
            继续
          </el-button>
        </div>
      </div>

      <!-- 进度信息 -->
      <div
        v-if="isUploading || uploadState.uploadProgress < 100"
        class="sl-minio-uploader__progress-area"
      >
        <!-- 处理状态 -->
        <!-- <div
          v-if="isUploading && uploadState.uploadProgress === 0"
          class="sl-minio-uploader__process-info"
        >
          <span>正在处理文件: {{ processedChunks }} / {{ totalChunks }} 分片</span>
          <el-progress
            :percentage="Math.floor((processedChunks / Math.max(totalChunks, 1)) * 100)"
            :stroke-width="10"
            status="warning"
          ></el-progress>
        </div> -->

        <!-- 上传进度 -->
        <div v-if="uploadState.uploadProgress > 0" class="sl-minio-uploader__progress">
          <el-progress
            :percentage="uploadState.uploadProgress"
            :format="progressFormat"
            :text-inside="true"
            :stroke-width="20"
          >
          </el-progress>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, computed, defineProps, defineEmits, onMounted, onBeforeUnmount } from 'vue'
import SparkMD5 from 'spark-md5'
import { checkMd5, merge, getPresignedUrl, directRequest } from '@/api/modules'
import { type UploadProps, ElMessage } from 'element-plus'
import { UploadFilled } from '@element-plus/icons-vue'

// 上传状态枚举
enum UploadStateEnum {
  Idle = 'idle', // 空闲状态
  Processing = 'processing', // 处理和上传中
  Success = 'success', // 上传成功
  Error = 'error', // 上传失败
  Cancelled = 'cancelled', // 上传取消
}

// 上传任务本地存储的键名
const STORAGE_KEY = 'sl-minio-uploader-state'
const fileList = ref<{ name: string; url: string }[]>([])
// 定义组件属性
const props = withDefaults(
  defineProps<{
    // 最大文件大小 (MB), 0表示不限制
    maxFileSize?: number
    // 接受的文件类型
    accept?: string
    // 最大并发上传数
    maxConcurrency?: number
    // 最大重试次数
    maxRetryCount?: number
    // 持久化上传状态
    persistState?: boolean
    // 组件ID，用于区分多个上传组件的存储数据
    uploaderId?: string
    // 使用抽样哈希的文件大小阈值(MB)，超过此大小使用抽样哈希而非完整哈希
    sampleThreshold?: number
    // 每个样本的大小(KB)
    sampleSize?: number
    // 中间取样数量
    middleSampleCount?: number
  }>(),
  {
    maxFileSize: 0,
    accept: '',
    maxConcurrency: 3,
    maxRetryCount: 3,
    persistState: true,
    uploaderId: 'default',
    sampleThreshold: 10, // 默认10MB
    sampleSize: 1024, // 默认1MB
    middleSampleCount: 5, // 默认取5个中间样本
  },
)
let presignedUrls: any[] = []
// 定义事件
const emit = defineEmits([
  'uploadStart', // 上传开始
  'uploadSuccess', // 上传成功
  'uploadError', // 上传错误
  'uploadProgress', // 上传进度
  'uploadCancel', // 上传取消
])

/**
 * 文件块接口定义
 */
interface FileChunk {
  start: number
  end: number
  index: number
  hash: string
  blob: Blob
}

/**
 * 保存的上传状态接口
 */
interface SavedUploadState {
  fileName: string
  fileSize: number
  fileType: string
  fileMd5: string
  lastModified: number
  uploadProgress: number
  timestamp: number
}

// 计算属性
const maxFileSizeMB = computed(() => props.maxFileSize)
const acceptFileTypes = computed(() => props.accept)
const storageKey = computed(() => `${STORAGE_KEY}-${props.uploaderId}`)
const savedFileName = computed(() => savedUploadState.value?.fileName || '')
const hasSavedUpload = computed(() => !!savedUploadState.value)

// 常量配置
const CHUNK_SIZE = 5 * 1024 * 1024 // 分片大小：5MB (MinIO合并最低要求)
const THREAD_COUNT = navigator.hardwareConcurrency || 4 // 使用可用的CPU核心数，默认为4
const MD5_CHUNK_SIZE = 2 * 1024 * 1024 // 用于计算文件MD5的分片大小（较小以提高速度）
// 计算抽样阈值（MB转字节）
const SAMPLE_THRESHOLD = props.sampleThreshold * 1024 * 1024
// 样本大小（KB转字节）
const SAMPLE_SIZE = props.sampleSize * 1024

// 上传状态
const uploadState = reactive({
  file: null as File | null, // 当前选择的文件
  md5: '', // 文件的MD5值
  uploadProgress: 0, // 上传进度百分比
  status: UploadStateEnum.Idle, // 上传状态
  shouldCancel: false, // 是否应该取消上传
  uploadStatus: 'success' as 'success' | 'exception' | 'warning', // 上传状态样式
})

// 控制变量
const uploadRef = ref() // 上传组件引用
const fileChunks = ref<FileChunk[]>([]) // 文件分片数组
const workers = ref<Worker[]>([]) // 存储worker引用
const activeUploads = ref<AbortController[]>([]) // 活跃的上传请求
const retryCountMap = ref<Map<number, number>>(new Map()) // 分片重试次数映射
const savedUploadState = ref<SavedUploadState | null>(null) // 保存的上传状态
const periodicSaveInterval = ref<number | null>(null) // 定时保存的间隔ID

// 文件处理状态
const processedChunks = ref(0)
const totalChunks = ref(0)
const chunkQueue = ref<{ chunk: FileChunk; chunkNumber: number }[]>([])
const processingComplete = ref(false)
const autoUploadAfterProcess = ref(false)

// 计算属性：是否正在处理或上传
const isUploading = computed(() => uploadState.status === UploadStateEnum.Processing)

// 生命周期钩子
onMounted(() => {
  // 从本地存储中恢复保存的上传状态
  if (props.persistState) {
    loadSavedState()
  }

  // 添加页面关闭事件，保存上传状态
  window.addEventListener('beforeunload', saveCurrentStateBeforeUnload)
})

onBeforeUnmount(() => {
  // 清理资源
  terminateAllWorkers()
  clearAllUploads()
  clearPeriodicSave()

  // 移除事件监听
  window.removeEventListener('beforeunload', saveCurrentStateBeforeUnload)
})
const handlePreview = () => {
  // a标签下载
  const a = document.createElement('a')
  a.href = fileList.value[0].url
  a.download = fileList.value[0].name
  a.click()
}
/**
 * 加载保存的上传状态
 */
const loadSavedState = () => {
  try {
    const savedState = localStorage.getItem(storageKey.value)
    if (savedState) {
      const parsedState = JSON.parse(savedState) as SavedUploadState

      // 检查保存时间是否超过24小时（86400000毫秒）
      const now = Date.now()
      if (now - parsedState.timestamp > 86400000) {
        localStorage.removeItem(storageKey.value)
        return
      }

      savedUploadState.value = parsedState
    }
  } catch (error) {
    console.error('加载上传状态失败:', error)
    localStorage.removeItem(storageKey.value)
  }
}

/**
 * 保存当前上传状态
 */
const saveCurrentState = () => {
  if (!props.persistState || !uploadState.file || !uploadState.md5) return

  try {
    const file = uploadState.file
    const stateToSave: SavedUploadState = {
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type,
      fileMd5: uploadState.md5,
      lastModified: file.lastModified,
      uploadProgress: uploadState.uploadProgress,
      timestamp: Date.now(),
    }

    localStorage.setItem(storageKey.value, JSON.stringify(stateToSave))
  } catch (error) {
    console.error('保存上传状态失败:', error)
  }
}

/**
 * 页面关闭前保存当前状态
 */
const saveCurrentStateBeforeUnload = () => {
  if (isUploading.value || (uploadState.file && uploadState.uploadProgress < 100)) {
    saveCurrentState()
  }
}

/**
 * 开始定期保存状态
 */
const startPeriodicSave = () => {
  if (!props.persistState) return

  // 清除可能存在的定时器
  clearPeriodicSave()

  // 每10秒保存一次状态
  periodicSaveInterval.value = window.setInterval(() => {
    if (isUploading.value) {
      saveCurrentState()
    } else {
      clearPeriodicSave()
    }
  }, 10000)
}

/**
 * 清除定期保存
 */
const clearPeriodicSave = () => {
  if (periodicSaveInterval.value) {
    clearInterval(periodicSaveInterval.value)
    periodicSaveInterval.value = null
  }
}

/**
 * 清除所有上传请求
 */
const clearAllUploads = () => {
  console.log(`中止 ${activeUploads.value.length} 个活跃上传请求`)

  // 终止所有活跃的上传请求
  activeUploads.value.forEach((controller) => {
    try {
      controller.abort()
    } catch (error) {
      console.error('取消上传请求失败:', error)
    }
  })

  // 清空活跃上传列表
  activeUploads.value = []
}

/**
 * 放弃保存的上传
 */
const discardSavedUpload = () => {
  localStorage.removeItem(storageKey.value)
  savedUploadState.value = null
}

/**
 * 恢复保存的上传
 */
const resumeSavedUpload = async () => {
  if (!savedUploadState.value) return

  try {
    // 模拟文件对象
    const savedState = savedUploadState.value

    // 显示正在准备的消息
    ElMessage.info('正在准备恢复上传，请稍候...')

    // 设置MD5，这样可以跳过MD5计算
    uploadState.md5 = savedState.fileMd5

    // 提示用户重新选择相同的文件
    ElMessage.warning('请重新选择相同的文件以恢复上传')

    // 清除已保存的状态，因为用户需要重新选择文件
    savedUploadState.value = null
  } catch (error) {
    ElMessage.error(`恢复上传失败: ${error instanceof Error ? error.message : String(error)}`)
    console.error('恢复上传失败:', error)
    discardSavedUpload()
  }
}

/**
 * 格式化进度条文字
 */
const progressFormat = (percentage: number) => {
  if (percentage === 100 && uploadState.uploadStatus === 'success') return '上传完成'
  if (uploadState.uploadStatus === 'exception') return '上传失败'
  if (uploadState.uploadStatus === 'warning') return '已取消'
  return `${Math.ceil(percentage)}%`
}

/**
 * 处理文件移除前
 */
const handleBeforeRemove = () => {
  // 如果正在上传，阻止移除
  if (isUploading.value) {
    ElMessage.warning('正在上传中，请先取消上传')
    return false
  }

  // 重置状态
  resetState()
  return true
}

/**
 * 重置状态
 */
const resetState = () => {
  uploadState.file = null
  uploadState.md5 = ''
  uploadState.uploadProgress = 0
  uploadState.status = UploadStateEnum.Idle
  uploadState.shouldCancel = false
  uploadState.uploadStatus = 'success'
  fileChunks.value = []
  processedChunks.value = 0
  totalChunks.value = 0
  chunkQueue.value = []
  processingComplete.value = false
  autoUploadAfterProcess.value = false
}

/**
 * 验证文件
 * @param file - 文件对象
 * @returns 验证结果，通过返回true
 */
const validateFile = (file: File): boolean => {
  // 验证文件大小
  if (props.maxFileSize > 0) {
    const fileSizeInMB = file.size / (1024 * 1024)
    if (fileSizeInMB > props.maxFileSize) {
      ElMessage.error(`文件大小超过限制，最大 ${props.maxFileSize}MB`)
      return false
    }
  }

  // 验证文件类型（如果有设置）
  if (props.accept && props.accept.trim() !== '') {
    const fileType = file.type
    const fileName = file.name
    const fileExtension = fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase()

    // 检查MIME类型或扩展名是否匹配接受的文件类型
    const acceptTypes = props.accept.split(',').map((type) => type.trim().toLowerCase())
    const isTypeMatched = acceptTypes.some((type) => {
      // 检查MIME类型
      if (type.includes('/')) {
        if (type === '*/*') return true
        if (type.endsWith('/*')) {
          const mainType = type.split('/')[0]
          return fileType.startsWith(mainType + '/')
        }
        return fileType === type
      }

      // 检查文件扩展名（.jpg, .png等）
      if (type.startsWith('.')) {
        return `.${fileExtension}` === type
      }

      return false
    })

    if (!isTypeMatched) {
      ElMessage.error(`不支持的文件类型，请上传${props.accept}格式的文件`)
      return false
    }
  }

  return true
}

/**
 * 检查是否是与保存的上传相同的文件
 * @param file - 文件对象
 * @returns 是否是相同的文件
 */
const isSameAsSavedFile = (file: File): boolean => {
  if (!savedUploadState.value) return false

  const saved = savedUploadState.value
  return (
    file.name === saved.fileName &&
    file.size === saved.fileSize &&
    file.lastModified === saved.lastModified
  )
}

/**
 * 格式化文件大小
 */
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'

  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 处理文件选择变更
 * @param uploadFile - 上传文件对象
 */
const handleFileChange: UploadProps['onChange'] = async (uploadFile) => {
  try {
    // 重置状态
    resetState()

    const file = uploadFile.raw as File

    // 验证文件
    if (!validateFile(file)) {
      uploadRef.value.clearFiles()
      return
    }

    uploadState.file = file
    console.log('uploadState.file', uploadState.file)

    // 检查是否是恢复的文件
    if (isSameAsSavedFile(file) && savedUploadState.value?.fileMd5) {
      console.log('savedUploadState.value', savedUploadState.value)

      // 使用保存的MD5值
      uploadState.md5 = savedUploadState.value.fileMd5
      ElMessage.success('已恢复之前的上传状态')

      // 清除保存的状态
      localStorage.removeItem(storageKey.value)
      savedUploadState.value = null
    }
    // 自动开始处理和上传文件
    startFileProcessingAndUpload()
  } catch (error) {
    processedChunks.value = 0
    uploadState.file = null
    ElMessage.error(`文件处理失败: ${error instanceof Error ? error.message : String(error)}`)
    console.error('文件处理错误:', error)
  }
}

/**
 * 开始文件处理和上传
 */
const startFileProcessingAndUpload = async (uploadProgress = 0) => {
  if (!uploadState.file || isUploading.value) return

  processedChunks.value = 0
  processingComplete.value = false
  uploadState.status = UploadStateEnum.Processing
  uploadState.uploadProgress = uploadProgress
  uploadState.uploadStatus = 'success'
  uploadState.shouldCancel = false
  retryCountMap.value.clear()

  // 开始定期保存状态
  if (props.persistState) {
    startPeriodicSave()
  }

  // 触发上传开始事件
  emit('uploadStart', { file: uploadState.file, md5: uploadState.md5 })

  try {
    // 计算总分片数
    const fileSize = uploadState.file.size
    totalChunks.value = Math.ceil(fileSize / CHUNK_SIZE)

    // 如果有MD5则使用，否则生成文件MD5
    if (!uploadState.md5) {
      uploadState.md5 = await generateFileHash(uploadState.file)
    }

    // 开始处理分片并同时上传
    await processAndUploadFileChunks(uploadState.file)

    // 如果上传被取消，不进行合并
    if (uploadState.shouldCancel) return

    // 通知后端合并分片
    if (uploadState.uploadProgress >= 100) {
      const result = await mergeFileChunks(uploadState.md5, uploadState.file.name)
      fileList.value = [
        {
          name: uploadState.file.name,
          url: result,
        },
      ]
      // 上传完成后清理
      // uploadRef.value.clearFiles()
      ElMessage.success('文件上传成功')

      // 清除保存的状态
      if (props.persistState) {
        localStorage.removeItem(storageKey.value)
        clearPeriodicSave()
      }

      // 触发成功事件
      emit('uploadSuccess', { url: result, fileName: uploadState.file.name, md5: uploadState.md5 })

      return result
    }
  } catch (error) {
    uploadState.uploadStatus = 'exception'
    ElMessage.error(`上传失败: ${error instanceof Error ? error.message : String(error)}`)
    console.error('上传错误:', error)

    // 保存当前状态以便恢复
    if (props.persistState) {
      saveCurrentState()
    }

    // 触发错误事件
    emit('uploadError', { error })
  } finally {
    uploadState.status = UploadStateEnum.Idle
    clearPeriodicSave()
  }
}

/**
 * 生成整个文件的哈希值
 * 使用抽样策略减少计算时间和内存消耗
 */
const generateFileHash = async (file: File): Promise<string> => {
  if (!file) return ''

  console.time('Hash Generation')

  // 对于小文件(小于设定阈值)，直接计算完整哈希值
  if (file.size < SAMPLE_THRESHOLD) {
    console.log(`小文件(${formatFileSize(file.size)})，计算完整哈希值`)
    const hash = await calculateCompleteFileHash(file)
    console.timeEnd('Hash Generation')
    return hash
  }

  // 对于大文件，采用抽样策略
  console.log(`大文件(${formatFileSize(file.size)})，使用抽样哈希策略`)
  console.time('Sampled Hash Generation')

  const totalSize = file.size
  const samples = []

  // 取文件头部样本
  samples.push(file.slice(0, Math.min(SAMPLE_SIZE, totalSize)))

  // 取文件尾部样本
  if (totalSize > SAMPLE_SIZE * 2) {
    samples.push(file.slice(Math.max(0, totalSize - SAMPLE_SIZE), totalSize))
  }

  // 从中间均匀取样
  if (totalSize > SAMPLE_SIZE * 10) {
    const middleCount = props.middleSampleCount
    for (let i = 1; i <= middleCount; i++) {
      const position = Math.floor((totalSize * i) / (middleCount + 1))
      samples.push(
        file.slice(
          Math.max(0, position - SAMPLE_SIZE / 2),
          Math.min(position + SAMPLE_SIZE / 2, totalSize),
        ),
      )
    }
  }

  // 添加文件大小和名称作为额外信息，降低哈希冲突的可能性
  const fileInfo = `${file.name}:${file.size}:${file.lastModified}`
  const infoBlob = new Blob([fileInfo])
  samples.push(infoBlob)

  console.log(
    `共采集 ${samples.length} 个样本点，总采样大小约 ${formatFileSize(samples.reduce((acc, sample) => acc + sample.size, 0))}`,
  )

  // 合并所有样本并计算哈希
  const combinedBlob = new Blob(samples)

  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const spark = new SparkMD5.ArrayBuffer()
        spark.append(e.target?.result as ArrayBuffer)
        // 添加 'sampled-' 前缀表示这是一个抽样哈希
        const hash = 'sampled' + spark.end()
        console.log(`抽样哈希生成完成: ${hash}`)
        console.timeEnd('Sampled Hash Generation')
        console.timeEnd('Hash Generation')
        resolve(hash)
      } catch (error) {
        console.error('生成哈希失败:', error)
        reject(error)
      }
    }
    reader.onerror = (e) => {
      console.error('读取文件失败:', e)
      reject(e)
    }
    reader.readAsArrayBuffer(combinedBlob)
  })
}

/**
 * 计算完整文件哈希值（用于小文件）
 */
const calculateCompleteFileHash = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const chunkSize = MD5_CHUNK_SIZE
    const chunks = Math.ceil(file.size / chunkSize)
    let currentChunk = 0
    const spark = new SparkMD5.ArrayBuffer()
    const fileReader = new FileReader()

    fileReader.onload = (e) => {
      spark.append(e.target?.result as ArrayBuffer)
      currentChunk++

      if (currentChunk < chunks) {
        loadNext()
      } else {
        const md5 = spark.end()
        resolve(md5)
      }
    }

    fileReader.onerror = (e) => {
      reject(e)
    }

    function loadNext() {
      const start = currentChunk * chunkSize
      const end = Math.min(start + chunkSize, file.size)
      fileReader.readAsArrayBuffer(file.slice(start, end))
    }

    loadNext()
  })
}

/**
 * 处理文件分片并同时上传
 */
const processAndUploadFileChunks = async (file: File) => {
  if (!file) {
    processedChunks.value = 0
    return
  }

  try {
    const totalChunksCount = Math.ceil(file.size / CHUNK_SIZE)
    totalChunks.value = totalChunksCount

    // 清除之前可能存在的workers
    terminateAllWorkers()

    // 重置队列
    chunkQueue.value = []

    // 记录已处理的分片数量
    processedChunks.value = 0

    // 获取已上传的分片信息
    let uploadedChunks = new Set<number>()
    try {
      uploadedChunks = await checkAlreadyUploadedChunks(uploadState.md5 || '')
      console.log('uploadedChunks', uploadedChunks)
    } catch (error) {
      console.warn('检查已上传分片失败，将上传所有分片:', error)
    }

    // 如果中途取消，立即返回
    if (uploadState.shouldCancel) {
      console.log('文件处理被取消')
      return
    }
    // 获取预签名地址
    const response = await getPresignedUrl({ md5: uploadState.md5, totalChunksCount })
    presignedUrls = response.entity
    // 开始并发上传处理器
    const uploadProcessor = startStreamingUploadProcessor(uploadedChunks, totalChunksCount)

    // 创建一个Promise，在所有分片处理完成时解析
    const processingPromise = new Promise<void>((resolve, reject) => {
      // 使用并发处理器
      const concurrency = Math.min(THREAD_COUNT, navigator.hardwareConcurrency || 4)
      let processedCount = 0

      // 监听分片处理完成事件
      const handleChunkProcessed = (chunk: FileChunk) => {
        // 如果已取消，直接返回
        if (uploadState.shouldCancel) return

        // 更新已处理分片数量
        processedCount++
        processedChunks.value = processedCount

        // 检查分片是否已上传
        const chunkNumber = chunk.index + 1

        // 只将未上传的分片加入队列
        if (!uploadedChunks.has(chunkNumber) && !uploadState.shouldCancel) {
          chunkQueue.value.push({ chunk, chunkNumber })
        }

        // 如果所有分片都已处理完成
        if (processedCount === totalChunksCount) {
          resolve()
        }
      }

      // 计算每个工作线程负责的分片数
      const chunksPerWorker = Math.ceil(totalChunksCount / concurrency)

      // 如果已经取消，直接返回
      if (uploadState.shouldCancel) {
        resolve()
        return
      }

      // 启动多线程处理文件分片
      for (let i = 0; i < concurrency; i++) {
        // 如果已取消，不再创建新的worker
        if (uploadState.shouldCancel) {
          resolve()
          return
        }

        // 计算当前线程负责的分片范围
        const startChunk = i * chunksPerWorker
        let endChunk = (i + 1) * chunksPerWorker

        // 确保最后一个线程不超出总分片数
        if (endChunk > totalChunksCount) {
          endChunk = totalChunksCount
        }

        if (startChunk >= endChunk) continue

        // 创建工作线程
        const worker = new Worker(new URL('../../workers/chunkHashWorker.js', import.meta.url), {
          type: 'module',
        })

        // 存储worker引用以便后续清理
        workers.value.push(worker)

        // 发送任务到工作线程，使用流式返回模式
        worker.postMessage({
          file,
          start: startChunk,
          end: endChunk,
          CHUNK_SIZE,
          returnIndividually: true,
        })

        // 处理工作线程返回的结果
        worker.onmessage = (e) => {
          // 如果已取消，忽略worker的消息
          if (uploadState.shouldCancel) {
            worker.terminate()
            return
          }

          try {
            // 检查是否返回了错误
            if (e.data.error) {
              terminateAllWorkers()
              reject(new Error(e.data.error))
              return
            }

            // 检查是否所有分片都已处理完成
            if (e.data.completed) {
              worker.terminate()
              return
            }

            // 处理单个分片
            if (Array.isArray(e.data) && e.data.length > 0) {
              e.data.forEach((chunk: FileChunk) => {
                handleChunkProcessed(chunk)
              })
            }
          } catch (error) {
            terminateAllWorkers()
            reject(
              `工作线程处理文件失败: ${error instanceof Error ? error.message : String(error)}`,
            )
          }
        }

        // 处理工作线程错误
        worker.onerror = (error) => {
          terminateAllWorkers()
          reject(`工作线程遇到错误: ${error}`)
        }
      }
    })

    // 等待处理完成或取消
    try {
      await Promise.race([
        processingPromise,
        // 监听取消事件
        new Promise((_, reject) => {
          const cancelCheck = setInterval(() => {
            if (uploadState.shouldCancel) {
              clearInterval(cancelCheck)
              reject(new Error('处理被用户取消'))
            }
          }, 100)

          // 清理定时器
          onBeforeUnmount(() => {
            clearInterval(cancelCheck)
          })
        }),
      ])
    } catch (error: any) {
      if (error.message === '处理被用户取消') {
        console.log('文件分片处理被用户取消')
        return
      }
      throw error
    }

    // 如果已取消，直接返回
    if (uploadState.shouldCancel) {
      return
    }

    // 设置处理完成标志
    processingComplete.value = true

    return uploadProcessor
  } catch (error) {
    console.error('处理文件分片错误:', error)
    processedChunks.value = 0
    throw error
  }
}

/**
 * 开始流式上传处理器
 */
const startStreamingUploadProcessor = async (
  uploadedChunks: Set<number>,
  totalChunksCount: number,
) => {
  console.log('开始流式上传处理器')

  // 并发控制
  const concurrency = props.maxConcurrency
  let uploading = 0
  let completed = uploadedChunks.size
  let isCompleted = false

  // 用于跟踪上传的分片
  const uploadedChunksMap = new Map<number, boolean>()
  uploadedChunks.forEach((num) => uploadedChunksMap.set(num, true))

  // 更新初始进度
  if (completed > 0) {
    uploadState.uploadProgress = (completed / totalChunksCount) * 100
    emit('uploadProgress', { progress: uploadState.uploadProgress })
  }

  return new Promise<void>(async (resolve) => {
    // 设置初始进度 让进度条立马显示
    console.log('completed', completed, totalChunksCount)
    uploadState.uploadProgress = (completed / totalChunksCount) * 100 || 0.0001
    emit('uploadProgress', { progress: uploadState.uploadProgress })
    // 定期检查队列中的分片并上传
    const checkInterval = setInterval(async () => {
      // 如果被取消，则立即停止上传
      if (uploadState.shouldCancel) {
        clearInterval(checkInterval)
        isCompleted = true
        resolve()
        return
      }

      // 如果处理已完成且队列为空且没有正在上传的分片，则完成
      if (
        processingComplete.value &&
        chunkQueue.value.length === 0 &&
        uploading === 0 &&
        !isCompleted
      ) {
        clearInterval(checkInterval)
        isCompleted = true
        resolve()
        return
      }

      // 尝试从队列中获取分片并上传
      while (chunkQueue.value.length > 0 && uploading < concurrency && !uploadState.shouldCancel) {
        const { chunk, chunkNumber } = chunkQueue.value.shift()!

        // 检查是否已经上传过
        if (uploadedChunksMap.get(chunkNumber)) {
          continue
        }

        uploading++

        // 上传分片
        uploadSingleChunkWithRetry(chunk.blob, chunkNumber)
          .then(() => {
            // 如果已取消，不更新进度
            if (uploadState.shouldCancel) return

            // 标记为已上传
            uploadedChunksMap.set(chunkNumber, true)
            completed++
            console.log(
              'completed',
              completed,
              totalChunksCount,
              Math.floor((completed / totalChunksCount) * 100),
            )
            // 更新上传进度
            uploadState.uploadProgress = (completed / totalChunksCount) * 100
            emit('uploadProgress', { progress: uploadState.uploadProgress })

            // 保存当前状态
            if (props.persistState) {
              saveCurrentState()
            }
          })
          .catch((error) => {
            if (!uploadState.shouldCancel) {
              console.error(`分片 ${chunkNumber} 上传失败:`, error)
              // 失败的分片重新加入队列
              chunkQueue.value.push({ chunk, chunkNumber })
            }
          })
          .finally(() => {
            uploading--
          })
      }
    }, 100)

    // 确保如果组件被销毁，interval也被清除
    onBeforeUnmount(() => {
      clearInterval(checkInterval)
    })
  })
}

/**
 * 处理文件上传（用户手动点击上传按钮时）
 */
const handleFileUpload = () => {
  // if (processingComplete.value && !isUploading.value) {
  //   // 如果文件已全部处理完但尚未上传，开始上传
  //   console.log('如果文件已全部处理完但尚未上传，开始上传')
  //   console.log(uploadState.uploadProgress)

  //   handleUpload()
  // } else if (!processedChunks.value && !isUploading.value && uploadState.file) {
  //   // 如果还没开始处理，先处理再上传
  //   console.log('如果还没开始处理，先处理再上传')
  //   startFileProcessingAndUpload()
  // } else if (processedChunks.value && !isUploading.value) {
  //   // 如果正在处理但尚未开始上传，开始上传
  //   console.log('如果正在处理但尚未开始上传，开始上传')
  //   startFileProcessingAndUpload(uploadState.uploadProgress)
  // } else {
  //   ElMessage.warning('上传已在进行中')
  // }
  startFileProcessingAndUpload(uploadState.uploadProgress)
}

/**
 * 取消上传
 */
const cancelUpload = () => {
  if (!isUploading.value) return

  console.log('取消上传')

  // 标记应该取消
  uploadState.shouldCancel = true
  uploadState.uploadStatus = 'warning'

  // 终止所有工作线程
  terminateAllWorkers()

  // 清空队列
  // chunkQueue.value = []

  // 终止所有活跃的上传请求
  clearAllUploads()

  // 更新状态
  uploadState.status = UploadStateEnum.Cancelled

  // 重置进度显示
  // processedChunks.value = 0

  // 延迟一点时间再设置为空闲状态，确保UI有时间显示取消状态
  setTimeout(() => {
    uploadState.status = UploadStateEnum.Idle
  }, 500)

  // 如果有设置持久化，保存当前状态
  if (props.persistState) {
    saveCurrentState()
  }

  // 触发取消事件
  emit('uploadCancel')

  ElMessage.info('上传已取消')
}

/**
 * 处理文件上传
 */
// const handleUpload = async () => {
//   if (!uploadState.file || isUploading.value) return

//   try {
//     // 更新状态
//     uploadState.status = UploadStateEnum.Processing
//     // uploadState.uploadProgress = 0
//     uploadState.uploadStatus = 'success'
//     uploadState.shouldCancel = false
//     retryCountMap.value.clear()

//     const file = uploadState.file
//     const md5 = uploadState.md5

//     // 开始定期保存状态
//     if (props.persistState) {
//       startPeriodicSave()
//     }

//     // 触发上传开始事件
//     emit('uploadStart', { file, md5 })

//     // 确保文件MD5已计算完成
//     if (!processingComplete.value && md5 === '') {
//       ElMessage.warning('文件仍在处理中，请稍候...')
//       return
//     }

//     // 检查已上传的分片
//     const uploadedChunks = await checkAlreadyUploadedChunks(md5)

//     // 上传所有分片
//     await uploadAllChunks(fileChunks.value, uploadedChunks, md5)

//     // 如果上传被取消，不进行合并
//     if (uploadState.shouldCancel) return

//     // 通知后端合并分片
//     const result = await mergeFileChunks(md5, file.name)

//     // 上传完成后清理
//     // uploadRef.value.clearFiles()
//     ElMessage.success('文件上传成功')

//     // 清除保存的状态
//     if (props.persistState) {
//       localStorage.removeItem(storageKey.value)
//       clearPeriodicSave()
//     }

//     // 重置状态但保留结果
//     resetState()

//     // 触发成功事件
//     emit('uploadSuccess', { url: result, fileName: file.name, md5 })

//     return result
//   } catch (error) {
//     uploadState.uploadStatus = 'exception'
//     ElMessage.error(`上传失败: ${error instanceof Error ? error.message : String(error)}`)
//     console.error('上传错误:', error)

//     // 保存当前状态以便恢复
//     if (props.persistState) {
//       saveCurrentState()
//     }

//     // 触发错误事件
//     emit('uploadError', { error })
//   } finally {
//     uploadState.status = UploadStateEnum.Idle
//     clearPeriodicSave()
//   }
// }

/**
 * 检查已经上传的分片
 * @param md5 - 文件MD5值
 * @returns 已上传的分片集合
 */
const checkAlreadyUploadedChunks = async (md5: string): Promise<Set<number>> => {
  if (uploadState.shouldCancel) return new Set()

  try {
    // 如果是抽样哈希，需要去除前缀以适配后端API
    const hashToCheck = md5.startsWith('sampled-') ? md5 : md5
    const response = await checkMd5(hashToCheck)
    console.log('response', response)

    // 后端返回的已上传分片序号列表 (例如: "1:2:3")
    return new Set((response?.entity || '').split(':').filter(Boolean).map(Number))
  } catch (error) {
    console.error('检查已上传分片失败:', error)
    return new Set() // 如果检查失败，返回空集合，表示需要上传所有分片
  }
}

/**
 * 上传所有文件分片（带并发控制）
 * @param chunks - 所有文件分片
 * @param uploadedChunks - 已上传的分片集合
 * @param md5 - 文件MD5值
 */
// const uploadAllChunks = async (chunks: FileChunk[], uploadedChunks: Set<number>, md5: string) => {
//   // 统一数据结构，确保所有元素都有相同的格式
//   const chunksToProcess = processingComplete.value
//     ? chunks.map((chunk) => ({
//         chunk,
//         chunkNumber: chunk.index + 1,
//       }))
//     : chunkQueue.value.map((q) => ({
//         chunk: q.chunk,
//         chunkNumber: q.chunkNumber,
//       }))

//   // 需要上传的分片
//   const chunksToUpload = chunksToProcess.filter(
//     ({ chunkNumber }) => !uploadedChunks.has(chunkNumber),
//   )

//   // 已完成的分片数（包括已上传的）
//   let completedCount = uploadedChunks.size
//   const totalChunksCount = processingComplete.value ? chunks.length : totalChunks.value

//   // 更新初始进度
//   if (completedCount > 0) {
//     uploadState.uploadProgress = (completedCount / totalChunksCount) * 100
//     emit('uploadProgress', { progress: uploadState.uploadProgress })
//   }

//   // 没有需要上传的分片，直接返回
//   if (chunksToUpload.length === 0 && processingComplete.value) return

//   // 并发控制
//   const concurrency = Math.min(props.maxConcurrency, chunksToUpload.length || 1)
//   let currentIndex = 0

//   // 使用Promise.all并发上传
//   const uploadTasks = Array(concurrency)
//     .fill(null)
//     .map(async () => {
//       while (
//         (currentIndex < chunksToUpload.length || !processingComplete.value) &&
//         !uploadState.shouldCancel
//       ) {
//         // 如果还有分片要上传
//         if (currentIndex < chunksToUpload.length) {
//           const { chunk, chunkNumber } = chunksToUpload[currentIndex++]

//           try {
//             await uploadSingleChunkWithRetry(chunk.blob, chunkNumber, md5)
//             console.log(11)

//             // 更新进度
//             completedCount++
//             uploadState.uploadProgress = (completedCount / totalChunksCount) * 100
//             emit('uploadProgress', { progress: uploadState.uploadProgress })

//             // 保存当前状态
//             if (props.persistState) {
//               saveCurrentState()
//             }
//           } catch (error) {
//             if (!uploadState.shouldCancel) {
//               throw error // 重新抛出错误以便外部捕获
//             }
//           }
//         }
//         // 如果文件仍在处理中，需要等待新的分片
//         else if (!processingComplete.value) {
//           // 等待一段时间后检查队列中是否有新的分片
//           await new Promise((resolve) => setTimeout(resolve, 100))

//           // 更新要上传的分片列表
//           const newChunks = chunkQueue.value
//             .slice(chunksToUpload.length)
//             .filter(({ chunkNumber }) => !uploadedChunks.has(chunkNumber))

//           // 将新的分片添加到上传列表中
//           if (newChunks.length > 0) {
//             chunksToUpload.push(...newChunks)
//           }

//           // 如果处理已完成但队列中没有新分片，则退出循环
//           if (processingComplete.value && newChunks.length === 0) {
//             break
//           }
//         } else {
//           // 如果处理已完成且没有更多分片要上传，则退出循环
//           break
//         }
//       }
//     })

//   await Promise.all(uploadTasks)
// }

/**
 * 上传单个文件分片（带重试）
 * @param chunk - 文件分片数据
 * @param chunkNumber - 分片编号
 * @param md5 - 文件MD5值
 */
const uploadSingleChunkWithRetry = async (chunk: Blob, chunkNumber: number) => {
  // 每次检查是否已取消
  if (uploadState.shouldCancel) return

  let retryCount = retryCountMap.value.get(chunkNumber) || 0

  while (retryCount <= props.maxRetryCount) {
    // 再次检查是否已取消
    if (uploadState.shouldCancel) return

    try {
      await uploadSingleChunk(chunk, chunkNumber)

      // 上传成功，清除重试计数
      retryCountMap.value.delete(chunkNumber)
      return
    } catch (error) {
      // 如果已取消，不再重试
      if (uploadState.shouldCancel) return

      retryCount++
      retryCountMap.value.set(chunkNumber, retryCount)

      if (retryCount <= props.maxRetryCount) {
        // 计算退避时间：100ms * 2^重试次数 (最多等待3.2秒)
        const backoffTime = Math.min(3200, 100 * Math.pow(2, retryCount))
        console.warn(
          `分片 ${chunkNumber} 上传失败，${backoffTime}ms后重试 (${retryCount}/${props.maxRetryCount})`,
        )

        // 等待一段时间后重试
        await new Promise((resolve) => {
          // 设置重试计时器
          const timeout = setTimeout(resolve, backoffTime)

          // 取消监听
          const cancelWatcher = () => {
            if (uploadState.shouldCancel) {
              clearTimeout(timeout)
              resolve(null) // 结束等待
              return true
            }
            return false
          }

          // 立即检查一次是否已取消
          if (cancelWatcher()) return

          // 定期检查取消状态
          const cancelCheck = setInterval(() => {
            if (cancelWatcher()) {
              clearInterval(cancelCheck)
            }
          }, 50)

          // 确保在timeout执行后清理interval
          setTimeout(() => {
            clearInterval(cancelCheck)
          }, backoffTime + 50)
        })
      } else {
        // 重试次数用完，抛出错误
        throw new Error(
          `分片 ${chunkNumber} 上传失败，已重试 ${props.maxRetryCount} 次: ${error instanceof Error ? error.message : String(error)}`,
        )
      }
    }
  }
}

/**
 * 上传单个文件分片
 * @param chunk - 文件分片数据
 * @param chunkNumber - 分片编号
 * @param md5 - 文件MD5值
 */
const uploadSingleChunk = async (chunk: Blob, chunkNumber: number) => {
  // 如果应该取消上传，直接返回
  if (uploadState.shouldCancel) return
  try {
    // 创建AbortController用于取消请求
    const controller = new AbortController()
    activeUploads.value.push(controller)
    console.log('上传单个文件分片', chunkNumber - 1, chunkNumber)
    await directRequest(presignedUrls[chunkNumber - 1], chunk, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      signal: controller.signal,
    })

    // 上传成功后从活跃列表中移除
    const index = activeUploads.value.indexOf(controller)
    if (index !== -1) {
      activeUploads.value.splice(index, 1)
    }
  } catch (error) {
    // 检查是否是取消导致的错误
    if (uploadState.shouldCancel) return

    // 从活跃列表中移除
    activeUploads.value = activeUploads.value.filter((c) => c.signal.aborted)

    // 抛出错误以便调用者处理
    throw new Error(
      `分片 ${chunkNumber} 上传失败: ${error instanceof Error ? error.message : String(error)}`,
    )
  }
}

/**
 * 通知后端合并文件分片
 * @param md5 - 文件MD5值
 * @param fileName - 文件名称
 * @returns 合并后的文件URL
 */
const mergeFileChunks = async (md5: string, fileName: string) => {
  // 如果应该取消上传，直接返回
  if (uploadState.shouldCancel) throw new Error('上传已取消')

  let retryCount = 0
  const maxRetries = props.maxRetryCount

  while (retryCount <= maxRetries) {
    try {
      const params: any = {
        md5,
        fileName,
      }

      // 如果是抽样哈希，添加标记字段
      if (md5.startsWith('sampled-')) {
        params.isSampledHash = 'true'
      }

      const result = await merge(null, { params })
      console.log('文件上传成功，URL:', result.message)
      return result.entity
    } catch (error) {
      retryCount++

      if (retryCount <= maxRetries) {
        // 计算退避时间
        const backoffTime = Math.min(3200, 100 * Math.pow(2, retryCount))
        console.warn(`合并文件失败，${backoffTime}ms后重试 (${retryCount}/${maxRetries})`)

        // 等待一段时间后重试
        await new Promise((resolve) => setTimeout(resolve, backoffTime))
      } else {
        throw new Error(
          `文件合并失败，已重试 ${maxRetries} 次: ${error instanceof Error ? error.message : String(error)}`,
        )
      }
    }
  }

  // 这里实际上不会执行到，因为循环中要么成功返回，要么抛出异常
  throw new Error('未知错误，无法合并文件')
}

/**
 * 终止所有活跃的worker
 */
const terminateAllWorkers = () => {
  console.log(`终止 ${workers.value.length} 个工作线程`)
  workers.value.forEach((worker) => {
    try {
      worker.terminate()
    } catch (e) {
      console.error('终止Worker失败:', e)
    }
  })
  workers.value = []
}
</script>

<style scoped>
.sl-minio-uploader {
  width: 100%;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei',
    Arial, sans-serif;
  cursor: pointer;
}

.sl-minio-uploader__container {
  width: 100%;
  margin: 0;
  padding: 10px;
  border-radius: 4px;
  background-color: #fff;
  border: 1px solid #dcdfe6;
}

.sl-minio-uploader__upload-area {
  width: 100%;
  margin-bottom: 10px;
}

.sl-minio-uploader__upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 12px 0;
}

.sl-minio-uploader__upload-text {
  margin: 6px 0;
  font-size: 14px;
  color: #606266;
}

.sl-minio-uploader__upload-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 6px;
  text-align: center;
}

.sl-minio-uploader__actions {
  display: flex;
  justify-content: flex-end;
  margin: 8px 0;
}

.sl-minio-uploader__action-button {
  min-width: 90px;
}

.sl-minio-uploader__progress {
  margin: 10px 0 5px;
}

.sl-minio-uploader__saved {
  margin: 8px 0;
  padding: 8px 12px;
  background-color: #f0f9eb;
  border-radius: 4px;
  border: 1px solid #e1f3d8;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
}

.sl-minio-uploader__saved-info {
  display: flex;
  align-items: center;
  color: #67c23a;
  font-size: 13px;
}

.sl-minio-uploader__saved-info i {
  margin-right: 4px;
}

.sl-minio-uploader__saved-actions {
  display: flex;
  gap: 6px;
}

.sl-minio-uploader__status {
  margin: 8px 0;
  padding: 8px 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
}

.sl-minio-uploader__file-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.sl-minio-uploader__file-name {
  font-size: 13px;
  font-weight: normal;
  color: #303133;
  word-break: break-all;
}

.sl-minio-uploader__file-size {
  font-size: 12px;
  color: #909399;
}

.sl-minio-uploader__progress-area {
  margin: 8px 0;
}

.sl-minio-uploader__process-info {
  margin-bottom: 6px;
  font-size: 13px;
  color: #606266;
}

/* 响应式调整 */
@media screen and (max-width: 480px) {
  .sl-minio-uploader__saved {
    flex-direction: column;
    align-items: flex-start;
  }

  .sl-minio-uploader__saved-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .sl-minio-uploader__status {
    flex-direction: column;
    align-items: flex-start;
  }

  .sl-minio-uploader__actions {
    width: 100%;
    justify-content: flex-end;
  }
}

.sl-minio-uploader__advanced-tip {
  display: block;
  margin-top: 4px;
  color: #909399;
  font-size: 11px;
  font-style: italic;
}
</style>
