<template>
  <div class="sl-form-container">
    <slot name="globalFormSlot"></slot>
    <div class="sl-form">
      <el-form
        ref="elFormRef"
        :model="formModel"
        :label-position="labelPosition"
        :label-width="labelWidth"
        :size="size"
        :show-message="showMessage"
      >
        <template v-for="group in optionsRef">
          <slot name="globalGroupSlot"></slot>
          <div
            v-if="!group.hidden"
            :style="group.style"
            :key="group.groupName"
            class="group-con bg-fff sl-layout"
          >
            <sl-block-title
              :title="group.groupName"
              :block-title-filter="group.blockTitleFilter"
              class="pb20"
              :style="group.blockTitleStyle || ''"
              v-if="showBlockTitle && !group.hideBlockTitle"
            >
              <template v-if="!!group.blockTitleSlot">
                <slot :name="group.blockTitleSlot" :group="group"></slot>
              </template>
              <template #blockTitleFilter>
                <slot
                  v-if="!!group.blockTitleFilter?.slot"
                  :name="group.blockTitleFilter?.slot"
                  :scope="group.blockTitleFilter"
                ></slot>
              </template>
            </sl-block-title>
            <slot v-if="group.slot" :name="group.slot" :form="formModel" :group="group"></slot>
            <el-row v-else class="sl-layout-inner" :gutter="group.gutter ?? 80">
              <template v-for="item in group.groupItems" :key="item.key">
                <el-col :offset="item.offset || 0" :span="item.span || 12" v-if="!item.hidden">
                  <!-- render函数 -->
                  <component
                    v-if="item.render && !item.hidden"
                    :is="item.render"
                    :form="formModel"
                    :item="item"
                  ></component>
                  <slot
                    :name="item.slotName"
                    :form="formModel"
                    :item="item"
                    v-else-if="item.type === 'slot' && !item.hidden && item.noFormItem"
                  >
                    <el-empty description="暂无数据" :image-size="100"> </el-empty>
                  </slot>
                  <!-- 纯文本 -->
                  <el-form-item
                    :label-width="item.labelWidth || labelWidth"
                    v-else-if="item.type === 'text' && !item.hidden"
                    :label="item.label + ':'"
                  >
                    <span :style="item.valueStyle || group.valueStyle" class="medium sle">
                      <el-tooltip
                        :content="String(getValue(formModel, item) || '')"
                        placement="top"
                      >
                        {{ getValue(formModel, item) || '' }}
                      </el-tooltip>
                    </span>
                  </el-form-item>
                  <!-- element组件 -->
                  <el-form-item
                    :required="item.required"
                    :label-width="item.labelWidth || labelWidth"
                    v-else-if="!item.hidden"
                    :label="item.hiddenLabel ? '' : item.label + ' :'"
                    :prop="item.key"
                    :rules="item.rules"
                  >
                    <!-- input -->
                    <el-input
                      :placeholder="item.placeholder || '请输入'"
                      v-bind="item.props || {}"
                      v-if="item.type === 'input'"
                      v-model="formModel[item.key]"
                      clearable
                    />
                    <el-input-number
                      v-bind="item.props || {}"
                      v-if="item.type === 'inputNumber'"
                      v-model="formModel[item.key]"
                      clearable
                    />
                    <!-- select -->
                    <el-select
                      clearable
                      filterable
                      multiple
                      :multiple-limit="item.limit"
                      v-bind="item.props?.select || {}"
                      v-if="item.type === 'select' && item.multiple == true"
                      v-model="formModel[item.key]"
                      @change="
                        (option) =>
                          onMultiSelectChange(
                            item,
                            option,
                            dicCollection[item.dicKey] ?? item.options,
                            formModel,
                          )
                      "
                    >
                      <el-option
                        v-for="option in dicCollection[item.dicKey] ?? item.options"
                        :key="option[item.valueField || 'value']"
                        :value="option[item.valueField || 'value']"
                        :label="option[item.labelField || 'label'] as any"
                        v-bind="item.props?.option || {}"
                      >
                        {{ option[item.labelField || 'label'] }}
                      </el-option>
                    </el-select>
                    <el-select
                      clearable
                      filterable
                      v-bind="item.props?.select || {}"
                      v-if="item.type === 'select' && item.multiple != true"
                      v-model="formModel[item.key]"
                      @change="
                        (option) =>
                          onChange(
                            item,
                            option,
                            dicCollection[item.dicKey] ?? item.options,
                            formModel,
                          )
                      "
                    >
                      <el-option
                        v-for="option in dicCollection[item.dicKey] ?? item.options"
                        :key="option[item.valueField || 'value']"
                        :value="option[item.valueField || 'value']"
                        :label="option[item.labelField || 'label'] as any"
                        v-bind="item.props?.option || {}"
                      >
                        {{ option[item.labelField || 'label'] }}
                      </el-option>
                    </el-select>
                    <!-- 单选 -->
                    <el-radio-group
                      v-bind="item.props?.group"
                      v-if="item.type === 'radio'"
                      v-model="formModel[item.key]"
                    >
                      <el-radio
                        v-bind="item.props?.radio"
                        v-for="option in dicCollection[item.dicKey] ?? item.options"
                        :key="option[item.valueField || 'value']"
                        :value="option[item.valueField || 'value']"
                      >
                        {{ option[item.labelField || 'label'] }}
                      </el-radio>
                    </el-radio-group>
                    <!-- 级联 -->
                    <el-cascader
                      style="width: 100%"
                      v-if="item.type === 'cascader'"
                      :options="item.options"
                      v-bind="item.props?.group"
                      v-model="formModel[item.key]"
                      filterable
                      @change="
                        (selected) =>
                          onCascaderChange(
                            item,
                            selected,
                            dicCollection[item.dicKey] ?? item.options,
                            formModel,
                          )
                      "
                      clearable
                    />
                    <!-- 时间日期选择器 -->
                    <el-date-picker
                      placeholder="请选择"
                      clearable
                      style="width: 100%"
                      v-if="item.type === 'date'"
                      v-model="formModel[item.key]"
                      v-bind="item.props"
                      v-on="item.listeners || {}"
                    />

                    <!-- 时间范围选择器 -->
                    <el-time-picker
                      palseholder="请选择时间"
                      clearable
                      style="width: 100%"
                      v-if="item.type === 'time'"
                      v-model="formModel[item.key]"
                      v-bind="item.props"
                      v-on="item.listeners || {}"
                    />

                    <!-- 上传文件 -->
                    <SlUpload
                      v-if="item.type === 'upload'"
                      style="width: 100%"
                      v-model:file-list="formModel[item.key]"
                      :file-type="item.fileType"
                      v-bind="item.props?.upload"
                    />
                    <el-switch
                      v-if="item.type === 'switch'"
                      v-model="formModel[item.key]"
                      v-bind="item.props || {}"
                    />
                    <!-- 插槽 -->
                    <slot
                      v-if="item.type === 'slot'"
                      :name="item.slotName"
                      :form="formModel"
                      :item="item"
                    ></slot>
                    <span v-if="item.suffix" class="suffix-item" style="margin-left: 4px">{{
                      item.suffix
                    }}</span>
                    <div class="suffix-item" v-if="item.suffixSlot">
                      <slot :name="item.key + 'SuffixSlot'" :form="formModel" :item="item"></slot>
                    </div>
                  </el-form-item>
                </el-col>
              </template>
            </el-row>
          </div>
        </template>
      </el-form>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, toRef } from 'vue'
import SlBlockTitle from '../base/SlBlockTitle.vue'
import type { FormInstance, FormValidateCallback } from 'element-plus'
import SlUpload from '@/components/SlUpload/index.vue'

const elFormRef = ref<FormInstance>()
defineSlots<{
  [key: string]: any
}>()
const props = withDefaults(
  defineProps<{
    gutter?: number
    labelPosition?: 'top' | 'left' | 'right'
    options: Array<any>
    modelValue?: any
    labelWidth?: string | number
    showBlockTitle?: boolean
    dicCollection?: any // 字典集合
    size?: 'large' | 'default' | 'small'
    showMessage?: boolean
  }>(),
  {
    gutter: 80,
    labelPosition: 'right',
    labelWidth: 'auto',
    options: () => [],
    modelValue: () => ({}),
    showBlockTitle: false,
    dicCollection: () => ({}),
    size: 'default',
    showMessage: true,
  },
)
const optionsRef = toRef(props.options)
const formModel = reactive(props.modelValue)

const getValue = (formModel: any, item: any) => {
  if (item.getter && typeof item.getter === 'function') {
    return item.getter(formModel, item) + (item.suffix || '')
  }
  return formModel[item.key] + (item.suffix || '')
}

const findOptionByValue = (options: any[], value: any, item: any) => {
  const valueField = item.valueField ?? 'value'
  const selectedOption = options.find((option) => option[valueField] === value)
  return selectedOption
}
const onChange = (item: any, option: any, options: any, formModel: any) => {
  if (!(item.onChange && typeof item.onChange === 'function')) return
  const selectedOption = findOptionByValue(options, option, item)
  item.onChange?.(selectedOption, formModel)
}
//多选下拉列表
const onMultiSelectChange = (item: any, option: any, options: any, formModel: any) => {
  const idSet = new Set(option)
  if (!(item.onChange && typeof item.onChange === 'function')) return
  const selectedOption = options.filter((obj: any) => idSet.has(obj.id))
  item.onChange?.(selectedOption, formModel)
}

const onCascaderChange = (item: any, selected: any, options: any, formModel: any) => {
  if (!(item.onChange && typeof item.onChange === 'function')) return
  const selectedOptions: any = []
  let currentOptions = options
  selected &&
    selected.forEach((ele: any) => {
      const selectedOption = findOptionByValue(currentOptions, ele, item)
      selectedOptions.push(selectedOption)
      currentOptions = selectedOption.children
    })
  item.onChange?.(selectedOptions, formModel)
}

const validate = async (cb?: FormValidateCallback) => {
  if (!elFormRef.value) return
  return await elFormRef.value.validate((valid, fields) => {
    if (cb) cb(valid, fields)
  })
}
const clearValidate = (props: any) => {
  if (!elFormRef.value) return
  elFormRef.value.clearValidate(props)
}
const validateField = async (props: any, cb: FormValidateCallback) => {
  if (!elFormRef.value) return
  return await elFormRef.value.validateField(props, (valid, fields) => {
    if (cb) cb(valid, fields)
  })
}
const scrollToField = (props: any) => {
  if (!elFormRef.value) return
  elFormRef.value.scrollToField(props)
}
const resetFields = () => {
  if (!elFormRef.value) return
  elFormRef.value.resetFields()
}
const fields = elFormRef.value?.fields || []

defineExpose({
  validate,
  resetFields,
  clearValidate,
  validateField,
  scrollToField,
  fields,
})
</script>

<style scoped lang="scss">
.empty-view {
  text-align: center;
  color: #999;
  padding: 20px;
}

.sl-form-container .sl-form .suffix-item {
  flex: 0;
  margin-left: 4px;
  display: flex;
}
.sl-form-container {
  position: relative;
  :deep(.el-form-item__content > *) {
    flex: 1;
  }
}
</style>
