<template>
  <SlProTable
    ref="proTable"
    highlight-current-row
    :columns="columns"
    style="min-height: 300px"
    :request-api="vpcList"
    :init-param="queryParams"
    :current-change="currentChange"
    hidden-table-header
    row-key="id"
  >
  </SlProTable>
</template>
<script setup lang="tsx" name="dataList">
import { ref, reactive } from 'vue'
import type { ColumnProps, ProTableInstance } from '@/components/SlProTable/interface'
import { vpcList, cycleBinCreate } from '@/api/modules/resourecenter'
import { useRouter } from 'vue-router'
import { View, Plus } from '@element-plus/icons-vue'
import eventBus from '@/utils/eventBus'
import { ElMessage } from 'element-plus'
import { useRecycleValidation } from '../hooks/useRecycleValidation'
import type { ResourceType } from '../types'

const router = useRouter()
const { queryParams, isHiddenSelection, hideOperations } = defineProps<{
  queryParams: Record<string, any>
  isHiddenSelection?: boolean
  hideOperations?: boolean
}>()

const emit = defineEmits(['currentChange'])
const currentChange = (currentRow: any, oldCurrentRow: any) => {
  emit('currentChange', currentRow, oldCurrentRow)
}
function editItem(row: any) {
  const path = row.sourceType == 'DG' ? '/corporate/vpcView' : '/vpcForm'
  router.push({
    path,
    query: {
      vpcId: row.id,
    },
  })
}
function addSubnet(row: any) {
  const path = row.sourceType == 'DG' ? '/corporate/vpcView' : '/vpcForm'
  router.push({
    path,
    query: { vpcId: row.id, addSubnet: '1' },
  })
}

// 定义操作列渲染函数
function operationRender({ row }: any) {
  return (
    <>
      <el-button v-permission="View" onClick={() => editItem(row)} type="success" icon={View} link>
        查看
      </el-button>
      <el-button
        v-permission="AddSubnet"
        onClick={() => addSubnet(row)}
        type="primary"
        icon={Plus}
        link
      >
        增加子网
      </el-button>
    </>
  )
}

// 增加操作列判断逻辑
const opColumn: [ColumnProps<any>] | [] =
  isHiddenSelection || hideOperations
    ? []
    : [
        {
          prop: 'operation',
          label: '操作',
          width: 180,
          fixed: 'right',
          render: operationRender,
        },
      ]

// 表格配置项
const columns = reactive<ColumnProps<any>[]>([
  { type: 'selection', width: 55 },
  { type: 'index', label: '序号', minWidth: 55, fixed: 'left' },
  {
    label: 'VPC名称',
    minWidth: 150,
    render: ({ row }) => (
      <el-link onClick={() => editItem(row)} type="primary">
        {row.vpcName}
      </el-link>
    ),
    fixed: 'left',
  },
  { prop: 'tenantName', label: '租户', minWidth: 150 },
  { prop: 'poolName', label: '资源池', minWidth: 200 },
  { prop: 'orderCode', label: '订单编号', minWidth: 150 },
  { prop: 'cidr', label: '网段', minWidth: 150 },
  { prop: 'subnetNum', label: '子网个数', minWidth: 100 },
  { prop: 'billId', label: '计费号', width: 150 },
  { prop: 'userName', label: '订购人', minWidth: 100 },
  { prop: 'createdTime', label: '订单时间', minWidth: 150 },
  ...opColumn,
])

const proTable = ref<ProTableInstance>()

// 回收功能
const currentRecycleIdsList = ref<any[]>([])

// 使用回收校验钩子函数
const { validateRecycle } = useRecycleValidation()

const handleCreateRecycle = async (goodsItems: any[]) => {
  const res = await cycleBinCreate({
    goodsType: 'vpc',
    goodsItems,
  })
  if (res.code != 200) {
    return ElMessage.error(res.message || '接口请求失败')
  }
  ElMessage.success('已加入回收站')
  proTable.value?.getTableList()
  proTable.value?.clearSelection()
  eventBus.emit('cycleBins:updateCount')
}

// 批量回收功能
const handleBatchRecycle = async () => {
  const selectedList = proTable.value?.selectedList || []

  // 使用新的验证逻辑
  if (validateRecycle(selectedList, 'vpc' as ResourceType)) {
    currentRecycleIdsList.value = selectedList.map((i) => ({ goodsId: i.id.trim() }))
    handleCreateRecycle(currentRecycleIdsList.value)
  }
}

defineExpose({
  handleBatchRecycle,
})
</script>
