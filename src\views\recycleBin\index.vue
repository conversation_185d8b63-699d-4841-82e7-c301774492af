<template>
  <div id="shopping-carts" class="table-box">
    <sl-page-header
      :title="isEdit ? '编辑' : '回收站'"
      :icon="{
        Vnode: isEdit ? Edit : Delete,
        color: '#0052D9',
        size: '20px',
      }"
    ></sl-page-header>
    <el-scrollbar wrap-class="shopping-carts-scroll-view" class="scroll-view">
      <div v-if="catrsModel.baseList.length" style="background: #fff; overflow: hidden">
        <sl-form
          :key="catrsModel.baseList[0].id"
          ref="slFormRef"
          show-block-title
          :options="formOptions"
          :model-value="catrsModel.baseList[0].orderJson"
          size="small"
        >
        </sl-form>
      </div>
      <sl-base-tabs
        show-count
        :tabs="tabs"
        v-model="activeTab"
        @update:modelValue="tabClick"
        @search="submit"
      >
      </sl-base-tabs>
      <ecs-tab
        :goods-list="catrsModel.ecsList"
        class="goods-tab"
        v-show="activeTab === 'ecs'"
      ></ecs-tab>
      <nat-tab
        :goods-list="catrsModel.natList"
        class="goods-tab"
        v-show="activeTab === 'nat'"
      ></nat-tab>
      <evs-tab
        :goods-list="catrsModel.evsList"
        class="goods-tab"
        v-show="activeTab === 'evs'"
      ></evs-tab>
      <gcs-tab
        :goods-list="catrsModel.gcsList"
        class="goods-tab"
        v-show="activeTab === 'gcs'"
      ></gcs-tab>
      <obs-tab
        :goods-list="catrsModel.obsList"
        class="goods-tab"
        v-show="activeTab === 'obs'"
      ></obs-tab>
      <slb-tab
        :goods-list="catrsModel.slbList"
        class="goods-tab"
        v-show="activeTab === 'slb'"
      ></slb-tab>
      <vpc-tab
        :goods-list="catrsModel.vpcList"
        class="goods-tab"
        v-show="activeTab === 'vpc'"
      ></vpc-tab>
      <network-tab
        :goods-list="catrsModel.networkList"
        class="goods-tab"
        v-show="activeTab === 'network'"
      ></network-tab>
      <eip-tab
        :goods-list="catrsModel.eipList"
        class="goods-tab"
        v-show="activeTab === 'eip'"
      ></eip-tab>
      <mysql-tab
        :goods-list="catrsModel.mysqlList"
        class="goods-tab"
        v-show="activeTab === 'mysql'"
      ></mysql-tab>
      <redis-tab
        :goods-list="catrsModel.redisList"
        class="goods-tab"
        v-show="activeTab === 'redis'"
      ></redis-tab>
    </el-scrollbar>
    <div class="footer">
      <div class="button-group">
        <el-button @click="cancel">取消</el-button>
        <template v-if="isEdit">
          <sl-button :api-function="submit" :style="{ width: '100px' }" type="primary">
            确定修改
          </sl-button>
        </template>
        <template v-else>
          <el-button
            v-permission="'Storage'"
            :style="{ width: '60px' }"
            @click="saveDraft"
            type="primary"
          >
            暂存
          </el-button>
          <sl-button
            v-if="!isEdit"
            :style="{ width: '60px' }"
            :api-function="submitDraftOrder"
            type="primary"
          >
            草稿
          </sl-button>
          <sl-button
            v-permission="'Submit'"
            :api-function="submit"
            :style="{ width: '100px' }"
            type="primary"
          >
            提交申请
          </sl-button>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="tsx" name="workOrder">
import { ref, reactive, computed } from 'vue'
import ecsTab from './tabs/ecs.vue'
import evsTab from './tabs/evs.vue'
import obsTab from './tabs/obs.vue'
import slbTab from './tabs/slb.vue'
import gcsTab from './tabs/gcs.vue'
import natTab from './tabs/nat.vue'
import vpcTab from './tabs/vpc.vue'
import mysqlTab from './tabs/mysql.vue'
import redisTab from './tabs/redis.vue'
import networkTab from './tabs/network.vue'
import eipTab from './tabs/eip.vue'
import { useRoute, useRouter } from 'vue-router'
import SlForm from '@/components/form/SlForm.vue'
import { useCycleBinFormOptions } from '@/views/resourceCenter/hooks/useCycleBinFormOptions'
import { Delete } from '@element-plus/icons-vue'
import { useCycleBin, type ICartsModel } from '@/views/resourceCenter/hooks/useRecycleBin'
import SlMessage from '@/components/base/SlMessage'
import eventBus from '@/utils/eventBus'
import { submitRecoveryOrder, addDraftsRecoveryOrder } from '@/api/modules/resourecenter'
import { Edit } from '@element-plus/icons-vue'
import SlButton from '@/components/base/SlButton.vue'
import type { ICycleBinBaseModel } from '@/views/resourceCenter/hooks/useGoodsModels'
const route = useRoute()
const router = useRouter()

const isEdit = computed(() => route.query.orderId !== undefined)

const catrsModel = useCycleBin()

const orderFormOptions = useCycleBinFormOptions(
  catrsModel.baseList[0]?.orderJson as ICycleBinBaseModel,
)
const formOptions = reactive([orderFormOptions])

const slFormRef = ref()
const orderId = (route.query.orderId as string) || ''

function cancel() {
  router.go(-1)
}
function composeArguments(catrsModel: ICartsModel) {
  const orederItem = catrsModel.baseList[0]
  // 工单信息
  const order = {
    id: orderId,
    businessSystemId: orederItem.orderJson?.busiSystemId,
    orderTitle: orederItem.orderJson?.orderTitle,
    levelThreeLeaderId: orederItem.orderJson?.levelThreeLeaderId,
    orderDesc: orederItem.orderJson?.orderDesc,
    manufacturer: orederItem.orderJson?.manufacturer,
    manufacturerContacts: orederItem.orderJson?.manufacturerContacts,
    manufacturerMobile: orederItem.orderJson?.manufacturerMobile,
    bureauUserName: orederItem.orderJson?.applyUserName,
    departmentName: orederItem.orderJson?.department,
    levelThreeLeaderName: orederItem.orderJson?.levelThreeLeaderLabel,
    businessSystemName: orederItem.orderJson?.busiSystemName,
    idArr: [orederItem.id],
    ids: '',
    goods: [],

    // 产品列表
    ecsIdList: [],
    gcsIdList: [],
    evsIdList: [],
    eipIdList: [],
    obsIdList: [],
    slbIdList: [],
    natIdList: [],
    vpcIdList: [],
    syncRecoveryIdList: [],
    networkIdList: [],
    mysqlIdList: [],
    redisIdList: [],
  }
  // 添加产品信息
  addGoodsParams<any>(order, catrsModel)
  if (!isEdit.value) {
    order.ids = order.idArr.join(',')
  }
  return order
}
function addGoodsParams<T extends ICartsModel>(order: any, goodsItem: T) {
  const prefix = [
    'ecs',
    'gcs',
    'evs',
    'obs',
    'slb',
    'nat',
    'vpc',
    'network',
    'eip',
    'mysql',
    'redis',
  ] as const
  prefix.forEach((item) => {
    const goodsItemKey = (item + 'List') as keyof ICartsModel
    if (goodsItem[goodsItemKey].length > 0) {
      order[item + 'IdList'] = goodsItem[goodsItemKey].map(({ id, storageId, syncRecovery }) => {
        order.idArr.push(storageId)
        if (
          syncRecovery !== false &&
          ['ecs', 'gcs', 'slb', 'nat', 'mysql', 'redis'].includes(item)
        ) {
          order.syncRecoveryIdList.push(id)
        }
        return id
      })
    }
  })
}
async function submitDraftOrder() {
  const validate = await slFormRef.value.validateField(['orderTitle'])
  if (!validate) return
  const order = composeArguments(catrsModel)
  if (order.idArr.length === 1) return SlMessage.error('请添加产品')
  const { code } = await addDraftsRecoveryOrder(order)
  if (code === 200) {
    SlMessage.success('提交成功')
    eventBus.emit('cycleBins:refresh')
    slFormRef.value.resetFields()
  }
}
async function submit() {
  const validate = await slFormRef.value.validate()
  if (!validate) return
  const order = composeArguments(catrsModel)
  if (order.idArr.length === 1) return SlMessage.error('请添加产品')
  const { code } = await submitRecoveryOrder(order)
  if (code === 200) {
    SlMessage.success('提交成功')
    if (isEdit.value) {
      cancel()
    } else {
      eventBus.emit('cycleBins:refresh')
      slFormRef.value.resetFields()
    }
  }
}
function saveDraft() {
  eventBus.emit('cycleBins:updateGoods')
}
const activeTab = ref('ecs')
activeTab.value = (route.query.activeTab as string) || 'ecs'

const ecsCount = computed(() => {
  return catrsModel?.ecsList?.length || 0
})
const gcsCount = computed(() => {
  return catrsModel?.gcsList?.length || 0
})
const evsCount = computed(() => {
  return catrsModel?.evsList?.length || 0
})
const obsCount = computed(() => {
  return catrsModel?.obsList?.length || 0
})
const slbCount = computed(() => {
  return catrsModel?.slbList?.length || 0
})
const natCount = computed(() => {
  return catrsModel?.natList?.length || 0
})
const vpcCount = computed(() => {
  return catrsModel?.vpcList?.length || 0
})
const networkCount = computed(() => {
  return catrsModel?.networkList?.length || 0
})
const eipCount = computed(() => {
  return catrsModel?.eipList?.length || 0
})
const mysqlCount = computed(() => {
  return catrsModel?.mysqlList?.length || 0
})
const redisCount = computed(() => {
  return catrsModel?.redisList?.length || 0
})
// tab栏切换
const tabs: any = ref([
  { label: '云主机', name: 'ecs', count: ecsCount },
  { label: 'GPU云主机', name: 'gcs', count: gcsCount },
  { label: '云硬盘', name: 'evs', count: evsCount },
  { label: '弹性公网', name: 'eip', count: eipCount },
  { label: '对象存储', name: 'obs', count: obsCount },
  { label: '负载均衡', name: 'slb', count: slbCount },
  { label: 'NAT网关', name: 'nat', count: natCount },
  { label: 'VPC', name: 'vpc', count: vpcCount },
  { label: '网络', name: 'network', count: networkCount },
  { label: '云数据库', name: 'mysql', count: mysqlCount },
  { label: '云中间件', name: 'redis', count: redisCount },
])
const tabClick = (name: string) => {
  router.replace({
    query: {
      ...route.query,
      activeTab: name,
    },
  })
}
</script>

<style scoped lang="scss">
.scroll-view {
  margin-top: 2px;
}
.footer {
  height: 48px;
  padding: 8px 20px;
  background: #ffffff;
  display: flex;
  align-items: center;
  .button-group {
    display: flex;
    align-items: center;
    justify-content: right;
    flex-grow: 1;
  }
}
.add-busisystem-btn {
  display: flex;
  align-items: center;
  font-size: 16px;
  cursor: pointer;
}
#shopping-carts {
  :deep(.el-drawer__body) {
    padding: 0;
  }
}
</style>
