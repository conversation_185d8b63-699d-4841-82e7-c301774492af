import { validateGoodsName } from '../utils'
import useLevelLeaderIdOptions from './useLevelLeaderIdOptions'
import { useSelectLabel } from '@/hooks/useSelectLabel'
import { watchEffect } from 'vue'
import { busisystemDetail } from '@/api/modules/resourecenter'
import type { ICycleBinBaseModel } from './useGoodsModels'

// 获取厂家负责人
async function getBusisystemDetail(id: string, formModel: any) {
  const { entity } = await busisystemDetail({ id })
  formModel.manufacturer = entity.manufacturer
  formModel.manufacturerContacts = entity.manufacturerContacts
  formModel.manufacturerMobile = entity.manufacturerMobile
  formModel.applyUserName = entity.applyUserName
  formModel.departmentName = entity.departmentName
}
export function useCycleBinFormOptions(
  formModel: ICycleBinBaseModel,
  callback: (v: any) => void = () => null,
  computedLabel: boolean = true,
) {
  const { levelTwoLeaderIdOptions, levelThreeLeaderIdOptions } = useLevelLeaderIdOptions()
  watchEffect(() => {
    if (formModel.busiSystemId) {
      getBusisystemDetail(formModel.busiSystemId, formModel)
    } else {
      formModel.manufacturer = ''
      formModel.manufacturerContacts = ''
      formModel.manufacturerMobile = ''
      formModel.applyUserName = ''
    }
  })
  if (computedLabel) {
    useSelectLabel(
      () => levelThreeLeaderIdOptions,
      () => formModel.levelThreeLeaderId,
      (option) => {
        formModel.levelThreeLeaderLabel = option.label
      },
    )
  }
  callback({
    levelTwoLeaderIdOptions,
    levelThreeLeaderIdOptions,
  })
  return {
    gutter: 60,
    groupName: '工单信息',
    style: 'margin:0;',
    groupItems: [
      {
        label: '申请人',
        type: 'text',
        key: 'applicant',
        span: 8,
      },
      {
        label: '所属部门',
        type: 'text',
        key: 'department',
        span: 8,
      },
      {
        label: '工单类型',
        type: 'text',
        key: 'workOrdertype',
        span: 8,
      },
      {
        label: '标题',
        type: 'input',
        key: 'orderTitle',
        span: 8,
        props: {
          maxlength: 50,
          showWordLimit: true,
        },
        rules: [
          { required: true, message: '请输入标题', trigger: ['blur', 'change'] },
          { validator: validateGoodsName, trigger: ['blur', 'change'] },
        ],
      },
      {
        label: '业务系统',
        type: 'text',
        key: 'busiSystemName',
        span: 8,
      },
      {
        label: '局方负责人',
        type: 'input',
        key: 'applyUserName',
        props: {
          disabled: true,
        },
        span: 8,
      },
      {
        label: '厂家',
        type: 'input',
        key: 'manufacturer',
        span: 8,
        props: {
          disabled: true,
        },
      },
      {
        label: '厂家负责人',
        type: 'input',
        key: 'manufacturerContacts',
        span: 8,
        props: {
          disabled: true,
        },
      },
      {
        label: '电话',
        type: 'input',
        props: {
          disabled: true,
        },
        key: 'manufacturerMobile',
        span: 8,
      },
      {
        label: '三级业务部门领导',
        type: 'select',
        key: 'levelThreeLeaderId',
        span: 8,
        options: levelThreeLeaderIdOptions,
        rules: [{ required: true, message: '请选择三级业务部门领导', trigger: ['change'] }],
      },
      {
        label: '资源回收说明',
        type: 'input',
        props: {
          type: 'textarea',
          autosize: { minRows: 2, maxRows: 4 },
          row: 4,
          maxlength: 200,
          showWordLimit: true,
        },
        key: 'orderDesc',
        span: 24,
        rules: [{ required: true, message: '请输入资源回收说明', trigger: ['blur', 'change'] }],
      },
    ],
  }
}
