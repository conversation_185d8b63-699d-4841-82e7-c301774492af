import { type Ref, watch, type Reactive, toRaw, isRef, ref } from 'vue'

interface IOption {
  value: string | number
  label: string
  children?: IOption[]
  [key: string]: string | number | IOption[] | undefined
}
type Value = string | number
type Callback = (item: any) => Value | Value[] | void | undefined

function convertToRaw<T>(data: Ref<T[]> | T[]): T[] {
  // 如果是 Ref，先取 .value，再解除响应式
  if (isRef(data)) {
    return toRaw(data.value)
  }
  // 如果是 Reactive 数组，直接解除响应式
  return toRaw(data)
}

export function useSelectLabel<T extends Ref<Value[] | Value>>(
  optionsFn: () => Ref<IOption[]> | Reactive<IOption[]>,
  valueFn: () => Value[] | Value,
  callback?: Callback | null,
  multiple?: boolean,
): T {
  let label: T = ref('') as T
  const fn = () => {
    const options = optionsFn()
    const value = valueFn()
    const optionsValue = convertToRaw(options)
    if (!optionsValue || !optionsValue.length) return
    if (!value) {
      label.value = ''
      if (callback) callback(resetObject(optionsValue[0]))
      return
    }

    const selectedOption = findOption(optionsValue || options, value, multiple!)
    if (selectedOption) {
      label.value = getLabel(selectedOption, callback!)
    } else {
      label.value = ''
    }
  }
  // 监听 value 的变化，自动更新 label
  watch(
    () => {
      return {
        value: valueFn(),
        options: optionsFn(),
      }
    },
    fn,
    { immediate: true, deep: true },
  )

  return label
}
/**
 * 重置对象或数组的值
 * @param obj 需要重置的对象或数组
 * @returns 重置后的对象或数组
 */
function resetValue(value: any): any {
  if (typeof value === 'object' && value !== null) {
    if (Array.isArray(value)) {
      // 如果是数组，返回空数组
      return []
    } else {
      // 如果是对象，递归重置每个属性
      const resetObj: Record<string, any> = {}
      for (const key in value) {
        if (Object.prototype.hasOwnProperty.call(value, key)) {
          resetObj[key] = resetValue(value[key])
        }
      }
      return resetObj
    }
  } else {
    // 如果是基本类型，返回空字符串
    return ''
  }
}

/**
 * 重置对象或数组的值
 * @param obj 需要重置的对象或数组
 * @returns 重置后的对象或数组
 */
function resetObject<T extends Record<string, any> | any[]>(obj: T): T {
  return resetValue(obj) as T
}
function findSingleOption(options: IOption[], value: Value) {
  const selectedOption = options.find((option) => option.value === value)
  return selectedOption
}

function findOption(options: IOption[], value: Value | Value[], multiple: boolean) {
  if (!Array.isArray(value)) return findSingleOption(options, value)
  let currOptions: IOption[] | undefined, currOption: IOption
  return value.map((v) => {
    if (!currOptions) currOptions = options
    currOption = findSingleOption(currOptions, v) || { value: 'null', label: 'null' }
    if (!multiple) currOptions = currOption.children

    return currOption
  })
}

function getLabel(option: IOption | IOption[], callback: Callback) {
  if (callback) {
    if (Array.isArray(option)) {
      if (option.length) {
        const res = callback(option)
        if (res !== undefined) return res
      } else {
        return ''
      }
    }
    const res = callback(option)
    if (res !== undefined) return res
  }
  if (Array.isArray(option)) {
    return option.map((o) => o.label).join('/')
  }

  return option.label
}
