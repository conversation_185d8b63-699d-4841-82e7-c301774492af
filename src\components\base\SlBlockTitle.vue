<template>
  <div
    :class="noBar ? 'block-title-container-no-bar' : 'block-title-container'"
    :style="`font-size:${size}px`"
  >
    <div class="block-title">
      <slot>
        {{ title }}
      </slot>
    </div>
    <slot :model="form" name="blockTitleFilter" v-if="!!blockTitleFilter">
      <el-form class="block-title-form" :inline="true" :model="formInline">
        <template :key="item.key" v-for="item in blockTitleFilter.formItems">
          <!-- element组件 -->
          <el-form-item :label="item.label + ' :'" :prop="item.key" :rules="item.rules">
            <!-- input -->
            <el-input
              v-bind="item.props || {}"
              v-if="item.type === 'input'"
              v-model="form[item.key]"
            />
            <el-input-number
              v-bind="item.props || {}"
              v-if="item.type === 'inputNumber'"
              v-model="form[item.key]"
            />
            <!-- select -->
            <el-select
              v-bind="item.props?.select || {}"
              v-if="item.type === 'select'"
              clearable
              v-model="form[item.key]"
            >
              <el-option
                v-bind="item.props?.option || {}"
                :label="option.label"
                :value="option.value"
                :key="option.value"
                v-for="option in item.options"
              />
            </el-select>
            <!-- 单选 -->
            <el-radio-group
              v-bind="item.props?.group"
              v-if="item.type === 'radio'"
              v-model="form[item.key]"
            >
              <el-radio
                v-bind="item.props?.radio"
                :key="option.value"
                v-for="option in item.options"
                :value="option.value"
              >
                {{ option.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </template>
        <el-button @click="search" :icon="Search" type="primary">查询</el-button>
      </el-form>
    </slot>
  </div>
</template>
<script setup lang="ts">
import { reactive } from 'vue'
import { Search } from '@element-plus/icons-vue'

const formInline = reactive({
  user: '',
  region: '',
  date: '',
})

const props = defineProps({
  size: {
    type: Number,
    default: 18,
  },
  title: {
    type: String,
    default: '标题',
  },
  blockTitleFilter: {
    type: Object,
    default: null,
  },
  noBar: {
    type: Boolean,
    default: false,
  },
})
const form = reactive(props.blockTitleFilter?.form || {})
const search = () => {
  props.blockTitleFilter?.search(form)
}
</script>
<style scoped>
.block-title-form .el-form-item {
  margin-bottom: 0 !important;
}
.block-title {
  flex: 1;
}
.block-title-container::before {
  content: '';
  width: 4px;
  background-color: var(--el-color-primary);
  margin-right: 10px;
  height: 0.78em;
}

.block-title-container {
  display: flex;
  padding: 10px 0;
  font-weight: bold;
  font-size: 18px;
  color: #000c1f;
  display: flex;
  align-items: center;
}

.block-title-container-no-bar {
  display: flex;
  padding: 10px 0;
  font-weight: bold;
  font-size: 18px;
  color: #000c1f;
  display: flex;
  align-items: center;
}
.block-title-form {
  font-weight: normal;
}
.block-title-form .el-input {
  --el-input-width: 100px;
}

.block-title-form .el-select {
  --el-select-width: 100px;
}
</style>
