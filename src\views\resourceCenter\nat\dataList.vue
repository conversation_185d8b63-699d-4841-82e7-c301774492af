<template>
  <SlProTable
    ref="proTable"
    highlight-current-row
    :columns="columns"
    style="min-height: 300px"
    :request-api="getResourceList"
    :init-param="queryParams"
    :current-change="currentChange"
    @selection-change="handleSelectionChange"
    hidden-table-header
    row-key="goodsOrderId"
  >
  </SlProTable>

  <!-- 资源延期弹窗 -->
  <ResourceChangeDialog
    v-model:visible="delayDialogVisible"
    resource-type="nat"
    :selected-resources="selectedResources"
    :is-delay="true"
    @confirm="handleConfirm"
  />
</template>
<script setup lang="tsx" name="dataList">
import { ref, reactive, type VNode } from 'vue'
import type { ColumnProps, ProTableInstance } from '@/components/SlProTable/interface'
import { getResourceList, cycleBinCreate } from '@/api/modules/resourecenter'
import { useRouter } from 'vue-router'
import eventBus from '@/utils/eventBus'
import ResourceChangeDialog from '../components/ResourceChangeDialog.vue'
import { ElMessage } from 'element-plus'
import { useResourceChange } from '../hooks/useResourceChange'
import { useRecycleValidation } from '../hooks/useRecycleValidation'
import type { ResourceType } from '../types'
import { useVerifyThePromptBox } from '@/hooks/useVerifyThePromptBox'

const { queryParams, hideOperations, isBindDialog } = defineProps<{
  queryParams: Record<string, any>
  hideOperations?: boolean
  isBindDialog?: boolean
}>()

const { validateResources } = useResourceChange()

const emit = defineEmits(['currentChange', 'selectDevice'])
const currentChange = (currentRow: any, oldCurrentRow: any) => {
  emit('currentChange', currentRow, oldCurrentRow)
}

// 表格配置项
const columns = reactive<ColumnProps<any>[]>([
  queryParams?.hideSelection
    ? { type: 'index', label: '序号', width: 55 }
    : { type: 'selection', width: 55 },
  { type: 'index', label: '序号', width: 55, fixed: 'left' },
  isBindDialog
    ? { prop: 'deviceName', label: '网关名称', width: 150, fixed: 'left' }
    : {
        prop: 'deviceName',
        label: '网关名称',
        width: 150,
        render: deviceNameRender,
        fixed: 'left',
      },
  { prop: 'deviceId', label: '资源ID', width: 200 },
  { prop: 'spec', label: '实例规格', width: 200 },
  { prop: 'vpcName', label: 'VPC名称', width: 150 },
  { prop: 'subnetName', label: '子网名称', width: 150 },
  { prop: 'subnetCidr', label: '子网网段', width: 150 },
  { prop: 'eip', label: '弹性公网IP', width: 150 },
  { prop: 'bandWidth', label: '带宽大小', width: 120 },
  { prop: 'applyTime', label: '申请时长', width: 100 },
  { prop: 'tenantName', label: '租户', width: 150 },
  { prop: 'businessSysName', label: '业务系统', width: 150, filter: true },
  { prop: 'cloudPlatform', label: '所属云', width: 150, filter: true },
  { prop: 'resourcePoolName', label: '资源池', width: 150, filter: true },
  { prop: 'orderCode', label: '工单编号', width: 150 },
  { prop: 'effectiveTime', label: '开通时间', width: 150 },
  { prop: 'expireTime', label: '到期时间', width: 150 },
  { prop: 'billId', label: '计费号', width: 150 },
  { prop: 'deviceStatusCn', label: '状态', width: 100 },
  { prop: 'recoveryStatusCn', label: '回收状态', width: 100 },
  { prop: 'changeStatusCn', label: '变更状态', width: 100 },
  { prop: 'applyUserName', label: '申请人', width: 100 },
])

if (!hideOperations && isBindDialog) {
  columns.push({
    prop: 'operation',
    label: '操作',
    width: 100,
    fixed: 'right',
    render: bindDialogOperationRender,
  })
}

function deviceNameRender({ row }: any): VNode {
  return (
    <>
      <el-link onClick={() => handleDeviceNameClick(row)} type="primary" link>
        {row.deviceName}
      </el-link>
    </>
  )
}

const router = useRouter()
const handleDeviceNameClick = (row: any) => {
  router.push({
    path: '/natDetail',
    query: {
      id: row.id,
      orderId: row.goodsOrderId,
      hasEip: row.eip ? 'true' : 'false',
    },
  })
}

const proTable = ref<ProTableInstance>()

// const currentRecycleIdsList = ref<string[]>([])

// 使用回收校验钩子函数
const { validateRecycle, validateChange } = useRecycleValidation()

const handleCreateRecycle = async (goodsItems: any[]) => {
  const res = await cycleBinCreate({
    goodsType: 'nat',
    goodsItems,
  })
  if (res.code != 200) {
    return ElMessage.error(res.message || '接口请求失败')
  }
  ElMessage.success('已加入回收站')
  proTable.value?.getTableList()
  proTable.value?.clearSelection()
  eventBus.emit('cycleBins:updateCount')
}

// 批量回收功能
const handleBatchRecycle = async () => {
  const selectedList = proTable.value?.selectedList || []

  // 使用新的验证逻辑
  if (validateRecycle(selectedList, 'nat' as ResourceType)) {
    const arr = await useVerifyThePromptBox(selectedList)
    if (!arr || !arr.length) return
    handleCreateRecycle(arr)
  }
}

// 多选数据
const multipleSelection = ref<any[]>([])
const delayDialogVisible = ref(false)
const selectedResources = ref<any[]>([])

// 处理资源延期
const handleResourceDelay = async () => {
  if (!multipleSelection.value.length) {
    ElMessage.warning('请选择要延期的资源')
    return
  }
  selectedResources.value = multipleSelection.value

  // 使用新的校验逻辑
  if (validateChange(selectedResources.value)) {
    // 使用钩子函数中的validateResources进行业务系统一致性校验
    const isValid = await validateResources(selectedResources.value)
    if (isValid) {
      // 校验通过，才显示弹窗
      delayDialogVisible.value = true
    }
  }
}

// 处理确认
const handleConfirm = () => {
  proTable.value?.getTableList()
}

// 选择变化事件
const handleSelectionChange = (selection: any[]) => {
  multipleSelection.value = selection
}

// 绑定弹窗中的操作列渲染函数
function bindDialogOperationRender({ row }: any): VNode {
  return (
    <>
      <el-button
        onClick={() => handleSelectDevice(row)}
        type="primary"
        disabled={row.recoveryStatus !== 0 || row.changeStatusCn !== '未变更'}
        link
      >
        绑定
      </el-button>
    </>
  )
}

// 选择设备
const handleSelectDevice = (row: any) => {
  emit('selectDevice', row)
}

defineExpose({
  handleBatchRecycle,
  handleResourceDelay,
})
</script>
