import { getBusinessSystemListApi } from '@/api/modules/computingPowerMap'
import { ref } from 'vue'
/**
 * 获取业务系统列表
 * @param type 0: 用户下的所有业务系统, 1: 所有业务系统
 * @returns
 */
export default function useBusiSystemOptions() {
  const busiSystemOptions = ref<any>([]) // 业务系统
  getBusiSystem()
  async function getBusiSystem() {
    const { entity: records } = await getBusinessSystemListApi()
    if (records && Array.isArray(records)) {
      busiSystemOptions.value = records?.map((e: any) => ({
        value: e.systemId,
        label: e.systemName,
      }))
    }
  }
  return {
    busiSystemOptions,
  }
}
