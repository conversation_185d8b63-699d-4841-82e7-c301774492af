/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    Branch: typeof import('./src/components/base/SlBaseSteps/branch.vue')['default']
    FilterPopover: typeof import('./src/components/SlSearchForm/components/FilterPopover.vue')['default']
    GridItem: typeof import('./src/components/SlGrid/components/GridItem.vue')['default']
    Item: typeof import('./src/components/base/SlBaseSteps/item.vue')['default']
    Pagination: typeof import('./src/components/SlProTable/components/Pagination.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SearchFormItem: typeof import('./src/components/SlSearchForm/components/SearchFormItem.vue')['default']
    SlAuditDialog: typeof import('./src/components/SlAuditDialog/index.vue')['default']
    SlBaseInput: typeof import('./src/components/base/SlBaseInput.vue')['default']
    SlBaseSteps: typeof import('./src/components/base/SlBaseSteps.vue')['default']
    SlBaseTabs: typeof import('./src/components/base/SlBaseTabs.vue')['default']
    SlBlockTitle: typeof import('./src/components/base/SlBlockTitle.vue')['default']
    SlBreadcrumb: typeof import('./src/components/breadcrumb/SlBreadcrumb.vue')['default']
    SlButton: typeof import('./src/components/base/SlButton.vue')['default']
    SlCard: typeof import('./src/components/SlCard/index.vue')['default']
    SlDialog: typeof import('./src/components/SlDialog/index.vue')['default']
    SLdialog: typeof import('./src/components/dialog/SLdialog.vue')['default']
    SlDynamicFormField: typeof import('./src/components/SlDynamicFormField/index.vue')['default']
    SlErrorPage: typeof import('./src/components/SlErrorPage.vue')['default']
    SlFilterForm: typeof import('./src/components/base/SlFilterForm.vue')['default']
    SlForm: typeof import('./src/components/form/SlForm.vue')['default']
    SlGrid: typeof import('./src/components/SlGrid/index.vue')['default']
    SlGroupMenu: typeof import('./src/components/groupMenu/SlGroupMenu.vue')['default']
    SlGroupMenuItem: typeof import('./src/components/groupMenu/SlGroupMenuItem.vue')['default']
    SlHeader: typeof import('./src/components/SlHeader.vue')['default']
    SlList: typeof import('./src/components/list/SlList.vue')['default']
    SlMinioUploader: typeof import('./src/components/SlMinioUploader/index.vue')['default']
    SlPageHeader: typeof import('./src/components/SlPageHeader/index.vue')['default']
    SlPagination: typeof import('./src/components/base/SlPagination.vue')['default']
    SlPhrases: typeof import('./src/components/SlPhrases/index.vue')['default']
    SlProTable: typeof import('./src/components/SlProTable/index.vue')['default']
    SlSearchForm: typeof import('./src/components/SlSearchForm/index.vue')['default']
    SlSteps: typeof import('./src/components/SlSteps/index.vue')['default']
    SlStringIcon: typeof import('./src/components/icons/SlStringIcon.vue')['default']
    SlUpload: typeof import('./src/components/SlUpload/index.vue')['default']
    Steps: typeof import('./src/components/base/SlBaseSteps/steps.vue')['default']
  }
}
