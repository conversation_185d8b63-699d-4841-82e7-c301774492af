import http from '@/api'
import { CLOUD, USER, PERFORMANCE } from '../config/servicePort'
import type {
  cityListType,
  regionsApiType,
  latestType,
  reportType,
  PerformanceType,
  byUserType,
} from '@/views/computingPowerMap/operationsOverview/interface/type'
import type { GetNoticeListType } from '@/views/computingPowerMap/tenantView/interface/type'

// /**
//  * @name 资源列表-代办工单查询
//  */

// export const getExamineTodoQueryApi = (config: GetResourceListParamsType) =>
//   http.post<ResPage<ResourceListType>>(PORT2 + '/order/service/todo', config)

/**
 * @name 查询云平台
 */
export const getPlatformTypesApi = () =>
  // http.get(PORT3 + '/cloud/resourcecenter/resUsage/platformTypes',)
  http.get(CLOUD + '/platform_types')

/**
 * @name 查询城市
 */
export const getCitysApi = (config: { platTypeId: string }) =>
  http.get<cityListType[]>(CLOUD + '/citys', config)

/**
 * @name 查询VM一类
 */
export const getRegionsApi = (config: any) =>
  http.post<regionsApiType[]>(CLOUD + '/regions', config)
/**
 * @name 查询总览数据
 */
export const getLatestApi = (config: any) => http.post<latestType[]>(CLOUD + '/latest', config)
/**
 * @name 查询分配率top5数据
 */
export const getReportTop5Api = (config: any) => http.get<reportType[]>(CLOUD + '/report', config)
/**
 * @name 查询资源池利用率top5数据
 */
export const getPerformanceTopApi = (config: any) =>
  http.get<PerformanceType[]>(PERFORMANCE + '/view/pm/top', config)
/**
 * @name 查询当前登录人的租户列表
 */
export const getbyUserApi = (config: any) =>
  http.get<byUserType>(USER + '/tenant/list/byUser', config)

/**
 * 获取告警数据信息
 * @returns
 */
export const getAlarmLevelsListApi = (config: any) =>
  http.get<GetNoticeListType[]>(PERFORMANCE + '/alarm/levels', config)
/**
 * 获取告警列表数据
 * @returns
 */
export const getAlarmListDataApi = (config: any) =>
  http.get<GetNoticeListType[]>(PERFORMANCE + '/alarm/list', config)
/**
 * 获取业务系统数据
 * @returns
 */
export const getBusinessListDataApi = (config: any) =>
  http.get<GetNoticeListType[]>(PERFORMANCE + '/app/list', config)
/**
 * 获取参数数据
 * @returns
 */
export const getParmsListDataApi = (config: any) =>
  http.get<GetNoticeListType[]>(PERFORMANCE + '/alarm/parmsList', config)
/**
 * 获取告警数据详情
 * @returns
 */
export const getAlarmDetailApi = (config: any) =>
  http.get<GetNoticeListType[]>(PERFORMANCE + '/alarm/detail', config)
