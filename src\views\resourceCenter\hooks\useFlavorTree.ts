import { flavorModelTree } from '@/api/modules/resourecenter'
import { ref } from 'vue'

interface FlavorItem {
  label: string
  value: string
  children: FlavorItem[]
}

export const useFlavorTree = (type: string) => {
  const flavorMode = ref<FlavorItem[]>([])
  const getFlavorMode = async () => {
    const { entity } = await flavorModelTree({ type })
    flavorMode.value.length = 0
    flavorMode.value.push(...treeFeild(entity))
  }
  getFlavorMode()
  return flavorMode
}

function treeFeild(tree: any[]): FlavorItem[] {
  return tree.map((item: any) => {
    return {
      label: item.name,
      value: item.name,
      slbDesc: item.name,
      ...item,
      children: item.children ? treeFeild(item.children) : [],
    }
  })
}
