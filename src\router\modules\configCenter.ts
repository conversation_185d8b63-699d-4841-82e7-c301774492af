import type { RouteRecordRaw } from 'vue-router'

const configCenterRouter: RouteRecordRaw[] = [
  {
    path: '/mirrorImage',
    component: () => import('@/views/configCenter/mirrorImage/list.vue'),
    name: 'mirrorImage',
    meta: {
      title: '镜像管理',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/mirrorImageAdd',
    component: () => import('@/views/configCenter/mirrorImage/form.vue'),
    name: 'mirrorImageAdd',
    meta: {
      title: '镜像',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/resourcePool',
    component: () => import('@/views/configCenter/resourcepool/list.vue'),
    name: 'resourcePool',
    meta: {
      title: '资源池管理',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/resourcePoolAdd',
    component: () => import('@/views/configCenter/resourcepool/form.vue'),
    name: 'resourcePoolAdd',
    meta: {
      title: '资源池管理新增',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/standards',
    component: () => import('@/views/configCenter/standards/list.vue'),
    name: 'standards',
    meta: {
      title: '规格管理',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/standardsAdd',
    component: () => import('@/views/configCenter/standards/form.vue'),
    name: 'standardsAdd',
    meta: {
      title: '规格管理新增',
      icon: 'icon-gongdan',
    },
  },
]

export default configCenterRouter
