<template>
  <div class="demo-container">
    <h2>SlTabs 参照 HTML 示例效果</h2>

    <h3>原始 HTML 示例效果</h3>
    <div class="html-demo">
      <nav class="tab">
        <a class="tab-item">Svelte API</a>
        <a class="tab-item active">Svelte API</a>
        <a class="tab-item">Svelte API</a>
        <a class="tab-item">Svelte API</a>
      </nav>
    </div>

    <h3>SlTabs 实现的效果</h3>
    <sl-tabs v-model="activeTab1" :tabs="basicTabs">
      <el-tab-pane name="tab1">
        <div class="tab-content-demo">
          <p>这是第一个标签页的内容</p>
          <p>使用了与 HTML 示例相同的圆角过渡技术</p>
        </div>
      </el-tab-pane>
      <el-tab-pane name="tab2">
        <div class="tab-content-demo">
          <p>这是第二个标签页的内容</p>
          <p>box-shadow + clip-path 实现完美的圆角过渡</p>
        </div>
      </el-tab-pane>
      <el-tab-pane name="tab3">
        <div class="tab-content-demo">
          <p>这是第三个标签页的内容</p>
          <p>保留了 Element Plus 的所有功能</p>
        </div>
      </el-tab-pane>
    </sl-tabs>

    <h3>带数量显示的效果</h3>
    <sl-tabs v-model="activeTab2" :tabs="countTabs" show-count>
      <el-tab-pane name="all">
        <div class="tab-content-demo">
          <p>全部工单内容 (共 {{ countTabs.find((t) => t.name === 'all')?.count }} 个)</p>
          <p>数量标签与圆角过渡完美结合</p>
        </div>
      </el-tab-pane>
      <el-tab-pane name="pending">
        <div class="tab-content-demo">
          <p>待处理工单内容 (共 {{ countTabs.find((t) => t.name === 'pending')?.count }} 个)</p>
        </div>
      </el-tab-pane>
      <el-tab-pane name="completed">
        <div class="tab-content-demo">
          <p>已完成工单内容 (共 {{ countTabs.find((t) => t.name === 'completed')?.count }} 个)</p>
        </div>
      </el-tab-pane>
    </sl-tabs>

    <h3>工单审批示例</h3>
    <sl-tabs v-model="activeTab3" :tabs="workOrderTabs" show-count>
      <el-tab-pane name="ecs">
        <div class="tab-content-demo">
          <p>云主机工单列表</p>
          <ul>
            <li>ECS-001 - 申请云主机实例</li>
            <li>ECS-002 - 扩容云主机配置</li>
            <li>ECS-003 - 云主机续费</li>
          </ul>
        </div>
      </el-tab-pane>
      <el-tab-pane name="mysql">
        <div class="tab-content-demo">
          <p>MySQL数据库工单列表</p>
          <ul>
            <li>MySQL-001 - 申请MySQL实例</li>
            <li>MySQL-002 - 数据库扩容</li>
          </ul>
        </div>
      </el-tab-pane>
      <el-tab-pane name="redis">
        <div class="tab-content-demo">
          <p>Redis缓存工单列表</p>
          <ul>
            <li>Redis-001 - 申请Redis实例</li>
          </ul>
        </div>
      </el-tab-pane>
    </sl-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import SlTabs from '@/components/base/SlTabs/index.vue'

const activeTab1 = ref('tab2') // 默认选中第二个，展示效果
const activeTab2 = ref('pending')
const activeTab3 = ref('mysql')

const basicTabs = [
  { name: 'tab1', label: 'Svelte API' },
  { name: 'tab2', label: 'Svelte API' },
  { name: 'tab3', label: 'Svelte API' },
]

const countTabs = [
  { name: 'all', label: '全部', count: 156 },
  { name: 'pending', label: '待处理', count: 23 },
  { name: 'completed', label: '已完成', count: 133 },
]

const workOrderTabs = [
  { name: 'ecs', label: '云主机', count: 15 },
  { name: 'mysql', label: 'MySQL数据库', count: 8 },
  { name: 'redis', label: 'Redis缓存', count: 3 },
]
</script>

<style lang="scss" scoped>
.demo-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

h2 {
  color: #303133;
  margin-bottom: 20px;
}

h3 {
  color: #606266;
  margin: 30px 0 15px 0;
  font-size: 16px;
}

// 原始 HTML 示例样式
.html-demo {
  margin-bottom: 30px;
  padding: 20px;
  background: #f5f7fa;
  border-radius: 8px;
}

.tab {
  display: flex;
}

.tab-item {
  padding: 10px 15px;
  cursor: pointer;
  color: #909399;
  text-decoration: none;
  transition: 0.2s;
}

.tab-item.active {
  position: relative;
  background-color: red;
  color: white;
  border-radius: 12px 12px 0 0;
  transition: 0.2s;
}

.tab-item.active::before,
.tab-item.active::after {
  position: absolute;
  bottom: 0;
  content: '';
  width: 20px;
  height: 20px;
  border-radius: 100%;
  box-shadow: 0 0 0 40px red;
  transition: 0.2s;
}

.tab-item.active::before {
  left: -20px;
  clip-path: inset(50% -10px 0 50%);
}

.tab-item.active::after {
  right: -20px;
  clip-path: inset(50% 50% 0 -10px);
}

.tab-content-demo {
  padding: 20px;
  background: #fff;
  margin: 20px;
  border-radius: 8px;
  min-height: 150px;

  p {
    margin: 0 0 15px 0;
    color: #606266;
    font-size: 14px;
  }

  ul {
    margin: 0;
    padding-left: 20px;

    li {
      margin-bottom: 8px;
      color: #606266;
      font-size: 14px;
    }
  }
}
</style>
