<template>
  <div class="table-box">
    <sl-page-header
      title="开通工单管理"
      detail="开通工单管理提供了资源查询、资源审批功能。"
      :icon="{
        Vnode: Platform,
        color: '#0052D9',
        size: '20px',
      }"
    >
      <template #custom>
        <sl-base-tabs show-count :tabs="tabs" v-model="activeTab"></sl-base-tabs>
        <div style="width: 100%; margin-top: 10px; background-color: #fff; height: 500px">
          <SlTabs v-model="activeTab" :tabs="tabs"></SlTabs>
        </div>
      </template>
    </sl-page-header>
  </div>
</template>

<script setup lang="tsx" name="workOrder">
import { ref } from 'vue'
import { Platform } from '@element-plus/icons-vue'
import SlBaseTabs from '@/components/base/SlBaseTabs.vue'
import SlTabs from '@/components/base/SlTabs/index.vue'

defineOptions({
  name: 'workOrder',
})

// tab栏切换
const tabs: any = ref([
  { label: '待审批', name: 'pending', count: '' },
  { label: '已审批', name: 'approved', count: '' },
  { label: '驳回', name: 'rejected', count: '' },
  { label: '工单详情1', name: 'detail1', count: '' },
  { label: '工单详情2', name: 'detail2', count: '' },
  { label: '工单详情3', name: 'detail3', count: '' },
  { label: '工单详情4', name: 'detail4', count: '' },
  { label: '工单详情5', name: 'detail5', count: '' },
  { label: '工单详情6', name: 'detail6', count: '' },
  { label: '工单详情7', name: 'detail7', count: '' },
  { label: '工单详情8', name: 'detail8', count: '' },
  { label: '工单详情9', name: 'detail9', count: '' },
  { label: '工单详情10', name: 'detail10', count: '' },
  { label: '工单详情11', name: 'detail11', count: '' },
  { label: '工单详情12', name: 'detail12', count: '' },
  { label: '工单详情13', name: 'detail13', count: '' },
  { label: '工单详情14', name: 'detail14', count: '' },
  { label: '工单详情15', name: 'detail15', count: '' },
  { label: '工单详情16', name: 'detail16', count: '' },
  { label: '工单详情17', name: 'detail17', count: '' },
  { label: '工单详情18', name: 'detail18', count: '' },
  { label: '工单详情19', name: 'detail19', count: '' },
  { label: '工单详情20', name: 'detail20', count: '' },
])

const activeTab = ref('pending')
</script>

<style scoped lang="scss">
.sl-table-content {
  overflow: auto !important;
}
.sl-page-content {
  // 减去头部的标题高度 - 不减去会超出屏幕
  overflow-x: hidden;
  height: calc(100% - 90px);
}
</style>
