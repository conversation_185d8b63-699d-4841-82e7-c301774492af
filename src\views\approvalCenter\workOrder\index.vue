<template>
  <div class="table-box">
    <sl-page-header
      title="开通工单管理"
      detail="开通工单管理提供了资源查询、资源审批功能。"
      :icon="{
        Vnode: Platform,
        color: '#0052D9',
        size: '20px',
      }"
    >
      <template #custom>
        <sl-base-tabs show-count :tabs="tabs" v-model="activeTab"></sl-base-tabs>
      </template>
    </sl-page-header>
    <div class="sl-page-content table-main">
      <div v-permission="'Upload'" class="mb8 pl10">
        <el-button @click="funcexport" type="primary">
          <el-icon class="el-icon--left"><Upload /></el-icon>批量导出
        </el-button>
      </div>
      <!-- 代办 -->
      <SlProTable
        ref="proTable"
        :columns="columns"
        :request-api="getTableList"
        :init-param="form"
        row-key="orderCode"
      ></SlProTable>
    </div>
  </div>
</template>

<script setup lang="tsx" name="workOrder">
import { computed, onMounted, ref, watch, type VNode } from 'vue'
import { Edit, Platform, Refresh, View, Upload } from '@element-plus/icons-vue'
import type { GetResourceListParamsType } from './interface/type'
import {
  getStandardWorkorderListApi,
  getAuditCountApi,
  batchExport,
  standardWorkorderCancelApi,
} from '@/api/modules/approvalCenter'
import SlBaseTabs from '@/components/base/SlBaseTabs.vue'
import { useRoute, useRouter } from 'vue-router'
import { useDownload } from '@/hooks/useDownload'
import type { ColumnProps, ProTableInstance } from '@/components/SlProTable/interface'
import useBusiSystemOptions from '@/views/resourceCenter/hooks/useBusiSystemOptions'
import { getCloudPlatformDic } from '@/api/modules/dic'
import { changeDateFormat } from '@/utils'
import SlMessage from '@/components/base/SlMessage'
import { useGlobalDicStore } from '@/stores/modules/dic'
import { useAuthStore } from '@/stores/modules/auth'
const authStore = useAuthStore()
const btnPermissionList = computed(() => authStore.authButtonListGet.workOrder)
const vifEdit = computed(() => btnPermissionList.value.includes('Edit'))
const vifAudit = computed(() => btnPermissionList.value.includes('Audit'))
const vifView = computed(() => btnPermissionList.value.includes('View'))

defineOptions({
  name: 'workOrder',
})

// ProTable 实例
const proTable = ref<ProTableInstance>()
/*

查询审批节点的类型
1.待审批 code：pending
2.已审批 code：approved
3.已驳回 code：rejecteded
*/
// tab栏切换
const tabs: any = ref([
  { label: '待审批', name: 'pending', count: '' },
  { label: '已审批', name: 'approved', count: '' },
  { label: '驳回', name: 'rejected', count: '' },
])

const globalDic = useGlobalDicStore()
const { getDic } = globalDic

const activeTab = ref('pending')

const form = ref<GetResourceListParamsType>({})

const router = useRouter()

/**
 *
 * @param row 行数据
 * @param cancel 是否为修改
 */
const editOrder = (row: any) => {
  router.push({
    path: '/orderEdit',
    query: {
      orderId: row.id,
    },
  })
}

// 撤销
const refreshFn = async (row: any) => {
  await ElMessageBox.confirm('选中信息确认撤销吗? 撤销后流程终止!', '信息提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
  })
  await standardWorkorderCancelApi(row.id)

  proTable.value?.getTableList()

  SlMessage.success('撤销成功')
}
/**
 *  @description: 跳转到审核页面
 * @param row 行数据
 */
const editItem = (row: any, isStatus: number) => {
  router.push({
    path: '/orderApproval',
    query: {
      workOrderId: row.id,
      opStatus: isStatus,
    },
  })
}

const getnumaxaminefunc = async () => {
  try {
    const { entity } = await getAuditCountApi({})
    if (entity) {
      tabs.value[0].count = entity.pendingCount
      tabs.value[1].count = entity.approvedCount
      tabs.value[2].count = entity.rejectedCount
    }
  } finally {
  }
}

watch(activeTab, () => {
  proTable.value?.getTableList()
})

/**
 * @description: 批量导出
 */
const funcexport = async () => {
  const params: any = {
    ...proTable.value?.searchParam,
  }
  const isSelected = proTable.value?.isSelected

  if (isSelected) {
    params['workOrderIds'] = proTable.value?.selectedListIds.join(',')
  }
  params['approvalCode'] = activeTab.value

  const newParams = changeDateFormat(params, ['currentNodeTime', 'workOrderTime'])
  const temName = '工单数据.xlsx'
  useDownload(batchExport, temName, newParams)
}

const { busiSystemOptions } = useBusiSystemOptions()

const columns = computed<ColumnProps<any>[]>(() => [
  {
    type: 'selection',
    width: '55',
  },
  {
    type: 'index',
    label: '序号',
    width: '55',
    fixed: 'left',
  },
  {
    prop: 'orderTitle',
    label: '工单标题',
    minWidth: '220',
    fixed: 'left',
    render: ({ row }) => (
      <el-link
        onClick={() => {
          if (activeTab.value === 'pending') {
            if (row.currentNodeCode === 'user_task') {
              if (vifEdit.value) {
                editOrder(row)
              }
            } else {
              if (vifAudit.value) {
                editItem(row, 1)
              }
            }
          } else if (['approved', 'rejected'].includes(activeTab.value)) {
            if (vifView.value) {
              editItem(row, 2)
            }
          }
        }}
        type="primary"
      >
        {row.orderTitle}
      </el-link>
    ),
    search: {
      el: 'input',
      checked: true,
      defaultDisabled: true,
      order: 1,
    },
  },
  {
    prop: 'orderCode',
    label: '工单编号',
    minWidth: '200',
    search: {
      el: 'input',
    },
  },
  {
    prop: 'domainName',
    label: '云平台',
    minWidth: '180',
    enum: () => getCloudPlatformDic({}),
    isFilterEnum: false,
    fieldNames: {
      label: 'name',
      value: 'code',
    },
    search: {
      el: 'select',
      key: 'domainCode',
      props: {
        filterable: true,
      },
    },
  },
  {
    prop: 'businessSystemName',
    label: '业务系统',
    minWidth: '180',
    enum: busiSystemOptions,
    isFilterEnum: false,
    search: {
      el: 'select',
      key: 'businessSystemId',
      props: {
        filterable: true,
      },
    },
  },
  // {
  //   prop: 'orderTypeRemark',
  //   label: '工单类型',
  //   minWidth: '130',
  //   enum: orderTypeEnum,
  //   isFilterEnum: false,
  //   search: {
  //     el: 'select',
  //     key: 'orderType',
  //     props: {
  //       filterable: true,
  //     },
  //   },
  // },

  {
    prop: 'createdUserName',
    label: '申请人',
    minWidth: '140',
    search: {
      el: 'input',
    },
  },
  {
    prop: 'workOrderStartTime',
    label: '发起时间',
    minWidth: '180',
    search: {
      el: 'date-picker',
      key: 'workOrderTime',
      span: 1,
      props: {
        type: 'datetimerange',
        format: 'YYYY-MM-DD HH:mm:ss',
        valueFormat: 'YYYY-MM-DD HH:mm:ss',
      },
    },
  },
  {
    prop: 'currentNodeStartTime',
    label: '到达当前节点时间',
    minWidth: '180',
    search: {
      el: 'date-picker',
      key: 'currentNodeTime',
      span: 1,
      props: {
        type: 'datetimerange',
        format: 'YYYY-MM-DD HH:mm:ss',
        valueFormat: 'YYYY-MM-DD HH:mm:ss',
      },
    },
  },
  {
    prop: 'currentNodeName',
    label: '当前节点',
    minWidth: '150',
    enum: getDic('subscribe'),
    isFilterEnum: false,
    search: {
      el: 'select',
      key: 'currentNodeCode',
      order: 2,
      checked: true,
      defaultDisabled: true,
      props: {
        filterable: true,
      },
    },
  },
  {
    prop: 'operation',
    label: '操作',
    width: '150',
    fixed: 'right',
    render: operationrRender,
  },
])

// 可展示驳回节点的集合
// const RejectNodes = [
//   'tenant_task',
//   'user_task',
//   'schema_administrator',
//   'business_depart_leader2',
//   'business2_depart_leader',
//   'business_depart_leader',
//   'cloud_leader',
// ]
const refreshHide = () => {
  let flag = ['approved', 'pending'].includes(activeTab.value)
  return flag
}

function operationrRender({ row }: any): VNode {
  return (
    <>
      {/* 代办 */}
      {activeTab.value === 'pending' && (
        <>
          {row.currentNodeCode === 'user_task' ? (
            <el-button
              v-permission="Edit"
              onclick={() => editOrder(row)}
              type="warning"
              icon={Edit}
              link
            >
              修改
            </el-button>
          ) : (
            <el-button
              v-permission="Audit"
              onClick={() => editItem(row, 1)}
              type="primary"
              icon={Edit}
              link
            >
              审批
            </el-button>
          )}
          {/* 撤销 */}
        </>
      )}

      {/* 已办 - 已驳回 */}
      {['approved', 'rejected'].includes(activeTab.value) && (
        <>
          <el-button
            v-permission="View"
            onClick={() => editItem(row, 2)}
            type="success"
            icon={View}
            link
          >
            查看
          </el-button>
        </>
      )}
      {/* 撤销 已办 和 */}
      {refreshHide() && (
        <el-button
          v-permission="Refresh"
          disabled={!row.canRevoke}
          onclick={() => refreshFn(row)}
          type="warning"
          icon={Refresh}
          link
        >
          撤销
        </el-button>
      )}
    </>
  )
}

const getTableList = async (data: any) => {
  const params = changeDateFormat(data, ['currentNodeTime', 'workOrderTime'])
  params['approvalCode'] = activeTab.value
  getnumaxaminefunc()
  return getStandardWorkorderListApi(params)
}

const route = useRoute()
onMounted(() => {
  const query = route.query
  if (query && query.activeTab) {
    activeTab.value = query.activeTab as string
  }
})
watch(
  () => route.name,
  (newRouteName, oldRouteName) => {
    if (oldRouteName === 'orderApproval') {
      proTable.value?.getTableList()
    }
  },
  { immediate: true },
)
</script>

<style scoped lang="scss">
.sl-table-content {
  overflow: auto !important;
}
.sl-page-content {
  // 减去头部的标题高度 - 不减去会超出屏幕
  overflow-x: hidden;
  height: calc(100% - 90px);
}
</style>
