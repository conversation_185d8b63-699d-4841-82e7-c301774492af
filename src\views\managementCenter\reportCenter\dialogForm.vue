<template>
  <SlDialog
    @confirm="handleConfirm"
    @close="handleClose"
    v-model="dialogVisible"
    title="生成报表"
    width="70%"
  >
    <sl-form ref="slFormRef" :options="formOptions" :model-value="formData">
      <template #reportField>
        <el-checkbox
          v-model="checkAll"
          :indeterminate="isIndeterminate"
          @change="handleCheckAllChange"
        >
          全选
        </el-checkbox>
        <el-checkbox-group
          v-model="formData.exportFields"
          @change="handleCheckedFieldsChange"
          class="checkbox-group"
        >
          <el-checkbox
            v-for="field in fields"
            :key="field.value"
            :label="field.label"
            :value="field.value"
            class="checkbox-item"
          >
            {{ field.label }}
          </el-checkbox>
        </el-checkbox-group>
      </template>
    </sl-form>
  </SlDialog>
</template>

<script setup lang="tsx" name="dialogForm">
import { ref, watch, reactive } from 'vue'
import SlBlockTitle from '@/components/base/SlBlockTitle.vue'
import type { CheckboxValueType } from 'element-plus'
import { getCloudPlatformDic } from '@/api/modules/dic'
import { getReportRegionList, createReport } from '@/api/modules/managementCenter'
import fields from './field'

const cloudTypeOptions = ref<any[]>([])
const resourcePoolOptions = ref<any[]>([])
const slFormRef = ref<any>(null)
const formData = ref<any>({
  reportName: '',
  reportType: 'REGION',
  statType: '',
  domainCodes: [],
  regionIds: [],
  timeRange: [],
  exportFields: [],
})
const checkAll = ref(false)
const isIndeterminate = ref(false)

const handleCheckAllChange = (val: CheckboxValueType) => {
  formData.value.exportFields = val ? fields.map((field) => field.value) : []
  isIndeterminate.value = false
}

const getDomainOptions = async () => {
  const { entity } = await getCloudPlatformDic({})
  const options = entity.map((e: any) => ({
    value: e.code,
    label: e.name,
  }))
  cloudTypeOptions.value = options
}

const getReporRegion = async () => {
  const { entity }: any = await getReportRegionList({
    domainCodes: formData.value.domainCodes,
  })
  const options = entity.map((e: any) => ({
    value: e.id,
    label: e.name,
    regionCode: e.code,
  }))
  options.unshift({
    value: -1,
    label: '全部',
  })
  resourcePoolOptions.value = options
}

getDomainOptions()

watch(
  () => formData.value.domainCodes,
  (val) => {
    if (val) getReporRegion()
  },
)

const handleCheckedFieldsChange = (value: CheckboxValueType[]) => {
  const checkedCount = value.length
  checkAll.value = checkedCount === fields.length
  isIndeterminate.value = checkedCount > 0 && checkedCount < fields.length
}

const dialogVisible = ref(true)

const emit = defineEmits(['confirm', 'update:modelValue'])

const handleClose = () => {
  emit('update:modelValue', false)
}

const handleConfirm = async () => {
  const validate = await slFormRef.value.validate()
  if (!validate) return
  if (formData.value.exportFields.length === 0) {
    ElMessage.warning('请至少选择一个报表字段')
    return
  }
  const params = {
    reportType: formData.value.reportType,
    statType: formData.value.statType,
    regionIds: formData.value.regionIds,
    startTime: formData.value.timeRange[0],
    endTime: formData.value.timeRange[1],
    exportFields: formData.value.exportFields,
    queryCondition: '',
    reportName: formData.value.reportName,
    domainCodes: formData.value.domainCodes,
  }
  params.exportFields.push('dataTime')
  const queryCondition = {
    reportName: formData.value.reportName,
    domainCodesName: formData.value.domainCodes
      .map((e: any) => {
        return cloudTypeOptions.value.find((item: any) => item.value === e)?.label
      })
      .join(','),
    regionsName: formData.value.regionIds
      .map((e: any) => {
        return resourcePoolOptions.value.find((item: any) => item.value === e)?.label
      })
      .join(','),
    timeRange: `${formData.value.timeRange[0]}-${formData.value.timeRange[1]}`,
    exportFields: formData.value.exportFields,
    reportTypeName: '资源池',
    statTypeName: {
      HOUR: '每小时',
      DAY: '每天',
      MONTH: '每月',
    }[formData.value.statType as 'HOUR' | 'DAY' | 'MONTH'],
  }
  params.queryCondition = JSON.stringify(queryCondition)
  await createReport(params)
  ElMessage.success('提交成功')
  emit('update:modelValue', false)
  emit('confirm')
}

const formOptions = reactive([
  {
    style: 'margin: 0;padding: 0',
    gutter: 20,
    groupItems: [
      {
        span: 24,
        render() {
          return (
            <SlBlockTitle size={14} style="margin-bottom:20px">
              基本信息
            </SlBlockTitle>
          )
        },
      },
      {
        label: '报表名称',
        type: 'input',
        key: 'reportName',
        span: 8,
        required: true,
        rules: [{ required: true, message: '请输入报表名称' }],
      },
      {
        label: '报表类型',
        type: 'select',
        key: 'reportType',
        options: [
          {
            label: '资源池',
            value: 'REGION',
          },
        ],
        props: {
          select: {
            disabled: true,
          },
        },
        span: 8,
        required: true,
        rules: [{ required: true, message: '请输入报表类型' }],
      },
      {
        label: '时间粒度',
        type: 'select',
        key: 'statType',
        span: 8,
        required: true,
        rules: [{ required: true, message: '请选择时间粒度' }],
        options: [
          {
            label: '每小时',
            value: 'HOUR',
          },
          {
            label: '每天',
            value: 'DAY',
          },
          {
            label: '每月',
            value: 'MONTH',
          },
        ],
      },
      {
        span: 24,
        render() {
          return (
            <SlBlockTitle size={14} style="margin-bottom:20px">
              筛选条件
            </SlBlockTitle>
          )
        },
      },
      {
        label: '平台',
        type: 'select',
        key: 'domainCodes',
        options: cloudTypeOptions,
        span: 8,
        required: true,
        props: {
          select: {
            multiple: true,
          },
        },
        rules: [{ required: true, message: '请选择平台' }],
      },
      {
        label: '资源池',
        type: 'select',
        key: 'regionIds',
        options: resourcePoolOptions,
        span: 8,
        required: true,
        onChange: () => {
          if (formData.value.regionIds.includes(-1)) {
            formData.value.regionIds = [-1]
          }
          getReporRegion()
        },
        props: {
          select: {
            multiple: true,
          },
        },
        rules: [{ required: true, message: '请选择资源池' }],
      },
      {
        label: '时间范围',
        type: 'date',
        key: 'timeRange',
        span: 8,
        required: true,
        rules: [{ required: true, message: '请选择时间范围' }],
        props: {
          type: 'datetimerange',
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          rangeSeparator: '至',
          startPlaceholder: '开始时间',
          endPlaceholder: '结束时间',
        },
      },
      {
        span: 24,
        render() {
          return (
            <SlBlockTitle size={14} style="margin-bottom:20px">
              报表字段
            </SlBlockTitle>
          )
        },
      },
      {
        type: 'slot',
        noFormItem: true,
        slotName: 'reportField',
        span: 24,
      },
    ],
  },
])
</script>

<style lang="scss" scoped>
.checkbox-group {
  display: flex;
  flex-wrap: wrap;
  margin-top: 10px;

  .checkbox-item {
    width: 175px;
    margin-right: 10px;
    margin-bottom: 10px;
  }
}
</style>
