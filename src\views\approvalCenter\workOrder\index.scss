.card-box {
  background-color: #fff;
  padding: 10px;
  margin-bottom: 10px;
  padding-left: 30px;
}
.titleTip {
  position: relative;
  &::before {
    content: '';
    position: absolute;
    top: 5px;
    left: -20px;
    width: 8px;
    height: 8px;
    background-color: blue;
  }
}

.page-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  height: 30px;
}
/* 去掉 el-tabs 下边框 */
:deep() {
  .el-tabs__nav-wrap:after {
    display: none;
  }
}
.order-approval {
  position: relative;
  padding-bottom: 60px;
}
.page-botton {
  position: absolute;
  right: 20px;
  bottom: 20px;
}
