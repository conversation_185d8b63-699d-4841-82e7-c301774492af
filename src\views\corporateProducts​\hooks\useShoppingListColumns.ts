import type { ColumnProps } from '@/components/SlProTable/interface'
import { h } from 'vue'
import { ElButton, ElText } from 'element-plus'

/**
 * 购物清单各资源类型的表格列配置
 */
export function useShoppingListColumns({
  allowDelete = true,
  showStatusAndMessage = false,
}: {
  allowDelete?: boolean
  showStatusAndMessage?: boolean
} = {}) {
  // 状态字典
  const statuses = [
    { value: 'wait_open', label: '待开通' },
    { value: 'opening', label: '开通中' },
    { value: 'open_success', label: '开通成功' },
    { value: 'open_fail', label: '开通失败' },
  ]

  // 操作列渲染函数
  const renderOperation = (row: any, onDelete: (row: any) => void) => {
    return h(
      ElButton,
      {
        type: 'text',
        onClick: () => onDelete(row),
      },
      { default: () => '删除' },
    )
  }

  // 状态渲染函数
  const renderStatus = (row: any) => {
    const statusTypeMap: Record<string, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
      wait_open: 'primary',
      opening: 'warning',
      open_success: 'success',
      open_fail: 'danger',
    }
    const type = statusTypeMap[row.status] || 'primary'
    const statusLabel =
      statuses.find((item) => item.value === row.status)?.label || row.status || '--'

    return h(
      ElText,
      {
        type: type,
        underline: false,
      },
      { default: () => statusLabel },
    )
  }

  // 失败原因渲染函数
  const renderMessage = (row: any) => {
    return h('div', {}, row.message || '--')
  }

  // 格式化付费类型
  const formatPaymentType = (type: string): string => {
    const paymentTypeMap: Record<string, string> = {
      day: '按日付费',
      month: '按月付费',
      year: '按年付费',
    }
    return paymentTypeMap[type] || type
  }

  // 格式化是否挂载云主机
  const formatIsMounted = (value: any): string => {
    return value ? '是' : '否'
  }

  // 统一的VPC名称获取函数
  const getVpcName = (row: any): string => {
    if (!row.planeNetworkModel) return '--'

    if (Array.isArray(row.planeNetworkModel) && row.planeNetworkModel.length > 0) {
      return row.planeNetworkModel[0].name || '--'
    } else if (typeof row.planeNetworkModel === 'object') {
      return row.planeNetworkModel.name || '--'
    }

    return '--'
  }

  // 统一的子网名称获取函数
  const getSubnetName = (row: any): string => {
    if (!row.planeNetworkModel) return '--'

    if (
      Array.isArray(row.planeNetworkModel) &&
      row.planeNetworkModel.length > 0 &&
      row.planeNetworkModel[0].subnets &&
      row.planeNetworkModel[0].subnets.length > 0
    ) {
      return row.planeNetworkModel[0].subnets[0].subnetName || '--'
    } else if (
      typeof row.planeNetworkModel === 'object' &&
      row.planeNetworkModel.subnets &&
      row.planeNetworkModel.subnets.length > 0
    ) {
      return row.planeNetworkModel.subnets[0].subnetName || '--'
    }

    return '--'
  }

  // 格式化系统盘
  const formatSysDisk = (row: any) => {
    if (!row.sysDiskType && !row.sysDiskSize) return '--'
    return `${row.sysDiskType || '--'} / ${row.sysDiskSize || '--'} GB`
  }

  // 格式化数据盘
  const formatDataDisk = (row: any) => {
    if (!row.mountDataDisk) return '--'
    if (!row.mountDataDiskList || row.mountDataDiskList.length === 0) return '--'

    return row.mountDataDiskList
      .map((item: any) => `${item.sysDiskType || '--'} / ${item.sysDiskSize || '--'} GB`)
      .join(',')
  }

  // 格式化镜像
  const formatImage = (row: any) => {
    if (!row.imageVersion && !row.imageOs) return row.imageName || '--'
    return `${row.imageVersion || '--'} / ${row.imageOs || '--'}`
  }

  // 格式化带宽
  const formatBandwidth = (row: any) => {
    if (!row.bindPublicIp) return '--'
    if (!row.eipModelList || row.eipModelList.length === 0)
      return row.bandwidth ? `${row.bandwidth} M` : '--'

    return row.eipModelList
      .map((item: any) => (item.bandwidth ? `${item.bandwidth} M` : '--'))
      .join(',')
  }

  // 文本渲染函数
  const renderText = (text: string) => {
    return h('div', {}, text)
  }

  // ECS和GCS资源的列配置
  const getEcsGcsColumns = (onDelete: (row: any) => void): ColumnProps<any>[] => {
    const columns: ColumnProps<any>[] = [
      { type: 'index', label: '序号', minWidth: 60 },
      {
        prop: 'billType',
        label: '付费类型',
        width: 100,
        render: ({ row }) => renderText(formatPaymentType(row.billType)),
      },
      { prop: 'domainName', label: '云平台', minWidth: 120 },
      { prop: 'regionName', label: '资源池', minWidth: 120 },
      { prop: 'azName', label: '可用区', minWidth: 120 },
      {
        prop: 'vpcName',
        label: 'VPC',
        width: 150,
        render: ({ row }) => renderText(getVpcName(row)),
      },
      {
        prop: 'subnetName',
        label: '子网',
        width: 150,
        render: ({ row }) => renderText(getSubnetName(row)),
      },
      {
        prop: 'flavorName',
        label: '实例规格',
        width: 150,
        render: ({ row }) => {
          if (!row.flavorType && !row.flavorName) return renderText('--')
          return renderText(`${row.flavorType || '--'} / ${row.flavorName || '--'}`)
        },
      },
      {
        prop: 'imageName',
        label: '镜像',
        width: 150,
        render: ({ row }) => renderText(formatImage(row)),
      },
      {
        prop: 'sysDiskInfo',
        label: '系统盘',
        width: 150,
        render: ({ row }) => renderText(formatSysDisk(row)),
      },
      {
        prop: 'evsInfo',
        label: '数据盘',
        width: 150,
        render: ({ row }) => renderText(formatDataDisk(row)),
      },
      {
        prop: 'bandWiDthNumbers',
        label: '公网带宽',
        width: 100,
        render: ({ row }) => renderText(formatBandwidth(row)),
      },
      {
        prop: 'vmName',
        label: '实例名称',
        minWidth: 150,
        render: ({ row }) => renderText(row.vmName || '--'),
      },
      {
        prop: 'userName',
        label: '登录名',
        minWidth: 120,
        render: ({ row }) => renderText(row.userName || '--'),
      },
      { prop: 'tenantName', label: '所属租户', minWidth: 120 },
      {
        prop: 'openNum',
        label: '数量',
        minWidth: 80,
        render: ({ row }) => renderText(row.openNum || '--'),
      },
    ]

    // 只有在订单中心详情页面才显示状态和失败原因
    if (showStatusAndMessage) {
      columns.push(
        {
          prop: 'message',
          label: '失败原因',
          width: 300,
          render: ({ row }) => renderMessage(row),
        },
        {
          prop: 'status',
          label: '状态',
          width: 100,
          render: ({ row }) => renderStatus(row),
        },
      )
    }

    // 只有当允许删除时才添加操作列
    if (allowDelete) {
      columns.push({
        prop: 'operation',
        label: '操作',
        width: 100,
        fixed: 'right',
        render: ({ row }) => renderOperation(row, onDelete),
      })
    }

    return columns
  }

  // EVS资源的列配置
  const getEvsColumns = (onDelete: (row: any) => void): ColumnProps<any>[] => {
    const columns: ColumnProps<any>[] = [
      { type: 'index', label: '序号', minWidth: 60 },
      {
        prop: 'mountDataDisk',
        label: '是否挂载云主机',
        width: 130,
        render: ({ row }) => renderText(formatIsMounted(row.mountDataDisk)),
      },
      { prop: 'vmName', label: '云主机', minWidth: 150 },
      { prop: 'domainName', label: '云平台', minWidth: 120 },
      { prop: 'regionName', label: '资源池', minWidth: 120 },
      { prop: 'azName', label: '可用区', minWidth: 120 },
      {
        prop: 'billType',
        label: '付费类型',
        width: 100,
        render: ({ row }) => renderText(formatPaymentType(row.billType)),
      },
      { prop: 'sysDiskType', label: '类型', minWidth: 100 },
      {
        prop: 'sysDiskSize',
        label: '容量',
        width: 100,
        render: ({ row }) => renderText(`${row.sysDiskSize || 0} GB`),
      },
      {
        prop: 'openNum',
        label: '数量',
        minWidth: 80,
        render: ({ row }) => renderText(row.openNum || '--'),
      },
      { prop: 'tenantName', label: '所属租户', minWidth: 120 },
    ]

    // 只有在订单中心详情页面才显示状态和失败原因
    if (showStatusAndMessage) {
      columns.push(
        {
          prop: 'message',
          label: '失败原因',
          width: 300,
          render: ({ row }) => renderMessage(row),
        },
        {
          prop: 'status',
          label: '状态',
          width: 100,
          render: ({ row }) => renderStatus(row),
        },
      )
    }

    // 只有当允许删除时才添加操作列
    if (allowDelete) {
      columns.push({
        prop: 'operation',
        label: '操作',
        width: 100,
        fixed: 'right',
        render: ({ row }) => renderOperation(row, onDelete),
      })
    }

    return columns
  }

  // OBS资源的列配置
  const getObsColumns = (onDelete: (row: any) => void): ColumnProps<any>[] => {
    const columns: ColumnProps<any>[] = [
      { type: 'index', label: '序号', minWidth: 60 },
      {
        prop: 'billType',
        label: '付费类型',
        width: 100,
        render: ({ row }) => renderText(formatPaymentType(row.billType)),
      },
      { prop: 'domainName', label: '云平台', minWidth: 120 },
      { prop: 'regionName', label: '资源池', minWidth: 120 },
      { prop: 'azName', label: '可用区', minWidth: 120 },
      { prop: 'obsName', label: '对象存储名称', minWidth: 150 },
      { prop: 'storeType', label: '存储类型', minWidth: 120 },
      { prop: 'capacity', label: '容量', minWidth: 100 },
      { prop: 'tenantName', label: '所属租户', minWidth: 120 },
      {
        prop: 'openNum',
        label: '数量',
        minWidth: 80,
        render: ({ row }) => renderText(row.openNum || '--'),
      },
    ]

    // 只有在订单中心详情页面才显示状态和失败原因
    if (showStatusAndMessage) {
      columns.push(
        {
          prop: 'message',
          label: '失败原因',
          width: 300,
          render: ({ row }) => renderMessage(row),
        },
        {
          prop: 'status',
          label: '状态',
          width: 100,
          render: ({ row }) => renderStatus(row),
        },
      )
    }

    // 只有当允许删除时才添加操作列
    if (allowDelete) {
      columns.push({
        prop: 'operation',
        label: '操作',
        width: 100,
        fixed: 'right',
        render: ({ row }) => renderOperation(row, onDelete),
      })
    }

    return columns
  }

  // SLB资源的列配置
  const getSlbColumns = (onDelete: (row: any) => void): ColumnProps<any>[] => {
    const columns: ColumnProps<any>[] = [
      { type: 'index', label: '序号', minWidth: 60 },
      { prop: 'domainName', label: '云平台', minWidth: 120 },
      { prop: 'regionName', label: '资源池', minWidth: 120 },
      { prop: 'tenantName', label: '所属租户', minWidth: 120 },
      {
        prop: 'billType',
        label: '付费类型',
        width: 100,
        render: ({ row }) => renderText(formatPaymentType(row.billType)),
      },
      { prop: 'slbName', label: '负载均衡名称', minWidth: 150 },
      { prop: 'azName', label: '可用区', minWidth: 120 },
      {
        prop: 'flavorName',
        label: '规格',
        width: 120,
        render: ({ row }) => {
          if (!row.flavorType && !row.flavorName) return renderText('--')
          return renderText(`${row.flavorType || '--'} / ${row.flavorName || '--'}`)
        },
      },
      {
        prop: 'bandWiDthNumbers',
        label: '公网IP带宽',
        width: 120,
        render: ({ row }) => renderText(formatBandwidth(row)),
      },
      {
        prop: 'vpcName',
        label: 'VPC',
        width: 120,
        render: ({ row }) => renderText(getVpcName(row)),
      },
      {
        prop: 'subnetName',
        label: '子网',
        width: 120,
        render: ({ row }) => renderText(getSubnetName(row)),
      },
      {
        prop: 'openNum',
        label: '数量',
        minWidth: 80,
        render: ({ row }) => renderText(row.openNum || '--'),
      },
    ]

    // 只有在订单中心详情页面才显示状态和失败原因
    if (showStatusAndMessage) {
      columns.push(
        {
          prop: 'message',
          label: '失败原因',
          width: 300,
          render: ({ row }) => renderMessage(row),
        },
        {
          prop: 'status',
          label: '状态',
          width: 100,
          render: ({ row }) => renderStatus(row),
        },
      )
    }

    // 只有当允许删除时才添加操作列
    if (allowDelete) {
      columns.push({
        prop: 'operation',
        label: '操作',
        width: 100,
        fixed: 'right',
        render: ({ row }) => renderOperation(row, onDelete),
      })
    }

    return columns
  }

  // NAT资源的列配置
  const getNatColumns = (onDelete: (row: any) => void): ColumnProps<any>[] => {
    const columns: ColumnProps<any>[] = [
      { type: 'index', label: '序号', minWidth: 60 },
      { prop: 'domainName', label: '云平台', minWidth: 120 },
      { prop: 'regionName', label: '资源池', minWidth: 120 },
      { prop: 'tenantName', label: '所属租户', minWidth: 120 },
      {
        prop: 'billType',
        label: '付费类型',
        width: 100,
        render: ({ row }) => renderText(formatPaymentType(row.billType)),
      },
      { prop: 'natName', label: 'NAT名称', minWidth: 150 },
      { prop: 'azName', label: '可用区', minWidth: 120 },
      {
        prop: 'flavorName',
        label: '规格',
        width: 120,
        render: ({ row }) => {
          if (!row.flavorType && !row.flavorName) return renderText('--')
          return renderText(`${row.flavorType || '--'} / ${row.flavorName || '--'}`)
        },
      },
      {
        prop: 'bandWiDthNumbers',
        label: '公网IP带宽',
        width: 120,
        render: ({ row }) => renderText(formatBandwidth(row)),
      },
      {
        prop: 'vpcName',
        label: 'VPC',
        width: 120,
        render: ({ row }) => renderText(getVpcName(row)),
      },
      {
        prop: 'subnetName',
        label: '子网',
        width: 120,
        render: ({ row }) => renderText(getSubnetName(row)),
      },
      {
        prop: 'openNum',
        label: '数量',
        minWidth: 80,
        render: ({ row }) => renderText(row.openNum || '--'),
      },
    ]

    // 只有在订单中心详情页面才显示状态和失败原因
    if (showStatusAndMessage) {
      columns.push(
        {
          prop: 'message',
          label: '失败原因',
          width: 300,
          render: ({ row }) => renderMessage(row),
        },
        {
          prop: 'status',
          label: '状态',
          width: 100,
          render: ({ row }) => renderStatus(row),
        },
      )
    }

    // 只有当允许删除时才添加操作列
    if (allowDelete) {
      columns.push({
        prop: 'operation',
        label: '操作',
        width: 100,
        fixed: 'right',
        render: ({ row }) => renderOperation(row, onDelete),
      })
    }

    return columns
  }

  // VPN资源的列配置
  const getVpnColumns = (onDelete: (row: any) => void): ColumnProps<any>[] => {
    const columns: ColumnProps<any>[] = [
      { type: 'index', label: '序号', minWidth: 60 },
      { prop: 'domainName', label: '云平台', minWidth: 120 },
      { prop: 'regionName', label: '资源池', minWidth: 120 },
      { prop: 'azName', label: '可用区', minWidth: 120 },
      { prop: 'tenantName', label: '所属租户', minWidth: 120 },
      {
        prop: 'billType',
        label: '付费类型',
        width: 100,
        render: ({ row }) => renderText(formatPaymentType(row.billType)),
      },
      {
        prop: 'vpcName',
        label: 'VPC',
        width: 120,
        render: ({ row }) => renderText(getVpcName(row)),
      },
      {
        prop: 'subnetName',
        label: '子网',
        width: 120,
        render: ({ row }) => renderText(getSubnetName(row)),
      },
      {
        prop: 'bandwidth',
        label: '带宽',
        width: 100,
        render: ({ row }) => renderText(formatBandwidth(row)),
      },
      {
        prop: 'openNum',
        label: '数量',
        minWidth: 80,
        render: ({ row }) => renderText(row.openNum || '--'),
      },
    ]

    // 只有在订单中心详情页面才显示状态和失败原因
    if (showStatusAndMessage) {
      columns.push(
        {
          prop: 'message',
          label: '失败原因',
          width: 300,
          render: ({ row }) => renderMessage(row),
        },
        {
          prop: 'status',
          label: '状态',
          width: 100,
          render: ({ row }) => renderStatus(row),
        },
      )
    }

    // 只有当允许删除时才添加操作列
    if (allowDelete) {
      columns.push({
        prop: 'operation',
        label: '操作',
        width: 100,
        fixed: 'right',
        render: ({ row }) => renderOperation(row, onDelete),
      })
    }

    return columns
  }

  // 根据资源类型获取对应的列配置
  const getColumnsByResourceType = (
    resourceType: string,
    onDelete: (row: any) => void,
  ): ColumnProps<any>[] => {
    switch (resourceType) {
      case 'ecs':
        return getEcsGcsColumns(onDelete)
      case 'gcs':
        return getEcsGcsColumns(onDelete)
      case 'evs':
        console.log('evs')
        return getEvsColumns(onDelete)
      case 'obs':
        return getObsColumns(onDelete)
      case 'slb':
        return getSlbColumns(onDelete)
      case 'nat':
        return getNatColumns(onDelete)
      case 'vpn':
        return getVpnColumns(onDelete)
      default:
        return []
    }
  }

  return {
    getEcsGcsColumns,
    getEvsColumns,
    getObsColumns,
    getSlbColumns,
    getNatColumns,
    getVpnColumns,
    getColumnsByResourceType,
    formatPaymentType,
    formatIsMounted,
    getVpcName,
    getSubnetName,
    renderStatus,
    renderMessage,
  }
}
