<template>
  <el-pagination
    background
    v-model:current-page="selfCurrentPage"
    :page-size="selfPageSize"
    :page-sizes="pageSizes"
    :total="total"
    :layout="layout"
    @current-change="handlePageChange"
    @size-change="handleSizeChange"
  />
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
const props = withDefaults(
  defineProps<{
    currentPage: number
    pageSize: number
    pageSizes?: number[] // Optional
    total: number
    layout?: string // Optional
  }>(),
  {
    pageSizes: () => [10, 20, 30, 40, 50, 100],
    layout: 'total,->, prev, pager, next,sizes, jumper',
  },
)

const emits = defineEmits<{
  (event: 'update:currentPage', value: number): void
  (event: 'update:pageSize', value: number): void
}>()

// Reactive values
const selfCurrentPage = ref(props.currentPage || 1)
const selfPageSize = ref(props.pageSize || props.pageSizes[0])

// Watch for external prop changes
watch(
  () => props.currentPage,
  (newPage) => {
    selfCurrentPage.value = newPage
  },
)

watch(
  () => props.pageSize,
  (newSize) => {
    selfPageSize.value = newSize
  },
)

// Emit page change event
const handlePageChange = (page: number): void => {
  emits('update:currentPage', page)
}

// Emit size change event
const handleSizeChange = (size: number): void => {
  emits('update:pageSize', size)
}
</script>

<style scoped>
/* Custom styles for pagination */
.el-pagination {
  padding-top: 10px;
  display: flex;
  justify-content: space-between; /* Distribute items horizontally */
  align-items: center; /* Center items vertically */
}

/* Align total on the left */
.el-pagination .el-pager {
  margin-left: 0;
}

/* Align other pagination controls to the right */
.el-pagination .el-pagination__right {
  margin-left: auto; /* Align to the right */
}
</style>
