<template>
  <div>
    <div>
      <sl-page-header title="VPC"></sl-page-header>
    </div>
    <sl-form
      class="corporate-products​"
      label-position="left"
      :options="formOptions"
      :model-value="formData"
      :label-width="120"
      ref="formRef"
    >
      <template #ip-range-slot="{ form }">
        <div class="subnet-config">
          <div class="subnet-input-section">
            <el-input v-model="form.ipRange" placeholder="xxx.xx.x.x/xx" style="width: 200px" />
          </div>
          <div class="subnet-recommendations">
            <div class="recommendation-label">网段配置建议:</div>
            <div class="recommendation-options">
              <div
                v-for="subnet in subnetRecommendations"
                :key="subnet"
                class="recommendation-item"
                :class="{ active: form.subnet === subnet }"
                @click="form.ipRange = subnet"
              >
                <span class="subnet-text">{{ subnet }}</span>
                <span class="action-text">使用</span>
              </div>
            </div>
          </div>
        </div>
      </template>
    </sl-form>

    <!-- 右下角价格面板 -->
    <div class="price-panel">
      <div class="con">
        <div class="price-section">
          <!-- <div class="price-info">
            <div class="discount-info">
              <span class="label">折扣价格:</span>
              <span class="discount-price">¥ 300.00</span>
              <span class="discount-badge">(3折)</span>
            </div>
            <div class="original-info">
              <span class="original-label">原价:</span>
              <span class="original-price">¥ 1000</span>
              <el-button link type="primary" class="detail-link">查看明细</el-button>
            </div>
          </div>
          <div class="warning-text">当前价格不代表真实产品，最终价格以BOSS侧为准！</div> -->
        </div>
        <div class="action-section">
          <el-button class="cancel-btn" @click="handleCancel">取消</el-button>
          <!-- <el-button type="primary" plain class="cart-btn" @click="handleAddToCart">
            加入清单
          </el-button> -->
          <el-button type="primary" class="order-btn" @click="handleDirectOrder">
            直接开通
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="tsx">
import { markRaw, ref } from 'vue'
import RegionSelect from './components/RegionSelect.vue'
import Subnet from './components/Subnet.vue'
import { useTenant } from './hooks/useTenant'
import useGetTheParameters from './hooks/useGetTheParameters'
import SlForm from '@/components/form/SlForm.vue'
import { vpcCreateBatchDGApi } from '@/api/modules/resourecenter'
import { isValidAlphanumericWithUnderscore, validateGoodsName } from '../resourceCenter/utils'
import eventBus from '@/utils/eventBus'

const { tenantList: tenantListOptions } = useTenant()
const formData = ref<any>({
  region: 'placeholder',
  domain: null,
  resourcePool: null,
  az: null,
  name: '',
  tenant: '',
  ipRange: '',
  subnet: [
    {
      name: '',
      ip1: '',
      ip2: '',
      ip3: '',
      ip4: '',
      cidr: '',
    },
  ],
  catalogueDomainCode: 'cloudst_group_moc',
  catalogueDomainName: '移动云',
})

// 网段推荐选项
const subnetRecommendations = ['10.0.0.0/16', '**********/12', '***********/16']

const formOptions = ref([
  {
    style: 'margin:10px;padding:15px 10px;',
    gutter: 40,
    groupItems: [
      {
        label: '区域',
        type: 'component',
        key: 'region',
        span: 24,
        component: markRaw(RegionSelect),
        required: true,
        rules: [
          {
            validator: (rule: any, value: any, callback: any) => {
              if (!formData.value.domain) {
                callback(new Error('请选择云平台'))
              }
              if (!formData.value.resourcePool) {
                callback(new Error('请选择资源池'))
              }
              if (!formData.value.az) {
                callback(new Error('请选择可用区'))
              }
              callback()
            },
            trigger: ['blur', 'change'],
          },
        ],
        props: {
          maxlength: 64,
        },
      },
      {
        label: '名称',
        type: 'input',
        key: 'name',
        span: 13,
        required: true,
        rules: [
          { required: true, message: '请输入名称', trigger: ['blur', 'change'] },
          { validator: validateGoodsName, trigger: ['blur', 'change'] },
        ],
      },
      {
        label: '租户',
        type: 'select',
        key: 'tenant',
        span: 13,
        options: tenantListOptions,
        required: true,
        rules: [{ required: true, message: '请选择租户', trigger: ['blur', 'change'] }],
        props: {
          select: {
            valueKey: 'id',
            clearable: true,
          },
        },
      },
      {
        label: '网段',
        key: 'ipRange',
        type: 'slot',
        slotName: 'ip-range-slot',
        span: 24,
        required: true,
        rules: [{ required: true, message: '请输入网段', trigger: ['blur', 'change'] }],
      },
    ],
  },
  {
    style: 'margin:10px;padding:15px 10px;',
    gutter: 40,
    groupItems: [
      {
        label: '子网',
        type: 'component',
        key: 'subnet',
        span: 16,
        component: markRaw(Subnet),
        maxRows: 10,
        addButtonText: '添加子网',
        emptyText: '暂无子网配置',
        showEmptyState: true,
        required: true,
        rules: [
          {
            validator: (rule: any, value: any, callback: any) => {
              if (!formData.value.subnet.length) {
                callback(new Error('请输入子网'))
              }
              if (formData.value.subnet.some((item: any) => !item.name)) {
                callback(new Error('请输入子网名称'))
              }
              if (
                formData.value.subnet.some(
                  (item: any) => !isValidAlphanumericWithUnderscore(item.name),
                )
              ) {
                callback(new Error('子网名称不符合规范'))
              }
              if (
                formData.value.subnet.some(
                  (item: any) => !item.ip1 || !item.ip2 || !item.ip3 || !item.ip4,
                )
              ) {
                callback(new Error('请输入子网IP'))
              }
              if (formData.value.subnet.some((item: any) => !item.cidr)) {
                callback(new Error('请输入子网掩码'))
              }

              callback()
            },
            trigger: ['blur', 'change'],
          },
        ],
      },
    ],
  },
])

const formRef = ref<InstanceType<typeof SlForm>>()

// 处理取消操作
const handleCancel = () => {
  // 可以添加取消逻辑，比如清空表单或返回上一页
  closeWindow()
  eventBus.emit('corporateShoppingList:updateCount')
}

// // 处理加入清单操作
// const handleAddToCart = () => {
//   // 可以添加加入清单的逻辑
// }

// 处理直接开通操作
const handleDirectOrder = async () => {
  try {
    // 1. 校验表单
    const valid = await formRef.value?.validate()
    if (!valid) return

    // 2. 构建参数
    const params = useGetTheParameters(formData.value, 'vpc')

    // 3. 调用API
    await vpcCreateBatchDGApi(params)
    ElMessage.success('发起开通成功')
    closeWindow()
  } catch (error: any) {
    console.error(error)
  }
}

const closeWindow = () => {
  if (window.opener) {
    window.opener.updateVpc()
    window.close()
  }
}
</script>

<style scoped>
/* 通用的form label加粗样式 */
.corporate-products​ :deep(.el-form-item__label) {
  font-weight: bold;
}
.corporate-products​ :deep(.table-main) {
  border: 1px solid #e4e7ed;
  box-shadow: none;
}

/* 网段配置样式 */
.subnet-config {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.subnet-input-section {
  display: flex;
  align-items: center;
}

.subnet-recommendations {
  display: flex;
}

.recommendation-label {
  font-size: 14px;
  font-weight: 500;
  color: #495057;
  margin-bottom: 12px;
}

.recommendation-options {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  margin-left: 12px;
}

.recommendation-item {
  display: flex;
  justify-content: space-between;
  min-width: 140px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.subnet-text {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.action-text {
  font-size: 12px;
  color: #409eff;
  font-weight: 500;
  flex: 0.9;
}

.eip-config :deep(.el-checkbox) {
  font-size: 14px;
}

.eip-config :deep(.el-checkbox__label) {
  font-weight: normal;
  color: #606266;
}

/* 展开按钮容器样式 */
.expand-container {
  display: flex;
  align-items: center;
  position: relative;
}
/* 图标样式 */
.expand-icon {
  transition: transform 0.3s ease;
  font-size: 14px;
}

.expand-icon.rotated {
  transform: rotate(180deg);
}

/* 价格面板样式 */
.price-panel {
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  background: #ffffff;
  border-top: 1px solid #e4e7ed;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  padding: 0 10px;

  .con {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.price-section {
  padding: 16px;
  border-bottom: 1px solid #f0f2f5;
}

.price-info {
  display: block;
}

.discount-info {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.label {
  font-size: 14px;
  color: #606266;
  margin-right: 8px;
}

.discount-price {
  font-size: 24px;
  font-weight: bold;
  color: #ff6b35;
  margin-right: 8px;
}

.discount-badge {
  font-size: 12px;
  color: #ff6b35;
  background: #fff2e8;
  padding: 2px 6px;
  border-radius: 4px;
}

.original-info {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.original-label {
  font-size: 12px;
  color: #909399;
  margin-right: 8px;
}

.original-price {
  font-size: 12px;
  color: #909399;
  text-decoration: line-through;
  margin-right: 8px;
}

.detail-link {
  font-size: 12px;
  padding: 0;
  height: auto;
}

.warning-text {
  font-size: 11px;
  color: #f56c6c;
  line-height: 1.4;
}

.action-section {
  padding: 12px 16px;
  display: flex;
  gap: 8px;
  align-self: end;
}

.cancel-btn {
  flex: 1;
  height: 36px;
  font-size: 14px;
  color: #606266;
  border-color: #dcdfe6;
}

.cart-btn {
  flex: 1;
  height: 36px;
  font-size: 14px;
}

.order-btn {
  flex: 1;
  height: 36px;
  font-size: 14px;
}
</style>
