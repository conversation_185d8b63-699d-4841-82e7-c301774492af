<template>
  <div class="table-box">
    <sl-page-header
      title="项目管理"
      title-line="项目管理提供了项目查询功能。"
      :icon="{
        Vnode: Platform,
        color: '#0052D9',
        size: '50px',
      }"
      >\
    </sl-page-header>
    <div class="sl-page-content table-main">
      <SlProTable ref="proTable" :columns="columns" :request-api="getList" row-key="id">
      </SlProTable>
    </div>
  </div>
</template>
<script setup lang="tsx">
import { Platform } from '@element-plus/icons-vue'
import type { ColumnProps } from '@/components/SlProTable/interface'
import { changeDateFormat } from '@/utils'
import { getRegionsSelectAPi, getProjectListAPi } from '@/api/modules/externalDocking'

const getList = async (data: any) => {
  const params = changeDateFormat(data, ['createTime'])

  return getProjectListAPi(params)
}

const columns: ColumnProps[] = [
  { type: 'index', label: '序号', width: 55 },
  {
    prop: 'projectName',
    label: '项目名称',
    minWidth: 180,
    search: { el: 'input', checked: true, defaultDisabled: true },
  },
  {
    prop: 'appName',
    label: '业务系统名称',
    minWidth: 150,
    search: { el: 'input', checked: true, defaultDisabled: true },
  },
  {
    prop: 'regionName',
    label: '资源池名称',
    minWidth: 150,
    isFilterEnum: false,
    enum: async () => {
      const { entity } = await getRegionsSelectAPi({ pageSize: 9999, pageNum: 1 })
      return { entity: entity.records }
    },
    fieldNames: {
      label: 'name',
      value: 'id',
    },
    search: {
      el: 'select',
      key: 'regionId',
      props: {
        filterable: true,
      },
    },
  },
  {
    prop: 'cloudTenantName',
    label: '租户名称',
    minWidth: 150,
    search: { el: 'input', key: 'tenantName' },
  },
  {
    prop: 'createTime',
    label: '创建时间',
    minWidth: 180,
  },
]
</script>

<style scoped lang="sass"></style>
