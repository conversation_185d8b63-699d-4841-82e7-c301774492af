<template>
  <div id="VirtualIpDetail" class="table-box">
    <sl-page-header
      title="虚拟IP详情"
      :icon="{
        Vnode: Platform,
        color: '#0052D9',
        size: '20px',
      }"
      :show-back="true"
      :back="{
        title: '返回列表',
        function: handleGoBack,
      }"
    >
    </sl-page-header>
    <el-scrollbar wrap-class="slb-detail-scroll-view" class="scroll-view">
      <div class="sl-card">
        <sl-form
          :show-block-title="false"
          :label-width="140"
          ref="slFormRef"
          v-model="detailData"
          :options="formOptions"
        >
        </sl-form>
      </div>
    </el-scrollbar>
  </div>
</template>

<script setup lang="tsx">
import { onMounted, ref, reactive } from 'vue'
import { Platform } from '@element-plus/icons-vue'
import SlPageHeader from '@/components/SlPageHeader/index.vue'
import { useRouter, useRoute } from 'vue-router'
import { getVirtualIpDetail } from '@/api/modules/resourecenter'

const router = useRouter()
const route = useRoute()

// 获取资源详情ID
const resourceId = ref<string>((route.query.id as string) || '')

const detailData = reactive<any>({
  vipName: '',
  businessSystemName: '',
  catalogueDomainName: '',
  domainName: '',
  regionName: '',
  azName: '',
  vpcName: '',
  subnetName: '',
  ipAddress: '',
  createdTime: '',
})

const formOptions = reactive<any[]>([
  {
    groupItems: [
      {
        label: '虚拟IP名称',
        type: 'text',
        key: 'vipName',
        span: 8,
      },
      {
        label: '业务系统',
        type: 'text',
        key: 'businessSystemName',
        span: 8,
      },
      {
        label: '云类型',
        type: 'text',
        key: 'catalogueDomainName',
        span: 8,
      },
      {
        label: '云平台',
        type: 'text',
        key: 'domainName',
        span: 8,
      },
      {
        label: '资源池',
        type: 'text',
        key: 'regionName',
        span: 8,
      },
      {
        label: '可用区',
        type: 'text',
        key: 'azName',
        span: 8,
      },
      {
        label: 'VPC/网络',
        type: 'text',
        key: 'vpcName',
        span: 8,
      },
      {
        label: '子网',
        type: 'text',
        key: 'subnetName',
        span: 8,
      },
      {
        label: 'IP地址',
        type: 'text',
        key: 'ipAddress',
        span: 8,
      },
      {
        label: '创建时间',
        type: 'text',
        key: 'createdTime',
        span: 8,
      },
    ],
  },
])

const fetchResourceDetail = async () => {
  const res = await getVirtualIpDetail({
    id: resourceId.value,
  })
  if (res && res.entity) {
    for (const key in detailData) {
      detailData[key] = res.entity[key]
    }
  }
}

// 返回上一页
const handleGoBack = () => {
  router.push({
    path: '/virtualIpList',
  })
}

// 初始化
onMounted(async () => {
  if (resourceId.value) {
    await fetchResourceDetail()
  }
})
</script>

<style lang="scss" scoped>
.table-box {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.scroll-view {
  flex: 1;
  height: calc(100vh - 140px);
  overflow-x: hidden;
}

.sl-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 0;
  margin: 8px;
}
</style>
