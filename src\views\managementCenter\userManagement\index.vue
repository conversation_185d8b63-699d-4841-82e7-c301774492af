<template>
  <div class="table-box">
    <sl-page-header
      title="用户管理"
      title-line="用户管理提供了用户新增、用户编辑、授权角色等功能"
      :icon="{
        Vnode: Platform,
        color: '#0052D9',
        size: '50px',
      }"
    >
    </sl-page-header>
    <div class="btn op" style="margin-top: 8px">
      <el-button @click="handleCreateUser" type="primary"> 创建用户 </el-button>
    </div>
    <div class="filter-form-con">
      <sl-form
        class="filter-form"
        :class="{ collapsed: collapsed }"
        ref="formRef"
        :options="formOptions"
        v-model="formModel"
      >
      </sl-form>
    </div>
    <div class="table-layout">
      <DataList
        :query-params="queryParams"
        :role-list="roleList"
        :tenant-list="tenantList"
        @showDialog="showUserListDialog"
      ></DataList>
    </div>
    <!-- 创建用户弹窗 -->
    <SlDialog
      v-model="createUserDialogVisible"
      title="创建用户"
      width="600px"
      destroy-on-close
      @close="handleCreateUserClose"
      @confirm="handleCreateUserConfirm"
    >
      <sl-form
        ref="createUserFormRef"
        :options="createUserFormOptions"
        v-model="createUserFormModel"
      ></sl-form>
      <template #footer>
        <el-button @click="handleCreateUserClose">取消</el-button>
        <sl-button type="primary" :api-function="handleCreateUserConfirm">提交</sl-button>
      </template>
    </SlDialog>
  </div>
</template>

<script lang="tsx" setup>
import { reactive, ref } from 'vue'
import { Delete, Search, ArrowDown, ArrowUp } from '@element-plus/icons-vue'
import { Platform } from '@element-plus/icons-vue'
import DataList from './components/DataList.vue'
import ConditionFilter from '../../resourceCenter/conditionFilter.vue'
import { useGlobalDicStore } from '@/stores/modules/dic'
import { useUserHooks } from '../hooks/useUserHooks'
import { createUserApi } from '@/api/modules/managementCenter'
import SlMessage from '@/components/base/SlMessage'
import {
  validateAccount,
  validateTypeEmail,
  validatePassword,
  validatePhone,
  validateNameNoSpecialChars,
} from '@/utils/validate'
import { useTenant } from '@/views/corporateProducts​/hooks/useTenant'

const { tenantList: tenantListOptions } = useTenant()

const formRef = ref<any>(null)
const queryParams = ref<any>({})

const formModel = reactive({
  name: '',
  tenantType: '',
  tenantLevel: '',
  specName: '',
  orgName: '',
  departmentName: '',
  ownerName: '',
  createdTime: '',
  userCategory: 'OA',
})
function reset() {
  formRef.value!.resetFields()
  queryParams.value = { ...formModel }
}
function search() {
  queryParams.value = { ...formModel }
}

// 是否默认折叠搜索项
const collapsed = ref(true)

const globalDic = useGlobalDicStore()
const { getDic, getDicNumber } = globalDic
const { roleList, tenantList } = useUserHooks()

const formOptions = reactive([
  {
    style: 'padding: 0',
    groupItems: [
      {
        label: '用户账号',
        type: 'input',
        key: 'account',
        span: 8,
        disabled: true,
        hidden: false,
        defaultSelect: true,
      },
      {
        label: '用户姓名',
        type: 'input',
        key: 'username',
        span: 8,
        disabled: true,
        hidden: false,
        defaultSelect: true,
      },
      {
        span: 8,
        render() {
          return (
            <div style="display: flex;justify-content: flex-end;">
              <el-button type="primary" link>
                <ConditionFilter
                  formModel={formModel}
                  resourceList={formOptions[0].groupItems}
                ></ConditionFilter>
              </el-button>
              <el-button onClick={reset} icon={<Delete />}>
                重置
              </el-button>
              <el-button onClick={search} icon={<Search />} type="primary">
                搜索
              </el-button>
              <el-button
                type="primary"
                link
                class="search-isOpen"
                onClick={() => (collapsed.value = !collapsed.value)}
              >
                {collapsed.value ? '展开' : '折叠'}
                <el-icon class="el-icon--right">
                  {collapsed.value ? <ArrowDown /> : <ArrowUp />}
                </el-icon>
              </el-button>
            </div>
          )
        },
      },
      {
        label: '所属角色',
        type: 'select',
        key: 'roleId',
        options: roleList,
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '邮箱',
        type: 'input',
        key: 'email',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '手机号',
        type: 'input',
        key: 'mobilephone',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '所属租户',
        type: 'select',
        key: 'tenantId',
        options: tenantList,
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '用户类型',
        type: 'select',
        key: 'userCategory',
        options: getDic('userCategory'),
        span: 8,
        disabled: true,
        hidden: false,
        defaultSelect: true,
      },
      {
        label: '状态',
        type: 'select',
        key: 'status',
        options: getDicNumber('userStatus'),
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '创建人',
        type: 'input',
        key: 'createdByName',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '创建时间',
        type: 'date',
        key: 'createdTime',
        span: 8,
        disabled: false,
        hidden: true,
        props: {
          type: 'datetimerange',
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          rangeSeparator: '至',
          startPlaceholder: '开始时间',
          endPlaceholder: '结束时间',
        },
      },
    ],
  },
])

const dialogVisible = ref(false)
const tenantId = ref<number>(0)
const showUserListDialog = (item: any) => {
  tenantId.value = item.id
  dialogVisible.value = true
}

// 创建用户相关代码
// 创建用户
const createUserDialogVisible = ref(false)

// 创建用户表单模型
interface CreateUserFormModel {
  tenantIds: number[] | string[]
  userType: string
  account: string
  password: string
  confirmPassword: string
  username: string
  email: string
  mobilephone: string
  factoryName: string
}

const createUserFormModel = reactive<CreateUserFormModel>({
  tenantIds: [],
  userType: '',
  account: '',
  password: '',
  confirmPassword: '',
  username: '',
  email: '',
  mobilephone: '',
  factoryName: '',
})

// 账号类型选项
const userTypeOptions = [
  { label: '子账号', value: 'BILLING' },
  { label: '临时账号', value: 'TEMP' },
]

// 密码确认验证
const validateConfirmPassword = (rule: any, value: any, callback: any) => {
  if (!value) {
    callback(new Error('请再次输入密码'))
  } else if (value !== createUserFormModel.password) {
    callback(new Error('两次输入密码不一致'))
  } else {
    callback()
  }
}

// 创建用户表单配置
const createUserFormOptions = reactive([
  {
    groupName: '',
    groupItems: [
      {
        label: '所属租户',
        type: 'select',
        key: 'tenantIds',
        options: tenantListOptions,
        span: 24,
        props: {
          select: {
            multiple: true,
            collapseTags: true,
            collapseTagsTooltip: true,
          },
        },
        rules: [{ required: true, message: '请选择所属租户', trigger: ['blur', 'change'] }],
      },
      {
        label: '账号类型',
        type: 'select',
        key: 'userType',
        options: userTypeOptions,
        span: 24,
        rules: [{ required: true, message: '请选择账号类型', trigger: ['blur', 'change'] }],
      },
      {
        label: '用户账号',
        type: 'input',
        key: 'account',
        span: 24,
        props: {
          maxlength: 32,
          showWordLimit: true,
        },
        rules: [
          { required: true, message: '请输入用户账号', trigger: ['blur', 'change'] },
          { validator: validateAccount, trigger: ['blur', 'change'] },
        ],
      },
      {
        label: '用户密码',
        type: 'input',
        key: 'password',
        span: 24,
        props: {
          type: 'password',
          showPassword: true,
        },
        rules: [
          { required: true, message: '请输入用户密码', trigger: ['blur', 'change'] },
          { validator: validatePassword, trigger: ['blur', 'change'] },
        ],
      },
      {
        label: '确认密码',
        type: 'input',
        key: 'confirmPassword',
        span: 24,
        props: {
          type: 'password',
          showPassword: true,
        },
        rules: [
          { required: true, message: '请确认密码', trigger: ['blur', 'change'] },
          { validator: validateConfirmPassword, trigger: ['blur', 'change'] },
        ],
      },
      {
        label: '用户姓名',
        type: 'input',
        key: 'username',
        span: 24,
        props: {
          maxlength: 32,
          showWordLimit: true,
        },
        rules: [
          { required: true, message: '请输入用户姓名', trigger: ['blur', 'change'] },
          { validator: validateNameNoSpecialChars, trigger: ['blur', 'change'] },
        ],
      },
      {
        label: '用户邮箱',
        type: 'input',
        key: 'email',
        span: 24,
        rules: [
          { required: true, message: '请输入用户邮箱', trigger: ['blur', 'change'] },
          { validator: validateTypeEmail, trigger: ['blur', 'change'] },
        ],
      },
      {
        label: '手机号',
        type: 'input',
        key: 'mobilephone',
        span: 24,
        rules: [
          { required: true, message: '请输入手机号', trigger: ['blur', 'change'] },
          { validator: validatePhone, trigger: ['blur', 'change'] },
        ],
      },
      {
        label: '所属厂商',
        type: 'input',
        key: 'factoryName',
        span: 24,
        props: {
          maxlength: 32,
          showWordLimit: true,
        },
        rules: [{ required: true, message: '请输入所属厂商', trigger: ['blur', 'change'] }],
      },
    ],
  },
])

const handleCreateUser = () => {
  createUserDialogVisible.value = true
}

const createUserFormRef = ref()
const handleCreateUserConfirm = async () => {
  if (!(await createUserFormRef.value?.validate(() => true))) return
  const params = { ...createUserFormModel }

  // 加密密码，使用btoa简单加密
  params.password = btoa(params.password)

  // 安全地删除confirmPassword
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { confirmPassword, userType, tenantIds, ...baseParams } = params

  // 构建符合接口要求的参数
  const requestParams = {
    ...baseParams,
    // 将userType映射到accountType
    accountType: userType,
    // 构建租户数组
    tenants: tenantIds.map((id: number | string) => ({ tenantId: id })),
  }

  try {
    const res = await createUserApi(requestParams)
    if (res.code == 200) {
      SlMessage.success('创建用户成功')
      handleCreateUserClose()
      // 刷新表格
      queryParams.value = { ...formModel }
    } else {
      SlMessage.error(res.message || '创建用户失败')
    }
  } catch (error) {
    console.error('创建用户失败:', error)
    SlMessage.error('创建用户失败')
  }
}

const handleCreateUserClose = () => {
  createUserDialogVisible.value = false
  // 重置表单
  createUserFormRef.value?.resetFields()
}
</script>
<style lang="scss" scoped>
.sl-table-content {
  overflow: auto !important;
}
.sl-page-content {
  // 减去头部的标题高度 - 不减去会超出屏幕
  overflow-x: hidden;
  height: calc(100% - 90px);
}
.header-search-input {
  width: 350px;
}
.table-layout {
  margin: 2px 8px 8px 8px;
  flex: 1;
  overflow: hidden;
}
.btn.op {
  margin: 8px 8px 0 8px;
}
.filter-form-con {
  margin: 8px 8px 0 8px;
  background: #fff;
  border-radius: 8px;
}
.filter-form.collapsed {
  height: 50px;
}
.filter-form {
  height: 110px;
  overflow: hidden;
  transition: height 0.2s;
}
.operation-bar {
  margin: 16px 8px 0 8px;
  display: flex;
  justify-content: flex-start;
}
</style>
