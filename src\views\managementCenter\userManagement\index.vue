<template>
  <div class="table-box">
    <sl-page-header
      title="用户管理"
      title-line="用户管理提供了用户新增、用户编辑、授权角色等功能"
      :icon="{
        Vnode: Platform,
        color: '#0052D9',
        size: '50px',
      }"
    >
    </sl-page-header>
    <div class="filter-form-con">
      <sl-form
        class="filter-form"
        :class="{ collapsed: collapsed }"
        ref="formRef"
        :options="formOptions"
        v-model="formModel"
      >
      </sl-form>
    </div>
    <div class="table-layout">
      <DataList
        :query-params="queryParams"
        :role-list="roleList"
        :tenant-list="tenantList"
        @showDialog="showUserListDialog"
      ></DataList>
    </div>
  </div>
</template>

<script lang="tsx" setup>
import { reactive, ref } from 'vue'
import { Delete, Search, ArrowDown, ArrowUp } from '@element-plus/icons-vue'
import { Platform } from '@element-plus/icons-vue'
import DataList from './components/DataList.vue'
import ConditionFilter from '../../resourceCenter/conditionFilter.vue'
import { useGlobalDicStore } from '@/stores/modules/dic'
import { useUserHooks } from '../hooks/useUserHooks'

const formRef = ref<any>(null)
const queryParams = ref<any>({})

const formModel = reactive({
  name: '',
  tenantType: '',
  tenantLevel: '',
  specName: '',
  orgName: '',
  departmentName: '',
  ownerName: '',
  createdTime: '',
  userCategory: 'OA',
})
function reset() {
  formRef.value!.resetFields()
  queryParams.value = { ...formModel }
}
function search() {
  queryParams.value = { ...formModel }
}

// 是否默认折叠搜索项
const collapsed = ref(true)

const globalDic = useGlobalDicStore()
const { getDic, getDicNumber } = globalDic
const { roleList, tenantList } = useUserHooks()
const formOptions = reactive([
  {
    style: 'padding: 0',
    groupItems: [
      {
        label: '用户账号',
        type: 'input',
        key: 'account',
        span: 8,
        disabled: true,
        hidden: false,
        defaultSelect: true,
      },
      {
        label: '用户姓名',
        type: 'input',
        key: 'username',
        span: 8,
        disabled: true,
        hidden: false,
        defaultSelect: true,
      },
      {
        span: 8,
        render() {
          return (
            <div style="display: flex;justify-content: flex-end;">
              <el-button type="primary" link>
                <ConditionFilter
                  formModel={formModel}
                  resourceList={formOptions[0].groupItems}
                ></ConditionFilter>
              </el-button>
              <el-button onClick={reset} icon={<Delete />}>
                重置
              </el-button>
              <el-button onClick={search} icon={<Search />} type="primary">
                搜索
              </el-button>
              <el-button
                type="primary"
                link
                class="search-isOpen"
                onClick={() => (collapsed.value = !collapsed.value)}
              >
                {collapsed.value ? '展开' : '折叠'}
                <el-icon class="el-icon--right">
                  {collapsed.value ? <ArrowDown /> : <ArrowUp />}
                </el-icon>
              </el-button>
            </div>
          )
        },
      },
      {
        label: '所属角色',
        type: 'select',
        key: 'roleId',
        options: roleList,
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '邮箱',
        type: 'input',
        key: 'email',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '手机号',
        type: 'input',
        key: 'mobilephone',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '所属租户',
        type: 'select',
        key: 'tenantId',
        options: tenantList,
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '用户类型',
        type: 'select',
        key: 'userCategory',
        options: getDic('userCategory'),
        span: 8,
        disabled: true,
        hidden: false,
        defaultSelect: true,
      },
      {
        label: '状态',
        type: 'select',
        key: 'status',
        options: getDicNumber('userStatus'),
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '创建人',
        type: 'input',
        key: 'createdByName',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '创建时间',
        type: 'date',
        key: 'createdTime',
        span: 8,
        disabled: false,
        hidden: true,
        props: {
          type: 'datetimerange',
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          rangeSeparator: '至',
          startPlaceholder: '开始时间',
          endPlaceholder: '结束时间',
        },
      },
    ],
  },
])

const dialogVisible = ref(false)
const tenantId = ref<number>(0)
const showUserListDialog = (item: any) => {
  tenantId.value = item.id
  dialogVisible.value = true
}
</script>
<style lang="scss" scoped>
.sl-table-content {
  overflow: auto !important;
}
.sl-page-content {
  // 减去头部的标题高度 - 不减去会超出屏幕
  overflow-x: hidden;
  height: calc(100% - 90px);
}
.header-search-input {
  width: 350px;
}
.table-layout {
  margin: 2px 8px 8px 8px;
  flex: 1;
  overflow: hidden;
}
.btn.op {
  margin: 8px 8px 0 8px;
}
.filter-form-con {
  margin: 8px 8px 0 8px;
  background: #fff;
  border-radius: 8px;
}
.filter-form.collapsed {
  height: 50px;
}
.filter-form {
  height: 110px;
  overflow: hidden;
  transition: height 0.2s;
}
</style>
