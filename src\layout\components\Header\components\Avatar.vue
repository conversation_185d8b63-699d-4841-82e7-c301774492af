<template>
  <el-dropdown trigger="click">
    <div class="avatar ml10 pl10 pr10">
      <el-icon><User /></el-icon>
      {{ userStore.userInfo?.userName || '管理员' }}
    </div>
    <template #dropdown>
      <el-dropdown-menu>
        <el-dropdown-item divided @click="logout">
          <el-icon><SwitchButton /></el-icon>退出登录
        </el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>

<script setup lang="ts">
import { useUserStore } from '@/stores/modules/user'
import { ElMessageBox } from 'element-plus'
import SlMessage from '@/components/base/SlMessage'
import { User, SwitchButton } from '@element-plus/icons-vue'
const userStore = useUserStore()

// 退出登录
const logout = () => {
  ElMessageBox.confirm('您是否确认退出登录?', '温馨提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    // 1.执行退出登录接口
    // await logoutApi()

    // 2.清除 Token
    userStore.setToken('')

    // 3.重定向到登陆页
    window.location.href = '/login'
    SlMessage.success('退出登录成功！')
  })
}
</script>

<style scoped lang="scss">
.avatar {
  width: 100px;
  height: 35px;
  line-height: 35px;
  text-align: center;
  width: fit-content;
  cursor: pointer;
  background: rgba(72, 127, 239, 0.1);
  border-radius: 8px;
}
</style>
