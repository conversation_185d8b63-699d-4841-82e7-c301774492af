// useUserHooks.ts
import { ref, onMounted } from 'vue'
import { getSelectRoleListApi, getSelectTenantListApi } from '@/api/modules/managementCenter'

export const useUserHooks = () => {
  const roleList = ref<{ value: number; label: string }[]>([])
  const tenantList = ref<{ value: number; label: string }[]>([])

  const fetchRoleList = async () => {
    try {
      const { entity } = await getSelectRoleListApi()
      roleList.value = entity.map((i) => ({
        value: Number(i.id),
        label: i.name,
      }))
    } catch (error) {
      console.error('Failed to fetch role list:', error)
    }
  }

  const fetchTenantList = async () => {
    try {
      const { entity } = await getSelectTenantListApi()
      tenantList.value = entity.map((i) => ({
        value: Number(i.id),
        label: i.name,
      }))
    } catch (error) {
      console.error('Failed to fetch tenant list:', error)
    }
  }

  onMounted(() => {
    fetchRoleList()
    fetchTenantList()
  })

  return {
    roleList,
    tenantList,
  }
}
