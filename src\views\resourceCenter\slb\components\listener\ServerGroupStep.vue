<template>
  <div class="server-group-step">
    <sl-form
      ref="formRef"
      show-block-title
      :options="formOptions"
      :model-value="props.formData"
      label-width="140px"
      label-position="right"
    >
      <template #serverInfoModelListSlot>
        <div class="el-row">
          <el-button type="primary" @click="bindDialogVisible = true">添加云主机</el-button>
          <!-- 云主机弹窗 -->
          <EcsListDialog v-model:visible="bindDialogVisible" @handleConfirm="handleConfirm" />
        </div>
        <div class="ecs-list-wrapper" style="margin-top: 10px" v-if="ecsList.length > 0">
          <SlProTable ref="proTable" :data="ecsList" :columns="columns" :pagination="false">
            <template #port="scope">
              <div class="flex-center">
                <div>
                  <el-input v-model="scope.row.port" />
                </div>
                <el-icon @click="handleAddPort(scope.row)"><CirclePlus /></el-icon>
                <el-icon @click="handleDelete(scope.$index)"><Delete /></el-icon>
              </div>
            </template>
          </SlProTable>
        </div>
      </template>
    </sl-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import slForm from '@/components/form/SlForm.vue'
import type { FormInstance } from 'element-plus'
import EcsListDialog from './EcsListDialog.vue'
import type { ColumnProps } from '@/components/SlProTable/interface'
import { CirclePlus, Delete } from '@element-plus/icons-vue'
import { validatePort } from '@/utils/validate'
const props = defineProps({
  formData: {
    type: Object,
    required: true,
  },
})

const ecsList = computed(() => props.formData.serverInfoModelList)
// 表单引用
const formRef = ref<FormInstance>()

// 服务器组表单配置
const formOptions = reactive([
  {
    groupName: '服务器组配置',
    groupItems: [
      {
        label: '服务器组类型',
        type: 'select',
        key: 'serverGroupType',
        options: [
          { label: '虚拟服务器组', value: 0 },
          { label: '主备服务器组', value: 1 },
        ],
        rules: [{ required: true, message: '请选择服务器组类型', trigger: 'change' }],
        span: 24,
      },
      {
        label: '服务器组名称',
        type: 'input',
        key: 'serverGroupName',
        rules: [{ required: true, message: '请输入服务器组名称', trigger: 'blur' }],
        span: 24,
        props: {
          placeholder: '请输入服务器组名称',
        },
      },
      {
        label: '云主机',
        type: 'slot',
        slotName: 'serverInfoModelListSlot',
        key: 'serverInfoModelList',
        span: 24,
        props: {
          placeholder: '请输入服务器组名称',
        },
      },
      {
        label: '主备切换数量',
        type: 'inputNumber',
        key: 'groupPriorityGroup',
        rules: [{ required: true, message: '请输入主备切换数量', trigger: 'blur' }],
        hidden: computed(() => props.formData.serverGroupType === 0),
        span: 24,
        props: {
          min: 1,
          max: computed(() => Math.max(ecsList.value.length - 1, 1)),
          placeholder: '请输入主备切换数量',
        },
      },
    ],
  },
])

const emit = defineEmits<{
  (e: 'update:serverList', value: any): void
}>()
// 验证表单
const validate = async () => {
  let valid = false
  await formRef.value?.validate((isValid: boolean) => {
    valid = isValid
  })
  const portObj: Record<string, string> = {}
  let errorMsg = '请填写完整信息'
  ecsList.value.forEach((item: any) => {
    if (!item.port) {
      errorMsg = '请填写完成云主机端口'
      valid = false
    } else if (!validatePort(item.port)) {
      errorMsg = '端口范围为1-65535内的整数'
      valid = false
    } else {
      if (portObj[item.id] == item.port) {
        errorMsg = '相同云主机的端口不能重复'
        valid = false
      }
      portObj[item.id] = item.port
    }
  })
  if (!valid) {
    ElMessage.warning(errorMsg)
  }
  return valid
}

const columns = reactive<ColumnProps<any>[]>([
  { type: 'index', label: '序号', width: 55 },
  { prop: 'deviceName', label: '云主机名称', width: 150 },
  { prop: 'cloudPlatform', label: '所属云', width: 120 }, // 支持筛选过滤
  { prop: 'resourcePoolName', label: '资源池', width: 120 }, // 支持筛选过滤
  { prop: 'vpcName', label: 'VPC', width: 150 }, // 显示绑定的公网IP或占位符
  { prop: 'eip', label: '弹性公网IP', width: 150 }, // 显示绑定的公网IP或占位符
  { prop: 'ip', label: 'IP', width: 150 },
  { prop: 'deviceStatusCn', label: '状态', width: 100 }, // 运行中，已关机，异常
  { prop: 'port', label: '端口', width: 200, fixed: 'right' }, // 运行中，已关机，异常
])

const bindDialogVisible = ref(false)
const handleConfirm = (list: any) => {
  emit('update:serverList', [...ecsList.value, ...list])
}
const handleAddPort = (row: any) => {
  const newRow = JSON.parse(JSON.stringify(row))
  newRow.port = null
  emit('update:serverList', [...ecsList.value, newRow])
}
const handleDelete = (index: number) => {
  const newList = JSON.parse(JSON.stringify(ecsList.value))
  newList.splice(index, 1)
  emit('update:serverList', newList)
}

// 暴露方法给父组件
defineExpose({
  validate,
})
</script>

<style lang="scss" scoped>
.server-group-step {
  padding: 20px 0;
}

:deep(.el-form-item__content) {
  max-width: 400px;
}

:deep(.el-select) {
  width: 100%;
}

:deep(.el-form-item) {
  margin-bottom: 22px;
}

.flex-center {
  display: flex;
  align-items: center;
}

.el-icon {
  cursor: pointer;
  margin-left: 5px;
}
.ecs-list-wrapper {
  height: 300px;
  width: 70vw;
  max-width: 80vw;
}
</style>
