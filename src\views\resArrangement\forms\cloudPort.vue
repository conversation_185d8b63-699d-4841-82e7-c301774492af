<template>
  <sl-form
    class="goods-info-form"
    size="small"
    ref="slFormRef"
    label-position="top"
    :options="goodsInfoOptions"
    :model-value="goods"
  >
  </sl-form>
</template>
<script setup lang="ts">
import { reactive, ref } from 'vue'
import type { ICloudPortModel } from '../model'
import { useCloudPortModel } from '../model'
import slForm from '@/components/form/SlForm.vue'

const props = defineProps<{
  goods: ICloudPortModel
}>()

const formModel = reactive(Object.assign(useCloudPortModel(), props.goods))
const slFormRef = ref()

const goodsInfoOptions = reactive([
  {
    style: 'margin:0;padding:0;padding-top: 18px;',
    gutter: 40,
    groupItems: [
      {
        label: '云端口名称',
        type: 'input',
        key: 'instanceName',
        span: 24,
        rules: [{ required: true, message: '请输入云端口名称', trigger: 'blur' }],
        props: {
          maxlength: 50,
          showWordLimit: true,
          placeholder: '请输入',
        },
      },
      {
        label: 'VLAN ID',
        type: 'input',
        key: 'vlanId',
        span: 24,
        props: {
          placeholder: '请输入',
        },
        rules: [{ required: true, message: '请输入VLAN ID', trigger: 'blur' }],
      },
      {
        label: '本端地址',
        type: 'input',
        key: 'srcIp',
        span: 24,
        props: {
          placeholder: '示例:********/30,********/30',
        },
        rules: [{ required: true, message: '请输入本端地址', trigger: 'blur' }],
      },
      {
        label: 'CM2端地址',
        type: 'input',
        key: 'peerIp',
        span: 24,
        props: {
          placeholder: '示例:********/30,********/30',
        },
        rules: [{ required: true, message: '请输入CM2端地址', trigger: 'blur' }],
      },
      {
        label: 'BGP密钥',
        type: 'input',
        key: 'peerPassword',
        span: 24,
        props: {
          placeholder: '请输入',
        },
        rules: [{ required: true, message: '请输入BGP密钥', trigger: 'blur' }],
      },
    ],
  },
])

const validateForm = async () => {
  const res = await slFormRef.value.validate()
  return res
}
const submitForm = async () => {
  const goods = props.goods
  Object.assign(goods, formModel)
}
defineExpose({
  validateForm,
  submitForm,
})
</script>
<style scoped>
.goods-del-btn {
  position: absolute;
  top: -7px;
  right: -6px;
  z-index: 10;
  color: var(--el-color-danger);
  font-size: 1.2rem;
  cursor: pointer;
  display: none;
}
.goods-info-form:hover .goods-del-btn {
  display: block;
}

.evs-item {
  width: 100%;
  margin-bottom: 15px;
  .evs-item-content {
    width: 100%;
    display: flex;
    align-items: center;
  }
  &:last-child {
    margin-bottom: 0;
  }
  .evs-icons {
    display: flex;
    align-items: center;
    margin-left: 10px;
    .el-icon {
      cursor: pointer;
    }
  }
}
</style>
