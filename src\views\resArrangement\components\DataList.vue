<template>
  <SlProTable
    ref="proTable"
    highlight-current-row
    :columns="columns"
    style="min-height: 300px"
    :request-api="getTemplatePage"
    :init-param="queryParams"
    :current-change="currentChange"
    hidden-table-header
    row-key="id"
  >
  </SlProTable>
  <!-- 组合产品创建抽屉 -->
  <ProductDrawer
    v-model="drawerVisible"
    :current-row="currentRow"
    @close="handleCloseDrawer"
    @submit="handleSubmit"
  />
</template>

<script setup lang="tsx" name="DataList">
import { ref, computed, getCurrentInstance } from 'vue'
import type { ColumnProps, ProTableInstance } from '@/components/SlProTable/interface'
import { getTemplatePage, copyTemplate, updateTemplateRegion } from '@/api/modules/productCenter'
import { ElMessageBox, ElMessage } from 'element-plus'
import ProductDrawer from '@/views/resArrangement/components/ProductDrawer.vue'

const { queryParams } = defineProps<{
  queryParams: Record<string, any>
}>()

const emit = defineEmits(['currentChange'])
const parent = getCurrentInstance()?.parent

const currentChange = (currentRow: any, oldCurrentRow: any) => {
  emit('currentChange', currentRow, oldCurrentRow)
}

const drawerVisible = ref(false)
const currentRow = ref<any>(null)
const proTable = ref<ProTableInstance>()

// 表格列配置
const columns = computed(() => {
  const baseColumns: ColumnProps<any>[] = [
    { type: 'index', label: '编号', width: 55, fixed: 'left' },
    {
      prop: 'name',
      label: '模板名称',
      render: ({ row }) => {
        const hasReginCodes = row.reginCodeList && row.reginCodeList.length > 0
        return hasReginCodes ? (
          <el-button onClick={() => handleViewCombinationDetail(row)} type="primary" link>
            {row.name}
          </el-button>
        ) : (
          <span>{row.name}</span>
        )
      },
    },
    { prop: 'description', label: '描述' },
    { prop: 'createTime', label: '创建时间', width: 180 },
    { prop: 'creator', label: '创建人' },
    {
      prop: 'operation',
      label: '操作',
      width: 250,
      fixed: 'right',
      render: operationRender,
    },
  ]

  return baseColumns
})

// 操作列渲染
function operationRender({ row }: { row: any }) {
  const hasReginCodes = row.reginCodeList && row.reginCodeList.length > 0
  return (
    <div>
      {!hasReginCodes ? (
        <el-button onClick={() => handleViewDetail(row)} type="primary" link>
          模板编辑
        </el-button>
      ) : (
        <el-button onClick={() => handleViewTemplate(row, true)} type="primary" link>
          模板查看
        </el-button>
      )}
      <el-button onClick={() => handleCopy(row)} type="primary" link>
        复制
      </el-button>
      {!hasReginCodes && (
        <el-button onClick={() => handleCreate(row)} type="primary" link>
          组合产品创建
        </el-button>
      )}
      {hasReginCodes && (
        <el-button onClick={() => handleViewCombinationDetail(row)} type="primary" link>
          组合产品查看
        </el-button>
      )}
    </div>
  )
}

// 复制
const handleCopy = (row: any) => {
  ElMessageBox.confirm(`确定要复制模板"${row.name}"吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      await copyTemplate({ id: row.id })
      ElMessage.success('已提交复制')
      // 刷新表格
      proTable.value?.getTableList()
    })
    .catch(() => {
      // 取消删除
    })
}

// 组合产品创建
const handleCreate = (row: any) => {
  currentRow.value = row
  drawerVisible.value = true
}

// 关闭抽屉
const handleCloseDrawer = () => {
  drawerVisible.value = false
  setTimeout(() => {
    currentRow.value = null
  }, 500)
}

const handleSubmit = async (selectedResources: any[]) => {
  // 调用创建组合产品的接口
  await updateTemplateRegion({
    id: currentRow.value.id,
    reginCodeList: selectedResources.map((item) => item.code),
  })
  ElMessage.success('组合产品创建成功')
  proTable.value?.getTableList()
  drawerVisible.value = false
}

// 查看组合产品详情
const handleViewCombinationDetail = (row: any) => {
  // 打开组合产品详情抽屉，设置当前行数据
  currentRow.value = row
  drawerVisible.value = true
  // 组合产品详情的逻辑在ProductDrawer组件中处理
}

// 查看模板详情
const handleViewTemplate = (row: any, isReginCodes: boolean = false) => {
  // 构建查询参数
  const queryParams = new URLSearchParams()
  queryParams.append('id', row.id)
  queryParams.append('isView', 'true')
  queryParams.append('hasReginCodes', isReginCodes ? 'true' : 'false')

  // 使用window.open打开新页面
  window.open(`resArrangement?${queryParams.toString()}`, '_blank')
}

// 处理查看详情 - 模板编辑
const handleViewDetail = (row: any) => {
  // 调用父组件的编辑抽屉方法
  if (parent?.exposed?.handleOpenEditDrawer) {
    parent.exposed.handleOpenEditDrawer(row)
  }
}

defineExpose({
  proTable,
})
</script>

<style lang="scss" scoped>
.data-list {
  width: 100%;
}
</style>
