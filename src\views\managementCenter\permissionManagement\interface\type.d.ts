/**
 * @name 权限管理接口类型
 */
export interface PermissionType {
  id?: number // 权限 ID
  key?: string // 唯一标识
  name: string // 权限名称
  url: string // 权限路径
  operateCode?: number // 操作编码 菜单 1-修改 3-不做修改 权限 0-新增 1-修改 2-删除 3-不做修改
  menus?: PermissionType[]
}

// export interface RoleListType extends RoleType {
//     createUserName?: string // 创建人
//     createTime?: string // 创建时间，格式为 'YYYY-MM-DD HH:mm:ss'
//     [key: string]: any
// }
