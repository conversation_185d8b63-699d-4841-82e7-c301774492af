<template>
  <div class="table-box">
    <sl-page-header
      title="镜像"
      title-line="镜像（OS Image）是对操作系统完整安装数据的封装文件，包含系统核心文件、驱动程序、预装软件及配置信息"
      :icon="{
        Vnode: Platform,
        color: '#0052D9',
        size: '50px',
      }"
    >
    </sl-page-header>
    <div class="filter-form-con">
      <sl-form
        class="filter-form"
        :class="{ collapsed: collapsed }"
        ref="formRef"
        :options="formOptions"
        v-model="formModel"
      >
      </sl-form>
    </div>
    <div class="table-layout">
      <dataList ref="dataListRef" :query-params="queryParams"></dataList>
    </div>

    <!-- 上传对话框 -->
    <addDialog
      v-model="dialogVisible"
      :region-code="regionCode"
      @confirm="handleUploadConfirm"
      @dialogClose="dialogVisible = false"
    />
  </div>
</template>

<script lang="tsx" setup>
import { reactive, ref } from 'vue'
import { Delete, Search, ArrowDown, ArrowUp } from '@element-plus/icons-vue'
import { Platform } from '@element-plus/icons-vue'
import SlPageHeader from '@/components/SlPageHeader/index.vue'
import slForm from '@/components/form/SlForm.vue'
import dataList from './dataList.vue'
import addDialog from './addDialog.vue'
import ConditionFilter from '../conditionFilter.vue'

const formRef = ref<any>(null)
const queryParams = ref<any>({})

const formModel = reactive<any>({})
function reset() {
  formRef.value!.resetFields()
  queryParams.value = { ...formModel }
}
function search() {
  queryParams.value = { ...formModel }
}

// 是否默认折叠搜索项
const collapsed = ref(true)
const formOptions = reactive([
  {
    style: 'padding: 0',
    groupItems: [
      {
        label: '镜像名称',
        type: 'input',
        key: 'imageName',
        span: 8,
        disabled: false,
        hidden: false,
        defaultSelect: true,
      },
      {
        label: '上传时间',
        type: 'date',
        key: 'uploadTime',
        span: 8,
        disabled: false,
        hidden: false,
        defaultSelect: true,
        props: {
          type: 'datetimerange',
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          rangeSeparator: '至',
          startPlaceholder: '开始时间',
          endPlaceholder: '结束时间',
        },
      },
      {
        span: 8,
        render() {
          return (
            <div style="display: flex; justify-content: flex-end;">
              <el-button type="primary" link>
                <ConditionFilter
                  formModel={formModel}
                  resourceList={formOptions[0].groupItems}
                ></ConditionFilter>
              </el-button>
              <el-button onClick={reset} icon={<Delete />}>
                重置
              </el-button>
              <el-button onClick={search} icon={<Search />} type="primary">
                搜索
              </el-button>
              <el-button onClick={handleUpload} type="primary">
                上传镜像
              </el-button>
              <el-button
                type="primary"
                link
                class="search-isOpen"
                onClick={() => (collapsed.value = !collapsed.value)}
              >
                {collapsed.value ? '展开' : '折叠'}
                <el-icon class="el-icon--right">
                  {collapsed.value ? <ArrowDown /> : <ArrowUp />}
                </el-icon>
              </el-button>
            </div>
          )
        },
      },
      {
        label: '操作系统名称',
        type: 'input',
        key: 'osName',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '操作系统版本',
        type: 'input',
        key: 'osVersion',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '大小',
        type: 'input',
        key: 'size',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '格式',
        type: 'input',
        key: 'format',
        span: 8,
        disabled: false,
        hidden: true,
      },
    ],
  },
])

// 上传对话框
const dialogVisible = ref(false)
const regionCode = ref('default')

// 处理上传
const dataListRef = ref()
const handleUpload = () => {
  // 打开上传对话框
  dialogVisible.value = true
}

// 处理上传确认
const handleUploadConfirm = (data: any) => {
  console.log('上传确认', data)
  // 刷新表格数据
  dataListRef.value?.proTable?.getTableList()
}
</script>
<style lang="scss" scoped>
.sl-table-content {
  overflow: auto !important;
}
.sl-page-content {
  // 减去头部的标题高度 - 不减去会超出屏幕
  overflow-x: hidden;
  height: calc(100% - 90px);
}
.header-search-input {
  width: 350px;
}
.table-layout {
  margin: 2px 8px 8px 8px;
  flex: 1;
  overflow: hidden;
}
.btn.op {
  margin: 8px 8px 0 8px;
}
.filter-form-con {
  margin: 8px 8px 0 8px;
  background: #fff;
  border-radius: 8px;
}
.filter-form.collapsed {
  height: 50px;
}
.filter-form {
  height: 110px;
  overflow: hidden;
  transition: height 0.2s;
}
</style>
