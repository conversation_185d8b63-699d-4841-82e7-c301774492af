<template>
  <div class="table-box">
    <sl-page-header
      title="角色管理"
      title-line="角色管理提供了角色新增、角色编辑、权限调整等功能。"
      :icon="{
        Vnode: Platform,
        color: '#0052D9',
        size: '50px',
      }"
    ></sl-page-header>
    <div class="sl-page-content table-main">
      <SlProTable
        ref="proTable"
        :columns="columns"
        :request-api="getPermissionList"
        row-key="id"
        :is-show-pagination="false"
      >
        <template #menus="{ row }">
          <el-checkbox-group v-model="row.permissions">
            <el-checkbox
              v-for="item in row.menus"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-checkbox-group>
        </template>
      </SlProTable>
    </div>
    <div class="page-footer">
      <el-button @click="emit('close')">取 消</el-button>
      <el-button type="primary" @click="submit">保 存</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { ColumnProps, ProTableInstance } from '@/components/SlProTable/interface'
import { Platform } from '@element-plus/icons-vue'
import { reactive, ref, onMounted } from 'vue'
import SlMessage from '@/components/base/SlMessage'
import {
  selectMenusDetailApi,
  updateRolePermissionApi,
  getPermissionByRolesApi,
} from '@/api/modules/managementCenter'
const props = defineProps<{
  roleId: string | number
}>()

const emit = defineEmits<{
  (e: 'close'): void
}>()

const proTable = ref<ProTableInstance>()

const getPermissionList = () => {
  return selectMenusDetailApi({
    pageNum: 1,
    pageSize: 9999,
  })
}

onMounted(async () => {
  const { entity } = await getPermissionByRolesApi({
    roleIds: props.roleId,
  })
  setTimeout(() => {
    const tableData = proTable.value?.tableData || []

    entity.forEach((item) => {
      proTable.value?.element?.toggleRowSelection(item, true)
      const menu = tableData.find((i: any) => i.id == item.id)
      if (menu) {
        menu.permissions = item.menus.map((i: any) => i.id)
      }
    })
  }, 1000)
})

const columns = reactive<ColumnProps[]>([
  { type: 'index', label: '序号', width: 55 },
  { prop: 'name', label: '菜单权限', width: 230 },
  { type: 'selection', width: 55 },
  { prop: 'menus', label: '操作权限', align: 'left', minWidth: 350 },
])

const submit = async () => {
  const tableData = proTable.value?.tableData || []
  const selectedListIds = proTable.value?.selectedListIds || []

  // 确保 item.permissions 是一个数组，并且扁平化处理
  const ids = tableData.reduce<string[]>((acc, item) => {
    if (Array.isArray(item.permissions)) {
      return [...acc, ...item.permissions]
    }
    return acc
  }, [])
  await updateRolePermissionApi({
    roleId: props.roleId,
    menu: [...selectedListIds, ...ids].map((i) => {
      return { privilegeId: i, status: 1 }
    }),
  })
  SlMessage.success('保存成功')
  emit('close')
}
</script>

<style scoped>
.sl-page-content {
  overflow: hidden;
}
.page-footer {
  height: 48px;
  padding: 8px 20px;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
</style>
