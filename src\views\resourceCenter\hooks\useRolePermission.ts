import { computed } from 'vue'
import { useUserStore } from '@/stores/modules/user'

export const useRolePermission = () => {
  const userStore = useUserStore()

  // 判断当前用户是否具有特定角色
  const hasRole = (roleCode: string) => {
    // 确保用户信息和角色信息已加载
    if (!userStore.userInfo || !userStore.userInfo.oacRoles) {
      return false
    }

    return userStore.userInfo.oacRoles.some((role) => role.roleCode === roleCode)
  }

  // 判断是否是网络运维人员
  const isNetworkOperator = computed(() => {
    return hasRole('operation_group_network')
  })

  // 判断是否应该隐藏资源操作按钮
  const shouldHideResourceOperations = computed(() => {
    return isNetworkOperator.value
  })

  return {
    hasRole,
    isNetworkOperator,
    shouldHideResourceOperations,
  }
}
