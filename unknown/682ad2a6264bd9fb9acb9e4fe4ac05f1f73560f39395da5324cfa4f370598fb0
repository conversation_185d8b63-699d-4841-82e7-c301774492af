import { ElMessage, ElNotification } from 'element-plus'

export default function aDownload(url: string, name: string, notification = true) {
  const a = document.createElement('a')
  a.href = url
  a.download = name // 设置下载文件名
  a.style.display = 'none' // 隐藏a标签
  document.body.appendChild(a)

  try {
    a.click()
    if (notification) {
      ElNotification({
        title: '下载提示🔔',
        message: '下载已启动,请在浏览器右上角下载器中查看',
        type: 'success',
      })
    }
  } catch (error) {
    console.error('下载失败:', error)
    ElMessage.error('下载失败，请稍后重试')
  } finally {
    // 下载完成后移除a标签
    document.body.removeChild(a)
  }
}
