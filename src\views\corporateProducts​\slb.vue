<template>
  <div>
    <div>
      <sl-page-header title="负载均衡"></sl-page-header>
    </div>
    <sl-form
      class="corporate-products​"
      label-position="left"
      :options="formOptions"
      :model-value="formData"
      :label-width="120"
      ref="formRef"
    >
    </sl-form>

    <!-- 右下角价格面板 -->
    <div class="price-panel">
      <div class="con">
        <div class="price-section">
          <!-- <div class="price-info">
            <div class="discount-info">
              <span class="label">折扣价格:</span>
              <span class="discount-price">¥ 300.00</span>
              <span class="discount-badge">(3折)</span>
            </div>
            <div class="original-info">
              <span class="original-label">原价:</span>
              <span class="original-price">¥ 1000</span>
              <el-button link type="primary" class="detail-link">查看明细</el-button>
            </div>
          </div>
          <div class="warning-text">当前价格不代表真实产品，最终价格以BOSS侧为准！</div> -->
        </div>
        <div class="action-section">
          <el-button class="cancel-btn" @click="handleCancel">取消</el-button>
          <el-button type="primary" plain class="cart-btn" @click="handleAddToCart">
            加入清单
          </el-button>
          <el-button type="primary" class="order-btn" @click="handleDirectOrder">
            直接开通
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="tsx">
import { ref, nextTick, reactive, watch, markRaw } from 'vue'
import CustomRadio from './components/CustomRadio.vue'
import RegionSelect from './components/RegionSelect.vue'
import SlBlockTitle from '@/components/base/SlBlockTitle.vue'
import CustomTable from './components/CustomTable.vue'
import { useTenant } from './hooks/useTenant'
import {
  getEcsSpecList,
  corporateOrderApi,
  corporateOrderTemSaveApi,
} from '@/api/modules/resourecenter'
import { getAzListDic } from '@/api/modules/dic'
import { getCorporateVpcTreeApi } from '@/api/modules/approvalCenter'
import useGetTheParameters from './hooks/useGetTheParameters'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import SlForm from '@/components/form/SlForm.vue'
import { isValidAlphanumericWithUnderscore } from '../resourceCenter/utils'
import eventBus from '@/utils/eventBus'

const formData = ref<any>({
  isBindEip: false,
  tenant: null,
  paymentType: 'month',
  slb: [
    {
      vpc: null,
      subnet: null,
      specType: null,
      specValue: null,
      isBindEip: false,
      bandwidth: 0,
    },
  ],
  region: 'placeholder',
  domain: null,
  resourcePool: null,
  catalogueDomainCode: 'cloudst_group_moc',
  catalogueDomainName: '移动云',
})
const flavorMode = ref<any[]>([])
const getFlavorMode = async (regionId: string) => {
  const { entity } = await getEcsSpecList({ type: 'slb', pageSize: 10000, regionId })
  flavorMode.value = entity.records
}

const azOptions = ref<any[]>([])
async function getAzOptions(regionId: string) {
  const { entity } = await getAzListDic({ regionId: regionId })
  if (entity) {
    azOptions.value = entity
  }
}

// 租户资源池变化 重置 可用区 和网络
watch(
  () => formData.value.tenant,
  () => {
    formData.value.slb.forEach((item: any) => {
      item.az = null
      item.vpc = null
      item.subnet = null
    })
  },
)
watch(
  () => formData.value.resourcePool?.code,
  (newCode, oldCode) => {
    // 只有当code真正改变时才执行
    if (newCode !== oldCode) {
      // 重置可用区
      formData.value.slb.forEach((item: any) => {
        item.az = null
        item.vpc = null
        item.subnet = null
        item.specType = null
        item.specValue = null
      })
      azOptions.value = []
      // 如果选择了资源池，获取对应的可用区
      if (formData.value.resourcePool && formData.value.resourcePool.id) {
        getAzOptions(formData.value.resourcePool.id)
        getFlavorMode(formData.value.resourcePool.id)
      }
    }
  },
)

// 为每一行管理规格选项 - 使用 reactive 确保深度响应式
const rowSpecOptions = reactive<Record<string, any[]>>({})

// 计算属性来获取指定行的规格选项
// const getRowSpecOptions = computed(() => {
//   return (id: string) => rowSpecOptions[id] || []
// })

// 更新指定行的规格选项
const updateRowSpecOptions = async (rowId: string, specType: any, row: any) => {
  row.specValue = null
  if (specType && specType.children) {
    const newOptions = specType.children.map((item: any) => ({
      label: item.name,
      value: item,
    }))
    // 直接赋值给 reactive 对象
    rowSpecOptions[rowId] = newOptions

    // 确保 DOM 更新
    await nextTick()
  } else {
    rowSpecOptions[rowId] = []
  }
}
const vpcOptions = reactive<Record<string, any[]>>({})
const updateVpcOptions = async (az: any, row: any) => {
  row.subnet = null
  const { entity } = await getCorporateVpcTreeApi({
    tenantId: formData.value.tenant?.id,
    regionCode: formData.value.resourcePool?.code,
    azCode: row.az?.code,
  })
  vpcOptions[row.az!.code] = entity
}
const subnetOptions = reactive<Record<string, any[]>>({})
const updateSubnetOptions = async (vpc: any, row: any) => {
  row.vpcSubnet = null
  if (vpc) subnetOptions[vpc.id] = vpc.vpcSubnetOrderList
}

const { tenantList: tenantListOptions } = useTenant()
const router = useRouter()

const formOptions = ref([
  {
    style: 'margin:10px;padding:15px 10px;',
    gutter: 40,
    groupItems: [
      {
        label: '区域',
        type: 'component',
        key: 'region',
        span: 24,
        noAz: true,
        component: markRaw(RegionSelect),
        required: true,
        rules: [
          {
            validator: (rule: any, value: any, callback: any) => {
              if (!formData.value.domain) {
                callback(new Error('请选择云平台'))
              }
              if (!formData.value.resourcePool) {
                callback(new Error('请选择资源池'))
              }
              callback()
            },
            trigger: ['blur', 'change'],
          },
        ],
      },
      {
        label: '租户',
        type: 'select',
        key: 'tenant',
        span: 10,
        required: true,
        rules: [{ required: true, message: '请选择租户', trigger: ['blur', 'change'] }],
        options: tenantListOptions,
        props: {
          select: {
            valueKey: 'id',
          },
        },
      },
    ],
  },
  {
    style: 'margin:10px;padding:15px 10px;',
    gutter: 40,
    groupItems: [
      {
        label: '付费类型',
        type: 'component',
        key: 'paymentType',
        span: 24,
        required: true,
        options: [
          {
            label: '按日付费',
            value: 'day',
          },
          {
            label: '按月付费',
            value: 'month',
          },
          {
            label: '按年付费',
            value: 'year',
          },
        ],
        component: markRaw(CustomRadio),
      },
    ],
  },
  {
    style: 'margin:10px;padding:15px 10px;',
    gutter: 40,
    groupItems: [
      {
        label: '负载均衡',
        type: 'component',
        key: 'slb',
        span: 24,
        component: markRaw(CustomTable),
        columns: [
          {
            prop: 'name',
            label: '名称',
            width: 150,
            type: 'input',
            props: {
              clearable: true,
            },
            placeholder: '请输入名称',
          },
          {
            prop: 'az',
            label: '可用区',
            width: 150,
            type: 'select',
            placeholder: '请选择可用区',
            render: ({ row }: { row: any }) => {
              return (
                <el-select
                  clearable
                  onChange={(value: any) => updateVpcOptions(value, row)}
                  v-model={row.az}
                  placeholder="请选择可用区"
                  value-key="id"
                >
                  {azOptions.value.map((item: any) => (
                    <el-option label={item.name} value={item} />
                  ))}
                </el-select>
              )
            },
          },
          {
            prop: 'spec',
            label: '规格',
            width: 220,
            render: ({ row }: { row: any }) => {
              return (
                <div style="display: flex; gap: 10px">
                  <el-select
                    v-model={row.specType}
                    placeholder="请选择类型"
                    clearable
                    onChange={(value: any) => updateRowSpecOptions(value.label, value.value, row)}
                    value-key="id"
                  >
                    {flavorMode.value.map((item) => (
                      <el-option label={item.name} value={item} />
                    ))}
                  </el-select>
                  {/* <el-select clearable v-model={row.specValue} placeholder="请选择规格">
                    {getRowSpecOptions.value(row.specType?.label).map((item: any) => (
                      <el-option label={item.label} value={item} value-key="name" />
                    ))}
                  </el-select> */}
                </div>
              )
            },
          },
          {
            prop: 'isBindEip',
            label: '是否绑定公网IP',
            width: 150,
            render: ({ row }: { row: any }) => {
              return (
                <div style="display: flex; gap: 10px">
                  <el-checkbox
                    onChange={(value: any) => (row.bandwidth = value ? 5 : 0)}
                    v-model={row.isBindEip}
                  />
                  <el-input-number
                    disabled={!row.isBindEip}
                    min={0}
                    size="small"
                    v-model={row.bandwidth}
                  />
                </div>
              )
            },
          },
          {
            prop: 'vpc',
            label: 'vpc信息',
            width: 220,
            render: ({ row }: { row: any }) => {
              return (
                <div style="display: flex; gap: 10px">
                  <el-select
                    onChange={(value: any) => updateSubnetOptions(value, row)}
                    v-model={row.vpc}
                    clearable
                    placeholder="请选择VPC"
                    value-key="id"
                  >
                    {vpcOptions[row.az?.code]?.map((item: any) => (
                      <el-option label={item.vpcName} value={item} />
                    ))}
                  </el-select>
                  <el-select
                    clearable
                    v-model={row.vpcSubnet}
                    placeholder="请选择子网"
                    value-key="id"
                  >
                    {row.vpc?.id &&
                      subnetOptions[row.vpc!.id]?.map((item: any) => (
                        <el-option label={item.subnetName} value={item} />
                      ))}
                  </el-select>
                </div>
              )
            },
          },
          {
            prop: 'action',
            label: '操作',
            width: 60,
            type: 'action',
            action: 'delete',
          },
        ],
        canAdd: true,
        maxRows: 10,
        addButtonText: '添加负载均衡',
        emptyText: '暂无负载均衡配置',
        showEmptyState: true,
        defaultRow: {
          vpc: null,
          subnet: null,
          specType: null,
          specValue: null,
          isBindEip: false,
          bandwidth: 5,
        },
      },
    ],
  },
  {
    style: 'margin:10px;padding:15px 10px;',
    gutter: 40,
    groupItems: [
      {
        span: 24,
        render() {
          return (
            <SlBlockTitle size={16} style="margin-bottom:20px">
              配置概要
            </SlBlockTitle>
          )
        },
      },
      {
        label: '区域',
        type: 'text',
        getter: (form: any) =>
          form.resourcePool?.name ? `${form.domain?.name} - ${form.resourcePool.name}` : '',
        span: 8,
      },
      {
        label: '所属租户',
        type: 'text',
        getter: (form: any) => form.tenant?.name || '',
        span: 8,
      },
      {
        label: '付费类型',
        type: 'text',
        getter: (form: any) =>
          form.paymentType === 'day'
            ? '按日付费'
            : form.paymentType === 'month'
              ? '按月付费'
              : '按年付费',
        span: 8,
      },
      {
        label: '负载均衡',
        type: 'text',
        getter: (form: any) =>
          form.slb?.map((item: any) => {
            return `${item.name ?? ''} / ${item.az?.name ?? ''} / ${item.specType?.name ?? ''} / ${item.vpc?.vpcName ?? ''} / ${item.vpcSubnet?.subnetName ?? ''} / ${item.isBindEip ? '是' : '否'} / ${item.bandwidth}M`
          }),
        span: 8,
      },
    ],
  },
])

const formRef = ref<InstanceType<typeof SlForm>>()

// 处理取消操作
const handleCancel = () => {
  router.go(-1)
  eventBus.emit('corporateShoppingList:updateCount')
}

// 处理加入清单操作
const handleAddToCart = async () => {
  try {
    // 1. 校验表单数据
    const valid = await formRef.value?.validate()
    if (!valid) return

    // 验证每个负载均衡配置
    for (let i = 0; i < formData.value.slb.length; i++) {
      const slb = formData.value.slb[i]
      if (!slb.name) {
        ElMessage.error(`第${i + 1}个负载均衡请输入名称`)
        return
      }
      if (!isValidAlphanumericWithUnderscore(slb.name)) {
        ElMessage.error(`第${i + 1}个负载均衡名称不符合规范`)
        return
      }
      if (!slb.az) {
        ElMessage.error(`第${i + 1}个负载均衡请选择可用区`)
        return
      }
      if (!slb.specType) {
        ElMessage.error(`第${i + 1}个负载均衡请选择规格`)
        return
      }
    }

    // 2. 构建参数
    const params = useGetTheParameters(formData.value, 'slb', false)

    // 3. 调用API
    await corporateOrderTemSaveApi(params)
    ElMessage.success('加入清单成功')
    handleCancel()
  } catch (error: any) {
    console.error(error)
  }
}

// 处理直接开通操作
const handleDirectOrder = async () => {
  try {
    // 1. 校验表单数据
    const valid = await formRef.value?.validate()
    if (!valid) return

    // 验证每个负载均衡配置
    for (let i = 0; i < formData.value.slb.length; i++) {
      const slb = formData.value.slb[i]
      if (!slb.name) {
        ElMessage.error(`第${i + 1}个负载均衡请输入名称`)
        return
      }
      if (!isValidAlphanumericWithUnderscore(slb.name)) {
        ElMessage.error(`第${i + 1}个负载均衡名称不符合规范`)
        return
      }
      if (!slb.az) {
        ElMessage.error(`第${i + 1}个负载均衡请选择可用区`)
        return
      }
      if (!slb.specType) {
        ElMessage.error(`第${i + 1}个负载均衡请选择规格`)
        return
      }
    }

    // 2. 构建参数
    const params = useGetTheParameters(formData.value, 'slb')

    // 3. 调用API
    await corporateOrderApi(params)
    ElMessage.success('发起开通成功')
    handleCancel()
  } catch (error: any) {
    console.error(error)
  }
}
</script>

<style scoped>
/* 通用的form label加粗样式 */
.corporate-products​ :deep(.el-form-item__label) {
  font-weight: bold;
}
.corporate-products​ :deep(.table-main) {
  border: 1px solid #e4e7ed;
  box-shadow: none;
}

.eip-config :deep(.el-checkbox) {
  font-size: 14px;
}

.eip-config :deep(.el-checkbox__label) {
  font-weight: normal;
  color: #606266;
}

/* 展开按钮容器样式 */
.expand-container {
  display: flex;
  align-items: center;
  position: relative;
}
/* 图标样式 */
.expand-icon {
  transition: transform 0.3s ease;
  font-size: 14px;
}

.expand-icon.rotated {
  transform: rotate(180deg);
}

/* 价格面板样式 */
.price-panel {
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  background: #ffffff;
  border-top: 1px solid #e4e7ed;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  padding: 0 10px;

  .con {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.price-section {
  padding: 16px;
  border-bottom: 1px solid #f0f2f5;
}

.price-info {
  display: block;
}

.discount-info {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.label {
  font-size: 14px;
  color: #606266;
  margin-right: 8px;
}

.discount-price {
  font-size: 24px;
  font-weight: bold;
  color: #ff6b35;
  margin-right: 8px;
}

.discount-badge {
  font-size: 12px;
  color: #ff6b35;
  background: #fff2e8;
  padding: 2px 6px;
  border-radius: 4px;
}

.original-info {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.original-label {
  font-size: 12px;
  color: #909399;
  margin-right: 8px;
}

.original-price {
  font-size: 12px;
  color: #909399;
  text-decoration: line-through;
  margin-right: 8px;
}

.detail-link {
  font-size: 12px;
  padding: 0;
  height: auto;
}

.warning-text {
  font-size: 11px;
  color: #f56c6c;
  line-height: 1.4;
}

.action-section {
  padding: 12px 16px;
  display: flex;
  gap: 8px;
  align-self: end;
}

.cancel-btn {
  flex: 1;
  height: 36px;
  font-size: 14px;
  color: #606266;
  border-color: #dcdfe6;
}

.cart-btn {
  flex: 1;
  height: 36px;
  font-size: 14px;
}

.order-btn {
  flex: 1;
  height: 36px;
  font-size: 14px;
}
</style>
