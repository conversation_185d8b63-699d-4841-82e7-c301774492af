<template>
  <div class="table-box">
    <sl-page-header
      title="NAT网关详情"
      :icon="{
        Vnode: Platform,
        color: '#0052D9',
        size: '20px',
      }"
      :show-back="true"
      :back="{
        title: '返回列表',
        function: handleGoBack,
      }"
    >
    </sl-page-header>

    <el-scrollbar wrap-class="nat-detail-scroll-view" class="scroll-view">
      <!-- Tabs组件 -->
      <sl-base-tabs :show-count="false" :tabs="tabs" v-model="activeTab"></sl-base-tabs>

      <div class="sl-page-content">
        <!-- 详情信息 -->
        <div v-show="activeTab === 'basic'" class="tab-content">
          <NatBasicInfo ref="natBasicInfoRef" :id="id"></NatBasicInfo>
        </div>

        <!-- 规则列表 -->
        <div v-show="activeTab === 'rules'" class="tab-content">
          <RuleList
            ref="ruleDataListRef"
            :order-id="orderId"
            :query-params="queryParams"
          ></RuleList>
        </div>
      </div>
    </el-scrollbar>
  </div>
</template>

<script setup lang="tsx">
import { ref, computed } from 'vue'
import { Platform } from '@element-plus/icons-vue'
import SlPageHeader from '@/components/SlPageHeader/index.vue'
import SlBaseTabs from '@/components/base/SlBaseTabs.vue'
import { useRouter, useRoute } from 'vue-router'
import NatBasicInfo from '@/views/resourceCenter/nat/components/NatBasicInfo.vue'
import RuleList from '@/views/resourceCenter/nat/ruleList.vue'
import { useRolePermission } from '../hooks/useRolePermission'

const { isNetworkOperator } = useRolePermission()

const router = useRouter()
const route = useRoute()

// 获取资源详情ID和是否有eip标志
const id = ref<string>((route.query.id as string) || '')
const orderId = ref<string>((route.query.orderId as string) || '')
const hasEip = ref<boolean>(route.query.hasEip === 'true')

// 查询参数
const queryParams = ref<any>({
  goodsOrderId: orderId.value,
  id: id.value,
})

// 定义标签页 - 根据是否有eip决定是否显示规则列表
const tabs = computed(() => {
  // 始终显示详情信息标签
  const baseTabs = [{ name: 'basic', label: '详情信息' }]

  // 只有当具有eip且不是网络运维人员时，才显示规则列表标签
  if (hasEip.value && !isNetworkOperator.value) {
    baseTabs.push({ name: 'rules', label: 'NAT规则列表' })
  }

  return baseTabs
})

// 定义激活的标签页
const activeTab = ref('basic')

// 组件引用
const natBasicInfoRef = ref()
const ruleDataListRef = ref()

// 返回上一页
const handleGoBack = () => {
  router.push({
    path: '/natList',
  })
}
</script>

<style lang="scss" scoped>
.table-box {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.scroll-view {
  flex: 1;
  height: calc(100vh - 140px);
  overflow-x: hidden;
}

.sl-page-content {
  padding: 0 16px 16px;
}

.tab-content {
  margin-top: 16px;
}
</style>
