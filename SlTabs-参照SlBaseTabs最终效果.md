# SlTabs 参照 SlBaseTabs 最终效果

## 实现的视觉效果

完全参照 SlBaseTabs 的设计，实现了一致的视觉效果：

### 选中状态样式
- 🔵 **蓝色文字**: 使用主题色 `var(--el-color-primary)`
- ⚪ **白色背景**: 选中标签有白色背景
- 🔄 **上方圆角**: `border-radius: 8px 8px 0 0`
- 📝 **字体**: `font-weight: 400`
- 🌟 **圆角过渡**: 与相邻标签的平滑圆角过渡效果

### 未选中状态样式
- 🔘 **灰色文字**: `#909399` 颜色
- 🔍 **透明背景**: 无背景色
- 🖱️ **悬停效果**: 鼠标悬停时变为蓝色

### 圆角过渡效果
- 🔄 **左侧过渡**: 使用 `radial-gradient` 实现选中标签左侧的圆角过渡
- 🔄 **右侧过渡**: 相邻标签的圆角过渡效果
- 🎨 **背景层**: 使用 `::before` 和 `::after` 伪元素实现复杂的背景效果

### 数量标签样式
- 🏷️ **背景**: 半透明蓝色 `rgba(72, 127, 239, 0.1)`
- 📝 **文字**: 主题色
- 📏 **尺寸**: 12px 字体，2px 4px 内边距
- 🔄 **圆角**: 2px 圆角
- 📍 **间距**: 标题右侧 2px 间距

### 内容区域
- 🎨 **背景**: 浅灰色 `#f2f3f5`
- 📏 **最小高度**: 200px
- 🔗 **连接**: 与选中标签无缝连接

## 与 SlBaseTabs 的一致性

### 完全一致的样式
```scss
// 选中状态 - 与 SlBaseTabs 完全一致
&.is-active {
  color: var(--el-color-primary);
  font-weight: 400;
  background: #fff;
  border-radius: 8px 8px 0 0;
}

// 数量标签 - 与 SlBaseTabs 完全一致
.tab-count {
  background: rgba(72, 127, 239, 0.1);
  font-size: 12px;
  padding: 2px 4px;
  border-radius: 2px;
  margin-left: 2px;
  color: var(--el-color-primary);
}

// 内容区域 - 与 SlBaseTabs 完全一致
.el-tabs__content {
  background: #f2f3f5;
}
```

### 圆角过渡效果
```scss
// 左侧圆角过渡
&::after {
  background: radial-gradient(circle at top right, transparent 8px, #f2f3f5 8px);
}

// 相邻元素圆角效果
&:has(+ .is-active)::before {
  border-bottom-right-radius: 12px;
}

&.is-active + &::before {
  border-bottom-left-radius: 12px;
}
```

## 保留的 Element Plus 功能

### 完整功能支持
- ✅ **所有原生 API**: `el-tabs` 和 `el-tab-pane` 的所有功能
- ✅ **事件系统**: 完整的事件支持
- ✅ **键盘导航**: 无障碍访问
- ✅ **动画效果**: 原生的切换动画
- ✅ **响应式**: 自适应布局

### 增强功能
- ✅ **数量显示**: `showCount` 属性
- ✅ **事件扩展**: `tabChange` 事件
- ✅ **类型安全**: 完整的 TypeScript 支持

## 使用示例

### 基础用法
```vue
<template>
  <sl-tabs v-model="activeTab" :tabs="tabs">
    <el-tab-pane name="pending">
      <div>待审批内容</div>
    </el-tab-pane>
    <el-tab-pane name="approved">
      <div>已审批内容</div>
    </el-tab-pane>
    <el-tab-pane name="rejected">
      <div>驳回内容</div>
    </el-tab-pane>
  </sl-tabs>
</template>

<script setup>
const activeTab = ref('approved')

const tabs = [
  { name: 'pending', label: '待审批' },
  { name: 'approved', label: '已审批' },
  { name: 'rejected', label: '驳回' },
]
</script>
```

### 带数量显示
```vue
<sl-tabs v-model="activeTab" :tabs="tabsWithCount" show-count>
  <!-- 会显示类似 "已审批 23" 的效果 -->
</sl-tabs>

<script setup>
const tabsWithCount = [
  { name: 'pending', label: '待审批', count: 15 },
  { name: 'approved', label: '已审批', count: 128 },
  { name: 'rejected', label: '驳回', count: 3 },
]
</script>
```

## 技术特点

### 样式穿透技术
- 使用 `:deep()` 重写 Element Plus 样式
- 保持组件功能完整性
- 实现复杂的视觉效果

### 现代 CSS 特性
- `:has()` 选择器实现相邻元素联动
- `radial-gradient` 实现圆角过渡
- CSS 自定义属性支持主题色

### Vue 3 最佳实践
- Composition API
- TypeScript 类型安全
- 响应式数据绑定

## 总结

重构后的 SlTabs 组件完美复现了 SlBaseTabs 的视觉效果：

1. **视觉一致**: 与 SlBaseTabs 完全相同的选中样式
2. **功能完整**: 保留 Element Plus 的所有功能
3. **体验优秀**: 圆角过渡效果提供优秀的视觉体验
4. **易于使用**: 简单的 API，与现有代码兼容

现在您可以在项目中使用这个组件，获得与 SlBaseTabs 一致的视觉效果，同时享受 Element Plus 的强大功能！
