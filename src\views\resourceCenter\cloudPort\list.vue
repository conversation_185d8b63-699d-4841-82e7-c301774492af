<template>
  <div class="table-box">
    <sl-page-header
      title="云端口"
      title-line="云端口（Cloud Port）实现网络跨云域互通，提升多云环境下的资源协同效率。"
      :icon="{
        Vnode: Platform,
        color: '#0052D9',
        size: '50px',
      }"
    >
    </sl-page-header>
    <div class="btn op" style="margin-top: 8px" v-if="!shouldHideResourceOperations">
      <el-button @click="handleToCreate" type="primary"> 创建云端口 </el-button>
    </div>
    <div class="filter-form-con">
      <sl-form
        class="filter-form"
        :class="{ collapsed: collapsed }"
        ref="formRef"
        :options="formOptions"
        v-model="formModel"
      >
      </sl-form>
    </div>
    <div class="table-layout">
      <DataList
        ref="dataListRef"
        :query-params="queryParams"
        :hide-operations="shouldHideResourceOperations"
      ></DataList>
    </div>
  </div>
</template>

<script lang="tsx" setup>
import { reactive, ref } from 'vue'
import { Delete, Search } from '@element-plus/icons-vue'
import { Platform } from '@element-plus/icons-vue'
import SlPageHeader from '@/components/SlPageHeader/index.vue'
import slForm from '@/components/form/SlForm.vue'
import DataList from './components/DataList.vue'
import useBusiSystemOptions from '../hooks/useBusiSystemOptions'
import { useRouter } from 'vue-router'
import { useRolePermission } from '../hooks/useRolePermission'

const router = useRouter()
const { busiSystemOptions } = useBusiSystemOptions()
const { shouldHideResourceOperations } = useRolePermission()
const formRef = ref<any>(null)
const queryParams = ref<any>({ type: 'virtualNic' })

const formModel = reactive({})
function reset() {
  formRef.value!.resetFields()
  queryParams.value = { ...formModel, type: 'virtualNic' }
}
function search() {
  queryParams.value = { ...formModel, type: 'virtualNic' }
}
// 是否默认折叠搜索项
const collapsed = ref(true)
const formOptions = reactive([
  {
    style: 'padding: 0',
    gutter: 20,
    groupItems: [
      {
        label: '云端口名称',
        type: 'input',
        key: 'cloudPortName',
        span: 8,
        disabled: true,
        hidden: false,
        defaultSelect: true,
      },
      {
        label: '业务系统',
        type: 'select',
        key: 'businessSystemId',
        span: 8,
        options: busiSystemOptions,
        disabled: true,
        hidden: false,
        defaultSelect: true,
      },
      {
        span: 8,
        render() {
          return (
            <div style="display: flex;justify-content: flex-end;">
              <el-button type="primary" link></el-button>
              <el-button onClick={reset} icon={<Delete />}>
                重置
              </el-button>
              <el-button onClick={search} icon={<Search />} type="primary">
                搜索
              </el-button>
            </div>
          )
        },
      },
    ],
  },
])

// 跳转到创建虚拟网卡页面
const handleToCreate = () => {
  router.push({
    path: '/cloudPortCreate',
  })
}
</script>
<style lang="scss" scoped>
.sl-table-content {
  overflow: auto !important;
}
.sl-page-content {
  // 减去头部的标题高度 - 不减去会超出屏幕
  overflow-x: hidden;
  height: calc(100% - 90px);
}
.header-search-input {
  width: 350px;
}
.table-layout {
  margin: 2px 8px 8px 8px;
  flex: 1;
  overflow: hidden;
}
.btn.op {
  margin: 8px 8px 0 8px;
}
.filter-form-con {
  margin: 8px 8px 0 8px;
  background: #fff;
  border-radius: 8px;
}
.filter-form.collapsed {
  height: 50px;
}
.filter-form {
  height: 110px;
  overflow: hidden;
  transition: height 0.2s;
}
</style>
