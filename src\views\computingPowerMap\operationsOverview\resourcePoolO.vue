<template>
  <div class="f-r f-a-c fz-w resourcePoolTitle">
    <!-- <el-icon :size="20" color="#409efc">
          <ArrowLeftBold />
        </el-icon> -->
    <el-icon :size="18" color="#759ff3" class="m-r-10 c-p" @click="closeMethod">
      <ArrowLeftBold />
    </el-icon>

    <p class="fz-20 fz-ls-1">当前所选资源池</p>
  </div>
  <div class="f-d-c f-a-c w100 resourcePoolBox">
    <!-- <el-button class="resourcePoolOBtn m-b-12 selectCheck" color="#759ff3" size="large" plain
      :autofocus="true">杭州-租户域</el-button> -->
    <div
      :title="item.name"
      class="resourcePoolOBtn m-b-12"
      v-for="(item, index) in regionLists_"
      :key="index"
    >
      <span class="resourcePoolOBtn_">
        {{ item.name }}
      </span>
    </div>
    <div v-if="regionLists_.length == 0" class="resourcePoolOBtn m-b-12">
      <span class="resourcePoolOBtn_"> 全部资源池 </span>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ArrowLeftBold } from '@element-plus/icons-vue'
import { ref, watch, onMounted } from 'vue'
const props = defineProps({
  cloudPlatform: {
    type: Array,
    default: () => [],
  },
  regionLists: {
    type: Array,
    default: () => [],
  },
  drawer: {
    type: Boolean,
    default: false,
  },
})
onMounted(() => {
  regionLists_.value = props.regionLists
})

const emit = defineEmits<{
  (e: 'isClose', payload: boolean): void
}>()
const closeMethod = () => {
  emit('isClose', false)
}

let regionLists_ = ref<any[]>([])
watch(
  () => props.drawer,
  (newVal) => {
    if (newVal) {
      // if (props.prosCloudPlatformList) {
      //   regionLists_.value = regionfun(props.prosCloudPlatformList, true)
      // } else if (props.prosCloudPlatformList && props.prosCloudPlatformList.length == 0) {
      //   regionLists_.value = []
      // } else {
      //   regionLists_.value = regionfun(props.regionLists, false)
      // }
      if (props.regionLists) {
        regionLists_.value = props.regionLists
      } else {
        regionLists_.value = []
      }
    }
  },
)
</script>
<style scoped lang="scss">
.resourcePoolTitle {
  width: 100%;
  position: fixed;
  top: 0px;
}
.resourcePoolBox {
  margin-top: 40px;
  height: calc(100vh - 80px);
  overflow-y: auto !important;
}
.resourcePoolOBtn {
  width: 200px;
  padding: 0px 14px;
  height: 38px;
  line-height: 38px;
  font-size: 14px;
  margin-left: 0 !important;
  border-radius: 4px !important;
  background-color: #fff !important;
  color: #759ff3;
  border: 1px solid #cedefd;
  border-radius: 6px;
  .resourcePoolOBtn_ {
    display: block;
    width: 100%;
    text-align: center;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
</style>
