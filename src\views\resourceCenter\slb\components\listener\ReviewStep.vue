<template>
  <div class="review-step">
    <div class="review-content">
      <!-- 协议监听部分 -->
      <div class="review-section">
        <div class="section-header">
          <h3 class="section-title">协议监听</h3>
          <el-button type="primary" link class="edit-btn" @click="emit('onPrev', 0)" v-if="!isView">
            修改
          </el-button>
        </div>
        <div class="items">
          <div class="item">选择负载均衡协议: {{ getProtocolText(formData.protocol) }}</div>
          <div class="item">监听名称: {{ formData.listenerName }}</div>
          <div class="item">监听端口: {{ formData.port }}</div>
          <div class="item" v-if="formData.protocol === 'https'">
            服务器证书: {{ formData.serverCertificate || '无' }}
          </div>
          <div class="item" v-if="formData.protocol === 'https'">
            CA证书: {{ formData.caCertificate || '无' }}
          </div>
          <div class="item">调度算法: {{ getLbAlgorithmText(formData.lbAlgorithm) }}</div>
          <div class="item">会话保持: {{ formData.sessionPersistence ? '开启' : '关闭' }}</div>
          <div class="item" v-if="formData.sessionPersistence">
            cookie处理方式: {{ getCookieHandlingText(formData.cookieHandling) }}
          </div>
        </div>
      </div>

      <!-- 健康检查部分 -->
      <div class="review-section">
        <div class="section-header">
          <h3 class="section-title">健康检查</h3>
          <el-button type="primary" link class="edit-btn" @click="emit('onPrev', 1)" v-if="!isView">
            修改
          </el-button>
        </div>
        <div class="items">
          <div class="item">健康检查协议: {{ getProtocolText(formData.healthCheckProtocol) }}</div>
          <div class="item">健康检查间隔时间: {{ formData.healthCheckInterval }}秒</div>
          <div class="item">超时时间: {{ formData.timeout }}秒</div>
          <div class="item">尝试次数: {{ formData.maxRetries || 3 }}次</div>
          <div class="item" v-if="formData.healthCheckProtocol === 'http'">
            健康检查URL: {{ formData.healthCheckUrl }}
          </div>
          <div class="item" v-if="formData.healthCheckProtocol === 'http'">
            检查内容: {{ formData.healthCheckContent }}
          </div>
        </div>
      </div>

      <!-- 服务器组部分 -->
      <div class="review-section">
        <div class="section-header">
          <h3 class="section-title">服务器组</h3>
          <el-button type="primary" link class="edit-btn" @click="emit('onPrev', 2)" v-if="!isView">
            修改
          </el-button>
        </div>
        <div class="items">
          <div class="item">
            服务器组类型: {{ getServerGroupTypeText(formData.serverGroupType) }}
          </div>
          <div class="item">服务器组名称: {{ formData.serverGroupName }}</div>
          <div class="item" v-if="formData.serverGroupType === 1">
            主备切换数量: {{ formData.groupPriorityGroup }}
          </div>
        </div>

        <!-- 服务器列表 -->
        <div class="server-list-section">
          <SlProTable
            ref="proTable"
            :data="formData.serverInfoModelList"
            :columns="columns"
            :pagination="false"
          >
          </SlProTable>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive } from 'vue'
import type { ColumnProps } from '@/components/SlProTable/interface'
import type { ListenerFormData } from './types'
import SlProTable from '@/components/SlProTable/index.vue'

// 服务器列表数据类型定义
interface ReviewProps {
  formData: ListenerFormData
  isView: boolean
}

const props = defineProps<ReviewProps>()
const { formData, isView } = props

const emit = defineEmits(['onPrev'])

// 表格列定义
const columns = reactive<ColumnProps<any>[]>([
  { type: 'index', label: '序号', width: 55, fixed: 'left' },
  { prop: 'deviceName', label: '云主机名称', fixed: 'left' },
  { prop: 'cloudPlatform', label: '所属云' }, // 支持筛选过滤
  { prop: 'resourcePoolName', label: '资源池' }, // 支持筛选过滤
  { prop: 'vpcName', label: 'VPC', width: 150 }, // 显示绑定的公网IP或占位符
  { prop: 'eip', label: '弹性公网IP', width: 150 }, // 显示绑定的公网IP或占位符
  { prop: 'ip', label: 'IP', width: 150 },
  { prop: 'deviceStatusCn', label: '状态', width: 100 }, // 运行中，已关机，异常
  { prop: 'port', label: '端口', width: 200 }, // 运行中，已关机，异常
])

// 工具函数
const getProtocolText = (protocol: string) => {
  const map: Record<string, string> = {
    tcp: 'TCP',
    http: 'HTTP',
    https: 'HTTPS',
    udp: 'UDP',
  }
  return map[protocol] || protocol
}

const getLbAlgorithmText = (lbAlgorithm: string) => {
  const map: Record<string, string> = {
    ROUND_ROBIN: '加权轮询(WRR)',
    LEAST_CONNECTION: '加权最小连接数(WLC)',
  }
  return map[lbAlgorithm] || lbAlgorithm
}

const getCookieHandlingText = (cookieHandling: string) => {
  const map: Record<string, string> = {
    1: '植入',
    2: '重写',
  }
  return map[cookieHandling] || cookieHandling
}

const getServerGroupTypeText = (type: number) => {
  const map: Record<number, string> = {
    0: '虚拟服务器组',
    1: '主备服务器组',
  }
  return map[type] || type
}
</script>

<style lang="scss" scoped>
.review-step {
  padding: 20px 0;
}

.review-content {
  width: 100%;
  margin: 0 auto;
}

.review-section {
  margin-bottom: 30px;
  background-color: #fff;
  border-radius: 4px;
  padding: 0 0 20px;
}

.items {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(33%, 1fr));
  padding-left: 30px;
  box-sizing: border-box;
}
.item {
  padding: 10px 30px;
  box-sizing: border-box;
  font-size: 14px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  margin: 0;
}

.edit-btn {
  padding: 2px 0;
}

.server-list-section {
  margin-top: 20px;
  padding: 0 20px;
}
</style>
