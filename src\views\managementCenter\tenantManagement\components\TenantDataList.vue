<template>
  <SlProTable
    highlight-current-row
    :columns="columns"
    style="min-height: 300px"
    :request-api="getTenantListApi"
    :init-param="queryParams"
    hidden-table-header
  >
  </SlProTable>
</template>
<script setup lang="tsx" name="TenantDataList">
import { reactive, type VNode } from 'vue'
import type { ColumnProps } from '@/components/SlProTable/interface'
import { getTenantListApi, deleteTenantApi } from '@/api/modules/managementCenter'
import SlMessage from '@/components/base/SlMessage'
const { queryParams = { vmType: 'ecs' } } = defineProps<{
  queryParams?: any
}>()
const emit = defineEmits(['delete', 'showDialog'])
// 表格配置项
const columns = reactive<ColumnProps<any>[]>([
  { type: 'index', label: '序号', width: 55, fixed: 'left' },
  { prop: 'name', label: '租户名称', width: 200, fixed: 'left' },
  { prop: 'tenantType', label: '租户类型', width: 150 },
  { prop: 'tenantLevel', label: '租户等级', width: 120 },
  { prop: 'companyName', label: '所属公司', width: 120 },
  { prop: 'orgName', label: '所属部门', width: 150 },
  { prop: 'departmentName', label: '所属科室', width: 150 },
  {
    prop: 'ownerName',
    label: '租户所属人（账号）',
    width: 200,
    render: ({ row }: any) => {
      return `${row.ownerName}(${row.ownerId})`
    },
  },
  { prop: 'createdTime', label: '创建时间', width: 180 },
  { prop: 'operation', label: '操作', width: 200, fixed: 'right', render: operationRender },
])

function operationRender({ row }: any): VNode {
  return (
    <>
      <el-button onClick={() => handleDelete(row)} type="primary" link v-permission="Delete">
        删除
      </el-button>
      <el-button
        onClick={() => handleShowUserDialog(row)}
        type="primary"
        link
        v-permission="UserList"
      >
        用户列表
      </el-button>
    </>
  )
}

const handleDelete = async (row: any) => {
  await ElMessageBox.confirm('确认删除选中租户信息吗？', '确认删除', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
  })

  const res = await deleteTenantApi({
    id: row.id,
  })
  if (res.code === 200) {
    SlMessage.success('删除成功')
    emit('delete')
  } else {
    SlMessage.error(res.message || '删除失败')
  }
}

const handleShowUserDialog = (row: any) => {
  emit('showDialog', row)
}
</script>
