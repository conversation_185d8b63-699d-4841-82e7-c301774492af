<template>
  <div id="PermissionAdd">
    <sl-card class="box-card" v-for="(item, index) in formData" :key="item.key">
      <template #header>
        <div class="card-header">
          <span>菜单权限</span>
          <div v-if="!currentPermission.id">
            <el-button
              type="primary"
              link
              :icon="CirclePlus"
              @click="handleMenuAdd"
              v-if="formData.length == index + 1"
            >
              添加
            </el-button>
            <el-button
              type="danger"
              link
              :icon="Delete"
              @click="handleMenuRemove(index)"
              v-if="formData.length > 1"
            >
              删除
            </el-button>
          </div>
        </div>
      </template>
      <sl-form
        :ref="(el) => setFormRef(el, index)"
        :options="formOptions"
        v-model="formData[index]"
      >
        <template #menuSlot>
          <sl-card>
            <template #header>
              <div class="card-header">
                <span>操作权限</span>
              </div>
            </template>
            <el-row
              class="menu-item"
              v-for="(operation, operationIndex) in formData[index].menus?.filter(
                (i) => i.operateCode != 2,
              )"
              :key="operation.key"
            >
              <el-col :span="11">
                <el-form-item
                  class="module-item"
                  :prop="'menus.' + operationIndex + '.name'"
                  label="操作名称"
                  :rules="operationRules"
                >
                  <div class="module-item-content">
                    <el-input v-model="operation.name" placeholder="请输入操作名称" />
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="11">
                <el-form-item
                  class="module-item"
                  :prop="'menus.' + operationIndex + '.url'"
                  label="权限码"
                  :rules="[
                    { required: true, message: '请输入权限码', trigger: ['blur', 'change'] },
                  ]"
                >
                  <div class="module-item-content">
                    <el-input v-model="operation.url" placeholder="请输入权限码" />
                  </div>
                </el-form-item>
              </el-col>
              <el-col class="operation" :span="2">
                <el-icon
                  @click="handleOperationRemove(operation)"
                  v-if="formData[index]?.menus?.filter((i) => i.operateCode != 2).length != 1"
                >
                  <RemoveFilled />
                </el-icon>
                <el-icon
                  @click="handleOperationAdd(formData[index]?.menus || [])"
                  v-if="
                    formData[index]?.menus?.filter((i) => i.operateCode != 2).length ==
                    operationIndex + 1
                  "
                >
                  <CirclePlusFilled />
                </el-icon>
              </el-col>
            </el-row>
          </sl-card>
        </template>
      </sl-form>
    </sl-card>
    <div class="footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="submitValidator">确定</el-button>
    </div>
  </div>
</template>
<script setup lang="ts" name="PermissionAdd">
import { reactive, watch, ref } from 'vue'
import { CirclePlus, Delete, RemoveFilled, CirclePlusFilled } from '@element-plus/icons-vue'
import type { PermissionType } from '../interface/type'
import { addPermissionApi, updatePermissionApi } from '@/api/modules/managementCenter'
import { validateNoSpecialChars } from '@/utils/validate'
import SlMessage from '@/components/base/SlMessage'

const props = defineProps<{
  currentPermission: PermissionType
}>()

const formData = reactive<PermissionType[]>([])
const formRefs = ref<any[]>([])

const setFormRef = (el: any, index: number) => {
  if (el) {
    formRefs.value[index] = el
  }
}
const handleOperationAdd = (menus: any[]) => {
  menus.push({
    key: 'operation' + Date.now().toString(),
    name: '',
    url: '',
    operateCode: 0,
  })
}

watch(
  () => props.currentPermission,
  (newPermission) => {
    if (newPermission.id) {
      formData.length = 0
      formData.push(JSON.parse(JSON.stringify(newPermission)))
    } else {
      formData.length = 0
      const newBtn = {
        key: Date.now().toString(),
        name: '',
        url: '',
        operateCode: 0,
      }
      formData.push({
        key: Date.now().toString(),
        name: '',
        url: '',
        menus: [newBtn],
      })
    }

    // 检查 menus 是否为空，如果为空则添加一个默认项
    formData.forEach((item) => {
      if (!item.menus || item.menus.length === 0) {
        item.menus = []
        handleOperationAdd(item.menus)
      }
    })
  },
  {
    immediate: true,
  },
)

const formOptions = [
  {
    groupItems: [
      {
        label: '菜单名称',
        type: 'input',
        key: 'name',
        span: 12,
        rules: [
          { required: true, message: '请输入菜单名称', trigger: ['blur', 'change'] },
          { validator: validateNoSpecialChars, trigger: ['blur', 'change'] },
        ],
      },
      {
        label: '权限码',
        type: 'input',
        key: 'url',
        span: 12,
        rules: [{ required: true, message: '请输入权限码', trigger: ['blur', 'change'] }],
      },
      {
        label: '',
        type: 'slot',
        slotName: 'menuSlot',
        key: 'menu',
        hiddenLabel: true,
        span: 24,
      },
    ],
  },
]

const operationRules = [
  { required: true, message: '请输入权限码', trigger: ['blur', 'change'] },
  { validator: validateNoSpecialChars, trigger: ['blur', 'change'] },
]
const handleMenuAdd = () => {
  formData.push({
    key: Date.now().toString(),
    name: '',
    url: '',
    menus: [
      {
        key: 'operation' + Date.now().toString(),
        name: '',
        url: '',
      },
    ],
  })
}

const handleMenuRemove = (index: number) => {
  formData.splice(index, 1)
}

const handleOperationRemove = (operation: PermissionType) => {
  operation.operateCode = 2
}

const emit = defineEmits(['submit', 'close'])

const getFormatFormData = (formData: PermissionType[]) => {
  const newData = JSON.parse(JSON.stringify(formData))
  const originalMenu = props.currentPermission
  if (originalMenu.id) {
    newData.forEach((menu: PermissionType) => {
      menu.operateCode = menu.name == originalMenu.name && menu.url == originalMenu.url ? 3 : 1
      menu?.menus?.forEach((operation: PermissionType) => {
        if (operation.id && operation.operateCode != 2) {
          const originalOperation = originalMenu.menus?.find(
            (item: PermissionType) => item.id == operation.id,
          )
          operation.operateCode =
            operation.name == originalOperation?.name && operation.url == originalOperation?.url
              ? 3
              : 1
        }
      })
    })
  }
  return newData[0]
}
const submitValidator = async () => {
  try {
    Promise.all(formRefs.value.map((formRef) => formRef.validate()))
      .then(async (res) => {
        if (res.length > 0 && res.every(Boolean)) {
          let updatedFormData: PermissionType[]
          if (props.currentPermission.id) {
            updatedFormData = getFormatFormData(formData)
            await updatePermissionApi(updatedFormData)
            SlMessage.success('编辑成功')
          } else {
            await addPermissionApi(formData)
            SlMessage.success('创建成功')
          }

          emit('submit')
        }
      })
      .catch((err) => {
        console.error(err)
      })
    // return
  } catch {
    SlMessage.error('操作失败')
  }
}

const handleClose = () => {
  emit('close')
}
</script>
<style lang="scss" scoped>
#PermissionAdd {
  .box-card {
    margin-bottom: 10px;
  }
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  :deep(.el-card__body) {
    padding: 10px;
  }
  .menu-item {
    padding: 10px;
  }
  .operation {
    display: flex;
    align-items: center;
    font-size: 16px;
    .el-icon {
      cursor: pointer;
      margin-left: 5px;
    }
  }
  .footer {
    height: 48px;
    margin: 0 8px;
    background: #ffffff;
    border-radius: 8px 8px 8px 8px;
    display: flex;
    align-items: center;
    justify-content: right;
    padding: 0 14px;
  }
}
</style>
