/**
 * Web Worker 用于文件分片处理和哈希计算
 * 此Worker接收一个文件片段范围，计算每个分片的MD5哈希值
 */
import SparkMD5 from 'spark-md5'

// 监听主线程消息
self.onmessage = async (e) => {
  try {
    const { file, start, end, CHUNK_SIZE, returnIndividually } = e.data

    if (returnIndividually) {
      // 逐个处理分片并返回
      for (let i = start; i < end; i++) {
        const startByte = i * CHUNK_SIZE
        const endByte = Math.min(startByte + CHUNK_SIZE, file.size)
        const chunk = file.slice(startByte, endByte)

        // 计算分片的哈希值
        const hash = await calculateHash(chunk)

        const chunkInfo = {
          start: startByte,
          end: endByte,
          index: i,
          hash,
          blob: chunk,
        }

        // 将单个处理好的分片信息返回给主线程
        self.postMessage([chunkInfo])
      }

      // 通知处理完成
      self.postMessage({ completed: true })
    } else {
      // 批量处理分片
      const chunks = []

      // 处理分配给此worker的文件块
      for (let i = start; i < end; i++) {
        const startByte = i * CHUNK_SIZE
        const endByte = Math.min(startByte + CHUNK_SIZE, file.size)
        const chunk = file.slice(startByte, endByte)

        // 计算分片的哈希值
        const hash = await calculateHash(chunk)

        chunks.push({
          start: startByte,
          end: endByte,
          index: i,
          hash,
          blob: chunk,
        })
      }

      // 将处理好的分片信息返回给主线程
      self.postMessage(chunks)
    }
  } catch (error) {
    // 发生错误时通知主线程
    self.postMessage({ error: error.message || '处理文件时发生错误' })
  }
}

/**
 * 使用SparkMD5计算Blob的哈希值
 * @param {Blob} blob - 需要计算哈希的数据块
 * @returns {Promise<string>} - 返回MD5哈希值
 */
const calculateHash = (blob) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()

    reader.onload = (e) => {
      try {
        const spark = new SparkMD5.ArrayBuffer()
        spark.append(e.target.result)
        const hash = spark.end()
        resolve(hash)
      } catch (error) {
        reject(error || new Error('计算哈希值失败'))
      }
    }

    reader.onerror = (error) => {
      reject(error || new Error('读取文件失败'))
    }

    // 读取Blob数据为ArrayBuffer用于计算哈希
    reader.readAsArrayBuffer(blob)
  })
}
