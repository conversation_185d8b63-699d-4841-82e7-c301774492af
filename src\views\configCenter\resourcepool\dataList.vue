<template>
  <SlProTable
    ref="proTable"
    highlight-current-row
    :columns="columns"
    style="min-height: 300px"
    :request-api="resourcePoolList"
    :init-param="queryParams"
    :current-change="currentChange"
    hidden-table-header
    row-key="id"
  >
  </SlProTable>
  <!--  <SlDialog-->
  <!--    v-model="recycleDialogVisible"-->
  <!--    title="资源回收"-->
  <!--    width="600px"-->
  <!--    destroy-on-close-->
  <!--    confirm-text="提交"-->
  <!--    @close="handleRecycleClose"-->
  <!--    @confirm="handleRecycleConfirm"-->
  <!--  >-->
  <!--    <sl-form-->
  <!--      ref="recycleFormRef"-->
  <!--      :options="recycleFormOptions"-->
  <!--      v-model="recycleFormModel"-->
  <!--    ></sl-form>-->
  <!--  </SlDialog>-->
</template>
<script setup lang="tsx" name="dataList">
import { ref, reactive } from 'vue'
import type { ColumnProps, ProTableInstance } from '@/components/SlProTable/interface'
import { resourcePoolList, deleteResourcePoolByIdApi } from '@/api/modules/configCenter'
import { Delete } from '@element-plus/icons-vue'
// import {
//   useRecycleFormModel,
//   useRecycleFormOptions,
//   createRecycleWorkOrder,
//   type RecycleFormModel,
// } from '../../resourceCenter/hooks/useRecycleModels'
import SlMessage from '@/components/base/SlMessage'

const { queryParams } = defineProps<{
  queryParams: any
}>()

const emit = defineEmits(['currentChange'])
const currentChange = (currentRow: any, oldCurrentRow: any) => {
  emit('currentChange', currentRow, oldCurrentRow)
}
// 表格配置项
const columns = reactive<ColumnProps<any>[]>([
  { type: 'selection', width: 55 },
  { type: 'index', label: '序号', minWidth: 55 },
  {
    prop: 'name',
    label: '资源池名称',
    minWidth: 150,
  },
  { prop: 'code', label: '资源池编码', minWidth: 150 },
  { prop: 'platformName', label: '云平台', minWidth: 200 },
  { prop: 'endpoint', label: '连接地址', minWidth: 150 },
  { prop: 'cityCode', label: '城市编码', minWidth: 150 },
  { prop: 'cityName', label: '城市名', minWidth: 100 },
  { prop: 'resourceCode', label: '资源池ID', width: 150 },
  { prop: 'realmType', label: '领域类型', width: 100 },
  { prop: 'description', label: '描述', minWidth: 100 },
  {
    prop: 'operation',
    label: '操作',
    width: '150',
    fixed: 'right',
    render: ({ row }) => {
      return (
        <>
          <el-button type="danger" link icon={Delete} onClick={() => deletePermission(row)}>
            删除
          </el-button>
        </>
      )
    },
  },
])

const proTable = ref<ProTableInstance>()
const reloadTableList = () => {
  proTable.value?.clearSelection()
  proTable.value?.getTableList()
}

/**
 * 删除
 * @param id
 */
const deletePermission = async (row: any) => {
  await ElMessageBox.confirm('确认删除资源池' + row.name + '吗？', '信息提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
  })
  await deleteResourcePoolByIdApi({
    id: row.id,
  })
  SlMessage.success('删除成功')
  proTable.value?.getTableList()
  proTable.value?.clearSelection()
}
// 回收功能
// const recycleDialogVisible = ref(false)
// const recycleFormModel = reactive<RecycleFormModel>(useRecycleFormModel())
// const recycleFormOptions = reactive(useRecycleFormOptions())

// const handleRecycleClose = () => {
//   recycleDialogVisible.value = false
//   recycleFormModel.recoveryGoodsIds = ''
//   recycleFormModel.orderTitle = ''
//   recycleFormModel.orderDesc = ''
// }

// const recycleFormRef = ref()
// const handleRecycleConfirm = async () => {
//   if (!(await recycleFormRef.value?.validate(() => true))) return
//   await createRecycleWorkOrder({ ...recycleFormModel, recoveryType: 'vpc' })
//   handleRecycleClose()
//   proTable.value?.getTableList()
//   proTable.value?.clearSelection()
// }

// 批量回收功能
// const handleBatchRecycle = async () => {
//   const selectedList = proTable.value?.selectedList || []
//   if (selectedList.length == 0) {
//     return SlMessage.warning('请先选择资源池')
//   }
//   recycleDialogVisible.value = true
//   recycleFormModel.recoveryGoodsIds = selectedList.map((i) => i.id.trim()).join(',')
// }

defineExpose({
  // handleBatchRecycle,
  reloadTableList,
})
</script>
