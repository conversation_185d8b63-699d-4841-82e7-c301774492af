function useModel() {
  return {
    applicant: '',
    department: '',
    workOrdertype: '开通申请',

    // @ productType 对应的产品名称
    goodsName: '镜像',
    operationName: '新增',
    // @工单id
    id: '',
    // @产品名称
    instanceName: '',
    // @功能模块
    functionalModule: '',
    functionMedelLabel: '',
    // @产品类型
    productType: '镜像',

    name: '',
    description: '',
    regionId: '',
    shares: '',
    tenantId: '', // 租户id
    osTypeId: '', // 镜像类型ID
    osType: '', // 操作系统类型
    diskFormat: '', // 磁盘格式
    virtType: '', // 虚拟化类型
    imageSize: '', // 镜像大小
    minCpu: '', // 最小cpu
    minRam: '', // 最小内存(MB)
    minDisk: '', // 最小磁盘(GB)
    password: '', // 镜像密码
    resourceId: '', // 底层镜像ID
    imageType: '', // 镜像类型
    version: '', // 版本号
    azId: '', //  可用区ID
    domainCode: '', //  云平台编码
    osTypeSource: '', //
  }
}

export default useModel
