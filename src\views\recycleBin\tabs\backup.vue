<template>
  <div class="form-container">
    <SlProTable
      ref="proTable"
      highlight-current-row
      :columns="columns"
      style="min-height: 300px"
      :data="goodsList"
      :pagination="false"
      hidden-table-header
      row-key="goodsOrderId"
    >
    </SlProTable>
  </div>
</template>
<script setup lang="tsx" name="dataList">
import { reactive } from 'vue'
import type { ColumnProps } from '@/components/SlProTable/interface'
import { useOp } from './useOp'

const { operationRender } = useOp()

const { goodsList = [] } = defineProps<{
  goodsList?: any[]
}>()
// 表格配置项
const columns = reactive<ColumnProps<any>[]>([
  { type: 'index', label: '序号', width: 55 },
  { prop: 'deviceName', label: '策略名称', minWidth: 150 },
  {
    prop: 'backupType',
    label: '备份类型',
    minWidth: 120,
    enum: [
      { label: '云主机', value: 'ECS' },
      {
        label: '云硬盘',
        value: 'EVS',
      },
    ],
  },
  {
    prop: 'frequency',
    label: '备份频率',
    minWidth: 120,
    enum: [
      { label: '每天', value: 'days' },
      { label: '每周', value: 'weeks' },
    ],
  },
  { prop: 'daysOfWeek', label: '备份周期', minWidth: 120 },
  { prop: 'tenantName', label: '租户', minWidth: 120 },
  { prop: 'businessSysName', label: '业务系统', minWidth: 150, filter: true },
  { prop: 'domainName', label: '所属云', minWidth: 100, filter: true },
  { prop: 'resourcePoolName', label: '资源池', minWidth: 150, filter: true },
  { prop: 'orderCode', label: '工单编号', minWidth: 150 },
  { prop: 'effectiveTime', label: '开通时间', minWidth: 150 },
  { prop: 'billId', label: '计费号', width: 150 },
  { prop: 'recoveryStatusCn', label: '回收状态', width: 100 },
  { prop: 'applyUserName', label: '申请人', minWidth: 100 },
  { prop: 'operation', label: '操作', minWidth: 100, fixed: 'right', render: operationRender },
])
</script>
<style lang="scss" scoped>
.form-container {
  margin: 8px;
}
</style>
