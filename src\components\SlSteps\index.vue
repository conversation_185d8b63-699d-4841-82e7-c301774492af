<template>
  <el-steps class="sl-steps" process-status="finish" align-center :active="currentSort">
    <el-step v-for="(step, index) in allTasks" :key="index" :title="step.taskName"></el-step>
  </el-steps>
</template>
<script setup lang="ts">
import type { ActiviteDetailVoListType } from '@/views/approvalCenter/workOrder/interface/type'
import { inject, ref, type Ref } from 'vue'

const allTasks = inject<Ref<ActiviteDetailVoListType[]>>('allTasks', ref([]))
const currentSort = inject('currentSort', ref(0))
</script>

<style lang="scss">
.sl-steps {
  .el-step__title.is-success {
    color: var(--el-color-primary);
  }
  .el-step__title {
    font-size: 14px;
  }
  .el-step.is-center .el-step__line {
    left: 62%;
    right: -38%;
  }
  .el-step__title.is-process {
    color: #000c1f;
    font-weight: normal;
  }
  .el-step__head.is-finish {
    .el-step__icon.is-text {
      background: var(--el-color-primary);
      color: #fff;
    }
  }
  &.closed {
    .el-step__head.is-finish {
      color: var(--el-text-color-placeholder);
      border-color: var(--el-text-color-placeholder);
    }
    .el-step__title.is-finish {
      color: var(--el-text-color-placeholder);
    }
    .el-step__head.is-finish .el-step__icon.is-text {
      background: var(--el-text-color-placeholder);
      color: #fff;
    }
  }
}
</style>
