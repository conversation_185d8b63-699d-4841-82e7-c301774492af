<template>
  <el-drawer
    v-model="visible"
    :title="isViewMode ? '组合产品详情' : '组合产品创建'"
    :size="isViewMode ? '40%' : '70%'"
    :destroy-on-close="true"
    :before-close="handleClose"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <div v-loading="loading" class="drawer-content">
      <div class="tree-container">
        <div class="search-box" v-if="!isViewMode">
          <el-input
            v-model="searchKeyword"
            placeholder="请输入关键字搜索"
            prefix-icon="Search"
            clearable
          />
        </div>
        <div class="selected-info" v-if="!isViewMode">
          已选择 {{ leafSelectedCount }} 个资源池
          <el-button type="primary" link @click="clearSelected">清空</el-button>
        </div>
        <div class="selected-info" v-else>已包含 {{ leafSelectedCount }} 个资源池</div>
        <div class="tree-wrapper">
          <div class="left-tree" v-show="!isViewMode">
            <el-tree
              ref="treeRef"
              :data="treeData"
              show-checkbox
              node-key="code"
              :props="defaultProps"
              :filter-node-method="filterNode"
              @check="handleCheckChange"
              :disabled="isViewMode"
            >
              <template #default="{ node }">
                <span class="custom-tree-node">
                  {{ node.label }}
                </span>
              </template>
            </el-tree>
          </div>
          <div class="right-selected">
            <div v-if="leafSelectedCount > 0" class="selected-tree-container">
              <el-tree :data="selectedTreeData" :props="defaultProps" default-expand-all>
                <template #default="{ node }">
                  <div class="selected-tree-node">
                    <span>{{ node.label }}</span>
                    <el-icon
                      v-if="node.isLeaf && !isViewMode"
                      class="remove-icon"
                      @click.stop="removeSelectedItem(node.data)"
                    >
                      <CircleClose />
                    </el-icon>
                  </div>
                </template>
              </el-tree>
            </div>
            <div v-else class="empty-selected">
              <el-empty description="暂无选中项" />
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="drawer-footer" v-if="!isViewMode">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确定</el-button>
    </div>
    <div class="drawer-footer" v-else>
      <el-button type="primary" @click="handleClose">关闭</el-button>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { CircleClose } from '@element-plus/icons-vue'
import { getRegionTree, type RegionTreeNode } from '@/api/modules/productCenter'

// 定义TreeNode类型，扩展自RegionTreeNode
type TreeNode = RegionTreeNode & {
  cloudPlatform?: string
}

// 定义props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  currentRow: {
    type: Object,
    default: () => ({}),
  },
})

// 定义事件
const emit = defineEmits(['update:modelValue', 'close', 'submit'])

// 计算属性：可见性
const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val),
})

// 是否是查看模式（当reginCodeList存在且不为空时）
const isViewMode = computed(() => {
  return (
    props.currentRow && props.currentRow.reginCodeList && props.currentRow.reginCodeList.length > 0
  )
})

const searchKeyword = ref('')
const treeRef = ref(null)
const selectedItems = ref<TreeNode[]>([])
const treeData = ref<any>([])
const loading = ref(false)

// 默认树形配置
const defaultProps = {
  children: 'children',
  label: 'name',
  disabled: 'disabled',
}

// 从接口获取树结构数据
const fetchTreeData = async () => {
  loading.value = true
  try {
    // 获取树结构数据
    const res = await getRegionTree()
    treeData.value = res.entity
  } finally {
    loading.value = false
  }
}

// 过滤节点方法
const filterNode = (value: string, data: any) => {
  if (!value) return true
  return data.name.includes(value)
}

// 监听搜索关键字变化
watch(searchKeyword, (val) => {
  nextTick(() => {
    if (treeRef.value) {
      ;(treeRef.value as any).filter(val)
    }
  })
})

// 判断节点是否是叶子节点（没有子节点或子节点为空数组）
const isLeafNode = (node: TreeNode): boolean => {
  return !node.children || node.children.length === 0
}

// 辅助函数：查找某个code对应的节点
const findNodeByCode = (
  code: number | string,
  nodes: TreeNode[] = treeData.value,
): TreeNode | null => {
  for (const node of nodes) {
    if (node.code === code) return node
    if (node.children && node.children.length) {
      const found = findNodeByCode(code, node.children)
      if (found) return found
    }
  }
  return null
}

// 辅助函数：查找节点的完整路径
const findNodePath = (
  code: number | string,
  nodes: TreeNode[] = treeData.value,
  path: TreeNode[] = [],
): TreeNode[] | null => {
  for (const node of nodes) {
    // 当前路径
    const currentPath = [...path, node]

    // 找到节点
    if (node.code === code) return currentPath

    // 继续在子节点中查找
    if (node.children && node.children.length) {
      const found = findNodePath(code, node.children, currentPath)
      if (found) return found
    }
  }

  return null
}

// 选中节点变化处理
const handleCheckChange = () => {
  // 获取所有选中的叶子节点ID
  const checkedKeys = (treeRef.value as any).getCheckedKeys()

  // 重置选中项
  selectedItems.value = []

  // 处理选中的节点
  checkedKeys.forEach((code: number | string) => {
    // 查找节点对象
    const node = findNodeByCode(code)
    if (!node) return

    // 只处理叶子节点（资源池级别）
    if (!isLeafNode(node)) return

    // 查找节点路径
    const nodePath = findNodePath(code)
    if (!nodePath) return

    // 保存完整路径信息到节点
    const pathInfo = nodePath.map((n) => ({ code: n.code, name: n.name }))

    // 将节点添加到选中列表，并附加路径信息
    selectedItems.value.push({
      ...node,
      pathInfo,
      // 从路径中获取第一级分类名称作为cloudType
      cloudType: pathInfo.length > 0 ? pathInfo[0].name : '未分类',
      // 如果有第二级分类，则作为cloudPlatform
      cloudPlatform: pathInfo.length > 1 ? pathInfo[1].name : '未分类',
    })
  })
}

// 移除选中项
const removeSelectedItem = (item: TreeNode) => {
  if (treeRef.value && item && item.code) {
    ;(treeRef.value as any).setChecked(item.code, false)
  }

  // 刷新选中项列表
  selectedItems.value = selectedItems.value.filter((node) => node.code !== item.code)
}

// 清空选中项
const clearSelected = () => {
  if (treeRef.value) {
    ;(treeRef.value as any).setCheckedKeys([])
  }
  selectedItems.value = []
}

// 监听抽屉显示
watch(
  () => visible.value,
  async (val) => {
    if (val) {
      // 重置搜索和选中状态
      searchKeyword.value = ''

      // 加载树结构数据
      await fetchTreeData()

      // 如果是查看模式，预先选中已存在的资源池
      if (isViewMode.value && props.currentRow.reginCodeList) {
        // 等待树加载完成
        nextTick(() => {
          // 预先选中已存在的资源池
          if (treeRef.value) {
            ;(treeRef.value as any).setCheckedKeys(props.currentRow.reginCodeList)
            // 更新选中列表
            handleCheckChange()
          }
        })
      } else {
        // 不是查看模式，清空选中项
        clearSelected()
      }
    }
  },
)

// 组件挂载时加载数据
onMounted(() => {
  if (visible.value) {
    fetchTreeData()
  }
})

// 选中数量计算（仅计算叶子节点）
const leafSelectedCount = computed(() => {
  // 计算所有最终选中的资源池数量
  let count = 0

  // 遍历selectedTreeData，统计所有叶子节点
  selectedTreeData.value.forEach((cloudType) => {
    if (cloudType.children) {
      cloudType.children.forEach((platform) => {
        if (platform.children) {
          count += platform.children.length
        }
      })
    }
  })

  return count
})

// 构建选中树形数据结构
const selectedTreeData = computed(() => {
  const treeData: TreeNode[] = []

  if (selectedItems.value.length === 0) {
    return treeData
  }

  // 按cloudType(第一级分类)分组
  const cloudTypeGroups = selectedItems.value.reduce((groups: Record<string, TreeNode[]>, item) => {
    const cloudType = item.cloudType || '未分类'
    if (!groups[cloudType]) {
      groups[cloudType] = []
    }
    groups[cloudType].push(item)
    return groups
  }, {})

  // 构建树结构
  Object.entries(cloudTypeGroups).forEach(([cloudType, items]) => {
    // 创建第一级节点
    const typeNode: TreeNode = {
      code: `type-${cloudType}`,
      name: cloudType,
      children: [],
    }

    // 按cloudPlatform(第二级分类)继续分组
    const platformGroups = items.reduce((groups: Record<string, TreeNode[]>, item) => {
      const platform = item.cloudPlatform || '未分类'
      if (!groups[platform]) {
        groups[platform] = []
      }
      groups[platform].push(item)
      return groups
    }, {})

    // 添加平台分组
    Object.entries(platformGroups).forEach(([platform, platformItems]) => {
      typeNode.children!.push({
        code: `${cloudType}-${platform}`,
        name: platform,
        children: platformItems,
      })
    })

    treeData.push(typeNode)
  })

  return treeData
})

// 关闭抽屉
const handleClose = () => {
  emit('close')
}

// 提交
const handleSubmit = () => {
  if (leafSelectedCount.value === 0) {
    ElMessage.warning('请至少选择一个资源池')
    return
  }

  // 获取所有选中的资源
  const allSelected = [...selectedItems.value]

  // 触发提交事件
  emit('submit', allSelected)
}
</script>

<style lang="scss" scoped>
.drawer-content {
  height: calc(100% - 60px);
  padding: 0 10px;
}

.drawer-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 10px 20px;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  border-top: 1px solid #ebeef5;
  background-color: #fff;
}

.tree-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.search-box {
  margin-bottom: 10px;
}

.selected-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  font-size: 14px;
  color: #606266;
}

.tree-wrapper {
  display: flex;
  flex: 1;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
}

.left-tree {
  flex: 1;
  padding: 10px;
  border-right: 1px solid #dcdfe6;
  overflow: auto;
}

.right-selected {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: auto;
}

.selected-tree-container {
  flex: 1;
  padding: 10px;
  overflow: auto;
}

.selected-tree-node {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 10px;
  width: 100%;
}

:deep(.el-tree-node__content) {
  height: auto;
  min-height: 32px;
}

:deep(.el-tree-node.is-expanded > .el-tree-node__children) {
  padding-left: 10px;
}

:deep(.el-tree-node__content:hover) {
  background-color: #f5f7fa;
}

:deep(.el-tree-node.is-leaf .selected-tree-node) {
  background-color: #f5f7fa;
  border-radius: 4px;
  margin: 2px 0;
}

.custom-tree-node {
  display: flex;
  align-items: center;
  gap: 8px;
}

.disabled-node {
  color: #909399;
  cursor: not-allowed;
}

.empty-selected {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.remove-icon {
  cursor: pointer;
  color: #909399;

  &:hover {
    color: #f56c6c;
  }
}
</style>
