<template>
  <div id="BusinessSystemList">
    <div class="container">
      <div class="title">业务系统：</div>
      <el-popover :visible="popoverVisible" placement="bottom-end" :width="600" trigger="click">
        <template #reference>
          <div class="select" @click="handleSelectedShow">
            <i class="iconfont menu"></i>
            <span>请选择</span>
          </div>
        </template>
        <div class="list">
          <div class="list-header">
            <div class="list-title">业务系统：</div>
            <div class="list-switch">
              <span>全选</span>
              <el-switch size="small" v-model="selectAll" @change="handleSelectAllChange" />
            </div>
          </div>
          <div class="list-items">
            <div
              class="list-item"
              :class="{ active: selectedSystemIds.includes(system.busiSystemId) }"
              v-for="system in props.systemList"
              :key="system.busiSystemId"
              @click="handleSystemItemClick(system.busiSystemId)"
            >
              {{ system.busiSystemName }}
            </div>
          </div>
          <div class="list-footer">
            <el-button @click="popoverVisible = false"> 取消 </el-button>
            <el-button type="primary" @click="handleSubmitSelectedSystemIds"> 确认 </el-button>
          </div>
        </div>
      </el-popover>
      <div class="selected-items">
        <div
          class="selected-item"
          v-for="(system, systemIndex) in selectedSystems"
          :key="system.busiSystemId"
        >
          <span>{{ system.busiSystemName }}</span>
          <el-icon @click="handleShowSystemDetails(system.busiSystemId)"><Warning /></el-icon>
          <el-icon @click="handleSystemDeselete(systemIndex)" v-if="selectedSystems.length > 1">
            <CircleClose />
          </el-icon>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts" name="BusinessSystemList">
import { ref, watchEffect, computed } from 'vue'
import type { PropType } from 'vue'
import type { BusinessSystemListType } from '../interface/type'
import SlMessage from '@/components/base/SlMessage'
import { Warning, CircleClose } from '@element-plus/icons-vue'
const props = defineProps({
  systemList: {
    type: Array as PropType<BusinessSystemListType[]>,
    default: () => [],
  },
  selectedIds: {
    type: Array,
    default: () => [],
  },
})
// 选择业务系统弹窗
const popoverVisible = ref(false)
const selectAll = ref(false)
const selectedSystemIds = ref<any>([])

watchEffect(() => {
  selectAll.value = selectedSystemIds.value.length == props.systemList.length
})
const handleSelectedShow = () => {
  popoverVisible.value = true
  selectedSystemIds.value = JSON.parse(JSON.stringify(props.selectedIds))
}

const handleSelectAllChange = (value: any) => {
  if (value) {
    const ids = props.systemList.map((item) => item.busiSystemId)
    selectedSystemIds.value = ids
  } else {
    selectedSystemIds.value = []
  }
}

const handleSystemItemClick = (id: number) => {
  const index = selectedSystemIds.value.indexOf(id)
  if (index == -1) {
    selectedSystemIds.value.push(id)
  } else {
    selectedSystemIds.value.splice(index, 1)
  }
}
const emit = defineEmits<{
  (e: 'systemSelected', ids: Array<number>): void
  (e: 'showSystemDetails', id: number): void
  (e: 'systemDeselected', id: number): void
}>()
const handleSubmitSelectedSystemIds = () => {
  if (selectedSystemIds.value.length == 0) {
    return SlMessage({
      message: '请至少选择一个业务系统',
      type: 'warning',
    })
  }
  emit('systemSelected', selectedSystemIds.value)
  popoverVisible.value = false
}

// 已选业务系统列表
const selectedSystems = computed(() => {
  return props.systemList.filter((item) => props.selectedIds.includes(item.busiSystemId))
})

const handleShowSystemDetails = (id: number) => {
  emit('showSystemDetails', id)
}

const handleSystemDeselete = (index: number) => {
  if (selectedSystems.value.length == 1) return
  emit('systemDeselected', index)
}
</script>
<style lang="scss" scoped>
#BusinessSystemList {
  font-size: 14px;
  margin: 0 10px;
  .container {
    height: 100%;
    padding: 10px 20px 5px 20px;
    color: var(--el-color-primary);
    background-color: #ecf2fd;
    display: flex;
    align-items: flex-start;
    .title {
      font-weight: 600;
      height: 28px;
      line-height: 28px;
      margin-bottom: 5px;
    }
    .select {
      display: flex;
      align-items: center;
      cursor: pointer;
      height: 28px;
      margin-bottom: 5px;
      .menu {
        margin: 0 5px;
      }
    }
  }
}

.list {
  .list-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .list-title {
      font-weight: 600;
    }
    .list-switch {
      display: flex;
      align-items: center;
      & > span {
        margin-right: 5px;
      }
    }
  }
  .list-items {
    display: flex;
    margin-top: 10px;
    flex-wrap: wrap;
    max-height: 300px;
    overflow: scroll;
    &::after {
      content: '';
      flex: auto;
    }
    .list-item {
      height: 30px;
      padding: 0 10px;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #666;
      background-color: #f5f6f8;
      margin-right: 10px;
      margin-bottom: 10px;
      border-radius: 5px;
      cursor: pointer;
      white-space: nowrap;
      &.active {
        color: white;
        background-color: var(--el-color-primary);
      }
    }
  }
  .list-footer {
    text-align: right;
  }
}

.selected-items {
  flex: 1;
  margin-left: 10px;
  display: flex;
  flex-wrap: wrap;
  max-height: 110px;
  overflow: scroll;
  &::-webkit-scrollbar {
    display: none;
  }
  .selected-item {
    height: 28px;
    padding: 0 10px;
    margin-right: 10px;
    margin-bottom: 5px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: white;
    background-color: var(--el-color-primary);
    border-radius: 5px;
    .el-icon {
      opacity: 0.7;
      margin-left: 5px;
      cursor: pointer;
    }
  }
}
</style>
