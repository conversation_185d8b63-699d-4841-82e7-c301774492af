<template>
  <sl-form
    class="goods-info-form"
    size="small"
    ref="slFormRef"
    label-position="top"
    :options="goodsInfoOptions"
    :model-value="formModel"
  >
    <!-- 数据盘插槽 -->
    <template #obsSlot="{ form, item }">
      <div style="display: flex; flex-grow: 1">
        <el-select clearable :disabled="item.disabled" v-model="form[item.key][0]">
          <el-option
            :key="option.value"
            v-for="option in item.options"
            :label="option.label"
            :min="10"
            :value="option.value"
          />
        </el-select>
        <el-input-number
          :disabled="item.disabled"
          v-bind="item.props"
          v-model="form[item.key][1]"
          style="margin: 0 4px; min-width: 120px"
        />
        GB
      </div>
    </template>
  </sl-form>
</template>
<script setup lang="ts">
import { reactive, ref } from 'vue'
import { type IObsModel, useObsModel } from '@/views/resArrangement/model'
import { useGlobalDicStore } from '@/stores/modules/dic'
import { validateObsName } from '@/views/resourceCenter/utils'
import slForm from '@/components/form/SlForm.vue'

const globalDic = useGlobalDicStore()
const { getDic } = globalDic

const props = defineProps<{
  goods: IObsModel
}>()

const formModel = reactive(Object.assign(useObsModel(), props.goods))

const slFormRef = ref()

const validateobsTypeobsSize = (rule: any, value: any, callback: any) => {
  if (!formModel.obs[0]) {
    callback(new Error('请选择数据盘类型'))
  } else if (!formModel.obs[1]) {
    callback(new Error('请输入数据盘大小'))
  } else {
    callback()
  }
}

const goodsInfoOptions = reactive([
  {
    style: 'margin:0;padding:0;padding-top: 18px;',
    gutter: 40,
    groupItems: [
      {
        label: '对象存储名称',
        type: 'input',
        key: 'instanceName',
        span: 24,
        props: {
          maxlength: 64,
          showWordLimit: true,
        },
        rules: [
          { required: true, message: '请输入对象存储名称', trigger: ['blur', 'change'] },
          { validator: validateObsName, trigger: ['blur', 'change'] },
        ],
      },
      {
        label: '存储',
        type: 'slot',
        slotName: 'obsSlot',
        key: 'obs',
        options: getDic('obs'),
        span: 24,
        props: {
          min: 1,
        },
        required: true,
        rules: [
          {
            validator: validateobsTypeobsSize,
            trigger: 'change',
          },
        ],
      },
      {
        label: '申请时长',
        type: 'select',
        key: 'time',
        span: 24,
        options: getDic('time'),
        rules: [{ required: true, message: '请选择申请时长', trigger: ['blur', 'change'] }],
      },
    ],
  },
])
const validateForm = async () => {
  const res = await slFormRef.value.validate()
  return res
}
const submitForm = async () => {
  const goods = props.goods
  Object.assign(goods, formModel)
}
defineExpose({
  validateForm,
  submitForm,
})
</script>
<style scoped>
.goods-del-btn {
  position: absolute;
  top: -7px;
  right: -6px;
  z-index: 10;
  color: var(--el-color-danger);
  font-size: 1.2rem;
  cursor: pointer;
  display: none;
}
.goods-info-form:hover .goods-del-btn {
  display: block;
}

.evs-item {
  width: 100%;
  margin-bottom: 15px;
  .evs-item-content {
    width: 100%;
    display: flex;
    align-items: center;
  }
  &:last-child {
    margin-bottom: 0;
  }
  .evs-icons {
    display: flex;
    align-items: center;
    margin-left: 10px;
    .el-icon {
      cursor: pointer;
    }
  }
}
</style>
