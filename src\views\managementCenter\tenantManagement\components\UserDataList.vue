<template>
  <div class="filter-form-con">
    <sl-form
      class="filter-form"
      :class="{ collapsed: collapsed }"
      ref="formRef"
      :options="formOptions"
      v-model="formModel"
    >
    </sl-form>
  </div>
  <SlProTable
    ref="proTable"
    highlight-current-row
    :columns="columns"
    style="min-height: 300px"
    :request-api="getTenantUserListApi"
    :init-param="queryParams"
    hidden-table-header
  >
  </SlProTable>
</template>
<script setup lang="tsx" name="TenantDataList">
import { ref, reactive, type VNode } from 'vue'
import type { ColumnProps, ProTableInstance } from '@/components/SlProTable/interface'
import { getTenantUserListApi, tenantUserTransferOwner } from '@/api/modules/managementCenter'
import ConditionFilter from '../../../resourceCenter/conditionFilter.vue'
import { Delete, Search, ArrowDown, ArrowUp } from '@element-plus/icons-vue'
import { useUserHooks } from '../../hooks/useUserHooks'
import SlMessage from '@/components/base/SlMessage'
const props = defineProps<{
  tenantId: number
}>()
const formRef = ref<any>(null)
const queryParams = ref<any>({
  tenantId: props.tenantId,
})
const formModel = reactive({
  id: '',
  account: '',
  userName: '',
  phone: '',
  userEmail: '',
  roleId: '',
  createdTime: '',
})
const proTable = ref<ProTableInstance>()
function reset() {
  formRef.value!.resetFields()
  queryParams.value = { ...formModel }
}
function search() {
  queryParams.value = { ...formModel }
}

// 是否默认折叠搜索项
const collapsed = ref(true)
const { roleList } = useUserHooks()

const formOptions = reactive([
  {
    style: 'padding: 0',
    groupItems: [
      {
        label: '用户ID',
        type: 'input',
        key: 'userId',
        span: 8,
        disabled: true,
        hidden: false,
        defaultSelect: true,
      },
      {
        label: '用户账号',
        type: 'input',
        key: 'account',
        span: 8,
        disabled: true,
        hidden: false,
        defaultSelect: true,
      },
      {
        span: 8,
        render() {
          return (
            <div style="display: flex;justify-content: flex-end;">
              <el-button type="primary" link>
                <ConditionFilter
                  formModel={formModel}
                  resourceList={formOptions[0].groupItems}
                ></ConditionFilter>
              </el-button>
              <el-button onClick={reset} icon={<Delete />}>
                重置
              </el-button>
              <el-button onClick={search} icon={<Search />} type="primary">
                搜索
              </el-button>
              <el-button
                type="primary"
                link
                class="search-isOpen"
                onClick={() => (collapsed.value = !collapsed.value)}
              >
                {collapsed.value ? '展开' : '折叠'}
                <el-icon class="el-icon--right">
                  {collapsed.value ? <ArrowDown /> : <ArrowUp />}
                </el-icon>
              </el-button>
            </div>
          )
        },
      },
      {
        label: '用户姓名',
        type: 'input',
        key: 'username',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '用户角色',
        type: 'select',
        key: 'roleId',
        options: roleList,
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '用户邮箱',
        type: 'input',
        key: 'email',
        valueField: 'lable',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '手机号',
        type: 'input',
        key: 'mobilephone',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '注册日期',
        type: 'date',
        key: 'createdTime',
        span: 16,
        disabled: false,
        hidden: true,
        props: {
          type: 'datetimerange',
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          rangeSeparator: '至',
          startPlaceholder: '开始时间',
          endPlaceholder: '结束时间',
        },
      },
    ],
  },
])
// 表格配置项
const columns = reactive<ColumnProps<any>[]>([
  { type: 'index', label: '序号', width: 55, fixed: 'left' },
  { prop: 'id', label: '用户ID', width: 120, fixed: 'left' },
  { prop: 'account', label: '用户账号', width: 150 },
  { prop: 'username', label: '用户姓名', width: 120 },
  { prop: 'roleNames', label: '用户角色', width: 120 },
  { prop: 'email', label: '用户邮箱', width: 150 },
  { prop: 'mobilephone', label: '手机号', width: 150 },
  { prop: 'createdTime', label: '注册日期', width: 150 },
  { prop: 'operation', label: '操作', width: 200, fixed: 'right', render: operationRender },
])

function operationRender({ row }: any): VNode {
  return (
    <>
      <el-button onClick={() => handleTransfer(row)} type="primary" link v-permission="Transfer">
        {row.activeAccount == 1 ? '' : '移交管理权'}
      </el-button>
    </>
  )
}

const handleTransfer = async (row: any) => {
  await ElMessageBox.confirm('确认移交管理权给所选用户吗？', '移交管理权', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      const res = await tenantUserTransferOwner({
        tenantId: props.tenantId,
        userId: row.id,
      })
      if (res.code == 200) {
        SlMessage.success('移交成功')
        proTable.value?.getTableList()
      } else {
        SlMessage.error(res.message || '移交失败')
      }
    })
    .catch(() => {
      console.log('移交取消')
    })
}
</script>
<style lang="scss" scoped>
.filter-form-con {
  margin: 8px 8px 0 8px;
  background: #fff;
  border-radius: 8px;
}
.filter-form.collapsed {
  height: 50px;
}
.filter-form {
  height: 110px;
  transition: height 0.2s;
}
</style>
