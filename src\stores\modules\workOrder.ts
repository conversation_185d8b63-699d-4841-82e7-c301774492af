import { defineStore } from 'pinia'
import { ref } from 'vue'
import { useUserStore } from './user'
import { WOC } from '@/api/config/servicePort'

// 定义工单数据接口
interface OrderCountData {
  workOrder: {
    pendingCount: number
    approvedCount: number
    rejectedCount: number
  }
  recycleOrder: {
    pendingCount: number
    approvedCount: number
    rejectedCount: number
  }
  changeOrder: {
    pendingCount: number
    approvedCount: number
    rejectedCount: number
  }
}

export const useWorkOrderStore = defineStore(
  'workOrder',
  () => {
    const userStore = useUserStore()

    // 工单数量数据
    const orderData = ref<OrderCountData>({
      workOrder: {
        pendingCount: 0,
        approvedCount: 0,
        rejectedCount: 0,
      },
      recycleOrder: {
        pendingCount: 0,
        approvedCount: 0,
        rejectedCount: 0,
      },
      changeOrder: {
        pendingCount: 0,
        approvedCount: 0,
        rejectedCount: 0,
      },
    })

    // 保存EventSource实例
    let eventSource: EventSource | null = null

    /**
     * 初始化SSE连接获取工单数量
     */
    const initOrderCountSSE = () => {
      // 如果已存在连接，先关闭
      if (eventSource) {
        eventSource.close()
        eventSource = null
      }

      // 检查token是否存在
      if (!userStore.token) {
        console.error('Token is missing')
        return
      }

      // 创建EventSource连接，将token作为查询参数传递
      eventSource = new EventSource(
        `${WOC}/sse/Statistics/orderCount?token=${encodeURIComponent(userStore.token)}`,
      )

      // 处理接收到的消息
      eventSource.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)
          if (data) {
            orderData.value = data
          }
        } catch (error) {
          console.error('Error parsing SSE message:', error)
        }
      }

      // 处理错误
      eventSource.onerror = (error) => {
        console.error('EventSource failed:', error)
        closeSSEConnection()
      }
    }

    /**
     * 关闭SSE连接
     */
    const closeSSEConnection = () => {
      if (eventSource) {
        eventSource.close()
        eventSource = null
      }
    }

    return {
      orderData,
      initOrderCountSSE,
      closeSSEConnection,
    }
  },
  {
    persist: true,
  },
)
