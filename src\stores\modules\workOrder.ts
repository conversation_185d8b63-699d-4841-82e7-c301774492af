import { defineStore } from 'pinia'
import { ref, watch } from 'vue'
import { useUserStore } from './user'
import { WOC } from '@/api/config/servicePort'

// 定义工单数据接口
interface OrderCountData {
  workOrder: {
    pendingCount: number
    approvedCount: number
    rejectedCount: number
  }
  recycleOrder: {
    pendingCount: number
    approvedCount: number
    rejectedCount: number
  }
  changeOrder: {
    pendingCount: number
    approvedCount: number
    rejectedCount: number
  }
}

export const useWorkOrderStore = defineStore(
  'workOrder',
  () => {
    const userStore = useUserStore()
    // 保存当前使用的token值
    const currentToken = ref<string>(userStore.token)

    // 工单数量数据
    const orderData = ref<OrderCountData>({
      workOrder: {
        pendingCount: 0,
        approvedCount: 0,
        rejectedCount: 0,
      },
      recycleOrder: {
        pendingCount: 0,
        approvedCount: 0,
        rejectedCount: 0,
      },
      changeOrder: {
        pendingCount: 0,
        approvedCount: 0,
        rejectedCount: 0,
      },
    })

    // 保存EventSource实例
    let eventSource: EventSource | null = null

    /**
     * 重置工单数据
     */
    const resetOrderData = () => {
      orderData.value = {
        workOrder: {
          pendingCount: 0,
          approvedCount: 0,
          rejectedCount: 0,
        },
        recycleOrder: {
          pendingCount: 0,
          approvedCount: 0,
          rejectedCount: 0,
        },
        changeOrder: {
          pendingCount: 0,
          approvedCount: 0,
          rejectedCount: 0,
        },
      }
    }

    /**
     * 初始化SSE连接获取工单数量
     */
    const initOrderCountSSE = () => {
      // 如果已存在连接，先关闭
      if (eventSource) {
        eventSource.close()
        eventSource = null
      }

      // 检查token是否存在
      if (!userStore.token) {
        console.error('Token is missing')
        return
      }

      // 更新当前使用的token
      currentToken.value = userStore.token

      // 创建EventSource连接，将token作为查询参数传递
      eventSource = new EventSource(
        `${WOC}/sse/Statistics/orderCount?token=${encodeURIComponent(userStore.token)}`,
      )

      // 处理接收到的消息
      eventSource.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)
          if (data) {
            orderData.value = data
          }
        } catch (error) {
          console.error('Error parsing SSE message:', error)
        }
      }

      // 处理错误
      eventSource.onerror = (error) => {
        console.error('EventSource failed:', error)
        closeSSEConnection()
      }
    }

    /**
     * 关闭SSE连接
     */
    const closeSSEConnection = () => {
      if (eventSource) {
        eventSource.close()
        eventSource = null
      }
    }

    // 监听token变化
    watch(
      () => userStore.token,
      (newToken) => {
        // 如果token变化且不为空
        if (newToken !== currentToken.value && newToken) {
          console.log('Token changed, reconnecting SSE...')
          // 关闭现有连接
          closeSSEConnection()
          // 重置数据
          resetOrderData()
          // 使用新token重新初始化连接
          initOrderCountSSE()
        } else if (!newToken) {
          // token为空（如登出）时，关闭连接并清空数据
          closeSSEConnection()
          resetOrderData()
        }
      },
    )

    return {
      orderData,
      initOrderCountSSE,
      closeSSEConnection,
      resetOrderData,
    }
  },
  {
    persist: true,
  },
)
