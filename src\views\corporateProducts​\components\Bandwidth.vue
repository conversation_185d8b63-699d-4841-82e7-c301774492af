<template>
  <div class="bandwidth-container">
    <el-row :gutter="20" align="middle">
      <!-- 带宽数值选择 -->
      <el-col style="display: flex" :span="24">
        <div class="bandwidth-options">
          <div
            v-for="option in bandwidthOptions"
            :key="option"
            class="bandwidth-option"
            :class="{ active: model[props.item.key] === option, disabled: props.item.disabled }"
            @click="!props.item.disabled && handleBandwidthSelect(option)"
          >
            {{ option }}
          </div>
        </div>
        <div style="width: 200px; margin-left: 24px" class="unit-selector">
          <el-input-number
            v-model="model[props.item.key]"
            :min="1"
            :max="1000"
            :step="1"
            :disabled="props.item.disabled"
          />
          <div class="unit-option" :class="{ disabled: props.item.disabled }">M</div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
const props = defineProps<{
  item: any
  form: any
}>()

// 预设的带宽选项
const bandwidthOptions = props.item.options || [1, 2, 3, 5, 10, 50, 100, 200]

const model = props.form

const handleBandwidthSelect = (value: number) => {
  model[props.item.key] = value
}
</script>

<style scoped>
.bandwidth-container {
  width: 100%;
}

.bandwidth-options {
  display: flex;
  gap: 0px;
  flex-wrap: wrap;
}

.bandwidth-option {
  min-width: 40px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #dcdfe6;
  border-right: none;
  cursor: pointer;
  font-size: 14px;
  color: #606266;
  background-color: #fff;
  transition: all 0.3s;
  padding: 0 8px;
}

.bandwidth-option:last-child {
  border-right: 1px solid #dcdfe6;
}

.bandwidth-option:hover {
  border-color: #409eff;
  color: #409eff;
  z-index: 1;
  position: relative;
}

.bandwidth-option:hover + .bandwidth-option {
  border-left: 1px solid #409eff;
}
.bandwidth-option.disabled:hover + .bandwidth-option {
  border-left: 1px solid #c0c4cc;
}

.bandwidth-option.active {
  border-color: #409eff;
  background-color: #409eff;
  color: #fff;
  z-index: 2;
  position: relative;
}

.bandwidth-option.active + .bandwidth-option {
  border-left: 1px solid #409eff;
}

.bandwidth-option.disabled {
  cursor: not-allowed;
  opacity: 0.5;
  background-color: #f5f7fa;
  color: #c0c4cc;
  border-color: #e4e7ed !important;
}

.bandwidth-option.disabled:hover {
  border-color: #e4e7ed;
  color: #c0c4cc;
  z-index: auto;
  border: 1px solid #c0c4cc;
}

.unit-selector {
  display: flex;
  border-radius: 4px;
  overflow: hidden;
  width: 200px;
}

.unit-option {
  flex: 0.4;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 14px;
  color: #606266;
  background-color: #fff;
  transition: all 0.3s;
}

.unit-option.active {
  color: #000000;
}

.unit-option.disabled {
  cursor: not-allowed;
  opacity: 0.5;
  color: #c0c4cc;
}

.unit-option.disabled:hover {
  color: #c0c4cc;
}
</style>
