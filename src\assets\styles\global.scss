html {
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: transparent;
  height: 100%;
  //禁用回弹效果
  touch-action: none;
  color: #323233;
  overscroll-behavior: none;
}

body {
  margin: 0;
  padding: 0;
  touch-action: none;
  -webkit-overflow-scrolling: touch;
  font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
  background-size: cover;
  -webkit-background-size: cover;
  -moz-background-size: cover;
  overscroll-behavior: none;
}
/* 解决 h1 标签在 webkit 内核浏览器中文字大小失效问题 */
:-webkit-any(article, aside, nav, section) h1 {
  font-size: 2em;
}
p {
  margin-block-start: 0.7em;
  margin-block-end: 0.7em;
  margin-inline-start: 0px;
  margin-inline-end: 0px;
  unicode-bidi: isolate;
}

.update-modal {
  position: absolute !important;
  top: 20% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
}
