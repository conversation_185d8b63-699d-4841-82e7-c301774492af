/**
 * @name 获取参数 - 优化版本
 */

// 通用基础参数构建器
const buildBaseParams = (data: any, productType: string) => {
  return {
    productType,
    billType: data.paymentType,
    domainCode: data.domain?.code,
    domainName: data.domain?.name,
    regionId: data.resourcePool?.id,
    regionCode: data.resourcePool?.code,
    regionName: data.resourcePool?.name,
    azId: data.az?.id,
    azCode: data.az?.code,
    azName: data.az?.name,
    tenantId: data.tenant?.id,
    tenantName: data.tenant?.name,
    applyTime: 'two_years',
  }
}

// 通用 EIP 参数构建器
const buildEipParams = (data: any) => {
  return data.isBindEip
    ? {
        eipModelList: [
          {
            bandwidth: data.bandwidth || 1,
          },
        ],
      }
    : {}
}

// 通用网络参数构建器
const buildNetworkParams = (data: any) => {
  if (!data.vpc) return {}

  return {
    planeNetworkModel: [
      {
        type: data.vpc.type,
        id: data.vpc.id,
        name: data.vpc.vpcName,
        plane: '',
        subnets: [
          {
            id: data.subnet.id,
            subnetName: data.subnet.subnetName,
            subnetIp: data.subnet.cidr,
            ipAddress: data.subnet.ipAddress,
          },
        ],
        sort: 0,
      },
    ],
  }
}

// 参数验证器
const validateParams = (data: any, type: string): string[] => {
  const errors: string[] = []

  // 通用必填字段验证
  if (!data.domain) errors.push('请选择云平台')
  if (!data.resourcePool) errors.push('请选择资源池')
  if (!data.az) errors.push('请选择可用区')
  if (!data.tenant) errors.push('请选择租户')
  if (!data.paymentType) errors.push('请选择付费类型')

  // 特定类型验证
  switch (type) {
    case 'ecs':
      if (!data.instanceName) errors.push('请输入实例名称')
      if (!data.loginName) errors.push('请输入登录用户名')
      if (!data.loginPassword) errors.push('请输入登录密码')
      if (!data.spec) errors.push('请选择规格')
      if (!data.image) errors.push('请选择镜像')
      if (!data.vpc) errors.push('请选择VPC')
      break
    case 'evs':
      if (!data.dataDisk || data.dataDisk.length === 0) errors.push('请添加数据盘')
      break
    case 'mysql':
      if (!data.instanceName) errors.push('请输入实例名称')
      if (!data.dbVersion) errors.push('请选择数据库版本')
      if (!data.spec) errors.push('请选择规格')
      break
    case 'redis':
      if (!data.instanceName) errors.push('请输入实例名称')
      if (!data.version) errors.push('请选择Redis版本')
      if (!data.spec) errors.push('请选择规格')
      break
  }

  return errors
}

/**
 *
 * @param data form
 * @param type 产品类型
 * @param flag  是否加入清单
 * @returns
 */
const useGetTheParameters = (data: any, type: string, flag: boolean = true) => {
  // 参数验证
  const validationErrors = validateParams(data, type)
  if (validationErrors.length > 0) {
    throw new Error(`参数验证失败: ${validationErrors.join(', ')}`)
  }

  let params: any = {
    tenantId: data.tenant?.id,
    tenantName: data.tenant?.name,
  }

  console.log('data', data)

  if (type === 'ecs') {
    const baseParams = buildBaseParams(data, 'ecs')
    const eipParams = buildEipParams(data)
    const networkParams = buildNetworkParams(data)

    const ecsModelList = [
      {
        ...baseParams,
        // ECS 特有参数
        flavorId: data.spec?.id,
        flavorName: data.spec?.name,
        flavorType: data.spec?.type,
        imageId: data.imageVersion.value,
        imageOs: data.image?.name,
        imageVersion: data.imageVersion.label,
        mountDataDisk: data.dataDisk?.length > 0,
        mountDataDiskList:
          data.dataDisk?.map((e: any) => ({
            sysDiskSize: e.capacity,
            sysDiskType: e.type,
            openNum: e.quantity,
          })) || [],
        sysDiskType: data.systemDisk?.[0]?.type,
        sysDiskSize: data.systemDisk?.[0]?.capacity,
        openNum: data.number || 1,
        bindPublicIp: data.isBindEip,
        ...eipParams,
        disasterRecovery: false,
        functionalModule: 'app',
        vmName: data.instanceName,
        userName: data.loginName,
        password: data.loginPassword,
        ...networkParams,
      },
    ]
    if (flag) {
      params['ecsModelList'] = ecsModelList
    } else {
      params['orderJson'] = {
        ecsList: ecsModelList,
      }
    }
  }

  if (type === 'evs') {
    const baseParams = buildBaseParams(data, 'evs')

    const evsModelList = data.dataDisk.map((item: any) => {
      return {
        ...baseParams,
        // EVS 特有参数
        evsName: item.instanceName,
        openNum: item.quantity || 1,
        sysDiskSize: item.capacity,
        sysDiskType: item.type,
      }
    })
    if (flag) {
      params['evsModelList'] = evsModelList
    } else {
      params['orderJson'] = {
        evsList: evsModelList,
      }
    }
  }

  // 新增 MySQL 支持
  if (type === 'mysql') {
    const baseParams = buildBaseParams(data, 'mysql')
    const networkParams = buildNetworkParams(data)

    const mysqlModelList = [
      {
        ...baseParams,
        // MySQL 特有参数
        dbName: data.instanceName,
        dbVersion: data.dbVersion,
        flavorId: data.spec?.id,
        flavorName: data.spec?.name,
        flavorType: data.spec?.flavorType,
        openNum: data.number || 1,
        storageSize: data.storageSize || 100,
        storageType: data.storageType || 'SSD',
        rootPassword: data.rootPassword,
        port: data.port || 3306,
        charset: data.charset || 'utf8mb4',
        disasterRecovery: data.disasterRecovery || false,
        ...networkParams,
      },
    ]

    params = {
      tenantId: data.tenant?.id,
      tenantName: data.tenant?.name,
      mysqlModelList,
    }
  }

  // 新增 Redis 支持
  if (type === 'redis') {
    const baseParams = buildBaseParams(data, 'redis')
    const networkParams = buildNetworkParams(data)

    const redisModelList = [
      {
        ...baseParams,
        // Redis 特有参数
        redisName: data.instanceName,
        version: data.version,
        flavorId: data.spec?.id,
        flavorName: data.spec?.name,
        flavorType: data.spec?.flavorType,
        openNum: data.number || 1,
        memorySize: data.memorySize || 1,
        password: data.password,
        port: data.port || 6379,
        maxConnections: data.maxConnections || 1000,
        disasterRecovery: data.disasterRecovery || false,
        ...networkParams,
      },
    ]

    params = {
      tenantId: data.tenant?.id,
      tenantName: data.tenant?.name,
      redisModelList,
    }
  }

  if (type === 'nat') {
    const baseParams = buildBaseParams(data, 'nat')
    const eipParams = buildEipParams(data)
    const networkParams = buildNetworkParams(data)

    const natModelList = [
      {
        ...baseParams,
        natName: data.instanceName,
        openNum: data.number || 1,
        bindPublicIp: data.isBindEip,
        ...eipParams,
        // NAT 网络参数稍有不同，使用单个对象而非数组
        planeNetworkModel: networkParams.planeNetworkModel?.[0] || {},
      },
    ]

    params = {
      tenantId: data.tenant?.id,
      tenantName: data.tenant?.name,
      natModelList,
    }
  }

  if (type === 'obs') {
    const baseParams = buildBaseParams(data, 'obs')

    const obsModelList = [
      {
        ...baseParams,
        obsName: data.instanceName,
        openNum: data.number || 1,
        storageDiskSize: data.capacity,
        storageDiskType: data.type,
      },
    ]

    params = {
      tenantId: data.tenant?.id,
      tenantName: data.tenant?.name,
      obsModelList,
    }
  }

  if (type === 'slb') {
    const baseParams = buildBaseParams(data, 'slb')
    const eipParams = buildEipParams(data)

    const slbModelList = [
      {
        ...baseParams,
        slbName: data.instanceName,
        openNum: data.number || 1,
        flavorId: data.slb?.id,
        flavorName: data.slb?.name,
        flavorType: data.slb?.desc,
        bindPublicIp: data.isBindEip,
        ...eipParams,
      },
    ]

    params = {
      tenantId: data.tenant?.id,
      tenantName: data.tenant?.name,
      slbModelList,
    }
  }

  if (type === 'vpc') {
    const baseParams = buildBaseParams(data, 'vpc')

    const vpcModelList = [
      {
        ...baseParams,
        vpcName: data.instanceName,
        openNum: data.number || 1,
        netRange: data.netRange,
        plane: data.plane,
        subnetDTOList:
          data.subnet?.map((item: any) => ({
            subnetName: item.subnetName,
            startIp: item.startIp,
            netmask: item.netmask,
          })) || [],
      },
    ]

    params = {
      tenantId: data.tenant?.id,
      tenantName: data.tenant?.name,
      vpcModelList,
    }
  }

  return params
}

export default useGetTheParameters
