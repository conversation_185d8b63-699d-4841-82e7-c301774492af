<template>
  <div class="health-check-step">
    <sl-form
      ref="formRef"
      show-block-title
      :options="formOptions"
      :model-value="props.formData"
      @update:model-value="handleUpdate"
      label-width="160px"
      label-position="right"
    >
    </sl-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import slForm from '@/components/form/SlForm.vue'
import type { FormInstance } from 'element-plus'

const props = defineProps({
  formData: {
    type: Object,
    required: true,
  },
})

// 表单引用
const formRef = ref<FormInstance>()

// 健康检查表单配置
const formOptions = reactive([
  {
    groupName: '健康检查配置',
    groupItems: [
      {
        label: '健康检查协议',
        type: 'select',
        key: 'healthCheckProtocol',
        options: [
          { label: 'TCP', value: 'tcp' },
          { label: 'HTTP', value: 'http' },
          { label: 'UDP', value: 'udp' },
        ],
        rules: [{ required: true, message: '请选择健康检查协议', trigger: 'change' }],
        span: 24,
        props: {
          placeholder: '请选择',
        },
      },
      {
        label: '健康检查间隔时间',
        type: 'inputNumber',
        key: 'healthCheckInterval',
        props: {
          min: 1,
          max: 40,
          step: 1,
          placeholder: '1-40秒',
        },
        rules: [{ required: true, message: '请输入健康检查间隔时间', trigger: 'blur' }],
        span: 24,
      },
      {
        label: '超时时间(秒)',
        type: 'inputNumber',
        key: 'timeout',
        props: {
          min: 2,
          max: 50,
          step: 1,
          placeholder: '1-50秒',
        },
        rules: [{ required: true, message: '请输入超时时间', trigger: 'blur' }],
        span: 24,
      },
      {
        label: '尝试次数',
        type: 'input',
        key: 'maxRetries',
        props: {
          disabled: true,
        },
        span: 24,
      },
      {
        label: '健康检查URL',
        type: 'input',
        key: 'healthCheckUrl',
        hidden: computed(() => props.formData.healthCheckProtocol !== 'http'),
        rules: [
          {
            required: computed(() => props.formData.healthCheckProtocol === 'http'),
            message: '请输入健康检查URL',
            trigger: 'blur',
          },
          {
            pattern: /^\//,
            message: '健康检查URL必须以/开头',
            trigger: 'blur',
          },
        ],
        span: 24,
        props: {
          placeholder: '请输入以/开头的URL',
        },
        description: '请输入以/开头的URL内容',
      },
      {
        label: '检查内容',
        type: 'input',
        key: 'healthCheckContent',
        hidden: computed(() => props.formData.healthCheckProtocol !== 'http'),
        rules: [
          {
            required: computed(() => props.formData.healthCheckProtocol === 'http'),
            message: '请输入检查内容',
            trigger: 'blur',
          },
        ],
        span: 24,
        props: {
          placeholder: '请输入检查内容',
        },
      },
    ],
  },
])

// 处理表单数据更新
const handleUpdate = (value: any) => {
  // 使用事件将更新后的值发送到父组件，而不是直接修改props
  Object.assign(props.formData, value)
}

// 验证表单
const validate = async () => {
  let valid = false
  await formRef.value?.validate((isValid: boolean) => {
    valid = isValid
  })
  return valid
}

// 暴露方法给父组件
defineExpose({
  validate,
})
</script>

<style scoped>
.health-check-step {
  padding: 20px 0;
}

.form-actions {
  margin-top: 24px;
  display: flex;
  justify-content: center;
  gap: 12px;
}

:deep(.el-form-item__content) {
  max-width: 400px;
}

:deep(.el-select) {
  width: 100%;
}

:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-form-item) {
  margin-bottom: 22px;
}
</style>
