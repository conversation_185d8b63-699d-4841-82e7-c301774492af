// src/views/login/verificate.vue
<template>
  <div class="login-container"></div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { oaLogin } from '@/api/modules'
import { useUserStore } from '@/stores/modules/user'
import { useRouter } from 'vue-router'
import { useKeepAliveStore } from '@/stores/modules/keepAlive'
import { useAuthStore } from '@/stores/modules/auth'
import Cookies from 'js-cookie' // 引入 js-cookie

const keepAliveStore = useKeepAliveStore()
const authStore = useAuthStore()
const userStore = useUserStore()
const router = useRouter()

const username = ref<string>('') // 初始化为空字符串
// 从 cookie 中获取 portal_userid 并赋值给 username
const getPortalUserIdFromCookie = () => {
  const portalUserId = Cookies.get('portal_userid')
  if (portalUserId) {
    username.value = portalUserId
    handleAutoLogin()
  } else {
    // 如果没有找到 portal_userid，可以选择重定向到登录页或其他处理
    window.location.href = '/login'
  }
}

// 登录处理逻辑
const handleAutoLogin = async () => {
  try {
    // 1.调取登录接口 todo
    const { entity } = await oaLogin({
      account: username.value, // 使用从 cookie 获取的 username
    })
    // 登录成功处理参数
    userStore.setUser(entity)
    userStore.setToken(entity.token)

    // 2.获取菜单列表
    await authStore.getAuthMenuList()

    // 3.清空 keepAlive 数据
    keepAliveStore.setKeepAliveName([])

    // 4.跳转到首页
    router.push({
      name: authStore.initialRouteNameGet || 'operationsOverview',
    })
  } catch (error) {
    console.log(error)
  }
}

onMounted(() => {
  getPortalUserIdFromCookie() // 获取 portal_userid 并赋值给 username
})
</script>
