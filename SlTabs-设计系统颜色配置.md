# SlTabs 设计系统颜色配置

## 最终颜色配置

根据您的要求，SlTabs 组件现在使用了符合设计系统的颜色配置：

### 背景颜色
- **内容区域背景**: 白色 `#fff`
- **容器背景**: 透明 `transparent`

### 选中状态
- **背景色**: `var(--el-bg-color-page)` (Element Plus 页面背景色)
- **文字颜色**: `var(--el-color-primary)` (主题色)
- **数量标签**: 主题色文字 + 半透明蓝色背景

### 未选中状态
- **背景色**: 透明 `transparent`
- **文字颜色**: 灰色 `#909399`
- **数量标签**: 主题色文字 + 半透明蓝色背景

## 代码实现

### 选中状态样式
```scss
&.is-active {
  position: relative;
  color: var(--el-color-primary);           // 主题色文字
  font-weight: 400;
  background: var(--el-bg-color-page);      // Element Plus 页面背景色
  border-radius: 12px 12px 0 0;
  transition: all 0.2s ease;
  
  // 圆角过渡效果
  &::before,
  &::after {
    box-shadow: 0 0 0 40px var(--el-bg-color-page); // 使用相同的页面背景色
  }
}
```

### 数量标签样式
```scss
.tab-count {
  background: rgba(72, 127, 239, 0.1);     // 半透明蓝色背景
  color: var(--el-color-primary);          // 主题色文字
  font-size: 12px;
  padding: 2px 4px;
  border-radius: 2px;
  margin-left: 2px;
}
```

### 内容区域样式
```scss
.el-tabs__content {
  background: #fff;                        // 白色背景
  min-height: 200px;
  padding: 0;
}
```

## 设计系统优势

### 1. 一致性
- ✅ **主题色统一**: 选中文字和数量标签都使用主题色
- ✅ **背景色统一**: 使用 Element Plus 的标准页面背景色
- ✅ **响应主题**: 自动适应明暗主题切换

### 2. 可维护性
- ✅ **CSS 变量**: 使用 Element Plus 的 CSS 变量
- ✅ **主题适配**: 自动适配不同的主题配置
- ✅ **设计规范**: 符合 Element Plus 的设计规范

### 3. 用户体验
- ✅ **清晰对比**: 选中状态有明显的背景色区分
- ✅ **视觉一致**: 与整个应用的设计风格保持一致
- ✅ **无障碍**: 良好的颜色对比度

## 使用示例

### 基础用法
```vue
<template>
  <sl-tabs v-model="activeTab" :tabs="tabs">
    <el-tab-pane name="pending">
      <div>待审批内容</div>
    </el-tab-pane>
    <el-tab-pane name="approved">
      <div>已审批内容</div>
    </el-tab-pane>
    <el-tab-pane name="rejected">
      <div>驳回内容</div>
    </el-tab-pane>
  </sl-tabs>
</template>

<script setup>
const activeTab = ref('approved')

const tabs = [
  { name: 'pending', label: '待审批' },
  { name: 'approved', label: '已审批' },
  { name: 'rejected', label: '驳回' },
]
</script>
```

### 带数量显示
```vue
<sl-tabs v-model="activeTab" :tabs="tabsWithCount" show-count>
  <!-- 数量标签使用主题色 -->
</sl-tabs>

<script setup>
const tabsWithCount = [
  { name: 'pending', label: '待审批', count: 15 },
  { name: 'approved', label: '已审批', count: 128 },
  { name: 'rejected', label: '驳回', count: 3 },
]
</script>
```

## 主题适配

### 明亮主题
- 选中背景: 浅灰色 (通常是 `#f5f7fa`)
- 选中文字: 蓝色主题色
- 数量标签: 蓝色主题色

### 暗黑主题
- 选中背景: 深色背景 (自动适配)
- 选中文字: 蓝色主题色 (自动适配)
- 数量标签: 蓝色主题色 (自动适配)

## 技术特点

### CSS 变量支持
- `var(--el-bg-color-page)`: 自动适配页面背景色
- `var(--el-color-primary)`: 自动适配主题色
- 支持运行时主题切换

### 圆角过渡效果
- 使用 `box-shadow` + `clip-path` 技术
- 背景色与选中状态保持一致
- 0.2s 平滑过渡动画

### Element Plus 兼容
- 完全保留原生功能
- 符合设计规范
- 支持所有主题配置

## 总结

现在的 SlTabs 组件完美符合设计系统要求：

1. **背景颜色**: 白色内容区域
2. **选中状态**: `--el-bg-color-page` 背景 + 主题色文字
3. **数量标签**: 统一使用主题色
4. **圆角过渡**: 使用相同的页面背景色
5. **主题适配**: 自动适配明暗主题

这种配置既保持了视觉的一致性，又确保了与整个应用设计系统的完美融合！
