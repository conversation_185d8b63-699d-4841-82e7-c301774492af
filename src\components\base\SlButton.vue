<template>
  <el-button v-bind="props" ref="btnRef" :loading="loading" @click="handleClick">
    <slot name="default"></slot>
    <slot name="loading"></slot>
    <slot name="icon"></slot>
  </el-button>
</template>
<script setup lang="ts">
import { ref, onMounted } from 'vue'
import type { ButtonProps } from 'element-plus'

interface ExtendedProps extends Partial<ButtonProps> {
  apiFunction?: () => Promise<any>
}

const props = defineProps<ExtendedProps>()
const loading = ref(false)
const handleClick = async () => {
  if (props.apiFunction) {
    loading.value = true
    try {
      await props.apiFunction()
    } catch (error) {
      console.error('调用失败:', error)
    } finally {
      loading.value = false
    }
  }
}
const disabled = ref(false)
const btnRef = ref<any>(null)
const shouldAddSpace = ref(false)
const size = ref('')
const type = ref('')
const insRef = ref(null)
onMounted(() => {
  if (btnRef.value) {
    disabled.value = btnRef.value.disabled
    shouldAddSpace.value = btnRef.value.shouldAddSpace
    size.value = btnRef.value.size
    type.value = btnRef.value.type
    insRef.value = btnRef.value
  }
})
defineExpose({
  disabled,
  shouldAddSpace,
  size,
  type,
  ref: insRef,
})
</script>
