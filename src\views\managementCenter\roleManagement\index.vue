<template>
  <div class="table-box">
    <template v-if="!roleId">
      <sl-page-header
        title="角色管理"
        title-line="角色管理提供了角色新增、角色编辑、权限调整等功能。"
        :icon="{
          Vnode: Platform,
          color: '#0052D9',
          size: '50px',
        }"
      ></sl-page-header>
      <div class="sl-page-content table-main">
        <SlProTable ref="proTable" :columns="columns" :request-api="getRoleListApi">
          <template #search>
            <el-button type="primary" :icon="Plus" @click="() => openDialog()" v-permission="'Add'">
              创建角色
            </el-button>
          </template>
          <template #operation="scope">
            <el-button
              type="primary"
              link
              :icon="EditPen"
              @click="openDialog(scope.row)"
              v-if="scope.row.isDefault != 1"
              v-permission="'Update'"
            >
              编辑
            </el-button>
            <el-button
              type="danger"
              link
              :icon="Delete"
              @click="deleteRole(scope.row.id)"
              v-if="scope.row.isDefault != 1"
              v-permission="'Delete'"
            >
              删除
            </el-button>
            <el-button
              type="primary"
              link
              :icon="Edit"
              @click="handleAdjust(scope.row)"
              v-permission="'PermissionAdjustment'"
            >
              权限调整
            </el-button>
          </template>
        </SlProTable>
      </div>
      <!-- 弹窗 -->
      <el-dialog
        v-model="dialogVisible"
        :title="formData.id ? '编辑角色' : '创建角色'"
        width="640px"
        :close-on-click-modal="false"
        @close="close"
      >
        <sl-form
          ref="formRef"
          v-if="dialogVisible"
          :show-block-title="false"
          v-model="formData"
          :options="options"
        >
        </sl-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="dialogVisible = false">取消</el-button>
            <el-button type="primary" @click="submit">提交</el-button>
          </span>
        </template>
      </el-dialog>
    </template>
    <template v-else>
      <PermissionsPage :role-id="roleId" @close="roleId = undefined" />
    </template>
  </div>
</template>

<script setup lang="ts" name="tenantManagement">
import {
  addRoleApi,
  deleteRoleByIdApi,
  getRoleListApi,
  updateRoleApi,
} from '@/api/modules/managementCenter'
import { Platform, Delete, EditPen, Plus, Edit } from '@element-plus/icons-vue'
import { reactive, ref } from 'vue'
import type { ColumnProps, ProTableInstance } from '@/components/SlProTable/interface'
import type { RoleListType, RoleType } from './interface/type'
import PermissionsPage from './components/PermissionsPage.vue'
import { validateNoSpecialChars } from '@/utils/validate'
import SlMessage from '@/components/base/SlMessage'
// -----------------------用户列表------------------------------
const proTable = ref<ProTableInstance>()

const columns = reactive<ColumnProps<RoleListType>[]>([
  { type: 'index', fixed: 'left', label: '序号', width: 55 },
  {
    prop: 'name',
    label: '角色名称',
    minWidth: 150,
    search: { el: 'input', checked: true, defaultDisabled: true },
  },
  { prop: 'description', label: '角色描述', minWidth: 250, search: { el: 'input' } },
  { prop: 'createdUserName', label: '创建人', width: 150, search: { el: 'input' } },
  {
    prop: 'createdTime',
    label: '创建时间',
    width: 180,
    search: {
      el: 'date-picker',
      span: 1,
      props: {
        type: 'datetimerange',
        format: 'YYYY-MM-DD HH:mm:ss',
        valueFormat: 'YYYY-MM-DD HH:mm:ss',
        rangeSeparator: '至',
        startPlaceholder: '开始时间',
        endPlaceholder: '结束时间',
      },
    },
  },
  { prop: 'operation', label: '操作', fixed: 'right', width: 250 },
])

// -----------------------编辑用户------------------------------

const options = [
  {
    groupItems: [
      {
        label: '角色名称',
        type: 'input',
        key: 'name',
        props: {
          type: 'textarea',
          rows: 1,
          maxlength: 50,
          showWordLimit: true,
        },
        span: 24,
        rules: [
          { required: true, message: '请输入角色名称', trigger: ['blur', 'change'] },
          { validator: validateNoSpecialChars, trigger: ['blur', 'change'] },
        ],
      },
      {
        label: '角色描述',
        type: 'input',
        key: 'description',
        rules: [{ required: true, message: '请输入角色描述', trigger: ['blur', 'change'] }],
        props: {
          type: 'textarea',
          rows: 4,
          maxlength: 200,
          showWordLimit: true,
        },
        span: 24,
      },
    ],
  },
]

const dialogVisible = ref(false)
const formData = ref<RoleType>({
  name: '',
  description: '',
})

const openDialog = (row?: any) => {
  dialogVisible.value = true
  if (!row) return
  // 获取详情 接口文档不清晰
  formData.value = {
    id: row.id,
    name: row.name,
    description: row.description,
  }
}

const formRef = ref<any>(null)

//修改添加
const submit = async () => {
  if (!(await formRef.value?.validate(() => true))) return
  formData.value.id ? await updateRoleApi(formData.value) : await addRoleApi(formData.value)
  dialogVisible.value = false
  SlMessage.success(formData.value.id ? '编辑成功' : '创建成功')
  proTable.value?.getTableList()
}

//删除
const deleteRole = async (id: number) => {
  await ElMessageBox.confirm('确认删除该角色吗？', '信息提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
  })
  await deleteRoleByIdApi({ id: id })
  SlMessage.success('删除成功')
  proTable.value?.getTableList()
}

// 操作全选
const roleId = ref<number>()
const handleAdjust = (row: any) => {
  roleId.value = row.id
}

//关闭弹窗
const close = () => {
  formData.value = {
    name: '',
    description: '',
  }
  formRef.value?.resetFields()
}
</script>

<style lang="scss" scoped>
.sl-page-content {
  // 减去头部的标题高度 - 不减去会超出屏幕
  overflow: hidden;
}
</style>
