<template>
  <div id="ResourceFilter">
    <el-popover placement="bottom-end" :width="400" trigger="hover">
      <template #reference>
        <el-icon @click="handleSelectedShow"><Filter /></el-icon>
      </template>
      <div class="list">
        <div class="list-header">
          <div class="list-title">条件筛选(最多5个)</div>
        </div>
        <div class="list-items">
          <el-checkbox-group
            v-model="selectKeys"
            :min="1"
            :max="5"
            @change="handleCheckedSystemChange"
          >
            <template v-for="resource in resourceList" :key="resource.key">
              <el-checkbox
                @change="(i) => handleCheckedChange(i, resource)"
                v-if="resource.key"
                :value="resource.key"
                :model-value="!resource.hidden"
                :disabled="resource.disabled"
              >
                {{ resource.label }}
              </el-checkbox>
            </template>
          </el-checkbox-group>
        </div>
      </div>
    </el-popover>
  </div>
</template>
<script setup lang="ts">
import { ref } from 'vue'
import { Filter } from '@element-plus/icons-vue'
interface Iprops {
  resourceList: {
    [key: string]: any
  }[]
  formModel: any
}
const { resourceList = [], formModel } = defineProps<Iprops>()
const selectKeys = ref(resourceList.filter((item) => item.defaultSelect).map((item) => item.key))
const checkAll = ref(false)
const isIndeterminate = ref(true)
const popoverVisible = ref(false)
const handleSelectedShow = () => {
  popoverVisible.value = true
}
const handleCheckedSystemChange = (value: string[]) => {
  const checkedCount = value.length
  checkAll.value = checkedCount === resourceList.length
  isIndeterminate.value = checkedCount > 0 && checkedCount < resourceList.length
}
const handleCheckedChange = (i: any, item: any) => {
  item.hidden = !i
  if (!i) {
    if (formModel[item.key]) {
      clearFormModelProp(formModel, item.key)
    }
  }
}

function clearFormModelProp(form: any, key: string) {
  form[key] = ''
}
</script>
<style lang="scss" scoped>
#ResourceFilter {
  width: 32px;
  height: 29px;
  font-size: 16px;
  display: flex;
  border-radius: 4px;
  justify-content: center;
  align-items: center;
  background-color: #f0f2f7;
  cursor: pointer;
  &:hover {
    background-color: #e1e3e9;
  }
}
.list {
  .list-header {
    .list-title {
      font-weight: 600;
    }
    .list-subtitle {
      margin-top: 10px;
      color: #999;
    }
  }
  .list-items {
    display: flex;
    margin-top: 10px;
    flex-wrap: wrap;
    &::after {
      content: '';
      flex: auto;
    }
    .list-item {
      height: 30px;
      padding: 0 10px;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #666;
      background-color: #f5f6f8;
      margin-right: 10px;
      margin-bottom: 10px;
      border-radius: 5px;
      cursor: pointer;
      white-space: nowrap;
      &.active {
        color: white;
        background-color: var(--el-color-primary);
      }
    }
  }
  .list-footer {
    text-align: right;
  }
}
</style>
