import type {
  BusinessSystemListType,
  GetCloudHostUsageListParamsType,
  CloudHostUsageListType,
  // EvsUsageListType,
  GetResourceNumberListParamsType,
  ResourceNumberListType,
  // GetBusinessSystemDetailsParamsType,
  GetPropertyConfigParamsType,
  GetPropertyConfigType,
  PropertyConfigFormDataType,
  GetNoticeListType,
} from '@/views/computingPowerMap/tenantView/interface/type'
import http from '@/api'
import { USER, PERFORMANCE, WOC } from '../config/servicePort'

// ------------------------租户视图-------------------------
/**
 * 获取业务系统列表
 */
export const getBusinessSystemListApi = (config: any = {}) =>
  http.post<BusinessSystemListType[]>(WOC + '/business/query/busiSystemlist', config)

/**
 * 新增业务系统
 * @param config
 * @returns
 */
export const businessSystemSubmit = (config: any) => http.post(USER + '/app/create', config)

/**
 * 编辑业务系统
 * @param config
 * @returns
 */
export const businessSystemUpdate = (config: any) => http.put(USER + '/app/info/update', config)

/**
 * 删除业务系统
 * @param id
 * @returns
 */
export const businessSystemDelete = (id: number | string) => http.post(USER + '/app/delete/' + id)

/**
 * 获取云主机使用情况
 */
export const getCloudHostUsageListApi = (config: GetCloudHostUsageListParamsType) => {
  return http.get<CloudHostUsageListType[]>(PERFORMANCE + '/view/vm/tenant', config)
}

/**
 * 获取租户视图资源数据
 * @param config
 * @returns
 */
export const getResourceNumberListApi = (config: GetResourceNumberListParamsType) => {
  return http.post<Array<ResourceNumberListType>>(WOC + '/home/<USER>/resourceOpen', config)
}

/**
 * @name 配置管理-配置属性列表
 */
export const getPropertyConfigApi = (config: GetPropertyConfigParamsType) => {
  return http.get<GetPropertyConfigType>(WOC + '/config/queryCurrentUserConfig', config, {
    loading: true,
  })
}
/**
 * @name 配置管理-修改,加配置属性列表
 */
export const submitPropertyConfigApi = (config: PropertyConfigFormDataType) =>
  http.post(WOC + '/config/upset', config, { loading: true })

/**
 * 获取公告列表
 * @returns
 */
export const getNoticeListApi = () => http.get<GetNoticeListType[]>(WOC + '/config/listNotice')
