// import SlLayout from '@/views/SlLayout.vue'
import type { RouteRecordRaw } from 'vue-router'

const managementCenterRouter: RouteRecordRaw[] = [
  {
    path: '/tenantManagement',
    component: () => import('@/views/managementCenter/tenantManagement/index.vue'),
    name: 'tenantManagement',
    meta: {
      title: '租户管理',
    },
  },
  {
    path: '/userManagement',
    component: () => import('@/views/managementCenter/userManagement/index.vue'),
    name: 'userManagement',
    meta: {
      title: '用户管理',
    },
  },
  {
    path: '/externalUserManagement',
    component: () => import('@/views/managementCenter/externalUserManagement/index.vue'),
    name: 'externalUserManagement',
    meta: {
      title: '外部用户管理',
    },
  },
  {
    path: '/roleManagement',
    component: () => import('@/views/managementCenter/roleManagement/index.vue'),
    name: 'roleManagement',
    meta: {
      title: '角色管理',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/permissionManagement',
    component: () => import('@/views/managementCenter/permissionManagement/index.vue'),
    name: 'permissionManagement',
    meta: {
      title: '权限管理',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/systemLogs',
    component: () => import('@/views/managementCenter/systemLogs/index.vue'),
    name: 'systemLogs',
    meta: {
      title: '系统日志',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/operationalAnalysis',
    component: () => import('@/views/managementCenter/operationalAnalysis/index.vue'),
    name: 'operationalAnalysis',
    meta: {
      title: '运营分析',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/projectManagement',
    component: () => import('@/views/managementCenter/projectManagement/index.vue'),
    name: 'projectManagement',
    meta: {
      title: '项目管理',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/reportCenter',
    component: () => import('@/views/managementCenter/reportCenter/list.vue'),
    name: 'reportCenter',
    meta: {
      title: '报表中心',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/orderCenter',
    component: () => import('@/views/managementCenter/orderCenter/list.vue'),
    name: 'orderCenter',
    meta: {
      title: '订单中心',
      icon: 'icon-gongdan',
    },
  },
]

export default managementCenterRouter
