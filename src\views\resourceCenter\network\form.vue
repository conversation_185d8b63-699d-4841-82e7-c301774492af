<template>
  <div class="resourcerequest">
    <sl-page-header
      :title="`${formModel.operationName}(${formModel.goodsName})`"
      :icon="{
        Vnode: Platform,
        color: '#0052D9',
        size: '20px',
      }"
      :show-back="true"
      :back="{
        title: '返回列表',
        function: () => router.push('networkList'),
      }"
    >
    </sl-page-header>
    <div class="resourcerequestbox">
      <div class="onebox">
        <div class="oneboxleft">
          <el-scrollbar class="scroll-view">
            <sl-form
              ref="slFormRef"
              show-block-title
              :options="formOptions"
              :model-value="formModel"
              style="overflow: hidden"
            >
            </sl-form>
            <sl-base-tabs
              show-count
              v-if="showTab"
              :tabs="tabs"
              v-model="activeTab"
              @update:modelValue="tabClick"
            ></sl-base-tabs>
            <template v-if="tabs.length">
              <tabItem
                v-for="tab in tabs"
                :key="tab.name"
                :tab-item="tab"
                :network-id="networkId"
                :plane="tab.name"
                v-show="tab.name === activeTab"
                :active-tab="activeTab"
              />
            </template>
            <el-empty v-else description="请先选择网络平面" :image-size="200">
              <el-button v-if="opType !== 'view'" type="primary" disabled>添加网络配置</el-button>
            </el-empty>
          </el-scrollbar>
        </div>
      </div>
      <div class="onefooter">
        <el-button @click="goBack">取消</el-button>
        <sl-button type="primary" v-if="opType !== 'view'" :api-function="submitfromdata">
          提交申请
        </sl-button>
      </div>
    </div>
  </div>
</template>
<script setup lang="tsx">
import { Platform } from '@element-plus/icons-vue'
import slForm from '@/components/form/SlForm.vue'
import { reactive, ref, computed, provide, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import SlMessage from '@/components/base/SlMessage'
import { useGlobalDicStore } from '@/stores/modules/dic'
import { useSelectLabel } from '@/hooks/useSelectLabel'
import { displayBackOrder } from '@/api/modules/resourecenter'
import useModel from './baseModel'
import {
  networkCreate,
  updateNetworkStatus,
  updateVlanStatus,
  getNetworkDetail,
  networkCreateBatch,
  addNetworkSubnetBatch,
} from '@/api/modules/resourecenter'
import SlButton from '@/components/base/SlButton.vue'
import tabItem from './tabItem.vue'
import type { IBaseFormProvider, IFormRefs, ISubmitData } from './types'
import { getAzListDic } from '@/api/modules/dic'
import useBusiSystemOptions from '../hooks/useBusiSystemOptions'
import useModuleIdOptions from '../hooks/useModuleIdOptions'
import { getCloudPlatformDic, getCloudTypeDic, getResourcePoolsDic } from '@/api/modules/dic'

let opType: 'createWithOrder' | 'addSubnet' | 'view' | 'create' = 'view'
const showTab = ref(true)
const activeTab = ref('承载网')
// tab栏切换
const tabs: any = ref([])
const tabClick = (name: string) => {
  router.replace({
    query: {
      ...route.query,
      activeTab: name,
    },
  })
}
const model = useModel()
const formModel = reactive(model)

const { busiSystemOptions } = useBusiSystemOptions()
const { moduleIdOptions } = useModuleIdOptions(formModel)

useSelectLabel(
  () => busiSystemOptions,
  () => formModel.busiSystemId,
  (option) => {
    formModel.businessSysName = option.label
  },
)
useSelectLabel(
  () => moduleIdOptions,
  () => formModel.moduleId,
  (option) => {
    formModel.moduleName = option.label
  },
)

const globalDic = useGlobalDicStore()

const slFormRef = ref()
const router = useRouter()
const { getDic } = globalDic
const route = useRoute()
const orderId = route.query.orderId as string
const networkId = (route.query.networkId as string) || ''
const addSubnet = (route.query.addSubnet as string) || ''
const planes = ref<string[]>([])
const jsonStr = ref<string>('')

const apiMap = {
  createWithOrder: networkCreate,
  create: networkCreateBatch,
  addSubnet: addNetworkSubnetBatch,
}
if (orderId) {
  opType = 'createWithOrder'
} else if (networkId && addSubnet) {
  opType = 'addSubnet'
} else if (networkId) {
  opType = 'view'
} else {
  opType = 'create'
}
console.log(opType)

// 是否创新池
const isCxc = computed(() => formModel.domainCode === 'plf_prov_nwc_zj_nfvo')
// select 配置项
const resourcePoolOptions = ref<any>([])
const cloudTypeOptions = ref<any>([])
const cloudPlatformOptions = ref<any>([])

async function displayBackOrederInfo(workOrderId: string) {
  const { entity } = await displayBackOrder({ workOrderId })
  formModel.domainCode = entity.domainCode
  formModel.domainName = entity.domainName
  formModel.catalogueDomainCode = entity.catalogueDomainCode
  formModel.catalogueDomainName = entity.catalogueDomainName

  resourcePoolOptions.value = entity.resourcePoolModelList.map((e: any) => ({
    value: e.regionId,
    label: e.regionName,
    regionCode: e.regionCode,
  }))
  getTabs(entity)
}
const azOptions = ref<any>([])

const netWorks: IFormRefs[] = []

function collectSubmits(form: IFormRefs) {
  netWorks.push(form)
}
const baseFormProvider = ref<IBaseFormProvider>({
  regionCode: '',
  collectSubmits,
  isCxc: isCxc.value,
  opType,
  uuids: [],
  catalogueDomainCode: '',
  domainCode: '',
})
provide('baseFormProvider', baseFormProvider)
watch(
  [
    () => formModel.regionCode,
    () => isCxc.value,
    () => planes.value,
    () => jsonStr.value,
    () => formModel.catalogueDomainCode,
    () => formModel.domainCode,
  ],
  ([regionCode, isCxc, planes, jsonStr, catalogueDomainCode, domainCode]) => {
    baseFormProvider.value.regionCode = regionCode
    baseFormProvider.value.isCxc = isCxc
    baseFormProvider.value.planes = planes
    baseFormProvider.value.jsonStr = jsonStr
    baseFormProvider.value.catalogueDomainCode = catalogueDomainCode
    baseFormProvider.value.domainCode = domainCode
  },
)

useSelectLabel(
  () => resourcePoolOptions,
  () => formModel.resourcePoolId,
  (option) => {
    formModel.regionCode = option.regionCode
    if (opType !== 'view') {
      formModel.azCode = ''
      azOptions.value = []
    }
    if (formModel.resourcePoolId) {
      getAzOptions(formModel.resourcePoolId)
    }
  },
)
const initData = async () => {
  const getResourcePools = async (domainCode: string = '') => {
    const { entity } = await getResourcePoolsDic({
      domainCode,
    })
    resourcePoolOptions.value = entity.map((e: any) => ({
      value: e.id,
      label: e.name,
      regionCode: e.code,
    }))
  }
  // 云类型
  const getCloudType = async () => {
    const { entity } = await getCloudTypeDic(null)
    cloudTypeOptions.value = entity
      .filter((e: any) => !['cloudst_group_moc', 'cloudst_group_ctn'].includes(e.code))
      .map((e: any) => ({
        value: e.code,
        label: e.name,
      }))
  }
  // 云平台
  const getCloudPlatform = async (catalogueDomainCode: string = '') => {
    const { entity } = await getCloudPlatformDic({
      parentCode: catalogueDomainCode,
    })
    cloudPlatformOptions.value = entity.map((e: any) => ({
      value: e.code,
      label: e.name,
    }))
  }
  useSelectLabel(
    () => cloudTypeOptions,
    () => formModel.catalogueDomainCode,
    (option) => {
      formModel.catalogueDomainName = option.label

      formModel.domainName = ''
      formModel.domainCode = ''
      cloudPlatformOptions.value = []

      formModel.resourcePoolId = ''
      formModel.regionCode = ''
      resourcePoolOptions.value = []
      if (formModel.catalogueDomainCode) {
        getCloudPlatform(option.value)
      }
    },
  )
  useSelectLabel(
    () => cloudPlatformOptions,
    () => formModel.domainCode,
    (option) => {
      formModel.domainName = option.label
      formModel.resourcePoolId = ''
      formModel.regionCode = ''
      resourcePoolOptions.value = []
      if (formModel.domainCode) {
        getResourcePools(option.value)
      }
    },
  )
  getCloudType()
  // 所属租户名称、id
  showTab.value = false
  watch(
    () => formModel.planeValue,
    (newValue) => {
      if (newValue) {
        tabs.value = [
          {
            label: newValue,
            name: formModel.planeValue,
            count: 0,
          },
        ]
        activeTab.value = tabs.value[0].name
      } else {
        tabs.value = []
      }
    },
    { immediate: true },
  )
}
// 如果有orderId 请求goodinfoList回显数据
if (orderId) {
  displayBackOrederInfo(orderId)
} else if (networkId) {
  displayBackDetail(networkId)
} else {
  initData()
}

function getTabs(goodsDetails: any) {
  const {
    ecsModelList = [],
    gcsModelList = [],
    mysqlModelList = [],
    redisModelList = [],
  } = goodsDetails
  tabs.value = [
    ...new Set(
      [
        ecsModelList.map((e: any) => e.plane.split(',')),
        gcsModelList.map((e: any) => e.plane.split(',')),
        mysqlModelList.map((e: any) => e.plane.split(',')),
        redisModelList.map((e: any) => e.plane.split(',')),
      ].flat(2),
    ),
  ].map((e: any) => ({
    label: e,
    name: e,
    count: 0,
  }))

  activeTab.value = tabs.value[0].name
}

async function displayBackDetail(networkId: string) {
  const { entity } = await getNetworkDetail({ networkId })
  jsonStr.value = entity.detail
  baseFormProvider.value.uuids = entity.uuids || []
  formModel.domainCode = entity.domainCode
  formModel.domainName = entity.domainName
  formModel.catalogueDomainCode = entity.catalogueDomainCode
  formModel.catalogueDomainName = entity.catalogueDomainName
  formModel.regionCode = entity.regionCode
  formModel.resourcePoolName = entity.regionName
  formModel.azCode = entity.azCode
  formModel.azName = entity.azName
  formModel.functionalModule = entity.functionalModule
  formModel.planeValue = entity.plane
  formModel.moduleId = entity.moduleId
  formModel.moduleName = entity.moduleName
  formModel.busiSystemId = entity.businessSysId
  formModel.businessSysName = entity.businessSysName
  // 设置平面
  tabs.value = [
    {
      label: entity.plane,
      name: entity.plane,
      count: 0,
    },
  ]
  activeTab.value = tabs.value[0].name
}

async function getAzOptions(regionId: string) {
  const { entity } = await getAzListDic({ regionId: regionId })
  if (entity) {
    azOptions.value = entity.map((e: any) => ({
      value: e.code,
      label: e.name,
    }))
  }
}

const formOptions = reactive([
  {
    style: 'margin:0;border-radius:0;',
    gutter: 10,
    groupName: '基本信息',
    groupItems: [
      ...(['view', 'createWithOrder', 'addSubnet'].includes(opType)
        ? [
            {
              label: '云类型',
              type: 'text',
              key: 'catalogueDomainName',
              span: 8,
            },
            {
              label: '云平台',
              type: 'text',
              key: 'domainName',
              span: 8,
            },
          ]
        : [
            {
              label: '云类型',
              type: 'select',
              key: 'catalogueDomainCode',
              options: cloudTypeOptions,
              span: 8,
            },
            {
              label: '云平台',
              type: 'select',
              key: 'domainCode',
              options: cloudPlatformOptions,
              span: 8,
            },
          ]),
      ...(['view', 'addSubnet'].includes(opType)
        ? [
            {
              label: '资源池',
              type: 'text',
              key: 'resourcePoolName',
              span: 8,
            },
            {
              label: '可用区',
              type: 'text',
              key: 'azName',
              span: 8,
            },
            {
              label: '功能模块',
              type: 'select',
              key: 'functionalModule',
              options: getDic('functionalModule'),
              span: 8,
              props: {
                select: {
                  disabled: true,
                },
              },
            },
            {
              label: '业务系统',
              type: 'text',
              key: 'businessSysName',
              span: 8,
            },
            {
              label: '所属业务模块',
              type: 'text',
              key: 'moduleName',
              span: 8,
            },
            {
              label: '网络平面',
              type: 'text',
              key: 'planeValue',
              span: 8,
            },
          ]
        : [
            {
              label: '资源池',
              type: 'select',
              key: 'resourcePoolId',
              options: resourcePoolOptions,
              span: 8,
              rules: {
                required: true,
                message: '请选择资源池',
                trigger: ['blur', 'change'],
              },
            },
            {
              label: '可用区',
              type: 'select',
              key: 'azCode',
              options: azOptions,
              rules: [{ required: true, message: '请选择可用区', trigger: ['blur', 'change'] }],
              span: 8,
            },
            {
              label: '功能模块',
              type: 'select',
              key: 'functionalModule',
              options: getDic('functionalModule'),
              span: 8,
              props: {
                select: {
                  disabled: ['view', 'addSubnet'].includes(opType),
                },
              },
              rules: {
                required: true,
                message: '请选择功能模块',
                trigger: ['blur', 'change'],
              },
            },
            {
              label: '业务系统',
              type: 'select',
              key: 'busiSystemId',
              options: busiSystemOptions,
              hidden: ['view', 'createWithOrder'].includes(opType),
              props: {
                select: {
                  disabled: ['view', 'addSubnet'].includes(opType),
                },
              },
              onChange() {
                formModel.moduleId = ''
              },
              span: 8,
              rules: [{ required: true, message: '请选择业务系统', trigger: ['change'] }],
              suffixSlot: true,
            },
            {
              label: '所属业务模块',
              type: 'select',
              key: 'moduleId',
              hidden: ['view', 'createWithOrder'].includes(opType),
              props: {
                select: {
                  disabled: ['view', 'addSubnet'].includes(opType),
                },
              },
              options: moduleIdOptions,
              span: 8,
              rules: [{ required: true, message: '请选择所属业务模块', trigger: ['change'] }],
            },
            {
              label: '网络平面',
              type: 'select',
              key: 'planeValue',
              hidden: ['view', 'createWithOrder'].includes(opType),
              props: {
                select: {
                  disabled: ['addSubnet'].includes(opType),
                },
              },
              span: 8,
              options: getDic('plane'),
              rules: [{ required: true, message: '请选择网络平面', trigger: ['blur', 'change'] }],
            },
          ]),
    ],
  },
])

async function updateVlan(instanceIds: string[]) {
  const params = {
    regionCode: formModel.regionCode,
    status: '1',
    instanceId: instanceIds,
  }
  updateVlanStatus(params)
}
async function updateIp(instanceIds: string[]) {
  const params = {
    regionCode: formModel.regionCode,
    status: '1',
    instanceId: instanceIds,
    ipVersion: 'IP3',
  }
  updateNetworkStatus(params)
}
const submitfromdata = async () => {
  const validate = await slFormRef.value.validate()
  if (!validate) return

  const submitData: (ISubmitData | null)[] = []
  const updateIps: string[] = []
  const updateVlans: string[] = []
  const formRefs = netWorks.flatMap((network) =>
    Array.isArray(network.value) ? network.value : [],
  )
  const res = await Promise.all(
    formRefs.filter((ele) => ele.ref).map(async (ele) => await ele.ref.submitfromdata()),
  )
  res.forEach((ele) => {
    if (ele?.updateIps) {
      updateIps.push(...ele.updateIps)
    }
    if (ele?.updateVlans) {
      updateVlans.push(...ele.updateVlans)
    }
    if (ele?.submitdata) {
      submitData.push(ele.submitdata)
    } else {
      submitData.push(null)
    }
  })
  if (submitData.length > 0) {
    console.log(submitData)

    if (!submitData.every((e: any) => !!e)) {
      SlMessage.error('请填写必填字段或确定新增子网信息')
      return
    }
    let params = {}
    if (opType === 'addSubnet') {
      params = {
        networkId: networkId,
        subnets: submitData[0]!.subnets,
        detail: submitData[0]!.detail,
      }
    } else {
      params = {
        regionCode: formModel.regionCode,
        orderId,
        systemSource: formModel.systemSource,
        functionalModule: formModel.functionalModule,
        azCode: formModel.azCode,
        catalogueDomainCode: formModel.catalogueDomainCode,
        catalogueDomainName: formModel.catalogueDomainName,
        domainCode: formModel.domainCode,
        domainName: formModel.domainName,
        networks: submitData,
        tenantId: formModel.tenantId,
        tenantName: formModel.tenantName,
        businessSysId: formModel.busiSystemId,
        businessSysName: formModel.businessSysName,
        moduleId: formModel.moduleId,
        moduleName: formModel.moduleName,
      }
    }

    try {
      const entity = await apiMap[opType as keyof typeof apiMap](params)
      if (entity.code == 200) {
        SlMessage.success('开通成功')
        if (!isCxc.value) updateVlan(updateVlans)
        updateIp(updateIps)
        goBack()
      } else {
        SlMessage.error('提交失败')
      }
    } catch (error) {
      console.log(error)
    }
  }
}
function goBack() {
  router.go(-1)
}
</script>
<style scoped>
:deep(.split-message) {
  word-break: break-all;
  font-size: 14px;
  background: #fff;
  padding: 6px 12px;
  border-radius: 4px;
  border-left: 4px solid var(--el-color-success);
  color: var(--el-color-success);
}
:deep(.split-message.error) {
  color: var(--el-color-error);
  border-left: 4px solid var(--el-color-error);
}
:deep(.subnet-row) {
  .goods-del-btn {
    position: absolute;
    top: -7px;
    right: -6px;
    z-index: 10;
    color: var(--el-color-danger);
    font-size: 1.2rem;
    cursor: pointer;
    display: none;
    display: none;
  }
}
:deep(.subnet-row:hover) {
  .goods-del-btn {
    display: block;
  }
}
.scroll-view {
  height: calc(100vh - 188px);
}

.resourcerequest {
  .resourcerequestbox {
    .onebox {
      display: flex;
      .oneboxleft {
        flex-grow: 3;
        margin: 1px 0px 8px 0;
      }

      .oneboxright {
        flex-grow: 1;
        flex-basis: 0;
        background: #ffffff;
        margin: 8px 8px 8px 0;
        border-radius: 6px;
        min-width: 300px;
      }
    }

    .onefooter {
      height: 48px;
      margin: 0 8px;
      background: #ffffff;
      border-radius: 8px 8px 8px 8px;
      display: flex;
      align-items: center;
      justify-content: right;
      padding: 0 14px;
    }
  }
}
</style>
