import { fileURLToPath, URL } from 'node:url'
import { createHtmlPlugin } from 'vite-plugin-html'
import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
// import vueDevTools from 'vite-plugin-vue-devtools'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import viteCompression from 'vite-plugin-compression'
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd())
  const uuid =
    Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)
  return {
    plugins: [
      vue(),
      vueJsx(),
      // vueDevTools(),
      AutoImport({
        resolvers: [ElementPlusResolver()],
      }),
      Components({
        resolvers: [ElementPlusResolver()],
      }),
      viteCompression(),
      createHtmlPlugin({
        minify: false,
        inject: {
          data: {
            comment: `<!-- uuid==${uuid}==uuid -->`,
          },
        },
      }),
    ],
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url)),
      },
    },
    css: {
      preprocessorOptions: {
        scss: {
          api: 'modern-compiler',
          additionalData: `@use "@/assets/styles/variables.scss" as *;`,
        },
      },
    },
    server: {
      host: '0.0.0.0', // 配置为 0.0.0.0 以允许局域网设备访问
      port: 8081, // 指定开发服务器的端口
      open: false, // 自动打开浏览器
      strictPort: false, // 端口被占用时允许切换到其他端口
      proxy: {
        // 配置代理
        '^/oac_api': {
          target: env.VITE_PROXY_TARGET_OAC, // mock 代理的目标地址
          changeOrigin: true, // 修改源头为目标地址
          rewrite: (path) => path.replace(/^\/oac_api/, env.VITE_PROXY_REWRITE_OAC), // mock 重写路径
        },
        '^/cloud_api': {
          target: env.VITE_PROXY_TARGET_CLOUD, // 代理的目标地址
          changeOrigin: true, // 修改源头为目标地址
          rewrite: (path) => path.replace(/^\/cloud_api/, env.VITE_PROXY_REWRITE_CLOUD), // mock 重写路径
        },
        '^/v1_cloud': {
          target: env.VITE_PROXY_TARGET_V1CLOUD, // 代理的目标地址
          changeOrigin: true, // 修改源头为目标地址
          rewrite: (path) => path.replace(/^\/v1_cloud/, env.VITE_PROXY_REWRITE_V1CLOUD), // mock 重写路径
        },
        '^/user_api': {
          target: env.VITE_PROXY_TARGET_USER, // 代理的目标地址
          changeOrigin: true, // 修改源头为目标地址
          rewrite: (path) => path.replace(/^\/user_api/, env.VITE_PROXY_REWRITE_USER), // mock 重写路径
        },
        '^/performance_api': {
          target: env.VITE_PROXY_TARGET_PERFORMANCE, // 代理的目标地址
          changeOrigin: true, // 修改源头为目标地址
          rewrite: (path) => path.replace(/^\/performance_api/, env.VITE_PROXY_REWRITE_PERFORMANCE), // mock 重写路径
        },
        '^/collect_api': {
          target: env.VITE_PROXY_TARGET_COLLECT, // 代理的目标地址
          changeOrigin: true, // 修改源头为目标地址
          rewrite: (path) => path.replace(/^\/collect_api/, env.VITE_PROXY_REWRITE_COLLECT), // mock 重写路径
        },
        '^/woc_api': {
          target: env.VITE_PROXY_TARGET_WOC, // mock 代理的目标地址
          changeOrigin: true, // 修改源头为目标地址
          rewrite: (path) => path.replace(/^\/woc_api/, env.VITE_PROXY_REWRITE_WOC), // mock 重写路径
        },
      },
      cors: true, // 允许跨域
    },
  }
})
