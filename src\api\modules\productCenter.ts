import http from '@/api'
import { WOC } from '../config/servicePort'
import { changeDateFormat } from '@/utils'

/**
 * 区域树节点类型
 */
export interface RegionTreeNode {
  code: string | number
  name: string
  children?: RegionTreeNode[]
  disabled?: boolean
  [key: string]: any
}

/**
 * @name 获取区域树结构
 */
export const getRegionTree = () => {
  return http.post(WOC + '/region/tree')
}

/**
 * @name 获取模板列表
 */
export const getTemplatePage = (config: any) => {
  return http.post(WOC + '/dagTemplate/page', config, changeDateFormat(config, ['createTime']))
}

/**
 * @name 创建模板
 */
export const updateTemplateRegion = (config: any) => {
  return http.post(WOC + '/dagTemplate/updateRegion', config)
}

/**
 * @name 复制模板
 */
export const copyTemplate = (config: any) => {
  return http.post(WOC + '/dagTemplate/copy', config)
}
