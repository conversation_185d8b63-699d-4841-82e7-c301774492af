<template>
  <sl-form
    class="goods-info-form"
    size="small"
    ref="slFormRef"
    label-position="top"
    :options="goodsInfoOptions"
    :model-value="formModel"
  >
    <template #sysDiskSlot="{ form, item }">
      <div style="display: flex; flex-grow: 1">
        <el-select style="flex: 1" clearable v-model="form[item.key][0]">
          <el-option
            :key="option.value"
            v-for="option in item.options"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
        <el-input-number
          v-model="form[item.key][1]"
          v-bind="item.props"
          style="margin: 0 4px; min-width: 90px"
        >
        </el-input-number>
        GB
      </div>
    </template>
    <template #disasterRecoverySlot="{ form, item }">
      <el-radio-group v-bind="item.props?.group" v-model="form[item.key]">
        <el-radio
          v-bind="item.props?.radio"
          v-for="option in item.options"
          :key="option[item.valueField || 'value']"
          :value="option[item.valueField || 'value']"
        >
          {{ option[item.labelField || 'label'] }}
        </el-radio>
      </el-radio-group>
      <div v-if="form[item.key] === '0'" class="disaster-recovery-tip">
        <el-tooltip content="容灾选“否”可能导致业务系统高可用缺失!" placement="top">
          容灾选“否”可能导致业务系统高可用缺失!
        </el-tooltip>
      </div>
    </template>
  </sl-form>
</template>
<script setup lang="ts">
import { reactive, ref } from 'vue'
import { type IGcsModel, useGcsModel } from '@/views/resArrangement/model'
import { validateGoodsName } from '@/views/resourceCenter/utils'
import { useGlobalDicStore } from '@/stores/modules/dic'
import slForm from '@/components/form/SlForm.vue'
import { useFlavorTree } from '@/views/resourceCenter/hooks/useFlavorTree'
import { useImageTree } from '@/views/resourceCenter/hooks/useImageTree'

const imageOptions = useImageTree()
const flavorOptions = useFlavorTree('gcs')

const globalDic = useGlobalDicStore()
const { getDic } = globalDic

const props = defineProps<{
  goods: IGcsModel
}>()

const formModel = reactive(Object.assign(useGcsModel(), props.goods))
const slFormRef = ref()

const validateDiskTypeSiskSize = (rule: any, value: any, callback: any) => {
  if (!formModel.sysDisk[0]) {
    callback(new Error('请选择系统盘类型'))
  } else if (!formModel.sysDisk[1]) {
    callback(new Error('请输入系统盘大小'))
  } else {
    callback()
  }
}

const validateInstanceSpecificationMemorySize = (rule: any, value: any, callback: any) => {
  if (!formModel.gcs || formModel.gcs.length === 0) {
    callback(new Error('请选择实例规格'))
  } else {
    callback()
  }
}

const goodsInfoOptions = reactive([
  {
    style: 'margin:0;padding:0;padding-top: 18px;',
    gutter: 40,
    groupItems: [
      {
        label: '主机名称',
        type: 'input',
        key: 'instanceName',
        span: 24,
        props: {
          maxlength: 64,
          showWordLimit: true,
        },
        rules: [
          { required: true, message: '请输入主机名称', trigger: ['blur', 'change'] },
          { validator: validateGoodsName, trigger: ['blur', 'change'] },
        ],
      },
      {
        label: '实例规格',
        type: 'cascader',
        key: 'gcs',
        options: flavorOptions,
        span: 24,
        required: true,
        rules: [{ validator: validateInstanceSpecificationMemorySize, trigger: 'change' }],
      },
      {
        label: '操作系统',
        type: 'cascader',
        key: 'imageOs',
        options: imageOptions,
        span: 24,
        rules: [{ required: true, message: '请选择操作系统', trigger: ['blur', 'change'] }],
      },
      {
        label: '系统盘',
        type: 'slot',
        slotName: 'sysDiskSlot',
        key: 'sysDisk',
        options: getDic('sysDisk'),
        props: {
          min: 40,
          max: 500,
          step: 1,
        },
        span: 24,
        required: true,
        rules: [{ validator: validateDiskTypeSiskSize, trigger: ['blur', 'change'] }],
      },
      {
        label: '是否容灾',
        type: 'slot',
        slotName: 'disasterRecoverySlot',
        key: 'disasterRecovery',
        options: getDic('trueOrFalse'),
        span: 24,
        rules: [{ required: true, message: '请选择是否容灾', trigger: ['blur', 'change'] }],
      },
      {
        label: '申请时长',
        type: 'select',
        key: 'time',
        span: 24,
        options: getDic('time'),
        rules: [{ required: true, message: '请选择申请时长', trigger: ['blur', 'change'] }],
      },
      {
        label: '网络平面',
        type: 'select',
        key: 'planeValue',
        span: 24,
        props: {
          select: {
            multiple: true,
            collapseTags: true,
            collapseTagsTooltip: true,
          },
        },
        options: getDic('plane'),
        rules: [{ required: true, message: '请选择网络平面', trigger: ['blur', 'change'] }],
      },
    ],
  },
])
const validateForm = async () => {
  const res = await slFormRef.value.validate()
  return res
}
const submitForm = async () => {
  const goods = props.goods
  Object.assign(goods, formModel)
}
defineExpose({
  validateForm,
  submitForm,
})
</script>
<style scoped>
.disaster-recovery-tip {
  margin-left: 8px;
  color: var(--el-color-warning);
  flex-basis: auto;
  position: absolute;
  top: 18px;
}
.goods-del-btn {
  position: absolute;
  top: -7px;
  right: -6px;
  z-index: 10;
  color: var(--el-color-danger);
  font-size: 1.2rem;
  cursor: pointer;
  display: none;
}
.goods-info-form:hover .goods-del-btn {
  display: block;
}

.evs-item {
  width: 100%;
  margin-bottom: 15px;
  .evs-item-content {
    width: 100%;
    display: flex;
    align-items: center;
  }
  &:last-child {
    margin-bottom: 0;
  }
  .evs-icons {
    display: flex;
    align-items: center;
    margin-left: 10px;
    .el-icon {
      cursor: pointer;
    }
  }
}
</style>
