<template>
  <el-badge
    :badge-style="{ fontSize: '10px' }"
    :style="style"
    :value="count"
    :offset="[6, -2]"
    :show-zero="false"
    class="item"
    @click="$router.push('/corporate/shoppingList')"
  >
    <el-icon style="font-size: 18px">
      <Document />
    </el-icon>
    <span class="text">购物清单</span>
  </el-badge>
</template>
<script setup lang="ts">
import { Document } from '@element-plus/icons-vue'
import { getCorporateTemSaveCount } from '@/api/modules/resourecenter'
import eventBus from '@/utils/eventBus'
import { ref, onUnmounted, watch } from 'vue'
import { useRoute } from 'vue-router'

const style = ref('')
const route = useRoute()
watch(
  () => route.path,
  (newVal) => {
    if (newVal === '/corporate/shoppingList') {
      style.value = 'color: var(--el-color-primary)'
    } else {
      style.value = ''
    }
  },
  { immediate: true },
)
const count = ref(0)
const getCorporateShoppingListCount = async () => {
  const { entity, code } = await getCorporateTemSaveCount()
  if (code === 200) {
    count.value = entity
  }
}
getCorporateShoppingListCount()
eventBus.on('corporateShoppingList:updateCount', getCorporateShoppingListCount)
onUnmounted(() => {
  eventBus.off('corporateShoppingList:updateCount', getCorporateShoppingListCount)
})
</script>
<style scoped lang="scss">
.item {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  margin-right: 8px;
  &:hover {
    color: var(--el-color-primary) !important;
  }
  .text {
    font-size: 14px;
    margin-left: 4px;
  }
}
</style>
