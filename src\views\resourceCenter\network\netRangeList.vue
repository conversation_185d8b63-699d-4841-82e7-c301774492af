<template>
  <SlProTable
    highlight-current-row
    :columns="columns"
    style="min-height: 300px"
    :request-api="ipRangeList"
    :init-param="queryParams"
    :current-change="currentChange"
    @selection-change="currentChange"
    :data-callback="dataCallback"
    hidden-table-header
    row-key="instanceId"
  >
  </SlProTable>
</template>
<script setup lang="tsx" name="dataList">
import { reactive, ref } from 'vue'
import type { ColumnProps, TypeProps } from '@/components/SlProTable/interface'
import { ipRangeList } from '@/api/modules/resourecenter'
const { queryParams, multCheck = false } = defineProps<{
  queryParams?: any
  multCheck?: boolean
}>()
const emit = defineEmits(['currentChange'])
const currentChange = (rowOrRows: any, oldCurrentRow?: any) => {
  console.log(rowOrRows)
  if (multCheck) {
    emit('currentChange', rowOrRows)
  } else {
    radioValue.value = rowOrRows.instanceId
    emit('currentChange', rowOrRows, oldCurrentRow)
  }
}

function dataCallback(entity: any) {
  entity?.records.forEach((ele: any) => {
    ele.ip = `${ele.prefix}/${ele.mask}`
  })
  return entity
}
const radioValue = ref()
// 表格配置项
const columns = reactive<ColumnProps<any>[]>([
  ...(multCheck
    ? [
        {
          type: 'selection' as TypeProps,
          width: 55,
        },
      ]
    : [
        {
          render: ({ row }: { row: any }) => {
            return (
              <el-radio
                modelValue={radioValue.value}
                value={row.instanceId}
                size="large"
              ></el-radio>
            )
          },
          label: '选择',
          width: 55,
        },
      ]),

  { prop: 'ip', label: 'IP地址', width: 200 },
  { prop: 'ipVersion', label: 'IP版本', width: 120 },
  { prop: 'relatedPool', label: '资源池', width: 120 },
  { prop: 'type', label: '类型', width: 150 },
  { prop: 'vpn', label: 'VPN' },
])
</script>
