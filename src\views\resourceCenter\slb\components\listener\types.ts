/**
 * 服务器列表数据类型定义
 */
export interface ServerItem {
  name: string
  cloud: string
  resourcePool: string
  vpc: string
  publicIp: string
  privateIp: string
  status: string
  port: string
}

/**
 * 监听表单数据类型定义
 */
export interface ListenerFormData {
  // 协议监听数据
  protocol: string // tcp/http/https/unp
  listenerName: string
  port: string | number
  serverCertificate?: string // 仅https
  caCertificate?: string // 仅https
  lbAlgorithm: string // ROUND_ROBIN/LEAST_CONNECTION
  sessionPersistence: boolean
  cookieHandling: string // INSERT/REWRITE

  // 健康检查数据
  healthCheckProtocol: string // tcp/http/unp
  healthCheckInterval: number
  timeout: number
  maxRetries: number
  healthCheckUrl: string
  healthCheckContent: string

  // 服务器组数据
  serverGroupType: number // 0-虚拟服务器组/1-主备服务器组
  serverGroupId: string
  serverGroupName: string

  // 其他可能的字段
  [key: string]: any
}
