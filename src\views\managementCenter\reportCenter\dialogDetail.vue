<template>
  <SlDialog :show-confirm="false" v-model="dialogVisible" title="报表详情" width="70%">
    <sl-form ref="slFormRef" :options="formOptions" :model-value="formData">
      <template #reportField>
        <el-checkbox-group v-model="formData.exportFields" class="checkbox-group">
          <el-checkbox
            v-for="field in fields"
            :key="field.value"
            disabled
            :label="field.label"
            :value="field.value"
            class="checkbox-item"
          >
            {{ field.label }}
          </el-checkbox>
        </el-checkbox-group>
      </template>
    </sl-form>
  </SlDialog>
</template>

<script setup lang="tsx" name="dialogForm">
import { ref, reactive } from 'vue'
import { getReportDetail } from '@/api/modules/managementCenter'
import { useRoute } from 'vue-router'

const route = useRoute()
const dialogVisible = ref(true)

const slFormRef = ref<any>(null)
const formData = ref<any>({
  reportName: '',
  reportTypeName: '',
  statTypeName: '',
  domainCodesName: '',
  regionsName: '',
  timeRange: '',
  exportFields: [],
  createTime: '',
  creator: '',
  statusDesc: '',
})

const getDetail = async () => {
  const { entity }: any = await getReportDetail({ id: route.query.id })
  const queryCondition = JSON.parse(entity.queryCondition)
  formData.value.reportName = queryCondition.reportName
  formData.value.reportTypeName = queryCondition.reportTypeName
  formData.value.statTypeName = queryCondition.statTypeName
  formData.value.domainCodesName = queryCondition.domainCodesName
  formData.value.regionsName = queryCondition.regionsName
  formData.value.timeRange = queryCondition.timeRange
  formData.value.exportFields = queryCondition.exportFields
  formData.value.createTime = entity.createTime
  formData.value.creator = entity.creator
  formData.value.status = entity.status
}

getDetail()
const fields = [
  {
    label: '平台',
    value: 'platform',
  },
  {
    label: '虚拟资源池名称',
    value: 'virtualRegionName',
  },
  {
    label: '计算服务器节点数',
    value: 'computeNodeNum',
  },
  {
    label: '虚拟机数量',
    value: 'vmNum',
  },
  {
    label: 'vCPU总量',
    value: 'vcpuTotal',
  },
  {
    label: 'vCPU可用总量',
    value: 'vcpuAvailable',
  },
  {
    label: '内存总量',
    value: 'memoryTotal',
  },
  {
    label: '内存可用总量(GB)',
    value: 'memoryAvailable',
  },
  {
    label: '存储总量(GB)',
    value: 'storageTotal',
  },
  {
    label: '存储可用总量(GB)',
    value: 'storageAvailable',
  },
  {
    label: 'vCPU已分配',
    value: 'vcpuAllocated',
  },
  {
    label: '内存已分配(GB)',
    value: 'memoryAllocated',
  },
  {
    label: '存储已分配(GB)',
    value: 'storageAllocated',
  },
  {
    label: 'vCPU剩余',
    value: 'vcpuRemaining',
  },
  {
    label: '内存剩余(GB)',
    value: 'memoryRemaining',
  },
  {
    label: '存储剩余(GB)',
    value: 'storageRemaining',
  },
  {
    label: 'vCPU分配率',
    value: 'vcpuAllocationRate',
  },
  {
    label: '内存分配率',
    value: 'memoryAllocationRate',
  },
  {
    label: '存储分配率',
    value: 'storageAllocationRate',
  },
  {
    label: 'vCPU使用率均值',
    value: 'vcpuUsageAvg',
  },
  {
    label: '内存使用率均值',
    value: 'memoryUsageAvg',
  },
  {
    label: '存储使用率均值',
    value: 'storageUsageAvg',
  },
  {
    label: 'vCPU使用率峰值',
    value: 'vcpuUsagePeak',
  },
  {
    label: '内存使用率峰值',
    value: 'memoryUsagePeak',
  },
  {
    label: '存储使用率峰值',
    value: 'storageUsagePeak',
  },
]

const formOptions = reactive([
  {
    style: 'margin: 0;padding: 0',
    gutter: 20,
    groupItems: [
      {
        label: '报表名称',
        type: 'text',
        key: 'reportName',
        span: 8,
      },
      {
        label: '报表类型',
        type: 'text',
        key: 'reportTypeName',
        span: 8,
      },
      {
        label: '时间粒度',
        type: 'text',
        key: 'statTypeName',
        span: 8,
      },
      {
        label: '云域',
        type: 'text',
        key: 'domainCodesName',
        span: 8,
      },
      {
        label: '资源池',
        type: 'text',
        key: 'regionsName',
        span: 8,
      },
      {
        label: '时间范围',
        type: 'text',
        key: 'timeRange',
        span: 8,
      },
      {
        label: '创建时间',
        type: 'text',
        key: 'createTime',
        span: 8,
      },
      {
        label: '创建人',
        type: 'text',
        key: 'creator',
        span: 8,
      },
      {
        label: '状态',
        type: 'text',
        getter: (formModel: any) => {
          console.log(formModel)

          return formModel.status === 0 ? '生成中' : formModel.status === 1 ? '完 成' : '失 败'
        },
        span: 8,
      },

      {
        type: 'slot',
        label: '报表字段',
        slotName: 'reportField',
        span: 24,
      },
    ],
  },
])
</script>

<style lang="scss" scoped>
.checkbox-group {
  display: flex;
  flex-wrap: wrap;

  .checkbox-item {
    width: 175px;
    margin-right: 10px;
    margin-bottom: 10px;
  }
}
</style>
