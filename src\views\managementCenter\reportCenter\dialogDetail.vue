<template>
  <SlDialog :show-confirm="false" v-model="dialogVisible" title="报表详情" width="70%">
    <sl-form ref="slFormRef" :options="formOptions" :model-value="formData">
      <template #reportField>
        <el-checkbox-group v-model="formData.exportFields" class="checkbox-group">
          <el-checkbox
            v-for="field in fields"
            :key="field.value"
            disabled
            :label="field.label"
            :value="field.value"
            class="checkbox-item"
          >
            {{ field.label }}
          </el-checkbox>
        </el-checkbox-group>
      </template>
    </sl-form>
  </SlDialog>
</template>

<script setup lang="tsx" name="dialogForm">
import { ref, reactive, onMounted } from 'vue'
import { getReportDetail } from '@/api/modules/managementCenter'
import fields from './field'

const dialogVisible = ref(true)

const { detailId } = defineProps<{
  detailId: string
}>()

const slFormRef = ref<any>(null)
const formData = ref<any>({
  reportName: '',
  reportTypeName: '',
  statTypeName: '',
  domainCodesName: '',
  regionsName: '',
  timeRange: '',
  exportFields: [],
  createTime: '',
  creator: '',
  statusDesc: '',
})
const getDetail = async () => {
  const { entity }: any = await getReportDetail({ id: detailId })
  const queryCondition = JSON.parse(entity.queryCondition)
  formData.value.reportName = queryCondition.reportName
  formData.value.reportTypeName = queryCondition.reportTypeName
  formData.value.statTypeName = queryCondition.statTypeName
  formData.value.domainCodesName = queryCondition.domainCodesName
  formData.value.regionsName = queryCondition.regionsName
  formData.value.timeRange = queryCondition.timeRange
  formData.value.exportFields = queryCondition.exportFields
  formData.value.createTime = entity.createTime
  formData.value.creator = entity.creator
  formData.value.status = entity.status
}

onMounted(() => {
  getDetail()
})

const formOptions = reactive([
  {
    style: 'margin: 0;padding: 0',
    gutter: 20,
    groupItems: [
      {
        label: '报表名称',
        type: 'text',
        key: 'reportName',
        span: 8,
      },
      {
        label: '报表类型',
        type: 'text',
        key: 'reportTypeName',
        span: 8,
      },
      {
        label: '时间粒度',
        type: 'text',
        key: 'statTypeName',
        span: 8,
      },
      {
        label: '平台',
        type: 'text',
        key: 'domainCodesName',
        span: 8,
      },
      {
        label: '资源池',
        type: 'text',
        key: 'regionsName',
        span: 8,
      },
      {
        label: '时间范围',
        type: 'text',
        key: 'timeRange',
        span: 8,
      },
      {
        label: '创建时间',
        type: 'text',
        key: 'createTime',
        span: 8,
      },
      {
        label: '创建人',
        type: 'text',
        key: 'creator',
        span: 8,
      },
      {
        type: 'slot',
        label: '报表字段',
        slotName: 'reportField',
        span: 24,
      },
    ],
  },
])
</script>

<style lang="scss" scoped>
.checkbox-group {
  display: flex;
  flex-wrap: wrap;

  .checkbox-item {
    width: 175px;
    margin-right: 10px;
    margin-bottom: 10px;
  }
}
</style>
