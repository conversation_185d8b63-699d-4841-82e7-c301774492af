function useModel() {
  return {
    id: '',
    // @产品名称
    instanceName: '',
    // @功能模块
    functionalModule: '',
    // @资源池id
    resourcePoolId: '',
    // @资源池code
    regionCode: '',
    // 可用区
    azCode: '',
    // @租户名称
    tenantName: '',
    tenantId: 0,
    // @产品类型
    productType: 'vpc',
    // @ productType 对应的产品名称
    goodsName: 'VPC',
    resourcePoolName: '',
    azName: '',
    // @网络平面
    planeValue: '私网地址',
    // @业务系统
    busiSystemId: '',
    businessSysName: '',
    // @所属业务模块
    moduleId: '',
    moduleName: '',
    // 云类型
    catalogueDomainCode: '',
    catalogueDomainName: '',
    // 云平台
    domainCode: '',
    domainName: '',
  }
}

export default useModel
