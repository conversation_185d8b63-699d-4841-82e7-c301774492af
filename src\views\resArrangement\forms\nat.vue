<template>
  <sl-form
    class="goods-info-form"
    size="small"
    ref="slFormRef"
    :options="goodsInfoOptions"
    :model-value="formModel"
    label-position="top"
  >
  </sl-form>
</template>
<script setup lang="ts">
import { reactive, ref } from 'vue'
import { type INatModel, useNatModel } from '../model'
import { validateGoodsName } from '@/views/resourceCenter/utils'
import { useGlobalDicStore } from '@/stores/modules/dic'
import slForm from '@/components/form/SlForm.vue'
import { useFlavorTree } from '@/views/resourceCenter/hooks/useFlavorTree'

const flavorOptions = useFlavorTree('nat')

const globalDic = useGlobalDicStore()
const { getDic } = globalDic

const props = defineProps<{
  goods: INatModel
}>()

const slFormRef = ref()

const formModel = reactive(Object.assign(useNatModel(), props.goods))

const goodsInfoOptions = reactive([
  {
    gutter: 40,
    style: 'margin:0;padding:0;padding-top: 18px;',
    groupItems: [
      {
        label: '网关名称',
        type: 'input',
        key: 'instanceName',
        span: 24,
        props: {
          maxlength: 64,
          showWordLimit: true,
        },
        rules: [
          { required: true, message: '请输入网关名称', trigger: ['blur', 'change'] },
          { validator: validateGoodsName, trigger: ['blur', 'change'] },
        ],
      },
      {
        label: '实例规格',
        type: 'select',
        key: 'nat',
        options: flavorOptions,
        span: 24,
        required: true,
        rules: {
          required: true,
          message: '请选择实例规格',
          trigger: ['blur', 'change'],
        },
      },
      {
        label: '申请时长',
        type: 'select',
        key: 'time',
        span: 24,
        options: getDic('time'),
        rules: [{ required: true, message: '请选择申请时长', trigger: ['blur', 'change'] }],
      },
    ],
  },
])
const validateForm = async () => {
  const res = await slFormRef.value.validate()
  return res
}
const submitForm = async () => {
  const goods = props.goods
  Object.assign(goods, formModel)
}
defineExpose({
  validateForm,
  submitForm,
})
</script>
<style scoped>
.goods-del-btn {
  position: absolute;
  top: -7px;
  right: -6px;
  z-index: 10;
  color: var(--el-color-danger);
  font-size: 1.2rem;
  cursor: pointer;
  display: none;
}
.goods-info-form:hover .goods-del-btn {
  display: block;
}

.evs-item {
  width: 100%;
  margin-bottom: 15px;
  .evs-item-content {
    width: 100%;
    display: flex;
    align-items: center;
  }
  &:last-child {
    margin-bottom: 0;
  }
  .evs-icons {
    display: flex;
    align-items: center;
    margin-left: 10px;
    .el-icon {
      cursor: pointer;
    }
  }
}
</style>
