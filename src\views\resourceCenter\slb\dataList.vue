<template>
  <SlProTable
    ref="proTable"
    highlight-current-row
    :columns="columns"
    style="min-height: 300px"
    :request-api="getResourceList"
    :init-param="queryParams"
    @selection-change="handleSelectionChange"
    hidden-table-header
    row-key="goodsOrderId"
  >
  </SlProTable>

  <!-- 资源变更弹窗 -->
  <ResourceChangeDialog
    v-model:visible="changeDialogVisible"
    resource-type="slb"
    :selected-resources="selectedResources"
    :allowed-change-types="allowedChangeTypes"
    @confirm="handleConfirm"
  />

  <!-- 资源延期弹窗 -->
  <ResourceChangeDialog
    v-model:visible="delayDialogVisible"
    resource-type="slb"
    :selected-resources="selectedResources"
    :is-delay="true"
    @confirm="handleConfirm"
  />
</template>
<script setup lang="tsx" name="dataList">
import { ref, reactive, type VNode } from 'vue'
import type { ColumnProps, ProTableInstance } from '@/components/SlProTable/interface'
import { getResourceList, cycleBinCreate } from '@/api/modules/resourecenter'
import eventBus from '@/utils/eventBus'
import ResourceChangeDialog from '../components/ResourceChangeDialog.vue'
import type { ResourceChangeType } from '../types'
import { ElMessage } from 'element-plus'
import { useResourceChange } from '../hooks/useResourceChange'
import { useRecycleValidation } from '../hooks/useRecycleValidation'
import { useVerifyThePromptBox } from '@/hooks/useVerifyThePromptBox'
import { useRouter } from 'vue-router'
const { queryParams, isBindDialog } = defineProps<{
  queryParams: Record<string, any>
  isBindDialog?: boolean
}>()

const { validateResources } = useResourceChange()

const emit = defineEmits(['selectDevice'])
// 表格配置项
const columns = reactive<ColumnProps<any>[]>([
  queryParams?.hideSelection
    ? { type: 'index', label: '序号', width: 55 }
    : { type: 'selection', width: 55 },
  { type: 'index', label: '序号', width: 55, fixed: 'left' },
  {
    prop: 'deviceName',
    label: '负载均衡名称',
    width: 220,
    fixed: 'left',
    render: ({ row }: any): VNode => {
      return (
        <el-button type="primary" link onClick={() => handleRowClick(row)}>
          {row.deviceName}
        </el-button>
      )
    },
  },
  { prop: 'deviceId', label: '资源ID', width: 200 },
  { prop: 'ip', label: 'VIP', width: 200 },
  { prop: 'spec', label: '实例规格', width: 250 },
  { prop: 'eip', label: '弹性公网IP', width: 150 },
  { prop: 'bandWidth', label: '带宽大小', width: 120 },
  { prop: 'vpcName', label: 'VPC名称', width: 150 },
  { prop: 'subnetName', label: '子网名称', width: 150 },
  { prop: 'subnetCidr', label: '子网网段', width: 150 },
  { prop: 'applyTime', label: '申请时长', width: 100 },
  { prop: 'tenantName', label: '租户', width: 150 },
  { prop: 'businessSysName', label: '业务系统', width: 150, filter: true },
  { prop: 'cloudPlatform', label: '所属云', width: 150, filter: true },
  { prop: 'resourcePoolName', label: '资源池', width: 150, filter: true },
  { prop: 'orderCode', label: '工单编号', width: 150 },
  { prop: 'effectiveTime', label: '开通时间', width: 150 },
  { prop: 'expireTime', label: '到期时间', width: 150 },
  { prop: 'billId', label: '计费号', width: 150 },
  { prop: 'deviceStatusCn', label: '状态', width: 100 },
  { prop: 'recoveryStatusCn', label: '回收状态', width: 100 },
  { prop: 'changeStatusCn', label: '变更状态', width: 100 },
  { prop: 'applyUserName', label: '申请人', width: 100 },
])

if (isBindDialog) {
  columns.push({
    prop: 'operation',
    label: '操作',
    width: 100,
    fixed: 'right',
    render: bindDialogOperationRender,
  })
}

function bindDialogOperationRender({ row }: any): VNode {
  return (
    <>
      <el-button
        onClick={() => handleSelectDevice(row)}
        type="primary"
        disabled={row.recoveryStatus !== 0 || row.changeStatusCn !== '未变更'}
        link
      >
        绑定
      </el-button>
    </>
  )
}

const proTable = ref<ProTableInstance>()

// 回收功能
// const currentRecycleIdsList = ref<string[]>([])

// 使用回收校验钩子函数
const { validateRecycle, validateChange } = useRecycleValidation()

const handleCreateRecycle = async (goodsItems: any[]) => {
  const res = await cycleBinCreate({
    goodsType: 'slb',
    goodsItems,
  })
  if (res.code != 200) {
    return ElMessage.error(res.message || '接口请求失败')
  }
  ElMessage.success('已加入回收站')
  proTable.value?.getTableList()
  proTable.value?.clearSelection()
  eventBus.emit('cycleBins:updateCount')
}

// 批量回收功能
const handleBatchRecycle = async () => {
  const selectedList = proTable.value?.selectedList || []

  // 使用新的验证逻辑
  if (validateRecycle(selectedList, 'slb')) {
    const arr = await useVerifyThePromptBox(selectedList)
    if (!arr || !arr.length) return
    handleCreateRecycle(arr)
  }
}

// 多选数据
const multipleSelection = ref<any[]>([])
const changeDialogVisible = ref(false)
const delayDialogVisible = ref(false)
const selectedResources = ref<any[]>([])

// SLB允许的变更类型
const allowedChangeTypes: ResourceChangeType[] = ['instance_spec_change', 'bandwidth_expand']

// 处理资源变更
const handleResourceChange = async () => {
  if (!multipleSelection.value.length) {
    ElMessage.warning('请选择要变更的资源')
    return
  }
  selectedResources.value = multipleSelection.value

  // 使用新的校验逻辑
  if (validateChange(selectedResources.value)) {
    // 使用钩子函数中的validateResources进行业务系统一致性校验
    const isValid = await validateResources(selectedResources.value)
    if (isValid) {
      // 校验通过，才显示弹窗
      changeDialogVisible.value = true
    }
  }
}

// 处理资源延期
const handleResourceDelay = async () => {
  if (!multipleSelection.value.length) {
    ElMessage.warning('请选择要延期的资源')
    return
  }
  selectedResources.value = multipleSelection.value

  // 使用新的校验逻辑
  if (validateChange(selectedResources.value)) {
    // 使用钩子函数中的validateResources进行业务系统一致性校验
    const isValid = await validateResources(selectedResources.value)
    if (isValid) {
      // 校验通过，才显示弹窗
      delayDialogVisible.value = true
    }
  }
}

// 处理确认
const handleConfirm = () => {
  proTable.value?.getTableList()
}

// 选择变化事件
const handleSelectionChange = (selection: any[]) => {
  multipleSelection.value = selection
}

const router = useRouter()
const handleRowClick = (row: any) => {
  router.push({
    path: '/slbDetail',
    query: {
      id: row.id,
      showPbServerTab: row.cloudPlatform === '融合边缘云-VMware' ? 'true' : 'false',
    },
  })
}

// 选择设备
const handleSelectDevice = (row: any) => {
  emit('selectDevice', row)
}

defineExpose({
  handleBatchRecycle,
  handleResourceChange,
  handleResourceDelay,
})
</script>
