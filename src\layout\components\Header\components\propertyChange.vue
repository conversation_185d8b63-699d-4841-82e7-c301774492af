<template>
  <el-badge
    :badge-style="{ fontSize: '10px' }"
    :style="style"
    :value="count"
    :offset="[6, -2]"
    :show-zero="false"
    class="item"
    @click="$router.push('/propertyChange')"
  >
    <el-icon style="font-size: 18px">
      <EditPen />
    </el-icon>
    <span class="text">变更区</span>
  </el-badge>
</template>
<script setup lang="ts">
import { EditPen } from '@element-plus/icons-vue'
import { propertyChangeCount } from '@/api/modules/resourecenter'
import eventBus from '@/utils/eventBus'
import { ref, onUnmounted, watch } from 'vue'
import { useRoute } from 'vue-router'

const style = ref('')
const route = useRoute()
watch(
  () => route.path,
  (newVal) => {
    if (newVal === '/propertyChange') {
      style.value = 'color: var(--el-color-primary)'
    } else {
      style.value = ''
    }
  },
  { immediate: true },
)
const count = ref(0)
const getpropertyChangeCount = async () => {
  const { entity, code } = await propertyChangeCount()
  if (code === 200) {
    count.value = entity
  }
}
getpropertyChangeCount()
eventBus.on('propertyChange:updateCount', getpropertyChangeCount)
onUnmounted(() => {
  eventBus.off('propertyChange:updateCount', getpropertyChangeCount)
})
</script>
<style scoped lang="scss">
.item {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  margin-right: 8px;
  &:hover {
    color: var(--el-color-primary) !important;
  }
  .text {
    font-size: 14px;
    margin-left: 4px;
  }
}
</style>
