import type { GlobalState } from '@/layout/interface/type'
import { defineStore } from 'pinia'

type ObjToKeyValArray<T> = {
  [K in keyof T]: [K, T[K]]
}[keyof T]
export const useGlobalStore = defineStore({
  id: 'sl-global',
  // 修改默认值之后，需清除 localStorage 数据
  state: (): GlobalState => ({
    // element 组件大小
    assemblySize: 'default',
    // 当前页面是否全屏
    maximize: false,
    // 主题颜色
    primary: '',
    // 折叠菜单
    isCollapse: false,
    // 菜单手风琴
    accordion: false,
    // 页脚
    footer: false,
  }),
  getters: {},
  actions: {
    // Set GlobalState
    setGlobalState(...args: ObjToKeyValArray<GlobalState>) {
      this.$patch({ [args[0]]: args[1] })
    },
  },
})
