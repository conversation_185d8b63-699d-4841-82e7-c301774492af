<template>
  <div class="table-box approvalDetailsPage">
    <sl-page-header
      :title="titleMap[opStatus]"
      :icon="{
        Vnode: Platform,
        color: '#0052D9',
        size: '20px',
      }"
      :show-back="true"
      :back="{
        title: '返回变更工单',
        function: handleBack,
      }"
    >
    </sl-page-header>
    <el-scrollbar wrap-class="shopping-carts-scroll-view" class="scroll-view">
      <!--步骤条 -->
      <div class="steps-con">
        <sl-steps :class="{ closed: orderDesc.orderStatus == 'CLOSE' }"></sl-steps>
      </div>
      <!-- Tabs 组件 -->
      <sl-base-tabs :show-count="false" :tabs="tabs" v-model="activeTab" v-if="currentTask">
      </sl-base-tabs>

      <div
        v-show="['home', 'profile'].includes(activeTab)"
        class="sl-page-content"
        v-if="currentTask"
      >
        <BasicInformation
          ref="basicInformationRef"
          v-show="activeTab == 'home'"
          :order-desc="orderDesc"
          :btn-auth="btnAuth"
        />

        <!-- 资源信息  -->
        <ResourceInformation
          ref="resourceInformationRef"
          v-show="activeTab == 'profile'"
          :order-desc="orderDesc"
          :btn-auth="btnAuth"
          v-model:disabled-btn="disabledBtn"
          @refresh="inintData"
        />
      </div>
      <div v-if="activeTab == 'settings'" class="sl-page-content table-main auditTable">
        <!-- 审核日志 -->
        <AuditTable :order-desc="orderDesc" />
      </div>
    </el-scrollbar>

    <!-- 按钮组件 -->
    <div
      v-if="pageStatus"
      v-show="['home', 'profile'].includes(activeTab)"
      class="sl-page-content page-footer"
    >
      <div class="sl-card">
        <RenderBtn />
      </div>
    </div>
    <!-- 审核组件 -->

    <SlAuditDialog
      v-show="visible"
      ref="SlAuditDialogRef"
      v-model:visible="visible"
      back-path="/changeWorkOrder"
      :audit-api="changeWorkOrdersAuditApi"
    />
  </div>
</template>

<script setup lang="tsx">
import SlBaseTabs from '@/components/base/SlBaseTabs.vue'
import { Platform } from '@element-plus/icons-vue'
import SlSteps from '@/components/SlSteps/index.vue'
import { computed, provide, ref, nextTick } from 'vue'
import {
  getChangeWorkOrdersDetailApi,
  changeWorkOrdersAuditApi,
} from '@/api/modules/approvalCenter'
import { useRoute, useRouter } from 'vue-router'
import useWorkOrder from '@/hooks/useWorkOrder'
import BasicInformation from './components/BasicInformation.vue'
import ResourceInformation from './components/ResourceInformation/index.vue'
import AuditTable from '@/views/approvalCenter/components/AuditTable.vue'
import type { AuditDialogInstance } from '@/components/SlAuditDialog/interface'
import type { ChangeBtnsType, ChangeWorkorderType } from '../interface/type'
import { getCloudLeaderDic } from '@/api/modules/dic'

let orderDesc = ref<ChangeWorkorderType>({
  activityTask: undefined,
})

const titleMap = {
  '1': '变更工单审批',
  '2': '变更工单查看',
  '3': '变更工单编辑',
  '4': '变更工单创建',
} as const

type IOpStatus = keyof typeof titleMap

const route = useRoute()
const router = useRouter()
const workOrderId = route.query?.workOrderId
const opStatus = route.query?.opStatus as IOpStatus
provide('workOrderId', workOrderId)
/**
 *  @readonly获取页面状态用来做表单权限控制
 *  */

const pageStatus = ref(opStatus === '1') //1:编辑 0:查看
provide('pageStatus', pageStatus)

let flag = ref(true) //判断是否第一次初始化参数
const inintData = async () => {
  if (!workOrderId) return
  // 获取工单详情
  const res = await getChangeWorkOrdersDetailApi({ workOrderId: String(workOrderId) })
  if (!res.entity) return
  // 给予默认值
  orderDesc.value = {
    ...res.entity,
  }
  getWorkOrderNode(orderDesc.value.activityTask!)
  nextTick(() => {
    resourceInformationRef.value?.initData(orderDesc.value, flag.value)
    basicInformationRef.value?.initData(orderDesc.value)
    flag.value = false
  })
}
inintData()

// 当前节点
const { allTasks, currentTask, currentSort, historyTasks, getWorkOrderNode } = useWorkOrder()
provide('allTasks', allTasks)
provide('currentSort', currentSort)
provide('currentTask', currentTask)
provide('historyTasks', historyTasks)

const handleBack = () => {
  router.push('/changeWorkOrder')
}

const tabs = [
  { name: 'home', label: '基础信息' },
  { name: 'profile', label: '资源信息' },
  { name: 'settings', label: '审核日志' },
]
const activeTab = ref('profile') // 默认激活的 tab

// 按钮权限控制
const btnAuth = computed<ChangeBtnsType>(() => {
  return {
    // 发起工单按钮权限控制
    user_task: currentTask.value === 'user_task',
    // 架构负责人审核按钮权限控制
    schema_administrator: currentTask.value === 'schema_administrator',
    // 三级业务部门领导审核按钮权限控制
    business2_depart_leader: currentTask.value === 'business2_depart_leader',
    // 二级业务部门领导审核按钮权限控制
    business_depart_leader: currentTask.value === 'business_depart_leader',
    // 三级云资源部领导审核按钮权限控制
    cloud_leader: currentTask.value === 'cloud_leader',
    // 二级云资源部领导审核按钮权限控制
    cloud_leader_2: currentTask.value === 'cloud_leader_2',
    // 屏蔽告警按钮权限控制
    alarm_suppression: currentTask.value === 'alarm_suppression',
    // 云主机关机按钮权限控制
    shutdown: currentTask.value === 'shutdown',
    // 资源变更按钮权限控制
    resource_change: currentTask.value === 'resource_change',
    // 租户确认按钮权限控制
    tenant_task: currentTask.value === 'tenant_task',
    end: currentTask.value === 'autodit end', //  结束节点
  }
})
provide('btnAuth', btnAuth)
//  表单组件
const resourceInformationRef = ref<InstanceType<typeof ResourceInformation> | null>(null)
const basicInformationRef = ref<InstanceType<typeof BasicInformation> | null>(null)

// ----------------------审核提交-----------------------

// 架构审核人表单
const schema_administrator_fields = ref<any[]>([
  {
    label: '三级云资源部领导',
    type: 'select',
    key: 'cloudLeaderId',
    labelWidth: 90,
    rules: [{ required: true, message: '请选择三级云资源部领导', trigger: 'change' }],
    labelField: 'userName',
    valueField: 'id',
    options: [],
  },
  {
    label: '二级云资源部领导',
    type: 'select',
    key: 'secondLevelLeaderId',
    labelWidth: 90,
    labelField: 'userName',
    valueField: 'id',
    rules: [{ required: true, message: '请选择二级云资源部领导', trigger: 'change' }],
    options: [],
  },
])

async function getAuditNode() {
  //后续别的表单可以根据这个逻辑去获取 添加if语句
  if (btnAuth.value.schema_administrator) {
    // 三级
    const { entity: cloudLeaderDic } = await getCloudLeaderDic({
      roleCode: 'cloud_leader',
    })
    // 二级
    const { entity: cloudLeader2Dic } = await getCloudLeaderDic({
      roleCode: 'cloud_leader_2',
    })
    return schema_administrator_fields.value.map((item: any) => {
      if (item.key === 'cloudLeaderId') {
        item.options = cloudLeaderDic
      }
      if (item.key === 'secondLevelLeaderId') {
        item.options = cloudLeader2Dic
      }
      return {
        ...item,
      }
    })
  }

  return []
}
// 审核组件
const SlAuditDialogRef = ref<AuditDialogInstance>()
const visible = ref(false)
//校验表单

// 获取提交的参数
async function changeParams(status: string) {
  let params: any = {
    orderId: route.query.workOrderId,
    currentNodeCode: currentTask.value,
    activiteStatus: status === 'pass' ? 1 : 0,
  }
  let fields: any[] = []

  if (status === 'reject') {
    params['nodeCode'] = allTasks.value[0].task
  }
  if (status === 'pass') fields = await getAuditNode()
  params = {
    ...params,
    ...(await resourceInformationRef.value?.submitForm()),
  }

  return {
    params,
    fields,
  }
}
/**
 * 审核提交
 * @param status 审核状态
 * @param falg 是否需要弹窗 默认打开
 *  */
const subAudit = async (status: string, falg: boolean = true) => {
  // 1. 校验表单 - 架构审核节点

  // 2.获取数据
  const { params, fields } = await changeParams(status)

  // 3.打开弹窗
  visible.value = true
  nextTick(() => {
    SlAuditDialogRef.value?.openDialog(
      {
        params,
        fields,
      },
      falg,
    )
  })
}

// ----------------------提交按钮-----------------------

const disabledBtn = ref(false)

// 渲染按钮组件
const RenderBtn = () => {
  const obj = {
    alarm_suppression: '完 成',
    shutdown: '完 成',
    tenant_task: '确认完成',
    resource_change: '变更完成',
  }
  // 1 .查看表单

  return (
    <>
      {!obj[currentTask.value as keyof typeof obj] && (
        <el-button onclick={() => subAudit('reject')}>审批驳回</el-button>
      )}
      <el-button disabled={disabledBtn.value} type="primary" onclick={() => subAudit('pass')}>
        {obj[currentTask.value as keyof typeof obj] || '审批通过'}
      </el-button>
    </>
  )
}
</script>

<style lang="scss" scoped>
// 页面通用 标题下面的通用布局

.steps-con {
  background: #fff;
  padding: 20px;
}
.auditTable {
  min-height: 350px;
}
.page-footer {
  text-align: right;
  padding-top: 2px;
  padding-bottom: 0;
}
</style>
