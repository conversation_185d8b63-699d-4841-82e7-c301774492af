<template>
  <el-steps class="sl-steps" align-center :active="activeStep">
    <el-step v-for="(step, index) in steps" :key="index" :title="step.title"></el-step>
  </el-steps>
</template>
<script setup lang="ts">
import { ref } from 'vue'

const activeStep = ref(4)
const steps = ref([
  { title: '发起工单' },
  { title: '资料概览' },
  { title: '架构负责人审核' },
  { title: '用户确认' },
  { title: '业务部门领导审批' },
  { title: '云资源部门领导审批' },
  { title: '网络开通' },
  { title: '资源开通' },
])
</script>

<style lang="scss">
.sl-steps {
  .el-step__title.is-success {
    color: var(--el-color-primary);
  }
  .el-step__title {
    font-size: 14px;
  }
  .el-step.is-center .el-step__line {
    left: 62%;
    right: -38%;
  }
  .el-step__title.is-process {
    color: #000c1f;
    font-weight: normal;
  }
  .el-step__head.is-finish {
    .el-step__icon.is-text {
      background: var(--el-color-primary);
      color: #fff;
    }
  }
}
</style>
