import type {
  GetResourceListResType,
  ActiviteDetailVoListType,
} from '@/views/approvalCenter/workOrder/interface/type'
import { ref } from 'vue'

/**
 * 获取当前流程步骤
 */

const useWorkOrder = () => {
  const allTasks = ref<ActiviteDetailVoListType[]>([])
  const currentTask = ref<string>('')
  const currentTaskName = ref<string>('')
  const currentSort = ref<number>(-1)
  const historyTasks = ref<ActiviteDetailVoListType[]>([])

  const getWorkOrderNode = async (entity: GetResourceListResType) => {
    currentTask.value = entity?.currentTask || ''
    currentTaskName.value = entity?.currentTaskName || ''
    allTasks.value = entity?.allTasks || []

    let sort = entity?.allTasks.findIndex((item) => item.task === currentTask.value)

    if (sort === entity?.allTasks.length - 1) sort += 1
    currentSort.value = sort
    historyTasks.value = entity?.allTasks.slice(0, sort)
  }

  return {
    allTasks, // 流程节点集合
    currentTask, // 当前节点code
    currentTaskName, // 当前节点名称
    currentSort, // 当前节点排序
    historyTasks, // 当前节点之前的节点集合
    getWorkOrderNode, // 获取当前流程步骤
  }
}

export default useWorkOrder
