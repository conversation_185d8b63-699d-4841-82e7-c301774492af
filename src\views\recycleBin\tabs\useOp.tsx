import eventBus from '@/utils/eventBus'
import { useRoute } from 'vue-router'
import type { VNode } from 'vue'
import { ElButton } from 'element-plus'

export function useOp() {
  const operationRender = ({ row }: any): VNode => {
    return (
      <>
        <ElButton
          style={{ fontSize: '14px' }}
          onClick={() => delItems(row)}
          type="danger"
          size="large"
          link
        >
          取消删除
        </ElButton>
      </>
    )
  }
  const route = useRoute()
  function delItems(row: any) {
    eventBus.emit('cycleBins:deleteGoods', {
      goods: row,
      isEdit: route.query.orderId ? true : false,
    })
  }
  return {
    delItems,
    operationRender,
  }
}
