/**
 * @name 镜像管理接口类型
 */
export interface ImageType {
  id?: number // 角色ID
  name: string // 角色名称
  description?: string // 角色描述
  regionId?: string // 资源池
  shares?: string // 是否可用
  tenantId?: string // 租户id
  osTypeId?: string // 镜像类型ID
  osType?: string // 镜像类型ID
  diskFormat?: string // 磁盘格式
  virtType?: string // 虚拟化类型
  imageSize?: string // 镜像大小
  minCpu?: string // 最小cpu
  minRam?: string // 最小内存(MB)
  minDisk?: string // 最小磁盘(GB)
  password?: string // 镜像密码
  resourceId?: string // 底层镜像ID
  imageType?: string // 镜像类型
  version?: string // 版本号
  azId?: string // 可用区ID
  domainCode?: string // 云平台编码
  osTypeSource?: string //
}

export interface RoleListType extends ImageType {
  createdUserName?: string // 创建人
  createTime?: string // 创建时间，格式为 'YYYY-MM-DD HH:mm:ss'
  [key: string]: any
}
