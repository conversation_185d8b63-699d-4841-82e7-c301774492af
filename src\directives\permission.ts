// src/directives/permission.ts
import { nextTick } from 'vue'
import type { Directive } from 'vue'

const getAuthStore = async () => {
  const { useAuthStore } = await import('@/stores/modules/auth')
  return useAuthStore()
}

const permissionDirective: Directive = {
  async mounted(el, binding) {
    const authStore = await getAuthStore()
    const { value } = binding

    if (typeof value !== 'string') {
      throw new Error(`expected value is string, but got ${typeof value}`)
    }

    const hasPermission = authStore.hasPermission(value)
    if (!hasPermission) {
      nextTick(() => {
        if (el.parentNode) {
          el.parentNode.removeChild(el)
        }
      })
    }
  },
}

export default permissionDirective
