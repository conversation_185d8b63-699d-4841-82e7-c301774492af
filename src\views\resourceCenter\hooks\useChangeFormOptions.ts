import { validateGoodsName } from '../utils'
import useBusiSystemOptions from './useBusiSystemOptions'
import useLevelLeaderIdOptions from './useLevelLeaderIdOptions'
import useModuleIdOptions from './useModuleIdOptions'
import { useSelectLabel } from '@/hooks/useSelectLabel'
import type { IBaseModel } from './useGoodsModels'

export function useChangeFormOptions(
  formModel: IBaseModel,
  callback: (v: any) => void = () => null,
  computedLabel: boolean = true,
) {
  const { busiSystemOptions } = useBusiSystemOptions()
  const { levelTwoLeaderIdOptions, levelThreeLeaderIdOptions } = useLevelLeaderIdOptions()
  const { moduleIdOptions } = useModuleIdOptions(formModel)
  if (computedLabel) {
    useSelectLabel(
      () => busiSystemOptions,
      () => formModel.busiSystemId,
      (option) => {
        formModel.busiSystemName = option.label
      },
    )
    useSelectLabel(
      () => levelTwoLeaderIdOptions,
      () => formModel.busiDepartLeaderId,
      (option) => {
        formModel.busiDepartLeaderLabel = option.label
      },
    )
    useSelectLabel(
      () => levelThreeLeaderIdOptions,
      () => formModel.levelThreeLeaderId,
      (option) => {
        formModel.levelThreeLeaderLabel = option.label
      },
    )

    useSelectLabel(
      () => moduleIdOptions,
      () => formModel.moduleId,
      (option) => {
        formModel.moduleName = option.label
      },
    )
  }
  callback({
    busiSystemOptions,
    levelTwoLeaderIdOptions,
    levelThreeLeaderIdOptions,
    moduleIdOptions,
  })
  return {
    gutter: 60,
    groupName: '工单信息',
    style: 'margin:0;',
    groupItems: [
      {
        label: '申请人',
        type: 'text',
        key: 'applicant',
        span: 8,
      },
      {
        label: '所属部门',
        type: 'text',
        key: 'department',
        span: 8,
      },
      {
        label: '工单类型',
        type: 'text',
        key: 'workOrdertype',
        span: 8,
      },
      {
        label: '标题',
        type: 'input',
        key: 'orderTitle',
        span: 8,
        props: {
          maxlength: 50,
          showWordLimit: true,
        },
        rules: [
          { required: true, message: '请输入标题', trigger: ['blur', 'change'] },
          { validator: validateGoodsName, trigger: ['blur', 'change'] },
        ],
      },
      {
        label: '业务系统',
        type: 'select',
        key: 'busiSystemId',
        options: busiSystemOptions,
        onChange() {
          formModel.moduleId = ''
        },
        props: {
          select: {
            disabled: true,
          },
        },
        span: 8,
        rules: [{ required: true, message: '请选择业务系统', trigger: ['change'] }],
        suffixSlot: true,
      },
      {
        label: '局方负责人',
        type: 'input',
        key: 'applyUserName',
        props: {
          disabled: true,
        },
        span: 8,
      },
      {
        label: '厂家',
        type: 'input',
        key: 'manufacturer',
        span: 8,
        props: {
          disabled: true,
        },
      },
      {
        label: '厂家负责人',
        type: 'input',
        key: 'manufacturerContacts',
        span: 8,
        props: {
          disabled: true,
        },
      },
      {
        label: '电话',
        type: 'input',
        props: {
          disabled: true,
        },
        key: 'manufacturerMobile',
        span: 8,
      },
      {
        label: '二级业务部门领导',
        type: 'select',
        key: 'busiDepartLeaderId',
        span: 8,
        options: levelTwoLeaderIdOptions,
        rules: [{ required: true, message: '请选择二级业务部门领导', trigger: ['change'] }],
      },
      {
        label: '三级业务部门领导',
        type: 'select',
        key: 'levelThreeLeaderId',
        span: 8,
        options: levelThreeLeaderIdOptions,
        rules: [{ required: true, message: '请选择三级业务部门领导', trigger: ['change'] }],
      },
      {
        label: '所属业务模块',
        type: 'select',
        key: 'moduleId',
        options: moduleIdOptions,
        span: 8,
        rules: [{ required: true, message: '请选择所属业务模块', trigger: ['change'] }],
      },
      {
        label: '资源变更说明',
        type: 'input',
        props: {
          type: 'textarea',
          autosize: { minRows: 2, maxRows: 4 },
          row: 4,
          maxlength: 200,
          showWordLimit: true,
        },
        key: 'orderDesc',
        span: 24,
        rules: [{ required: true, message: '请输入资源变更说明', trigger: ['blur', 'change'] }],
      },
      {
        label: '资源变更说明书',
        type: 'upload',
        key: 'files',
        rules: [{ required: true, message: '请上传资源变更说明书', trigger: ['blur', 'change'] }],
        span: 24,
        props: {
          upload: {
            fileMimeType: ['.doc', '.docx'],
            fileSize: 100,
            fileType: 'RESOURCE_EXPLAIN',
            tip: '请上传word格式文件，大小在 100M 以内',
            fileTemplate: {
              name: '模板下载',
              uid: 'XXXX上云设计说明书.docx',
              fileName: 'XXXX资源变更说明书.docx',
            },
          },
        },
      },
    ],
  }
}
