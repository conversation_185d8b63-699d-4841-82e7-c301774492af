<template>
  <sl-form
    class="goods-info-form"
    size="small"
    ref="slFormRef"
    :options="goodsInfoOptions"
    :model-value="goods.orderJson"
  >
    <!-- 删除按钮 -->
    <template #globalFormSlot>
      <div @click="handleGoodsDelete" class="goods-del-btn">
        <el-icon><CircleCloseFilled /></el-icon>
      </div>
    </template>
    <template #vCpusSlot="{ form, item }">
      <el-input-number v-bind="item.props || {}" v-model="form[item.key][0]" clearable />
      <span style="margin: 0 4px; max-width: 14px">核</span>
      <el-input-number v-bind="item.props || {}" v-model="form[item.key][1]" clearable />
      <span style="margin-left: 4px; max-width: 14px">G</span>
    </template>
    <template #gpuSlot="{ form, item }">
      <el-switch
        style="flex-grow: 0.2; margin-right: 4px"
        v-model="form[item.swithKey]"
        active-value="1"
        inactive-value="0"
      />
      <el-input-number v-bind="item.props || {}" v-model="form[item.key][0]" clearable />
      <span style="margin: 0 4px; max-width: 24px">算力</span>
      <el-input-number v-bind="item.props || {}" v-model="form[item.key][1]" clearable />
      <span style="margin: 0 4px; max-width: 14px">G</span>
      <el-input-number v-bind="item.props || {}" v-model="form[item.key][2]" clearable />
      <span style="margin-left: 4px; max-width: 14px">个</span>
    </template>
  </sl-form>
</template>
<script setup lang="ts">
import { reactive, ref, watchEffect } from 'vue'
import type { ICqModel } from '@/views/resourceCenter/hooks/useGoodsModels'
import type { IGoodsItem } from '@/views/resourceCenter/hooks/useShoppingCarts'
import { CircleCloseFilled } from '@element-plus/icons-vue'
import { validateGoodsName } from '@/views/resourceCenter/utils'
import { useGlobalDicStore } from '@/stores/modules/dic'
import eventBus from '@/utils/eventBus'
import slForm from '@/components/form/SlForm.vue'
import { useRoute } from 'vue-router'

const route = useRoute()

const globalDic = useGlobalDicStore()
const { getDic } = globalDic

const props = defineProps<{
  goods: IGoodsItem<ICqModel>
  flavorOptions: any
}>()
function handleGoodsDelete() {
  eventBus.emit('shoppingCarts:deleteGoods', {
    goods: props.goods,
    isEdit: route.query.orderId ? true : false,
  })
}
const formModel = props.goods.orderJson

const gupDisabled = ref(false)
const slFormRef = ref()
setRef(props.goods)
function setRef(goods: IGoodsItem<ICqModel>) {
  goods.ref = slFormRef
}
watchEffect(() => {
  if (formModel.isUseGpu === '0') {
    gupDisabled.value = true
    if (slFormRef.value) {
      formModel.gpu[0] = 0
      formModel.gpu[1] = 0
      formModel.gpu[2] = 0
      slFormRef.value.clearValidate(['gpuRatio', 'gpuVirtualMemory', 'gpuCore'])
    }
  } else {
    gupDisabled.value = false
    formModel.gpu[0] = formModel.gpu[0] || 0
    formModel.gpu[1] = formModel.gpu[1] || 0
    formModel.gpu[2] = formModel.gpu[2] || 0
  }
})

const validateGpu = (rule: any, value: any, callback: any) => {
  if (formModel.isUseGpu === '0') {
    callback()
  } else {
    if (formModel.gpu[0] === 0) {
      callback(new Error('请输入算力大小'))
    } else if (formModel.gpu[1] === 0) {
      callback(new Error('请输入GPU内存大小'))
    } else if (formModel.gpu[2] === 0) {
      callback(new Error('请输入GPU核心数'))
    } else {
      callback()
    }
  }
}
const validateVcpus = (rule: any, value: any, callback: any) => {
  if (formModel.cpu[0] === 0) {
    callback(new Error('请输入容器配额核心数'))
  } else if (formModel.cpu[1] === 0) {
    callback(new Error('请输入容器配额内存大小'))
  } else {
    callback()
  }
}
const goodsInfoOptions = reactive([
  {
    style: 'margin:0;padding:0;background: rgb(237 245 255);padding-top: 18px;',
    gutter: 40,
    groupItems: [
      {
        label: '配额名称',
        type: 'input',
        key: 'instanceName',
        span: 8,
        props: {
          maxlength: 64,
          showWordLimit: true,
        },
        rules: [
          { required: true, message: '请输入配额名称', trigger: ['blur', 'change'] },
          { validator: validateGoodsName, trigger: ['blur', 'change'] },
        ],
      },
      {
        label: '容器配额',
        type: 'slot',
        key: 'cpu',
        span: 8,
        slotName: 'vCpusSlot',
        required: true,
        rules: [{ validator: validateVcpus, trigger: ['blur', 'change'] }],
      },
      {
        label: '申请时长',
        type: 'select',
        key: 'time',
        span: 8,
        options: getDic('time'),
        rules: [{ required: true, message: '请选择申请时长', trigger: ['blur', 'change'] }],
      },
      {
        label: '是否使用GPU',
        type: 'slot',
        slotName: 'gpuSlot',
        swithKey: 'isUseGpu',
        key: 'gpu',
        props: {
          min: 0,
          step: 1,
          disabled: gupDisabled,
        },
        rules: [{ validator: validateGpu, trigger: ['blur', 'change'] }],
        span: 16,
      },
    ],
  },
])
</script>
<style scoped>
.goods-del-btn {
  position: absolute;
  top: -7px;
  right: -6px;
  z-index: 10;
  color: var(--el-color-danger);
  font-size: 1.2rem;
  cursor: pointer;
  display: none;
}
.goods-info-form:hover .goods-del-btn {
  display: block;
}

.evs-item {
  width: 100%;
  margin-bottom: 15px;
  .evs-item-content {
    width: 100%;
    display: flex;
    align-items: center;
  }
  &:last-child {
    margin-bottom: 0;
  }
  .evs-icons {
    display: flex;
    align-items: center;
    margin-left: 10px;
    .el-icon {
      cursor: pointer;
    }
  }
}
</style>
