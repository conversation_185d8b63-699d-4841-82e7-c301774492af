<script lang="ts" setup>
import { iconPropType } from '@/utils/index'
import step from './item.vue'
import { inject } from 'vue'

type IStatus = 'wait' | 'process' | 'finish' | 'error' | 'success'

interface branchStepPros {
  title: string
  icon?: typeof iconPropType
  description?: string
  branch?: branchStepPros[] | undefined
  branchSpace?: number
}
defineOptions({
  name: 'BaseBranchStep', // 显式定义组件名
})
withDefaults(defineProps<branchStepPros>(), {
  branchSpace: 20,
})

const parent = inject('SlBaseSteps') as {
  props: { finishStatus: IStatus; processStatus: IStatus; branchSpace: number }
}
</script>

<template>
  <step :title="title" :branch-space="branchSpace" :icon="icon" :description="description">
    <!-- 递归渲染子分支 -->
    <template v-if="branch?.length" #branch>
      <base-branch-step
        v-for="(item, index) in branch"
        :key="item.title + index"
        :title="item.title"
        :branch-space="item.branchSpace"
        :branch="item.branch"
        :icon="item.icon"
        :description="item.description"
        :finish-status="parent.props.finishStatus"
        :process-status="parent.props.processStatus"
      />
    </template>
  </step>
</template>
