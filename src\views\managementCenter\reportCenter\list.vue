<template>
  <div class="table-box">
    <sl-page-header
      title="报表中心"
      title-line="提供了丰富多样的报表类型选择服务，以及灵活精准的筛选条件设定功能。用户可轻松生成符合需求的报表，并便捷下载。助力用户高效获取、处理数据信息，提升数据运用与管理效能"
      :icon="{
        Vnode: Platform,
        color: '#0052D9',
        size: '50px',
      }"
    >
    </sl-page-header>
    <div class="filter-form-con">
      <sl-form
        class="filter-form"
        :class="{ collapsed: collapsed }"
        ref="formRef"
        :options="formOptions"
        v-model="formModel"
      >
      </sl-form>
    </div>
    <div class="table-layout">
      <dataList ref="dataListRef" :query-params="queryParams" @refresh="search"></dataList>
    </div>
    <dialogForm v-if="dialogVisible" v-model="dialogVisible" @confirm="search"></dialogForm>
  </div>
</template>

<script lang="tsx" setup>
import { reactive, ref } from 'vue'
import { Delete, Search, Download, Histogram } from '@element-plus/icons-vue'
import { Platform } from '@element-plus/icons-vue'
import SlPageHeader from '@/components/SlPageHeader/index.vue'
import slForm from '@/components/form/SlForm.vue'
import dataList from './dataList.vue'
import dialogForm from './dialogForm.vue'

const formRef = ref<any>(null)
const queryParams = ref<any>({ type: 'ecs' })
const dialogVisible = ref(false)
const dataListRef = ref<any>(null)

const formModel = reactive<any>({})
function reset() {
  formRef.value!.resetFields()
  queryParams.value = { ...formModel }
}
function search() {
  queryParams.value = { ...formModel }
}
const createReport = () => {
  dialogVisible.value = true
}
const batchDelete = () => {
  dataListRef.value?.batchDelete()
}
const batchDownload = () => {
  dataListRef.value?.batchDownload()
}
// 是否默认折叠搜索项
const collapsed = ref(true)
const formOptions = reactive([
  {
    style: 'padding: 0',
    gutter: 20,
    groupItems: [
      {
        label: '报表名称',
        type: 'input',
        key: 'reportName',
        span: 6,
      },
      {
        label: '创建人',
        type: 'input',
        key: 'creator',
        span: 6,
      },
      {
        span: 12,
        render() {
          return (
            <div style="display: flex;justify-content: flex-end;">
              <el-button onClick={reset} icon={<Delete />}>
                重置
              </el-button>
              <el-button onClick={search} icon={<Search />} type="primary">
                搜索
              </el-button>
              <el-button onClick={batchDelete} icon={<Delete />} type="danger">
                批量删除
              </el-button>
              <el-button onClick={batchDownload} icon={<Download />} type="primary">
                批量下载
              </el-button>
              <el-button onClick={createReport} icon={<Histogram />} type="primary">
                生成报表
              </el-button>
            </div>
          )
        },
      },
    ],
  },
])
</script>
<style lang="scss" scoped>
.table-layout {
  margin: 2px 8px 8px 8px;
  flex: 1;
  overflow: hidden;
}
.filter-form-con {
  margin: 8px 8px 0 8px;
  background: #fff;
  border-radius: 8px;
}
.filter-form.collapsed {
  height: 50px;
}
.filter-form {
  height: 110px;
  overflow: hidden;
  transition: height 0.2s;
}
</style>
