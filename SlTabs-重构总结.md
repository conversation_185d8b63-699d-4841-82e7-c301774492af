# SlTabs 组件重构总结

## 重构目标
参照 SlBaseTabs 的样式设计，重构 SlTabs 组件的选中与平时的样式，提供更好的视觉效果和用户体验。

## 主要改动

### 1. 模板结构重构
**原来**: 使用 Element Plus 的 `el-tabs` 组件
```vue
<el-tabs v-model="activeTab" type="card">
  <el-tab-pane v-for="item in tabs" :key="item.name" :label="item.label" :name="item.name">
    <template #label>{{ item.label }}</template>
  </el-tab-pane>
</el-tabs>
```

**现在**: 自定义实现，参照 SlBaseTabs 的结构
```vue
<div class="sl-tabs">
  <div class="tab-header">
    <div class="tab-item help first"><div class="bg"></div></div>
    <div v-for="tab in tabs" :class="['tab-item', { active: activeTab === tab.name }]">
      <div class="bg"></div>
      <span class="title">{{ tab.label }}</span>
      <span v-if="showCount && tab.count !== undefined" class="count">{{ tab.count }}</span>
    </div>
    <div class="tab-item help last"><div class="bg"></div></div>
  </div>
  <div class="tab-content">
    <slot></slot>
  </div>
</div>
```

### 2. 功能增强
- **数量显示**: 添加 `showCount` 属性，支持显示标签页的数量
- **双向绑定**: 完善 `emit` 事件，支持 `v-model` 双向绑定
- **插槽内容**: 使用 `<slot>` 替代固定的标签页内容，提供更大的灵活性

### 3. 样式设计

#### 基础样式
- **容器**: 使用 flexbox 布局，垂直排列
- **标签头**: 水平排列，14px 字体大小
- **标签项**: 8px 20px 内边距，相对定位

#### 选中状态样式
- **颜色**: 使用主题色 `var(--el-color-primary)`
- **背景**: 白色背景 `#fff`
- **字体**: 400 字重

#### 特殊效果
- **圆角过渡**: 
  - 选中标签: 上方圆角 `border-radius: 8px 8px 0 0`
  - 相邻标签: 底部圆角过渡效果 `border-bottom-left-radius: 12px` / `border-bottom-right-radius: 12px`
- **背景层**: 使用绝对定位的 `.bg` 元素实现复杂的背景效果
- **悬停效果**: 鼠标悬停时颜色变为 `#409eff`

#### 数量标签样式
- **背景**: 半透明蓝色 `rgba(72, 127, 239, 0.1)`
- **字体**: 12px 小字体
- **间距**: 2px 4px 内边距，2px 圆角
- **位置**: 标题右侧 2px 间距

#### 内容区域
- **背景**: 浅灰色 `#f2f3f5`
- **最小高度**: 200px
- **与标签头无缝连接**

### 4. 技术特点

#### CSS 现代特性
- **`:has()` 选择器**: 实现相邻元素的样式联动
- **CSS 自定义属性**: 使用 `var(--el-color-primary)` 主题色
- **层级管理**: 使用 `z-index` 管理元素层级

#### Vue 3 特性
- **Composition API**: 使用 `ref`, `watch` 等组合式 API
- **TypeScript**: 完整的类型定义
- **响应式**: 双向绑定和事件发射

### 5. 使用方式

#### 基础用法
```vue
<sl-tabs v-model="activeTab" :tabs="tabs">
  <div v-if="activeTab === 'tab1'">内容1</div>
  <div v-if="activeTab === 'tab2'">内容2</div>
</sl-tabs>
```

#### 显示数量
```vue
<sl-tabs v-model="activeTab" :tabs="tabs" show-count>
  <!-- 内容 -->
</sl-tabs>
```

#### 数据格式
```typescript
const tabs = [
  { name: 'tab1', label: '标签一', count: 10 },
  { name: 'tab2', label: '标签二', count: 5 },
]
```

## 优势对比

### 视觉效果
- **更现代**: 圆角过渡效果，视觉层次更丰富
- **更统一**: 与 SlBaseTabs 保持一致的设计语言
- **更清晰**: 选中状态更明显，用户体验更好

### 功能性
- **更灵活**: 支持任意内容的插槽
- **更完整**: 支持数量显示和双向绑定
- **更可控**: 自定义实现，样式完全可控

### 可维护性
- **代码清晰**: 结构简单，逻辑清楚
- **样式独立**: 不依赖第三方组件样式
- **易于扩展**: 可以轻松添加新功能

## 兼容性说明

- **浏览器支持**: 现代浏览器（支持 `:has()` 选择器）
- **Vue 版本**: Vue 3.x
- **TypeScript**: 完整支持

## 总结

重构后的 SlTabs 组件完全参照了 SlBaseTabs 的设计理念，提供了更好的视觉效果和用户体验。组件保持了简洁的 API 设计，同时增强了功能性和可定制性，是一个现代化的标签页组件实现。
