<template>
  <component
    :is="componentType"
    v-model="modelValuecomputed"
    v-bind="componentProps"
    @update:modelValue="handleUpdate"
    @change="handleChange"
  >
    <!-- 动态渲染 RadioGroup 的每个 Radio -->
    <template v-if="props.type === 'radio'">
      <el-radio
        v-for="option in options"
        :key="option.value"
        :label="option[valueField || 'value']"
        :disabled="option.disabled"
      >
        {{ option[labelField || 'label'] }}
      </el-radio>
    </template>

    <!-- 动态渲染 RadioGroup 的每个 Radio -->
    <template v-if="props.type === 'select'">
      <el-option
        v-for="option in options"
        :key="option.value"
        :value="option[valueField || 'value']"
        :label="option[labelField || 'label'] as any"
        :disabled="option.disabled"
      >
        {{ option[labelField || 'label'] }}
      </el-option>
    </template>

    <!-- 动态渲染 CheckboxGroup 的每个 Checkbox -->
    <template v-if="props.type === 'checkbox'">
      <el-checkbox
        v-for="option in options"
        :key="option.value"
        :label="option[valueField || 'value']"
        :disabled="option.disabled"
      >
        {{ option[labelField || 'label'] }}
      </el-checkbox>
    </template>
  </component>
</template>

<script lang="ts" setup name="SlDynamicFormField">
import { computed } from 'vue'
import {
  ElInput,
  ElSelect,
  ElRadioGroup,
  ElCheckboxGroup,
  ElSwitch,
  ElSlider,
  ElDatePicker,
  ElTimePicker,
  ElInputNumber,
  ElTreeSelect,
} from 'element-plus'
import type { FieldOptionType } from './interface'

const props = defineProps<{
  type: string
  modelValue: any
  attrs?: Record<string, any>
  options?: FieldOptionType[]
  labelField?: string
  valueField?: string
  emitChange?: boolean // Whether to emit change events
}>()

const emit = defineEmits(['update:modelValue', 'fieldChange'])

const modelValuecomputed = computed({
  set: (newvalue) => {
    emit('update:modelValue', newvalue)
  },
  get: () => props.modelValue,
})

const componentProps = computed(() => {
  const baseProps = { ...props.attrs }

  if (props.type === 'tree-select' && props.options) {
    baseProps.data = props.options
  }
  return baseProps
})

const componentType = computed(() => {
  switch (props.type) {
    case 'text':
      return ElInput
    case 'select':
      return ElSelect
    case 'radio':
      return ElRadioGroup
    case 'checkbox':
      return ElCheckboxGroup
    case 'switch':
      return ElSwitch
    case 'slider':
      return ElSlider
    case 'date':
      return ElDatePicker
    case 'time':
      return ElTimePicker
    case 'number':
      return ElInputNumber
    case 'tree-select':
      return ElTreeSelect // 新增的 tree-select 类型
    default:
      return ElInput
  }
})

const handleUpdate = (value: any) => {
  emit('update:modelValue', value)
}

const handleChange = (value: any) => {
  if (props.emitChange) {
    emit('fieldChange', { value })
  }
}
</script>
