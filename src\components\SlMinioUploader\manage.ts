import { ref } from 'vue'
import type {
  MinioResumableUpload as IMinioResumableUpload,
  IMinioResumableUploadOptions,
} from './sdk'
import { MinioResumableUpload } from './sdk'

type IUploaderMap = Map<string, MinioResumableUpload>

class UploaderManager {
  private static instance: UploaderManager
  public uploaders: IUploaderMap = new Map()
  private uploaderCount = ref(0)

  public static getInstance(): UploaderManager {
    if (!UploaderManager.instance) {
      UploaderManager.instance = new UploaderManager()
    }
    return UploaderManager.instance
  }

  public createUploader(options: IMinioResumableUploadOptions): IMinioResumableUpload {
    const uploader = new MinioResumableUpload(options, this)
    this.uploaders.set(uploader.md5, uploader)
    this.uploaderCount.value++
    return uploader
  }

  public getUploader(name: string): MinioResumableUpload | undefined {
    return this.uploaders.get(name)
  }

  public getUploaders(): IUploaderMap {
    return this.uploaders
  }

  public stopAll(): void {
    this.uploaders.forEach((uploader) => uploader.stop())
  }

  public stop(name: string): void {
    const uploader = this.getUploader(name)
    if (uploader) {
      uploader.stop()
    }
  }

  public terminateUploader(name: string): void {
    const uploader = this.getUploader(name)
    if (uploader) {
      uploader.stop()
      this.uploaders.delete(name)
      this.uploaderCount.value--
    }
  }

  public terminateAll(): void {
    this.uploaders.forEach((uploader) => uploader.stop())
    this.uploaders.clear()
    this.uploaderCount.value = 0
  }

  public getUploaderCount(): number {
    return this.uploaderCount.value
  }
}

export const uploaderManager = UploaderManager.getInstance()
export type { UploaderManager }
