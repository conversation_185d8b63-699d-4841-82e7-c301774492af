<template>
  <div>
    <div class="sl-card mb8">
      <sl-block-title>工单信息</sl-block-title>
      <sl-form
        v-if="form"
        :show-block-title="false"
        :label-width="140"
        ref="slFormRef"
        v-model="form"
        :options="options"
      >
        <template #enumText="{ item }">
          <EnumText :form-model="form" :fields="item" :dic-collection="dicCollection" />
        </template>
      </sl-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import EnumText from '@/views/approvalCenter/components/EnumText.vue'
import { ref } from 'vue'

const form = ref()

const dicCollection = ref({})

const initData = (data: any) => {
  form.value = JSON.parse(JSON.stringify(data))
}
const options = [
  {
    groupName: '工单信息',
    groupItems: [
      {
        label: '申请人',
        type: 'slot',
        slotName: 'enumText',
        keyName: 'createdUserName',
        span: 8,
      },
      {
        label: '所属部门',
        type: 'slot',
        slotName: 'enumText',
        keyName: 'departmentName',
        span: 8,
      },
      {
        label: '工单类型',
        type: 'slot',
        slotName: 'enumText',
        keyName: 'orderType',
        span: 8,
      },
      {
        label: '标题',
        type: 'slot',
        slotName: 'enumText',
        keyName: 'orderTitle',
        span: 8,
      },

      {
        label: '资源回收说明',
        type: 'slot',
        slotName: 'enumText',
        keyName: 'orderDesc',
        span: 16,
      },
    ],
  },
]

defineExpose({
  initData,
})
</script>

<style></style>
