// 云平台定义
export interface CloudPlatformType {
  id: string // id
  name: string // 名字
  [key: string]: any
}
// 地市定义
export interface cityListType {
  // id: number
  // createdTime: Date
  // status: number
  // domainCode: string
  // domainName: string
  // catalogueDomainId: number
  // open: number
  code: string
  name: string
  [key: string]: any
  children: Array
}
// VM一类查询定义
export interface regionsApiType {
  platformCode: string
  platformName: string
  cityCode: any
  areaCode: any
  regionCode: any
  regionName: any
  regions: Array
  [key: string]: any
}
// GPU类信息定义
export interface gpuItemType {
  name: string
  used: string
  other: string
  total: string
}
//总览查询定义
export interface latestType {
  vcpuTotal: number
  vcpuUsed: number
  vcpuAvi: number
  vcpuUsedRate: number
  memoryTotal: number
  memoryUsed: number
  memoryAvi: number
  memoryUsedRate: number
  storageTotal: number
  storageUsed: number
  storageAvi: number
  storageUsedRate: number
  eipTotal: number
  eipUsed: number
  eipAvi: number
  eipUsedRate: number
  gpuTotal: number
  gpuUsed: number
  gpuAvi: number
  gpuUsedRate: number
  dcnTotal: number
  dcnUsed: number
  dcnAvi: number
  dcnUsedRate: number
  hostNum: number
  vmNum: number
  businessNum: number
}
//top5左侧定义
export interface reportType {
  regionId: string
  regionCode: string
  regionName: string
  total: string
  used: string
  avi: string
  usedRate: string
}
//top5右侧定义
type PerformanceData = number[]

interface DataUtilEntry {
  performanceData: PerformanceData
  regionName: string
}

interface CpuMemData {
  topRegions: string[]
  days: string[]
  dataUtil: DataUtilEntry[]
}

interface DiskDataEntry extends DataUtilEntry {
  performanceData: (number | number)[]
}

interface DiskData extends CpuMemData {
  dataUtil: DiskDataEntry[]
}

interface Entity {
  cpuData: CpuMemData
  memData: CpuMemData
  diskData: DiskData
}
// 定义整个 JSON 数据的类型
export interface PerformanceType {
  cpuData: ResourceData
  memData: ResourceData
  diskData: ResourceData
}
export interface byUserType {
  [key: string]: any
}
