type GetDateRangeParams = {
  endDate?: string // 结束日期，格式为 yyyy-mm-dd
  days: number // 往前推的天数
  separator?: string // 分隔符，默认为 '-'
}

/**
 * 获取一段时间内的起止日期
 * @param endDate 结束日期，格式为 yyyy-mm-dd，可选参数，默认为昨天
 * @param days 往前推的天数，必须为整数
 * @param separator 分隔符，默认为 '-'
 * @returns 包含开始日期和结束日期的对象
 */
export function getDateRange({ endDate, days, separator = '-' }: GetDateRangeParams): {
  startDate: string
  endDate: string
} {
  // 获取昨天的日期，基于北京时间
  const yesterday = new Date(new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' }))
  yesterday.setDate(yesterday.getDate() - 1)

  // 如果没有传入结束日期，默认为昨天
  let end: Date
  if (endDate) {
    end = new Date(endDate)
    if (isNaN(end.getTime())) {
      throw new Error('Invalid endDate format')
    }
  } else {
    end = yesterday
  }

  // 计算开始日期
  let start = new Date(end.toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' }))
  start.setDate(start.getDate() - days)

  // 确保 endDate 晚于 startDate
  if (start > end) {
    ;[start, end] = [end, start]
  }

  // 格式化日期
  const formatDate = (date: Date): string => {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}${separator}${month}${separator}${day}`
  }

  return {
    startDate: formatDate(start),
    endDate: formatDate(end),
  }
}

type GetDateFromTodayParams = {
  days: number // 距离今天的天数
  separator?: string // 分隔符，默认为 '-'
}

/**
 * 获取距离今天的日期
 * @param days 距离今天的天数，必须为整数，默认为 0
 * @param separator 分隔符，默认为 '-'
 * @returns 日期字符串，格式为 yyyy-mm-dd
 */
export function getDateFromToday({ days = 0, separator = '-' }: GetDateFromTodayParams): string {
  const today = new Date()
  today.setDate(today.getDate() + days)

  // 格式化日期
  const formatDate = (date: Date): string => {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}${separator}${month}${separator}${day}`
  }

  return formatDate(today)
}
