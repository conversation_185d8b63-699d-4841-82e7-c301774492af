import {
  shoppingCartCreate,
  shoppingCartUpdate,
  shoppingCartList,
  shoppingCartDelete,
} from '@/api/modules/resourecenter'
import SlMessage from '@/components/base/SlMessage'
import { reactive, onUnmounted, type Reactive, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import eventBus from '@/utils/eventBus'
import {
  useBaseModel,
  useEvsModel,
  useEcsModel,
  useGcsModel,
  useNatModel,
  useObsModel,
  useSlbModel,
  useEipModel,
  useCqModel,
  useMysqlModel,
  useRedisModel,
  useBackupModel,
  type IEcsModel,
  type IBaseModel,
  type IEvsModel,
  type IGcsModel,
  type INatModel,
  type IObsModel,
  type ISlbModel,
  type IEipModel,
  type ICqModel,
  type IMysqlModel,
  type IRedisModel,
  type IBackupModel,
} from './useGoodsModels'
import { displayBackOrder } from '@/api/modules/resourecenter'
import { generateRandom8DigitNumber } from '@/utils'

type GoodsType =
  | 'ecs'
  | 'gcs'
  | 'obs'
  | 'slb'
  | 'evs'
  | 'nat'
  | 'base'
  | 'eip'
  | 'cq'
  | 'mysql'
  | 'redis'
  | 'backup'

export interface IGoodsItem<T> {
  id: number
  goodsType: GoodsType
  orderJson: T
  ref?: Reactive<any>
  a4FormRef?: Reactive<any>
}

export interface ICartsModel {
  ecsList: IGoodsItem<IEcsModel>[]
  gcsList: IGoodsItem<IGcsModel>[]
  obsList: IGoodsItem<IObsModel>[]
  slbList: IGoodsItem<ISlbModel>[]
  evsList: IGoodsItem<IEvsModel>[]
  natList: IGoodsItem<INatModel>[]
  eipList: IGoodsItem<IEipModel>[]
  baseList: IGoodsItem<IBaseModel>[]
  cqList: IGoodsItem<ICqModel>[]
  mysqlList: IGoodsItem<IMysqlModel>[]
  redisList: IGoodsItem<IRedisModel>[]
  backupList: IGoodsItem<IBackupModel>[]
}
type CartListKey = keyof Omit<ICartsModel, 'baseList'>

/**
 * 新增产品记录
 * @param goodsType 商品类型
 * @returns
 */
export async function createRecord(goodsType: GoodsType): Promise<number> {
  const res = await shoppingCartCreate({
    [`${goodsType}List`]: [
      {
        goodsType,
        orderJson: {},
      },
    ],
  })
  if (res.code === 200) {
    return res.entity as number
  }
  SlMessage.error(res.message || '接口请求失败')
  return 0
}

export async function updateRecord(cartsModel: ICartsModel): Promise<boolean> {
  const keys = [
    'ecsList',
    'evsList',
    'gcsList',
    'obsList',
    'slbList',
    'natList',
    'baseList',
    'eipList',
    'cqList',
    'mysqlList',
    'redisList',
    'backupList',
  ] as const
  const params: any = {}
  keys.forEach((key) => {
    if (cartsModel[key].length) {
      params[key] = cartsModel[key].map((ele) => ({
        id: ele.id,
        goodsType: ele.goodsType,
        orderJson: ele.orderJson,
      }))
    }
  })
  const res = await shoppingCartUpdate(params)
  if (res.code === 200) {
    SlMessage.success('暂存成功')
    return true
  }
  SlMessage.error(res.message || '接口请求失败')
  return false
}

async function clearRecord(cartsModel: ICartsModel): Promise<boolean> {
  const keys = [
    'ecsList',
    'evsList',
    'gcsList',
    'obsList',
    'slbList',
    'natList',
    'baseList',
    'eipList',
    'cqList',
    'mysqlList',
    'redisList',
    'backupList',
  ] as const
  const ids: number[] = []
  keys.forEach((key) => {
    if (cartsModel[key].length) {
      ids.push(...cartsModel[key].map((ele) => ele.id))
    }
  })
  const res = await deleteRecord(ids)
  return res
}

export async function deleteRecord(ids: number[], type: GoodsType | '' = ''): Promise<boolean> {
  const res = await shoppingCartDelete({ ids: ids.join(','), type })
  if (res.code === 200) {
    return true
  }
  return false
}

async function loadDatafromOrderEcho(cartsModel: ICartsModel, orderId: string): Promise<void> {
  const { entity, code } = await displayBackOrder({ workOrderId: orderId, aggregation: true })
  if (code !== 200) return
  const baseJson = cartsModel.baseList[0].orderJson
  baseJson.id = orderId
  baseJson.files = entity.resourceApplyFiles || []
  baseJson.orderTitle = entity.orderTitle
  baseJson.busiDepartLeaderId = entity.busiDepartLeaderId
  baseJson.levelThreeLeaderId = entity.levelThreeLeaderId
  baseJson.moduleId = entity.moduleId
  baseJson.orderDesc = entity.orderDesc
  baseJson.busiSystemId = entity.busiSystemId
  baseJson.a4Account = entity.cqList?.[0]?.a4Account
  baseJson.a4Phone = entity.cqList?.[0]?.a4Phone

  cartsModel.ecsList =
    entity.ecsModelList?.map((goods: any) => {
      const orderJson = useEcsModel()
      orderJson.numbers = goods.openNum
      orderJson.goodsId = goods.id
      orderJson.disasterRecovery = goods.disasterRecovery ? '1' : '0'
      orderJson.eipValue = goods.bindPublicIp ? Number(goods.eipModelList?.[0]?.bandwidth || 0) : 0
      orderJson.isBindPublicNetworkIp = goods.bindPublicIp ? '1' : '0'
      orderJson.planeValue = goods.plane?.split(',') || []
      orderJson.functionalModule = String(goods.functionalModule || '')
      orderJson.ecs = [goods.flavorType || '', goods.flavorName || '']
      orderJson.isMountEvs = goods.mountDataDisk ? '1' : '0'
      orderJson.evs = goods.mountDataDiskList?.map((item: any) => [
        String(item.sysDiskType || ''),
        item.sysDiskSize || 0,
      ])
      orderJson.sysDisk = [String(goods.sysDiskType || ''), Number(goods.sysDiskSize || 0)]
      orderJson.imageOs = [goods.imageOs, goods.imageVersion]
      orderJson.instanceName = goods.originName
      orderJson.time = goods.applyTime
      return {
        id: generateRandom8DigitNumber(),
        goodsType: 'ecs',
        orderJson: orderJson,
      }
    }) || []
  cartsModel.mysqlList =
    entity.mysqlModelList?.map((goods: any) => {
      const orderJson = useMysqlModel()
      orderJson.numbers = goods.openNum
      orderJson.goodsId = goods.id
      orderJson.disasterRecovery = goods.disasterRecovery ? '1' : '0'
      orderJson.eipValue = goods.bindPublicIp ? Number(goods.eipModelList?.[0]?.bandwidth || 0) : 0
      orderJson.isBindPublicNetworkIp = goods.bindPublicIp ? '1' : '0'
      orderJson.planeValue = goods.plane?.split(',') || []
      orderJson.functionalModule = String(goods.functionalModule || '')
      orderJson.ecs = [goods.flavorType || '', goods.flavorName || '']
      orderJson.isMountEvs = goods.mountDataDisk ? '1' : '0'
      orderJson.evs = goods.mountDataDiskList?.map((item: any) => [
        String(item.sysDiskType || ''),
        item.sysDiskSize || 0,
      ])
      orderJson.sysDisk = [String(goods.sysDiskType || ''), Number(goods.sysDiskSize || 0)]
      orderJson.imageOs = [goods.imageOs, goods.imageVersion]
      orderJson.instanceName = goods.originName
      orderJson.time = goods.applyTime
      return {
        id: generateRandom8DigitNumber(),
        goodsType: 'mysql',
        orderJson: orderJson,
      }
    }) || []
  cartsModel.redisList =
    entity.redisModelList?.map((goods: any) => {
      const orderJson = useRedisModel()
      orderJson.numbers = goods.openNum
      orderJson.goodsId = goods.id
      orderJson.disasterRecovery = goods.disasterRecovery ? '1' : '0'
      orderJson.eipValue = goods.bindPublicIp ? Number(goods.eipModelList?.[0]?.bandwidth || 0) : 0
      orderJson.isBindPublicNetworkIp = goods.bindPublicIp ? '1' : '0'
      orderJson.planeValue = goods.plane?.split(',') || []
      orderJson.functionalModule = String(goods.functionalModule || '')
      orderJson.ecs = [goods.flavorType || '', goods.flavorName || '']
      orderJson.isMountEvs = goods.mountDataDisk ? '1' : '0'
      orderJson.evs = goods.mountDataDiskList?.map((item: any) => [
        String(item.sysDiskType || ''),
        item.sysDiskSize || 0,
      ])
      orderJson.sysDisk = [String(goods.sysDiskType || ''), Number(goods.sysDiskSize || 0)]
      orderJson.imageOs = [goods.imageOs, goods.imageVersion]
      orderJson.instanceName = goods.originName
      orderJson.time = goods.applyTime
      return {
        id: generateRandom8DigitNumber(),
        goodsType: 'redis',
        orderJson: orderJson,
      }
    }) || []
  cartsModel.gcsList =
    entity.gcsModelList?.map((goods: any) => {
      const orderJson = useGcsModel()
      orderJson.numbers = goods.openNum
      orderJson.goodsId = goods.id
      orderJson.disasterRecovery = goods.disasterRecovery ? '1' : '0'
      orderJson.eipValue = goods.bindPublicIp ? Number(goods.eipModelList?.[0]?.bandwidth || 0) : 0
      orderJson.isBindPublicNetworkIp = goods.bindPublicIp ? '1' : '0'
      orderJson.planeValue = goods.plane?.split(',') || []
      orderJson.functionalModule = String(goods.functionalModule || '')
      orderJson.gcs = [goods.flavorType || '', goods.flavorName || '']
      orderJson.isMountEvs = goods.mountDataDisk ? '1' : '0'
      orderJson.evs = goods.mountDataDiskList?.map((item: any) => [
        String(item.sysDiskType || ''),
        item.sysDiskSize || 0,
      ])

      orderJson.sysDisk = [String(goods.sysDiskType || ''), Number(goods.sysDiskSize || 0)]
      orderJson.imageOs = [goods.imageOs, goods.imageVersion]
      orderJson.instanceName = goods.originName
      orderJson.time = goods.applyTime
      return {
        id: generateRandom8DigitNumber(),
        goodsType: 'gcs',
        orderJson: orderJson,
      }
    }) || []
  cartsModel.evsList =
    entity.evsModelList?.map((goods: any) => {
      const orderJson = useEvsModel()
      orderJson.isMountEcs = goods.vmId ? '1' : '0'
      orderJson.ecsName = goods.vmName
      orderJson.vmId = goods.vmId
      orderJson.numbers = goods.openNum
      orderJson.goodsId = goods.id
      orderJson.functionalModule = goods.functionalModule
      orderJson.evs = [[goods.sysDiskType, goods.sysDiskSize]]
      orderJson.instanceName = goods.originName
      orderJson.time = goods.applyTime
      return {
        id: generateRandom8DigitNumber(),
        goodsType: 'evs',
        orderJson: orderJson,
      }
    }) || []
  cartsModel.eipList =
    entity.eipModelList?.map((goods: any) => {
      const orderJson = useEipModel()
      orderJson.eipValue = goods.bandwidth
      orderJson.ecsName = goods.vmName
      orderJson.vmId = goods.vmId
      orderJson.numbers = goods.openNum
      orderJson.goodsId = goods.id
      orderJson.functionalModule = goods.functionalModule
      orderJson.instanceName = goods.originName
      orderJson.time = goods.applyTime
      return {
        id: generateRandom8DigitNumber(),
        goodsType: 'eip',
        orderJson: orderJson,
      }
    }) || []
  cartsModel.obsList =
    entity.obsModelList?.map((goods: any) => {
      const orderJson = useObsModel()
      orderJson.numbers = goods.openNum
      orderJson.goodsId = goods.id
      orderJson.functionalModule = goods.functionalModule
      orderJson.obs = [goods.storageDiskType, goods.storageDiskSize]
      orderJson.instanceName = goods.originName
      orderJson.time = goods.applyTime
      return {
        id: generateRandom8DigitNumber(),
        goodsType: 'obs',
        orderJson: orderJson,
      }
    }) || []

  cartsModel.slbList =
    entity.slbModelList?.map((goods: any) => {
      const orderJson = useSlbModel()
      orderJson.numbers = goods.openNum
      orderJson.goodsId = goods.id
      orderJson.eipValue = goods.bindPublicIp ? goods.eipModelList?.[0]?.bandwidth || 0 : 0
      orderJson.isBindPublicNetworkIp = goods.bindPublicIp ? '1' : '0'
      orderJson.functionalModule = goods.functionalModule
      orderJson.slb = goods.flavorName
      orderJson.instanceName = goods.originName
      orderJson.time = goods.applyTime
      return {
        id: generateRandom8DigitNumber(),
        goodsType: 'slb',
        orderJson: orderJson,
      }
    }) || []

  cartsModel.natList =
    entity.natModelList?.map((goods: any) => {
      const orderJson = useNatModel()
      orderJson.numbers = goods.openNum
      orderJson.goodsId = goods.id
      orderJson.eipValue = goods.bindPublicIp ? goods.eipModelList?.[0]?.bandwidth || 0 : 0
      orderJson.isBindPublicNetworkIp = goods.bindPublicIp ? '1' : '0'
      orderJson.functionalModule = goods.functionalModule
      orderJson.nat = goods.flavorName
      orderJson.instanceName = goods.originName
      orderJson.time = goods.applyTime
      return {
        id: generateRandom8DigitNumber(),
        goodsType: 'nat',
        orderJson: orderJson,
      }
    }) || []

  cartsModel.cqList =
    entity.cqModelList?.map((goods: any) => {
      const orderJson = useCqModel()
      orderJson.cpu[0] = goods.vCpus
      orderJson.cpu[1] = goods.ram
      orderJson.gpu[0] = goods.gpuRatio
      orderJson.gpu[1] = goods.gpuVirtualMemory
      orderJson.gpu[2] = goods.gpuCore
      orderJson.time = goods.applyTime
      orderJson.instanceName = goods.originName
      orderJson.isUseGpu = goods.gpuRatio ? '1' : '0'
      return {
        id: generateRandom8DigitNumber(),
        goodsType: 'cq',
        orderJson: orderJson,
      }
    }) || []
  cartsModel.backupList =
    entity.backupModelList?.map((goods: any) => {
      const orderJson = useBackupModel()
      orderJson.jobName = goods.originName
      orderJson.backupType = goods.backupType
      orderJson.frequency = goods.frequency
      orderJson.daysOfWeek = goods.daysOfWeek
      orderJson.objectIdList = goods.objectIdList
      return {
        id: generateRandom8DigitNumber(),
        goodsType: 'backup',
        orderJson: orderJson,
      }
    }) || []
}
async function loadDatafromShoppingCarts(cartsModel: ICartsModel): Promise<void> {
  const { entity, code } = await shoppingCartList()
  if (code === 200) {
    if (!entity.baseList?.length) {
      const baseId = await createRecord('base')
      if (baseId) {
        cartsModel.baseList[0].id = baseId
        Object.assign(cartsModel.baseList[0].orderJson, useBaseModel())
        nextTick(() => {
          cartsModel.baseList[0].ref.clearValidate()
        })
      }
    } else {
      const item: { id: number; goodsType: GoodsType; orderJson: string } = entity.baseList[0]
      cartsModel.baseList[0].id = item.id
      cartsModel.baseList[0].goodsType = item.goodsType
      cartsModel.baseList[0].orderJson = Object.assign(
        cartsModel.baseList[0].orderJson,
        item.orderJson,
      )
    }
    const keys: CartListKey[] = [
      'ecsList',
      'evsList',
      'gcsList',
      'obsList',
      'slbList',
      'natList',
      'eipList',
      'cqList',
      'mysqlList',
      'redisList',
      'backupList',
    ]
    keys.forEach((list: CartListKey) => {
      cartsModel[list] =
        entity[list]?.map((ele: { id: number; goodsType: GoodsType; orderJson: string }) => {
          let parsedOrderJson:
            | IEcsModel
            | IEvsModel
            | IGcsModel
            | IObsModel
            | ISlbModel
            | INatModel
            | IEipModel
            | ICqModel
            | IMysqlModel
            | IRedisModel
            | IBackupModel
          switch (ele.goodsType) {
            case 'ecs':
              parsedOrderJson = Object.assign(useEcsModel(), ele.orderJson)
              break
            case 'evs':
              parsedOrderJson = Object.assign(useEvsModel(), ele.orderJson)
              break
            case 'gcs':
              parsedOrderJson = Object.assign(useGcsModel(), ele.orderJson)
              break
            case 'obs':
              parsedOrderJson = Object.assign(useObsModel(), ele.orderJson)
              break
            case 'slb':
              parsedOrderJson = Object.assign(useSlbModel(), ele.orderJson)
              break
            case 'nat':
              parsedOrderJson = Object.assign(useNatModel(), ele.orderJson)
              break
            case 'eip':
              parsedOrderJson = Object.assign(useEipModel(), ele.orderJson)
              break
            case 'cq':
              parsedOrderJson = Object.assign(useCqModel(), ele.orderJson)
              break
            case 'mysql':
              parsedOrderJson = Object.assign(useMysqlModel(), ele.orderJson)
              break
            case 'redis':
              parsedOrderJson = Object.assign(useRedisModel(), ele.orderJson)
              break
            case 'backup':
              parsedOrderJson = Object.assign(useBackupModel(), ele.orderJson)
              break
            default:
              throw new Error(`Unknown goods type: ${ele.goodsType}`)
          }

          return {
            id: ele.id,
            goodsType: ele.goodsType,
            orderJson: parsedOrderJson,
          }
        }) || []
    })
  }
  return
}
export function useShoppingCarts() {
  const route = useRoute()
  const cartsModel: ICartsModel = reactive({
    ecsList: [],
    evsList: [],
    gcsList: [],
    obsList: [],
    slbList: [],
    natList: [],
    eipList: [],
    cqList: [],
    mysqlList: [],
    redisList: [],
    baseList: [
      {
        id: 0,
        goodsType: 'base',
        orderJson: useBaseModel(),
      },
    ],
    backupList: [],
  })
  if (route.query.orderId) {
    // 从工单回显数据
    loadDatafromOrderEcho(cartsModel, route.query.orderId as string)
  } else {
    // 从暂存回显数据
    loadDatafromShoppingCarts(cartsModel)
  }

  eventBus.on('shoppingCarts:addGoods', handleAddGoods)
  eventBus.on('shoppingCarts:deleteGoods', handleDelGoods)
  eventBus.on('shoppingCarts:updateGoods', handleUpdateGoods)
  eventBus.on('shoppingCarts:clear', handleClear)
  eventBus.on('shoppingCarts:refresh', handleRefresh)

  onUnmounted(() => {
    eventBus.off('shoppingCarts:addGoods')
    eventBus.off('shoppingCarts:handleDelGoods')
    eventBus.off('shoppingCarts:updateGoods')
    eventBus.off('shoppingCarts:clear')
    eventBus.off('shoppingCarts:refresh')
  })
  function handleRefresh() {
    eventBus.emit('shoppingCarts:updateCount')
    if (route.query.orderId) {
      loadDatafromOrderEcho(cartsModel, route.query.orderId as string)
    } else {
      loadDatafromShoppingCarts(cartsModel)
    }
  }
  async function handleClear() {
    const res = await clearRecord(cartsModel)
    if (res) {
      eventBus.emit('shoppingCarts:updateCount')
      loadDatafromShoppingCarts(cartsModel)
    }
  }

  function handleUpdateGoods(goods: any) {
    console.log('shoppingCarts:updateGoods', goods)
    updateRecord(cartsModel)
  }

  async function handleDelGoods({ goods, isEdit }: any) {
    const res = !isEdit ? await deleteRecord([goods.id], goods.goodsType) : true
    if (res) {
      if (!isEdit) eventBus.emit('shoppingCarts:updateCount')
      const listKey = `${goods.goodsType}List` as CartListKey
      const list = cartsModel[listKey]
      const index = list.findIndex((item: any) => item.id === goods.id)
      if (index > -1) {
        list.splice(index, 1)
      }
    }
  }

  async function handleAddGoods({ goodsType, isEdit }: any) {
    console.log('shoppingCarts:addGoods', goodsType)
    setTimeout(() => {
      const scrollView = document.querySelector('.shopping-carts-scroll-view')
      scrollView?.scrollTo({
        top: scrollView?.scrollHeight,
        behavior: 'smooth',
      })
    }, 200)
    // 暂存时，请求ID，创建记录；编辑时，生成随机ID，不创建记录
    const goodsId = !isEdit ? await createRecord(goodsType) : generateRandom8DigitNumber()
    if (goodsId) {
      if (!isEdit) eventBus.emit('shoppingCarts:updateCount')
      switch (goodsType) {
        case 'ecs':
          cartsModel.ecsList.push({
            id: goodsId,
            goodsType: 'ecs',
            orderJson: useEcsModel(),
          })
          break
        case 'obs':
          cartsModel.obsList.push({
            id: goodsId,
            goodsType: 'obs',
            orderJson: useObsModel(),
          })
          break
        case 'slb':
          cartsModel.slbList.push({
            id: goodsId,
            goodsType: 'slb',
            orderJson: useSlbModel(),
          })
          break
        case 'nat':
          cartsModel.natList.push({
            id: goodsId,
            goodsType: 'nat',
            orderJson: useNatModel(),
          })
          break

        case 'gcs':
          cartsModel.gcsList.push({
            id: goodsId,
            goodsType: 'gcs',
            orderJson: useGcsModel(),
          })
          break
        case 'evs':
          cartsModel.evsList.push({
            id: goodsId,
            goodsType: 'evs',
            orderJson: useEvsModel(),
          })
          break
        case 'eip':
          cartsModel.eipList.push({
            id: goodsId,
            goodsType: 'eip',
            orderJson: useEipModel(),
          })
          break
        case 'cq':
          cartsModel.cqList.push({
            id: goodsId,
            goodsType: 'cq',
            orderJson: useCqModel(),
          })
          break
        case 'mysql':
          cartsModel.mysqlList.push({
            id: goodsId,
            goodsType: 'mysql',
            orderJson: useMysqlModel(),
          })
          break
        case 'redis':
          cartsModel.redisList.push({
            id: goodsId,
            goodsType: 'redis',
            orderJson: useRedisModel(),
          })
          break
        case 'backup':
          cartsModel.backupList.push({
            id: goodsId,
            goodsType: 'backup',
            orderJson: useBackupModel(),
          })
          break
        default:
          throw new Error(`Unknown goods type: ${goodsType}`)
      }
    }
  }
  return cartsModel
}
