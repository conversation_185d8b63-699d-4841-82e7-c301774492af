<template>
  <el-popover
    v-model:visible="visible"
    :placement="placement"
    @hide="resetSelection"
    :width="400"
    trigger="click"
  >
    <template #reference>
      <el-button type="primary" link :icon="Filter"></el-button>
    </template>
    <div class="list-title">
      <div>添加筛选</div>
      <div>
        <el-icon @click="visible = false"><Filter /> </el-icon>
      </div>
    </div>
    <el-checkbox-group :min="minSelected" :max="maxSelected" v-model="selectedValues">
      <el-checkbox
        v-for="check in columns"
        :key="check.prop"
        :value="check.prop"
        :disabled="check.search.defaultDisabled"
      >
        {{ check.label }}
      </el-checkbox>
    </el-checkbox-group>
    <div class="button-group">
      <el-button @click="visible = false">取 消</el-button>

      <el-button type="primary" @click="confirmSelection" :disabled="selectedCount < minSelected">
        确 认
      </el-button>
    </div>
  </el-popover>
</template>

<script setup lang="ts">
import { computed, toRefs, ref } from 'vue'
import { Filter } from '@element-plus/icons-vue'
import type { ColumnProps } from '@/components/SlProTable/interface'
const props = withDefaults(
  defineProps<{
    columns: ColumnProps[]
    placement?: string // popover 的位置
    maxSelected?: number // 最大选中数量
    minSelected?: number // 最小选中数量
    values: string[]
  }>(),
  {
    columns: () => [],
    placement: 'left-start',
    maxSelected: 5,
    minSelected: 2,
  },
)
const emit = defineEmits<{
  (e: 'update:columns', columns: Array<any>, falg?: boolean): void // 自定义事件
}>()

const { columns, maxSelected, minSelected } = toRefs(props)
const selectedValues = ref<string[]>(props.values) // 用于存储选中的值

const visible = ref(false)

// 计算已选中的复选框数量
const selectedCount = computed(() => {
  return selectedValues.value.length
})

// 确认选择
const confirmSelection = () => {
  emit('update:columns', selectedValues.value, true) // 向父组件发送更新后的列数据
  visible.value = false
}

// 重置选择
const resetSelection = () => {
  // selectedValues.value =
  //   columns.value.filter((check) => check.search.checked)?.map((item) => item.prop!) ?? []

  // emit('update:columns', selectedValues.value, true) // 向父组件发送更新后的列数据
  selectedValues.value = props.values
}

//  添加选中默认值
const addDefaultChecked = (values: string[]) => {
  selectedValues.value = values
}
defineExpose({
  addDefaultChecked,
})
</script>

<style scoped>
.list-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-right: 10px;
  font-weight: bold;
  margin-bottom: 10px;
}
.button-group {
  margin-top: 10px;
  display: flex;
  justify-content: end;
}
</style>
