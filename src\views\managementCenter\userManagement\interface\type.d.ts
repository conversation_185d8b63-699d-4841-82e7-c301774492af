/**
 * @name 创建用户接口类型
 */

interface UserRequestType {
  id: number // 用户ID
  createdBy: number // 创建者ID
  createdTime: string // 创建时间
  updatedBy: number // 更新者ID
  updatedTime: string // 更新时间
  status: number // 状态
  loginName: string // 登录账号(oa用户ID 具有唯一标识)
  userName: string // 用户名
  phone: string // 手机号
  sex: string // 性别: male: 男, female: 女
  pwd: string // 账号密码
  userType: string // 用户类型
  userEmail: string // 邮箱
  orgId: number // 归属组织编号
  tenantId: number // 归属租户编号
  departmentDn: string // 部门dn, e.g., OU=信息技术部,O=zmcc
  oaLastTime: string // oa-最后变更时间
  jobName: string // 职位名称
  sort: number // 排序
  activeStatus: number // 激活状态(1: 激活, 0: 未激活, 2：已冻结)
  userOrgName: string // 所属部门组织全称
  oacRoles: Role[] // 角色列表
}

// 角色定义
interface Role {
  id: number // 角色id
  roleName: string // 角色名字
  roleCode: string // 角色编码
}

/**
 * @name 获取列表传参
 */
export interface UserQueryParamsType {
  // 用户信息模型接口
  id?: number // 主键id
  query?: string //
  loginName?: string // 登录账号
  userName?: string // 用户名
  phone?: string // 手机号
  sex?: 'male' | 'female' // 性别: male: 男, female: 女
  userType?: string // 用户类型
  orgId?: number // 归属组织编号
  tenantId?: number // 归属租户编号
  status?: number // 状态
  startTime?: string // 开始时间
  endTime?: string // 结束时间
  pageSize?: number // 页容量
  pageNum?: number // 当前页
}
