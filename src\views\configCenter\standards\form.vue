<template>
  <div class="resourcerequest">
    <sl-page-header
      :title="`${formModel.operationName}${formModel.goodsName}`"
      :icon="{
        Vnode: Platform,
        color: '#0052D9',
        size: '20px',
      }"
      :show-back="true"
      :back="{
        title: '返回列表',
        function: goBack,
      }"
    >
    </sl-page-header>
    <div class="resourcerequestbox">
      <div class="onebox">
        <div class="oneboxleft">
          <el-scrollbar class="scroll-view">
            <sl-form
              ref="slFormRef"
              show-block-title
              :options="formOptions"
              :model-value="formModel"
              style="overflow: hidden"
            >
            </sl-form>
          </el-scrollbar>
        </div>
      </div>
      <div class="onefooter">
        <el-button @click="goBack">取消</el-button>
        <el-button v-if="readOnly" type="primary" @click="submitfromdata">提交申请</el-button>
      </div>
    </div>
  </div>
</template>
<script setup lang="tsx">
import { Platform } from '@element-plus/icons-vue'
import slForm from '@/components/form/SlForm.vue'
import { reactive, ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import SlMessage from '@/components/base/SlMessage'
// import SlBlockTitle from '@/components/base/SlBlockTitle.vue'
import { useGlobalDicStore } from '@/stores/modules/dic'
// import { submitResourceRequest } from '@/api/modules/resourecenter'
import { useSelectLabel } from '@/hooks/useSelectLabel'
// import { getAz } from '@/api/modules/resourecenter'
// import { Delete } from '@element-plus/icons-vue'
import useModel from './model'
import {
  addFlavorApi,
  getFlavorTypeListApi,
  getPlatformListApi,
  getFlavorResTypesApi,
  getAzsListApi,
  getRegionsListApi,
  getFlavorPackagesTypesApi,
} from '@/api/modules/configCenter'
import { validateNumber } from '@/utils/validate'
// import {  getAz  } from '@/api/modules/resourecenter'

const model = useModel()
const formModel = reactive<{ [key: string]: any }>({})
Object.assign(formModel, model)

const globalDic = useGlobalDicStore()

const slFormRef = ref()
const router = useRouter()
const { getDic } = globalDic
const route = useRoute()
// const orderId = route.query.orderId as string
const flavorId = route.query.flavorId as string
const readOnly = !flavorId
// select 配置项
// const resourcePoolOptions = ref<any>([])

// 配置信息下拉label回显

formModel.functionalModuleLabel = useSelectLabel(
  () => getDic('functionalModule'),
  () => formModel.functionalModule,
)
const categoryCodeList: any = ref([])
// const categoryNameList: any = ref([])
const dominCodeList: any = ref([])
const regionList: any = ref([]) //  region列表
const flavorTypeList: any = ref([]) //  规格类型列表
const azList: any = ref([]) //  az列表
const configCodesList: any = ref([]) //  是否公有所选套餐信息

// 获取表单中需要选择数据项的原数据
function init() {
  // 根据云平台查询规格类型
  getFlavorResTypesApi({}).then((res: any) => {
    flavorTypeList.value = res.entity
  })

  // 根据云平台查询规格类型
  getFlavorPackagesTypesApi({}).then((res: any) => {
    configCodesList.value = res.entity
  })
}
init()
const formOptions = reactive([
  {
    style: 'margin-top: 0;margin-bottom: 0;',
    groupName: '基础信息',
    groupItems: [
      {
        label: '产品类型',
        type: 'select',
        key: 'flavorType',
        options: flavorTypeList,
        labelField: 'resName',
        valueField: 'resType',
        onChange(option: any) {
          if (option.resType == 'ecs' || option.resType == 'gcs') {
            formOptions[0].groupItems[9].hidden = false
            formOptions[0].groupItems[10].hidden = false
          } else {
            formOptions[0].groupItems[9].hidden = true
            formOptions[0].groupItems[10].hidden = true
          }
          formModel.domainCode = ''
          formModel.cateGoryCode = ''
          // 根据规格类型查询云平台
          getPlatformListApi({
            resType: option.resType,
          }).then((res: any) => {
            dominCodeList.value = res.entity
          })
          //根据规格类型查询云平台
          getFlavorTypeListApi({
            domainCode: formModel.domainCode,
            resType: option.resType,
          }).then((res: any) => {
            categoryCodeList.value = res.entity
          })
        },
        span: 12,
        rules: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
      },
      {
        label: '规格名称',
        type: 'input',
        key: 'serviceName',
        placeholder: '请输入',
        props: {
          rows: 1,
          maxlength: 50,
        },
        span: 12,
        rules: [{ required: true, message: '请输入规格名称', trigger: ['blur', 'change'] }],
      },
      {
        label: '规格编码',
        type: 'input',
        key: 'specCode',
        props: {
          rows: 1,
          maxlength: 50,
        },
        placeholder: '请输入，如ecs.c1.model.small.1',
        span: 12,
        rules: [{ required: true, message: '请输入规格编码', trigger: ['blur', 'change'] }],
      },
      {
        label: '规格id',
        type: 'input',
        key: 'resourceId',
        placeholder: '请输入，如406a1d71-fab4-4c65-93a4-d2b836f2b097',
        props: {
          rows: 1,
          maxlength: 200,
        },
        span: 12,
        rules: [{ required: true, message: '请输入规格id', trigger: ['blur', 'change'] }],
      },
      {
        label: '规格信息',
        type: 'input',
        key: 'specInfo',
        placeholder: '请输入，如2C2G',
        props: {
          rows: 1,
          maxlength: 100,
        },
        span: 12,
        rules: [{ required: true, message: '请输入规格信息', trigger: ['blur', 'change'] }],
      },
      {
        label: '云平台',
        type: 'select',
        key: 'domainCode',
        options: dominCodeList,
        labelField: 'domainName',
        valueField: 'domainCode',
        onChange(option: any) {
          // formModel.domainCode = option.domainCode
          formModel.regionId = []
          categoryCodeList.value = []
          formModel.cateGoryCode = ''
          getRegionsListApi({
            domainCode: option.domainCode,
            pageSize: 999,
          }).then((res: any) => {
            regionList.value = res.entity.records
          })
          getFlavorTypeListApi({
            domainCode: option.domainCode,
            resType: formModel.flavorType,
          }).then((res: any) => {
            categoryCodeList.value = res.entity
          })

          if (option.domainCode == 'plf_prov_nwc_zj_nfvo') {
            formOptions[0].groupItems[6].limit = 1
            formOptions[0].groupItems[7].hidden = false
          } else {
            formOptions[0].groupItems[6].limit = 0
            formOptions[0].groupItems[7].hidden = true
          }
        },
        span: 12,
        rules: {
          required: true,
          message: '请选择',
          trigger: ['blur', 'change'],
        },
      },
      {
        label: '资源池',
        type: 'select',
        key: 'regionId',
        options: regionList,
        labelField: 'name',
        valueField: 'id',
        multiple: true,
        limit: 0,
        onChange(option: any) {
          if (formModel.domainCode == 'plf_prov_nwc_zj_nfvo') {
            getAzsListApi({
              regionCode: option[0].code,
            }).then((res: any) => {
              azList.value = res.entity.records
            })
          }
        },
        rules: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
        span: 12,
      },
      {
        hidden: true,
        label: '可用区',
        type: 'select',
        key: 'azId',
        options: azList,
        labelField: 'azName',
        valueField: 'id',
        rules: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
        span: 12,
      },
      {
        label: '产品型号',
        type: 'select',
        key: 'cateGoryCode',
        options: categoryCodeList,
        labelField: 'productName',
        valueField: 'productCode',
        span: 12,
        onChange(option: any) {
          formModel.cateGoryName = option.productName
        },
        rules: {
          required: true,
          message: '请选择',
          trigger: ['blur', 'change'],
        },
      },
      {
        hidden: true,
        label: 'cpu(核)',
        type: 'input',
        key: 'vcpus',
        span: 12,
        // suffix: '核',
        rules: [
          { required: true, message: '请输入', trigger: ['blur', 'change'] },
          { validator: validateNumber, trigger: ['blur'] },
        ],
      },
      {
        hidden: true,
        label: '内存(GB)',
        type: 'input',
        key: 'ram',
        // suffix: 'MB',
        span: 12,
        rules: [
          { required: true, message: '请输入', trigger: ['blur', 'change'] },
          { validator: validateNumber, trigger: ['blur'] },
        ],
      },
      {
        label: '是否公有',
        type: 'select',
        key: 'shares',
        options: [
          {
            label: '是',
            value: '1',
          },
          {
            label: '否',
            value: '0',
          },
        ],
        rules: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
        onChange(option: any) {
          if (option.value == '1') {
            formOptions[0].groupItems[12].hidden = false
          } else {
            formOptions[0].groupItems[12].hidden = true
          }
        },
        span: 12,
      },
      {
        label: '所属套餐',
        type: 'select',
        hidden: true,
        multiple: true,
        key: 'configCodes',
        options: configCodesList,
        labelField: 'configName',
        valueField: 'configCode',
        rules: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
        span: 12,
      },
      {
        label: '描述',
        type: 'input',
        key: 'description',
        props: {
          type: 'textarea',
          rows: 4,
          maxlength: 255,
          showWordLimit: true,
        },
        span: 24,
      },
    ],
  },
])
//
const doSubmit = async () => {
  const validate = await slFormRef.value.validate()
  if (!validate) return
  const submitdata = {
    domainCode: formModel.domainCode, //云平台
    regionIdList: formModel.regionId, //资源池
    azId: Number(formModel.azId), //可用区
    serviceName: formModel.serviceName, //规格名称
    specCode: formModel.specCode, //规格编码
    resourceId: formModel.resourceId, //规格id
    specInfo: formModel.specInfo, //规格信息
    flavorType: formModel.flavorType, //规格类型
    cateGoryCode: formModel.cateGoryCode, //产品型号
    cateGoryName: formModel.cateGoryName, //规格型号
    vcpus: Number(formModel.vcpus) || 0, // cpu核数
    ram: Number(formModel.ram) || 0, //内存
    // disk: formModel.disk || 0, //系统盘
    // shares: formModel.shares, // 是否公有
    isShare: formModel.shares, // 是否公有
    configCodes: formModel.configCodes, // 套餐信息
    description: formModel.description, //描述
  }
  try {
    const entity = await addFlavorApi(submitdata)
    if (entity.code == 200) {
      SlMessage.success('提交成功')
      goBack()
    } else {
      SlMessage.error('提交失败')
    }
  } finally {
  }
}
const submitfromdata = async () => {
  if (!slFormRef.value) return
  doSubmit()
}
function goBack() {
  router.go(-1)
}
</script>
<style scoped>
.scroll-view {
  height: calc(100vh - 188px);
}

.resourcerequest {
  .resourcerequestbox {
    .onebox {
      display: flex;

      .oneboxleft {
        flex-grow: 3;
        margin: 8px 0 8px 0;
      }
    }

    .onefooter {
      height: 48px;
      margin: 0 8px;
      background: #ffffff;
      border-radius: 8px 8px 8px 8px;
      display: flex;
      align-items: center;
      justify-content: right;
      padding: 0 14px;
    }
  }
}
</style>
