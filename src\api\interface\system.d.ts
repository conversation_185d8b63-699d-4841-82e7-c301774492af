/*
 * @LastEditTime: 2023-11-25 17:34:23
 * @FilePath: /4.0/src/api/interface/system.d.ts
 */
export type SysMenu = {
  /**
   * 高亮地址
   */
  activeMenu?: string
  /**
   * 组件路径
   */
  component?: string
  /**
   * 菜单图标
   */
  icon?: string
  /**
   * 菜单是否固定在标签页中 0-否 1-是
   */
  isAffix?: number
  /**
   * 是否缓存（0缓存 1不缓存）
   */
  isCache?: string
  /**
   * 是否为外链（0是 1否）
   */
  isFrame?: string
  /**
   * 菜单是否全屏 0-否 1-是
   */
  isFull?: number
  /**
   * 菜单ID
   */
  menuId?: number
  /**
   * 菜单名称
   */
  menuName: string
  /**
   * 菜单类型（M目录 C菜单 F按钮）
   */
  menuType: string
  /**
   * 显示顺序
   */
  orderNum: number
  /**
   * 父菜单ID
   */
  parentId?: number
  /**
   * 路由地址
   */
  path?: string
  /**
   * 权限标识
   */
  perms?: string
  /**
   * 路由参数
   */
  queryParam?: string
  /**
   * 重定向地址
   */
  redirect?: string
  /**
   * 备注
   */
  remark?: string
  /**
   * 菜单状态（0正常 1停用）
   */
  status?: string
  /**
   * 显示状态（0显示 1隐藏）
   */
  visible?: string
}
