import http from '@/api'
import { USER, WOC } from '../config/servicePort'
import authMenuList from '@/assets/json/authMenuList.json'
// ---------------------全局通用接口存放地点----------------
/**
 * @name 上传文件
 */
export const uploadApi = (data: any, config?: any) =>
  http.post<any>(WOC + '/file/upload', data, config)

/**
 * @name 下载文件
 */
export const downloadApi = (config: any) =>
  http.get<any>(WOC + '/file/download', { fileId: config }, { responseType: 'blob' })
//

/**
 * @name 下载模板文件
 */
export const downloadTemplateApi = (config: any) =>
  http.get<any>(WOC + '/file/download/template', { templateName: config }, { responseType: 'blob' })
//

// ------------------------登录-------------------------

/**
 * @name 普通登录
 */
export const loginApi = (config: any) => http.post<any>(USER + '/user/login', config)

/**
 * @name 普通登录
 */
export const oaLogin = (config: any) => http.get<any>(USER + '/user/login_oa', config)

/**
 * @name 普通登录
 */
export const refreshToken = () => http.get<any>(USER + '/user/refresh_token')

/**
 * @name 图片验证码
 */
export const getCaptchaImage = (config: any) =>
  http.get<any>(USER + '/user/captcha/captchaImage', config)

/**
 * @name 短信验证码
 */
export const sendSmsCode = (config: any) =>
  http.post<any>(USER + '/user/captcha/sendSmsCode', config)

/**
 * @name 获取有权限的菜单列表
 */
export const getAuthMenuListApi = () => {
  return http.get<any[]>(USER + `/menu/selectMenuByUser`, {}, { loading: false })
}

/**
 * @name 获取所有菜单列表
 */
export const getAllMenuListApi = () => {
  return authMenuList
}

/**
 * @name 检查md5
 */
export const checkMd5 = (md5: string) => {
  return http.get<any>(WOC + '/file/check', { md5 })
}
/**
 * @name 合并文件
 */
export const merge = (data: any) => {
  return http.post<any>(WOC + '/file/merge', data)
}

/**
 * @name 获取预签名地址
 */
export const getPresignedUrl = (data: any) => {
  return http.get<any>(WOC + '/file/uploadChunk', data)
}

/**
 * @name 直连请求，带http://
 */
export const directRequest = (url: string, data: any, config: any) => {
  return http.put<any>(url, data, config)
  // return http.put<any>(url, {}, config)
}
