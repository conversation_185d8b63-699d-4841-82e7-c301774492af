<template>
  <el-dialog
    v-model="dialogVisible"
    title="选择云主机"
    width="80%"
    destroy-on-close
    top="10vh"
    :before-close="handleCancel"
    class="device-bind-dialog"
  >
    <div class="filter-form-con" style="margin-bottom: 0">
      <sl-form
        class="filter-form"
        ref="formRef"
        :options="formOptions"
        v-model="formModel"
      ></sl-form>
    </div>

    <div class="tab-wrapper">
      <div class="tab-content">
        <EcsList
          ref="dataListRef"
          :query-params="queryParams"
          :is-hidden-selection="true"
        ></EcsList>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">关闭</el-button>
        <el-button type="primary" @click="handleConfirm"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="tsx">
import { ref, reactive, computed, inject } from 'vue'
import { Delete, Search } from '@element-plus/icons-vue'
import slForm from '@/components/form/SlForm.vue'
import EcsList from '@/views/resourceCenter/ecs/dataList.vue'

const props = defineProps<{
  visible: boolean
}>()

const emit = defineEmits(['update:visible', 'handleConfirm'])
const slbDetail = inject<any>('slbDetail')
// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val),
})

// 当前激活的标签
const formRef = ref()
const dataListRef = ref()

// 设备查询条件
const formModel = reactive({
  deviceName: '',
  resourcePoolId: '',
})
const queryParams = ref<any>({
  hideSelection: false,
  type: 'ecs',
  vpcId: computed(() => slbDetail.value.vpcId || ''),
})

// 表单配置
const formOptions = reactive([
  {
    style: 'padding: 0',
    groupItems: [
      {
        label: '云主机名称',
        type: 'input',
        key: 'deviceName',
        span: 8,
        disabled: false,
        hidden: false,
      },
      {
        span: 8,
        render() {
          return (
            <div style="display: flex;justify-content: flex-end;">
              <el-button onClick={reset} icon={<Delete />}>
                重置
              </el-button>
              <el-button onClick={search} icon={<Search />} type="primary">
                搜索
              </el-button>
            </div>
          )
        },
      },
    ],
  },
])

function reset() {
  formRef.value!.resetFields()
  queryParams.value = { ...queryParams.value, ...formModel }
}
function search() {
  queryParams.value = { ...queryParams.value, ...formModel }
}

// 取消按钮
function handleCancel() {
  dialogVisible.value = false
}

// 确认按钮
function handleConfirm() {
  const selectedList = dataListRef.value?.selectedList
  dialogVisible.value = false
  emit('handleConfirm', selectedList)
}
</script>

<style lang="scss" scoped>
.device-bind-dialog {
  :deep(.el-dialog__body) {
    height: calc(80vh - 120px);
    overflow-y: auto;
  }
}

.filter-form-con {
  margin-bottom: 16px;
}

.filter-form-btn {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.dialog-footer {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.tab-wrapper {
  background-color: #fff;
  border-radius: 4px;
  overflow: hidden;
}

.orderDetailTab {
  background-color: #f2f3f5;
  position: relative;
  margin-bottom: 0;
  &::before {
    content: '';
    display: inline-block;
    position: absolute;
    left: 5px;
    top: 11px;
    width: 4px;
    z-index: 10;
    background-color: var(--el-color-primary);
    margin-right: 10px;
    height: 0.78em;
    font-size: 18px;
  }
}

.tab-content {
  min-height: 400px;
  height: calc(80vh - 250px);
  overflow-y: auto;
  background-color: #fff;
  padding: 16px;
}
</style>
