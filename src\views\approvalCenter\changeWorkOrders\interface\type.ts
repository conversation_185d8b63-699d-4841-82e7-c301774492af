import type { ColumnProps, ProTableInstance } from '@/components/SlProTable/interface'
import type { GetResourceListResType } from '../../workOrder/interface/type'

/**
 * @name 资源列表-变更工单
 */
export interface ChangeWorkorderType {
  [key: string]: any
  activityTask: GetResourceListResType | undefined
}

// 定义任务类型
type TaskType =
  | 'user_task'
  | 'schema_administrator'
  | 'business2_depart_leader'
  | 'business_depart_leader'
  | 'cloud_leader'
  | 'cloud_leader_2'
  | 'alarm_suppression'
  | 'shutdown'
  | 'resource_change'
  | 'tenant_task'
  | 'end'
// alarm_suppression  // 告警屏蔽
// shutdown // 关机
// resource_change // 资源变更

/**
 *  @description 权限按钮类型
 */
export type ChangeBtnsType = {
  [key in TaskType]: boolean
}

export type ChangeType = 'ecs' | 'gcs' | 'evs' | 'obs' | 'slb' | 'nat' | 'eip' | 'mysql' | 'redis'

export type ChangeColumnsType<T = any> = {
  [key in ChangeType]: ColumnProps<T>[]
}

export type ChangeTabsType = {
  label: string
  name: ChangeType
  count: number
  list: FormDataType[]
} & FormDataType

export type ChangeProTableType = {
  [key in ChangeType]: ProTableInstance | null
}
