<template>
  <div id="SecurityGroupDetail" class="table-box">
    <sl-page-header
      title="安全组详情"
      :icon="{
        Vnode: Platform,
        color: '#0052D9',
        size: '20px',
      }"
      :show-back="true"
      :back="{
        title: '返回列表',
        function: handleGoBack,
      }"
    >
    </sl-page-header>
    <el-scrollbar wrap-class="security-group-scroll-view" class="scroll-view">
      <!-- 基础信息 -->
      <div class="sl-card">
        <sl-block-title>基本信息</sl-block-title>
        <sl-form
          ref="formRef"
          v-model="detailData"
          :options="formOptions"
          label-width="120px"
          :disabled="true"
        ></sl-form>
      </div>

      <!-- 安全组规则 -->
      <div class="sl-card mt-20">
        <sl-block-title>安全组规则</sl-block-title>
        <el-table
          :data="ruleList"
          border
          style="width: 100%"
          row-class-name="rule-table-row"
          header-row-class-name="rule-table-header"
        >
          <el-table-column type="index" label="序号" width="60" align="center" />
          <el-table-column prop="directionText" label="方向" width="100" />
          <el-table-column prop="policyText" label="授权策略" width="100" />
          <el-table-column prop="priority" label="优先级" width="100" />
          <el-table-column prop="protocol" label="协议类型" width="120" />
          <el-table-column prop="portRange" label="端口范围" min-width="150" />
          <el-table-column prop="accreditIp" label="授权对象" min-width="150" />
        </el-table>
      </div>
    </el-scrollbar>
  </div>
</template>

<script setup lang="tsx">
import { onMounted, ref, reactive, computed } from 'vue'
import { Platform } from '@element-plus/icons-vue'
import SlPageHeader from '@/components/SlPageHeader/index.vue'
import SlBlockTitle from '@/components/base/SlBlockTitle.vue'
import { useRouter, useRoute } from 'vue-router'
import { getSecurityGroupDetail } from '@/api/modules/resourecenter'

const router = useRouter()
const route = useRoute()

// 获取资源详情ID
const resourceId = ref<string>((route.query.id as string) || '')

const detailData = reactive<any>({
  name: '',
  regionName: '',
  vpcName: '',
  systemName: '',
  description: '',
  createTime: '',
})

const formOptions = reactive<any[]>([
  {
    groupItems: [
      {
        label: '安全组名称',
        type: 'text',
        key: 'name',
        span: 8,
      },
      {
        label: '资源池',
        type: 'text',
        key: 'regionName',
        span: 8,
      },
      {
        label: 'VPC',
        type: 'text',
        key: 'vpcName',
        span: 8,
      },
      {
        label: '业务系统',
        type: 'text',
        key: 'systemName',
        span: 8,
        hidden: computed(() => route.query.sourceType === 'DG'),
      },
      {
        label: '创建时间',
        type: 'text',
        key: 'createTime',
        span: 8,
      },
      {
        label: '描述',
        type: 'text',
        key: 'description',
        span: 16,
      },
    ],
  },
])

// 安全组规则列表
const ruleList = ref<any[]>([])

const fetchSecurityGroupDetail = async () => {
  const res = await getSecurityGroupDetail({
    id: resourceId.value,
  })
  if (res && res.entity) {
    // 填充基本信息
    for (const key in detailData) {
      detailData[key] = res.entity[key]
    }

    // 填充规则列表
    if (res.entity.ruleList && Array.isArray(res.entity.ruleList)) {
      ruleList.value = res.entity.ruleList.map((rule: any) => ({
        ...rule,
        directionText: rule.direction === 'egress' ? '出方向' : '入方向',
        policyText: rule.accessStatus === 'allow' ? '允许' : '拒绝',
      }))
    }
  }
}

// 返回上一页
const handleGoBack = () => {
  router.push({
    path: '/securityGroupList',
  })
}

// 初始化
onMounted(async () => {
  if (resourceId.value) {
    await fetchSecurityGroupDetail()
  }
})
</script>

<style lang="scss" scoped>
.table-box {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.scroll-view {
  flex: 1;
  height: calc(100vh - 140px);
  overflow-x: hidden;
}

.sl-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 0;
  margin: 8px;
}

.mt-20 {
  margin-top: 20px;
}

.rule-table-row {
  height: 50px;
}

.rule-table-header {
  background-color: #f5f7fa;
}
</style>
