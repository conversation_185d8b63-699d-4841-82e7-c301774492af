<template>
  <div class="resourcerequest">
    <div class="resourcerequestbox">
      <div class="onebox">
        <div class="oneboxleft">
          <sl-form
            ref="slFormRef"
            show-block-title
            :options="formOptions"
            :model-value="formModel"
            style="overflow: hidden"
          >
          </sl-form>
        </div>
      </div>
    </div>
    <netRangeListDialog
      @confirm="selectIpRange"
      v-if="networkDialogVisible"
      title="选择网段"
      v-model="networkDialogVisible"
      :ip-version="netRangeListDialogProps.ipVersion"
      :form-item="netRangeListDialogProps.formItem"
      :level="netRangeListDialogProps.level"
      :region-code="baseFormProvider.regionCode"
      :is-sub-net="netRangeListDialogProps.isSubNet"
    ></netRangeListDialog>
  </div>
</template>
<script setup lang="tsx">
import slForm from '@/components/form/SlForm.vue'
import { reactive, ref, computed, watch, onBeforeUnmount, inject, type Ref } from 'vue'
import { useRoute } from 'vue-router'
import SlMessage from '@/components/base/SlMessage'
import SlBlockTitle from '@/components/base/SlBlockTitle.vue'
import useModel from './model'
import { Delete, Check } from '@element-plus/icons-vue'
import netRangeListDialog from '@/views/resourceCenter/network/netRangeListDialog.vue'
import { ipRangeCheck, ipRangeSplit } from '@/api/modules/resourecenter'
import { validateIpv4Range, validateIpv6Range } from '@/utils'
import { validateGoodsName } from '../utils'
import SlButton from '@/components/base/SlButton.vue'
import type { IBaseFormProvider, SubNetFormModel, ChildNetFormModel } from './types'

const props = defineProps<{
  plane: string
}>()

const baseFormProvider = inject<Ref<IBaseFormProvider>>('baseFormProvider')!

let netRangeListDialogProps = {
  ipVersion: '',
  formItem: {},
  level: 2,
  isSubNet: false,
}
const model = useModel()
const formModel = reactive(model)
formModel.plane = props.plane

const slFormRef = ref()
const route = useRoute()
const orderId = route.query.orderId as string
const vpcId = route.query.vpcId as string
const readOnly = !!vpcId
const networkDialogVisible = ref(false)
const subNetFormModel: SubNetFormModel[] = reactive([])

let childNetFormModel: ChildNetFormModel = reactive({
  ip2: '',
  ipVersion: 'IPv4',
  type2: '',
  vpn2: '',
  subnetStr: '',
  resourcePoolLabel: '',
  instanceId2: '',
  ipCheckSuccess: false,
  splitMessage: '',
  ref: null,
})

watch(
  () => baseFormProvider.value.jsonStr,
  (newVal) => {
    if (!newVal) return
    const obj = JSON.parse(newVal)
    console.log(obj)
    Object.assign(formModel, obj.formModel)
    Object.assign(childNetFormModel, obj.childNetFormModel)
    subNetFormModel.length = 0
    subNetFormModel.push(...obj.subNetFormModel)
  },
  { immediate: true },
)

function validateIpRangeRule(rule: any, value: any, callback: any) {
  const fn = childNetFormModel.ipVersion === 'IPv4' ? validateIpv4Range : validateIpv6Range
  if (!fn(value)) {
    callback(new Error(`请输入正确的${childNetFormModel.ipVersion}网段`))
  } else {
    callback()
  }
}

async function ipCheck(form: any) {
  const validate = await form.ref.validateField(['subnetStr'])
  if (!validate) return

  const params = {
    regionCode: baseFormProvider.value.regionCode,
    instanceId: form.instanceId2,
    subnets: form.subnetStr.split(';'),
  }
  try {
    const { entity } = await ipRangeCheck(params)
    if (!entity.length) return SlMessage.error('校验失败')
    if (entity.length && entity.every((ele: any) => ele.status)) {
      form.ipCheckSuccess = true
      SlMessage.success('校验成功')
    } else {
      SlMessage.error(
        entity
          .filter((ele: any) => !ele.status)
          .map((ele: any) => ele.message)
          .join(';'),
      )
    }
  } catch (data: any) {
    console.error(data.message || '校验失败')
  }
}

async function ipSplit(form: any) {
  const params = {
    regionCode: baseFormProvider.value.regionCode,
    instanceId: form.instanceId2,
    subnets: form.subnetStr.split(';').map((ele: any) => {
      const splited = ele.split('/')
      return {
        prefix: splited[0],
        mask: splited[1],
        vpn: form.vpn,
      }
    }),
  }
  try {
    await ipRangeSplit(params)
    form.splitMessage = '划分成功!'
  } catch (data: any) {
    SlMessage.error(data.message)
    form.splitMessage = '划分失败!' + data.message
  }
}

function selectIpRange({ row, formItem, level, isSubNet }: any) {
  if (isSubNet) {
    subNetFormModel.push(
      ...row.map((ele: any) => ({
        ip: ele.ip,
        type: ele.type,
        vpn: ele.vpn,
        instanceId: ele.instanceId,
        subnetName: '',
      })),
    )
  } else {
    formItem[`ip${level}`] = row.ip
    formItem[`type${level}`] = row.type
    formItem[`vpn${level}`] = row.vpn
    formItem[`instanceId${level}`] = row.instanceId
    formItem.resourcePoolLabel = row.relatedPool
  }
}
const stop = watch(
  () => childNetFormModel.subnetStr,
  (newVal, oldVal) => {
    if (newVal !== oldVal) {
      childNetFormModel.ipCheckSuccess = false
      childNetFormModel.splitMessage = ''
    }
  },
  { deep: true }, // 根据实际数据结构决定是否需要
)

// 组件卸载时清理
onBeforeUnmount(() => {
  stop()
})

// 主网
function setChildNetRef(ref: any, model: any) {
  // vue3 先创建再销毁，所以需要判断
  if (ref) model.ref = ref
}

// 子网
function setsubNetRef(ref: any, model: any) {
  if (ref) model.ref = ref
}

function removeSubNet(index: number) {
  subNetFormModel.splice(index, 1)
}
const subNetFormOptions = computed<any[]>(() =>
  subNetFormModel.map(() => [
    {
      style: 'padding:4px 10px;margin:0 0 0 -12px;background:transparent;',
      gutter: 10,
      groupItems: [
        {
          label: '子网名称',
          type: 'input',
          key: 'subnetName',
          rules: [
            { required: true, message: '请输入子网名称', trigger: ['blur', 'change'] },
            { validator: validateGoodsName, trigger: ['blur', 'change'] },
          ],
          props: {
            disabled: readOnly,
            maxlength: 48,
            showWordLimit: true,
          },
          span: 8,
        },
        {
          label: 'IP地址',
          type: 'text',
          key: 'ip',
          span: 5,
        },

        {
          label: '类型',
          type: 'text',
          key: 'type',
          span: 4,
        },
        {
          label: 'VPN',
          type: 'text',
          key: 'vpn',
          span: 6,
        },
      ],
    },
  ]),
)
const childNetFormOptions = [
  {
    style: 'padding:4px 10px;margin:0 0 0 -12px;background:transparent;',
    gutter: 10,
    groupItems: [
      {
        span: 24,
        render() {
          return (
            <SlBlockTitle size={14} style="margin-bottom:20px" no-bar>
              网络划分
            </SlBlockTitle>
          )
        },
      },
      {
        label: 'IP版本',
        type: 'radio',
        key: 'ipVersion',
        options: [
          {
            label: 'IPv4',
            value: 'IPv4',
          },
        ],
        span: 4,
      },
      {
        span: 18,
        render(props: any) {
          const { form } = props
          return (
            <el-button
              disabled={readOnly}
              onClick={() => {
                if (!form.ipVersion) {
                  SlMessage.error('请选择IP版本')
                  return
                }
                if (!baseFormProvider?.value?.regionCode) {
                  SlMessage.error('请选择资源池')
                  return
                }
                networkDialogVisible.value = true
                netRangeListDialogProps.ipVersion = form.ipVersion
                netRangeListDialogProps.formItem = form
                netRangeListDialogProps.level = 2
                netRangeListDialogProps.isSubNet = false
              }}
              size="small"
              type="primary"
              style="margin-top:4px"
            >
              选择网段
            </el-button>
          )
        },
      },
      {
        label: ' IP地址',
        type: 'text',
        key: 'ip2',
        span: 6,
      },
      {
        label: '资源池',
        type: 'text',
        key: 'resourcePoolLabel',
        span: 6,
      },
      {
        label: '类型',
        type: 'text',
        key: 'type2',
        span: 6,
      },
      {
        label: 'VPN',
        type: 'text',
        key: 'vpn2',
        span: 6,
      },
      {
        label: '网络划分',
        type: 'input',
        key: 'subnetStr',
        span: 12,
        props: {
          disabled: readOnly,
          placeholder: '请输入划分网段(先选择网段)',
        },
        rules: [
          { required: true, message: '请输入划分网段', trigger: 'change' },
          { validator: validateIpRangeRule, trigger: ['change'], index: 0 },
        ],
      },
      {
        span: 12,
        render(props: any) {
          const { form } = props
          return (
            <div style="margin-top:4px">
              <SlButton
                disabled={!form.instanceId2 || form.ipCheckSuccess || readOnly}
                apiFunction={() => ipCheck(form)}
                size="small"
                type={form.ipCheckSuccess ? 'success' : 'primary'}
                icon={form.ipCheckSuccess ? Check : ''}
              >
                {form.ipCheckSuccess ? '校验成功' : '校验'}
              </SlButton>
              <SlButton
                apiFunction={() => ipSplit(form)}
                disabled={!form.ipCheckSuccess || readOnly}
                size="small"
              >
                划分完成
              </SlButton>
            </div>
          )
        },
      },
      {
        span: 24,
        render(props: any) {
          const { form } = props
          return (
            form.splitMessage && (
              <div
                class={
                  form.splitMessage.includes('划分成功') ? 'split-message' : 'split-message error'
                }
              >
                <el-tooltip content={form.splitMessage} placement="top">
                  {form.splitMessage}
                </el-tooltip>
              </div>
            )
          )
        },
      },
      {
        span: 24,
        render() {
          return (
            <SlBlockTitle size={14} style="margin-bottom:20px" no-bar>
              配置信息
            </SlBlockTitle>
          )
        },
      },
      {
        span: 24,
        render(props: any) {
          const { form } = props
          return (
            <el-button
              disabled={readOnly}
              onClick={() => {
                console.log(form)
                if (!form.instanceId2) {
                  SlMessage.error('请选择二级网段')
                  return
                }
                if (!baseFormProvider?.value?.regionCode) {
                  SlMessage.error('请选择资源池')
                  return
                }
                networkDialogVisible.value = true
                netRangeListDialogProps.ipVersion = form.ipVersion
                netRangeListDialogProps.formItem = form
                netRangeListDialogProps.level = 3
                netRangeListDialogProps.isSubNet = true
              }}
              size="small"
              type="primary"
              style="margin-bottom:20px"
              no-bar
            >
              +添加子网
            </el-button>
          )
        },
      },
      {
        span: 24,
        render() {
          return subNetFormModel.map((model, index) => (
            <el-row class="subnet-row" style="background:#edf5ff;margin:10px 0;display:block">
              <sl-form
                key={model}
                ref={(ref: any) => setsubNetRef(ref, model)}
                options={subNetFormOptions.value[index]}
                modelValue={model}
              ></sl-form>
              {!readOnly && (
                <el-icon onClick={() => removeSubNet(index)} color="red" class="goods-del-btn-l3">
                  <Delete />
                </el-icon>
              )}
            </el-row>
          ))
        },
      },
    ],
  },
]
const formOptions = reactive([
  {
    style: 'margin-top: 0;margin-bottom: 0;',
    gutter: 60,
    hideBlockTitle: true,
    groupItems: [
      {
        label: 'VPC名称',
        type: 'input',
        key: 'instanceName',
        props: {
          disabled: readOnly,
          maxlength: 48,
          showWordLimit: true,
        },
        rules: [
          { required: true, message: '请输入网络名称', trigger: ['blur', 'change'] },
          { validator: validateGoodsName, trigger: ['blur', 'change'] },
        ],
        span: 12,
      },
      {
        label: '网络平面',
        type: 'text',
        key: 'plane',
        span: 12,
      },
      {
        span: 24,
        render() {
          return (
            <el-row class="subnet-row" style="background:#edf5ff;margin:10px 0">
              <sl-form
                key={childNetFormModel}
                ref={(ref: any) => setChildNetRef(ref, childNetFormModel)}
                options={childNetFormOptions}
                modelValue={childNetFormModel}
              ></sl-form>
            </el-row>
          )
        },
      },
    ],
  },
])
const doSubmit = async () => {
  const submitdata = {
    detail: JSON.stringify({
      formModel: { ...formModel, operationName: '查看资源申请单' },
      childNetFormModel,
      subNetFormModel,
      orderId,
    }),
    plane: props.plane,
    vpcName: formModel.instanceName,
    cidr: childNetFormModel.ip2,
    subnetDTOList: subNetFormModel.map((ele) => ({
      startIp: ele.ip.split('/')[0],
      netmask: ele.ip.split('/')[1],
      instanceId: ele.instanceId,
      level2InstanceId: childNetFormModel.instanceId2,
      subnetName: ele.subnetName,
    })),
  }
  return {
    submitdata,
    updateIps: [childNetFormModel.instanceId2],
  }
}
const submitfromdata = async () => {
  if (!slFormRef.value) return
  const validate = await slFormRef.value.validateField(['instanceName'])
  const subnetValidate = subNetFormModel.length
  if (!validate) return
  if (!subnetValidate) {
    SlMessage.error(`请添加${props.plane}子网`)
    return
  }
  const validateSubNet = await Promise.all(
    subNetFormModel.map(async (ele: any) => await ele.ref?.validate()),
  )
  if (!validateSubNet.every((ele) => ele)) return
  return doSubmit()
}
defineExpose({
  submitfromdata,
})
</script>
<style scoped>
:deep(.split-message) {
  word-break: break-all;
  font-size: 14px;
  background: #fff;
  padding: 6px 12px;
  border-radius: 4px;
  border-left: 4px solid var(--el-color-success);
  color: var(--el-color-success);
}
:deep(.split-message.error) {
  color: var(--el-color-error);
  border-left: 4px solid var(--el-color-error);
}
:deep(.subnet-row) {
  .goods-del-btn {
    position: absolute;
    top: -7px;
    right: -6px;
    z-index: 10;
    color: var(--el-color-danger);
    font-size: 1.2rem;
    cursor: pointer;
    display: none;
  }
  .goods-del-btn-l3 {
    position: absolute;
    top: 10px;
    right: -6px;
    z-index: 10;
    color: var(--el-color-danger);
    font-size: 1.2rem;
    cursor: pointer;
  }
}
:deep(.subnet-row:hover) {
  .goods-del-btn {
    display: block;
  }
}
.scroll-view {
  height: calc(100vh - 188px);
}

.resourcerequest {
  .resourcerequestbox {
    .onebox {
      display: flex;
      .oneboxleft {
        flex-grow: 3;
        margin: 8px 0px 8px 0;
      }

      .oneboxright {
        flex-grow: 1;
        flex-basis: 0;
        background: #ffffff;
        margin: 8px 8px 8px 0;
        border-radius: 6px;
        min-width: 300px;
      }
    }

    .onefooter {
      height: 48px;
      margin: 0 8px;
      background: #ffffff;
      border-radius: 8px 8px 8px 8px;
      display: flex;
      align-items: center;
      justify-content: right;
      padding: 0 14px;
    }
  }
}
</style>
