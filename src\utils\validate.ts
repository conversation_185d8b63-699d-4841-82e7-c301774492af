export const validateEmail = (rule: any, value: any, callback: any) => {
  const emailPattern = /^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$/
  if (!value) {
    callback(new Error('请输入邮箱地址'))
  } else if (!emailPattern.test(value)) {
    callback(new Error('请输入有效的邮箱地址'))
  } else {
    callback()
  }
}

export const validateTypeEmail = (rule: any, value: any, callback: any) => {
  const emailPattern = /^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$/
  if (value && !emailPattern.test(value)) {
    callback(new Error('请输入有效的邮箱地址'))
  }
  callback()
}

export const validatePhone = (rule: any, value: any, callback: any) => {
  const phonePattern = /^1[3-9]\d{9}$/
  if (!value) {
    callback(new Error('请输入手机号'))
  } else if (!phonePattern.test(value)) {
    callback(new Error('请输入有效的手机号'))
  } else {
    callback()
  }
}

export const validatePassword = (rule: any, value: any, callback: any) => {
  if (value) {
    if (!validatePasswordLength(value)) {
      callback('密码长度应不少于 8 个字符且不超过 32 个字符')
    } else if (!validateCharacterCombination(value)) {
      callback('密码需包含大小写字母、数字和特殊字符中至少两种字符类型的组合')
    }
  }
  callback()
}

function validateCharacterCombination(str: string): boolean {
  let hasUppercase = /[A-Z]/.test(str)
  let hasLowercase = /[a-z]/.test(str)
  let hasDigit = /[0-9]/.test(str)
  let hasSpecial = /[!@#$%^&*(),.?":{}|<>]/.test(str)

  let typesCount = 0
  if (hasUppercase) typesCount++
  if (hasLowercase) typesCount++
  if (hasDigit) typesCount++
  if (hasSpecial) typesCount++

  return typesCount >= 2
}

// 验证密码长度
function validatePasswordLength(str: string): boolean {
  return str.length >= 8 && str.length <= 32
}

export const validateNumber = (rule: any, value: any, callback: any) => {
  const numberPattern = /^[0-9]+$/
  if (!value) {
    callback(new Error('请输入数字'))
  } else if (!numberPattern.test(value)) {
    callback(new Error('请输入有效的数字'))
  } else {
    callback()
  }
}

export const validateEmpty = (rule: any, value: any, callback: any) => {
  if (rule.disabled) callback()
  if (!value) {
    callback(new Error('不能为空'))
  } else {
    callback()
  }
}

export const validateNoSpecialChars = (rule: any, value: any, callback: any) => {
  // 定义允许的字符模式（仅允许字母、数字、下划线和中文）
  const allowedPattern = /^[\u4e00-\u9fa5a-zA-Z0-9_-]+$/

  if (!value) {
    callback(new Error('请输入内容'))
  } else if (!allowedPattern.test(value)) {
    callback(new Error('不允许输入特殊符号或空格'))
  } else {
    callback()
  }
}
export const validateNameNoSpecialChars = (rule: any, value: any, callback: any) => {
  // 定义允许的字符模式（仅允许字母、数字、下划线和中文）
  const allowedPattern = /^[\u4e00-\u9fa5a-zA-Z0-9_]+$/

  if (!value) {
    callback(new Error('请输入内容'))
  } else if (!allowedPattern.test(value)) {
    callback(new Error('不允许输入特殊符号或空格'))
  } else {
    callback()
  }
}

export const validateAccount = (rule: any, value: any, callback: any) => {
  // 定义允许的字符模式（仅允许字母、数字、下划线）
  const allowedPattern = /^[a-zA-Z0-9_-]+$/

  if (!value) {
    callback(new Error('请输入内容'))
  } else if (!allowedPattern.test(value)) {
    callback(new Error('不允许输入特殊符号或空格'))
  } else {
    callback()
  }
}

export const validateEcsPassword = (rule: any, value: any, callback: any) => {
  const passwordPattern = /^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d()~!@#$%^&*-+=|{}[\]∶;',.?/]{8,26}$/
  if (!value) {
    callback(new Error('请输入密码'))
  } else if (!passwordPattern.test(value)) {
    callback(new Error('密码必须包含字母和数字，且长度在8到26个字符之间'))
  } else {
    callback()
  }
}

const validateCharacterCombinationNew = (str: string): boolean => {
  let hasUppercase = /[A-Z]/.test(str)
  let hasLowercase = /[a-z]/.test(str)
  let hasDigit = /[0-9]/.test(str)
  let hasSpecial = /[!@#$%^&*(),.?'`":{}|<>]/.test(str)

  let typesCount = 0
  if (hasUppercase) typesCount++
  if (hasLowercase) typesCount++
  if (hasDigit) typesCount++
  if (hasSpecial) typesCount++

  return typesCount >= 3
}

export const validatePasswordNew = (rule: any, value: any, callback: any) => {
  if (!value) {
    callback(new Error('请输入密码'))
  } else if (!validatePasswordLength(value)) {
    callback(new Error('密码长度必须为8-26位'))
  } else if (!validateCharacterCombinationNew(value)) {
    callback(
      new Error(
        `密码至少需要包含大写字母、小写字母、数字和特殊字符(~!@#$%^*()-_=+|[{}];:'${'`'}",./?)中的三种`,
      ),
    )
  } else {
    callback()
  }
}

// 验证端口号
export const validatePort = (value: any) => {
  const portPattern = /^[0-9]+$/
  if (!value) {
    return false
  }
  if (!portPattern.test(value)) {
    return false
  }
  if (value < 1 || value > 65535) {
    return false
  }
  return true
}
