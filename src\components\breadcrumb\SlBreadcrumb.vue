<template>
  <div class="con">
    <el-breadcrumb :separator-icon="ArrowRight">
      <el-breadcrumb-item :key="item.path" v-for="item in hierarchy" :to="{ path: item.path }">{{
        item.name
      }}</el-breadcrumb-item>
    </el-breadcrumb>
  </div>
</template>

<script lang="ts" setup>
import { ArrowRight } from '@element-plus/icons-vue'
import { useRoute } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { watch, ref } from 'vue'

const route = useRoute()

const hierarchy = ref<RouteRecordRaw[]>()

watch(
  () => route.path,
  () => {
    hierarchy.value = route.matched
  },
  { immediate: true },
)
</script>

<style scoped>
.con {
  padding: 10px 20px;
  border-top: 1px solid #eee;
  background: #fff;
}

.el-breadcrumb {
  font-size: 12px;
}
</style>
