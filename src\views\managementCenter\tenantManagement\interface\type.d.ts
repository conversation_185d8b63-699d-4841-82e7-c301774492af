/**
 * @name 创建租户管理接口类型
 */
export interface TenantRequestType {
  id?: string
  /**
   * 租户类型（0：内部租户；1：外部租户），默认是0
   */
  tenantType: 0 | 1

  /**
   * 租户名称（可选）
   */
  tenantName?: string

  /**
   * 租户所属人ID
   */
  tenantOwnerId: string

  /**
   * 租户所属人名称
   */
  tenantOwnerName: string

  /**
   * 租户所属人编码（可选）
   */
  tenantCode?: string

  /**
   * 所属部门组织编号（可选）
   */
  orgId?: number

  /**
   * 所属部门组织名称（可选）
   */
  orgName?: string

  /**
   * 城市名称
   */
  cityName: string
}

/**
 * @name 获取列表传参
 */
export interface TenantQueryParamsType {
  /**
   * 查询输入框（可选）
   */
  query?: string

  /**
   * 每页几条数据（可选）
   */
  pageSize: number

  /**
   * 当前页（可选）
   */
  pageNum: number
}
