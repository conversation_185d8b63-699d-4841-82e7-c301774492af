<template>
  <div id="ListenerForm" class="table-box">
    <sl-page-header
      :title="isView ? '查看监听' : '创建监听'"
      :icon="{
        Vnode: Platform,
        color: '#0052D9',
        size: '20px',
      }"
      :show-back="true"
      :back="{
        title: '返回详情列表',
        function: handleGoBack,
      }"
    >
    </sl-page-header>

    <el-scrollbar class="scroll-view">
      <div class="sl-card">
        <!-- 步骤条 -->
        <sl-steps :active="currentTask" :all-tasks="steps" />

        <!-- 表单内容 -->
        <div class="step-content">
          <!-- 步骤1：协议监听 -->
          <protocol-step v-show="currentTask === 0" :form-data="formData" ref="protocolStepRef" />

          <!-- 步骤2：健康检查 -->
          <health-check-step
            v-show="currentTask === 1"
            :form-data="formData"
            ref="healthCheckStepRef"
          />

          <!-- 步骤3：服务器组 -->
          <server-group-step
            v-show="currentTask === 2"
            :form-data="formData"
            :server-list="serverList"
            ref="serverGroupStepRef"
            @update:serverList="updateServerList"
          />

          <!-- 步骤4：配置审核 -->
          <review-step
            v-show="currentTask === 3"
            :form-data="formData"
            :is-view="isView"
            @onPrev="handlePrev"
          />
        </div>
      </div>
    </el-scrollbar>
    <!-- 底部操作按钮 -->
    <div class="footer">
      <div class="button-group" v-if="isView">
        <el-button @click="handleGoBack">返回</el-button>
      </div>
      <div class="button-group" v-else>
        <el-button @click="handleGoBack">取消</el-button>
        <el-button type="primary" @click="currentTask--" v-if="currentTask > 0">上一步</el-button>
        <el-button type="primary" @click="goNextStep" v-if="currentTask < 3">下一步</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="currentTask === 3">提交</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, provide } from 'vue'
import { Platform } from '@element-plus/icons-vue'
import SlPageHeader from '@/components/SlPageHeader/index.vue'
import SlSteps from '@/components/SlSteps/index.vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import type { ServerItem, ListenerFormData } from './components/listener/types'
import {
  createSlbListener,
  getResourceDetail,
  getSlbListenerDetail,
  getSlbServerGroupDetail,
} from '@/api/modules/resourecenter'

// 导入子组件
import ProtocolStep from './components/listener/ProtocolStep.vue'
import HealthCheckStep from './components/listener/HealthCheckStep.vue'
import ServerGroupStep from './components/listener/ServerGroupStep.vue'
import ReviewStep from './components/listener/ReviewStep.vue'

const router = useRouter()
const route = useRoute()

// 获取查询参数
const listenerId = route.query.id as string
const slbId = route.query.slbId as string

// 判断是创建还是查看
const isView = computed(() => !!listenerId)

// 当前步骤
const currentTask = ref(0)

// 步骤定义
const steps = [
  { task: 0, taskName: '协议监听' },
  { task: 1, taskName: '健康检查' },
  { task: 2, taskName: '服务器组' },
  { task: 3, taskName: '配置审核' },
]
provide('allTasks', steps)
provide('currentSort', currentTask)

// 子组件引用
const protocolStepRef = ref<any>()
const healthCheckStepRef = ref<any>()
const serverGroupStepRef = ref<any>()

// 表单数据
const formData = reactive<ListenerFormData>({
  // 协议监听数据
  protocol: 'tcp', // tcp/http/https/unp
  listenerName: '',
  port: '',
  serverCertificate: '', // 仅https
  caCertificate: '', // 仅https
  lbAlgorithm: 'ROUND_ROBIN', // ROUND_ROBIN/LEAST_CONNECTION
  sessionPersistence: false,
  cookieHandling: '', // 1-植入/2-重写

  // 健康检查数据
  healthCheckProtocol: 'tcp', // tcp/http/unp
  healthCheckInterval: 2,
  timeout: 5,
  maxRetries: 3,
  healthCheckUrl: '',
  healthCheckContent: '',

  // 服务器组数据
  serverGroupType: 0, // 0-虚拟服务器组/1-主备服务器组
  serverGroupId: '',
  serverGroupName: '',
  serverInfoModelList: [],
})

// 服务器列表数据（审核页面展示）
const serverList = ref<ServerItem[]>([])

// 更新服务器列表
const updateServerList = (list: any) => {
  formData.serverInfoModelList = list
}

// 返回上一页
const handleGoBack = () => {
  router.back()
}

// 下一步处理
const goNextStep = async () => {
  let valid = false

  // 根据当前步骤验证表单
  if (currentTask.value === 0) {
    valid = await protocolStepRef.value?.validate()
  } else if (currentTask.value === 1) {
    valid = await healthCheckStepRef.value?.validate()
  } else if (currentTask.value === 2) {
    valid = await serverGroupStepRef.value?.validate()
  }

  if (valid) {
    currentTask.value++
  }
}

const handlePrev = (step: number) => {
  currentTask.value = step
}

const slbDetail = ref<any>({})
// 获取SLB详情
const fetchResourceDetail = async () => {
  try {
    const res = await getResourceDetail({
      id: slbId,
    })
    if (res && res.entity) {
      slbDetail.value = res.entity
    }
  } catch (error) {
    console.error('获取SLB详情失败:', error)
  }
}
provide('slbDetail', slbDetail)

const submitLock = ref(false)
// 提交表单
const handleSubmit = async () => {
  if (submitLock.value) return
  submitLock.value = true
  try {
    // 准备提交数据
    const submitData = formatSubmitData(formData)

    // 调用创建API
    const res = await createSlbListener(submitData)
    if (res && res.code === 200) {
      ElMessage.success('创建监听成功')
    }
    // 返回到负载均衡详情页面
    handleGoBack()
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    setTimeout(() => {
      submitLock.value = false
    }, 1000)
  }
}

const formatSubmitData = (formData: ListenerFormData) => {
  const { id, deviceId, vpcId, vpcName } = slbDetail.value
  return {
    slbResourceDetailId: id,
    slbDeviceId: deviceId,
    vpcId,
    vpcName,
    runningStatus: 'active',
    listenerName: formData.listenerName,
    slbListenerProtocolModel: {
      protocolType: formData.protocol,
      listenerPort: formData.port,
      serverCertificateId: formData.serverCertificate,
      certificateId: formData.caCertificate,
      lbAlgorithm: formData.lbAlgorithm,
      sessionPersistence: formData.sessionPersistence == true ? 1 : 0,
      cookieMethod: formData.cookieHandling,
    },
    slbHealthCheckModel: {
      healthCheckProtocolType: formData.healthCheckProtocol,
      healthCheckIntervalTime: formData.healthCheckInterval,
      healthCheckTimeoutTime: formData.timeout,
      healthCheckUrl: formData.healthCheckUrl,
      healthCheckReturnValue: formData.healthCheckContent,
    },
    slbListenerServerGroup: {
      slbResourceDetailId: id,
      groupType: formData.serverGroupType,
      groupName: formData.serverGroupName,
      groupPriorityGroup: formData.groupPriorityGroup,
      serverInfoModelList: formData.serverInfoModelList.map((item: any) => ({
        resourceDetailId: item.id,
        deviceId: item.deviceId,
        port: item.port,
      })),
    },
  }
}

const fetchListenerDetail = async () => {
  const res = await getSlbListenerDetail({
    id: listenerId,
  })
  if (res && res.entity) {
    const { listenerName, slbListenerProtocolModel, slbHealthCheckModel, slbListenerServerGroup } =
      res.entity
    formData.protocol = slbListenerProtocolModel.protocolType
    formData.listenerName = listenerName
    formData.port = slbListenerProtocolModel.listenerPort
    formData.serverCertificate = slbListenerProtocolModel.serverCertificateId
    formData.caCertificate = slbListenerProtocolModel.certificateId
    formData.lbAlgorithm = slbListenerProtocolModel.lbAlgorithm
    formData.sessionPersistence = slbListenerProtocolModel.sessionPersistence == 1 ? true : false
    formData.cookieHandling = slbListenerProtocolModel.cookieMethod
    formData.healthCheckProtocol = slbHealthCheckModel.healthCheckProtocolType
    formData.healthCheckInterval = Number(slbHealthCheckModel.healthCheckIntervalTime)
    formData.timeout = Number(slbHealthCheckModel.healthCheckTimeoutTime)
    formData.maxRetries = Number(slbHealthCheckModel.maxRetries)
    formData.healthCheckUrl = slbHealthCheckModel.healthCheckUrl
    formData.healthCheckContent = slbHealthCheckModel.healthCheckReturnValue
    formData.serverGroupType = slbListenerServerGroup.groupType
    formData.serverGroupId = slbListenerServerGroup.id
    formData.serverGroupName = slbListenerServerGroup.groupName
    formData.serverInfoModelList = slbListenerServerGroup.serverInfoModelList
    const res2 = await getSlbServerGroupDetail({
      id: slbListenerServerGroup.id,
    })
    if (res2 && res2.entity) {
      const list = res2.entity.serverInfoModelList
      list.forEach((item: any) => {
        item.cloudPlatform = item.domainName
        item.resourcePoolName = item.regionName
        item.deviceStatusCn = item.deviceStatus
      })
      formData.serverInfoModelList = list
    }
    currentTask.value = 3
  }
}

// 初始化
onMounted(async () => {
  await fetchResourceDetail()
  if (isView.value) {
    await fetchListenerDetail()
  }
})
</script>

<style lang="scss" scoped>
.scroll-view {
  margin-top: 2px;
}
.footer {
  height: 48px;
  padding: 8px 20px;
  background: #ffffff;
  display: flex;
  align-items: center;
  .button-group {
    display: flex;
    align-items: center;
    justify-content: right;
    flex-grow: 1;
  }
}
</style>
