<template>
  <div class="f-r f-1">
    <!-- 服务器 -->
    <div class="topContentO topContentF f-3 f-r" style="flex: 3; flex-flow: column">
      <div class="f-r boxContiner">
        <div class="boxContinerBox" style="width: 66.6%">
          <div class="f-r f-1 boxContiner">
            <div class="topContentO topContentF f-1">
              <div class="p-h-20 p-t-20 p-b-30 bs-b topContentO_t topContentF_b br-6">
                <p class="fz-18 fz-w" title="物理资源池总数">物理资源池总数</p>
                <div class="m-t-32 f-r-c-b bg-f p-v-50 p-h-10 bs-b br-6 w100">
                  <div class="f-r f-j-c f-nw f-1">
                    <img
                      class="m-r-12"
                      src="@/assets/images/overview/icon_wuli_num.png"
                      alt=""
                      width="25%"
                      height="25%"
                    />
                    <div class="f-d-c f-j-c title">
                      <!--              <p class="fz-14 fz-ls-1">服务器数量</p>-->
                      <p class="fz-15">
                        <span class="c-theme fz-w">{{ latest.hardwarePoolNum }}</span>
                        <span class="fz-13 c-6 m-l-4">个</span>
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div class="p-h-20 p-t-20 p-b-30 bs-b topContentO_t topContentF_b br-6">
                <p class="fz-18 fz-w" title="虚拟资源池总数">虚拟资源池总数</p>
                <div class="m-t-32 f-r-c-b bg-f p-v-50 p-h-10 bs-b br-6 w100">
                  <div class="f-r f-j-c f-nw f-1">
                    <img
                      class="m-r-12"
                      src="@/assets/images/overview/icon_xuni_num.png"
                      alt=""
                      width="25%"
                      height="25%"
                    />
                    <div class="f-d-c f-j-c title">
                      <!--              <p class="fz-14 fz-ls-1">虚拟机数量</p>-->
                      <p class="fz-15">
                        <span class="c-theme fz-w">{{ latest.virtualPoolNum }}</span><span class="fz-13 c-6 m-l-4">个</span>
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="topContentO topContentF f-1 ramBox m-l-16 bs-b">
              <div class="p-h-20 p-t-20 p-b-30 bs-b topContentO_t topContentF_b br-6">
                <p class="fz-18 fz-w" title="计算服务器数量">计算服务器数量</p>
                <div class="m-t-32 f-r-c-b bg-f p-v-50 p-h-10 bs-b br-6 w100">
                  <div class="f-r f-j-c f-nw f-1">
                    <img
                      class="m-r-12"
                      src="@/assets/images/img/1.png"
                      alt=""
                      width="25%"
                      height="25%"
                    />
                    <div class="f-d-c f-j-c title">
                      <!--              <p class="fz-14 fz-ls-1">服务器数量</p>-->
                      <p class="fz-15">
                        <span class="c-theme fz-w">{{ latest.hostNum }}</span><span class="fz-13 c-6 m-l-4">台</span>
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div class="p-h-20 p-t-20 p-b-30 bs-b topContentO_t topContentF_b br-6">
                <p class="fz-18 fz-w" title="虚拟机数量">虚拟机数量</p>
                <div class="m-t-32 f-r-c-b bg-f p-v-50 p-h-10 bs-b br-6 w100">
                  <div class="f-r f-j-c f-nw f-1">
                    <img
                      class="m-r-12"
                      src="@/assets/images/img/2.png"
                      alt=""
                      width="25%"
                      height="25%"
                    />
                    <div class="f-d-c f-j-c title">
                      <!--              <p class="fz-14 fz-ls-1">虚拟机数量</p>-->
                      <p class="fz-15">
                        <span class="c-theme fz-w">{{ latest.vmNum }}</span><span class="fz-13 c-6 m-l-4">台</span>
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="f-r f-1">
            <div class="p-20 bs-b br-6 bg-f f-1">
              <p class="fz-18 fz-w">内存</p>
              <div class="m-t-32 br-6">
                <div class="f-r-c-b w100">
                  <div class="f-r f-a-c">
                    <img
                      src="@/assets/images/img/internal.svg"
                      alt="Icon Description"
                      class=""
                      width="16"
                      height="16"
                    />
                    <span class="fz-16 m-l-8">内存总量</span>
                  </div>
                  <p class="c-theme fz-w fz-18">{{ latest.memoryTotal }}{{ latest.memoryunits }}</p>
                </div>
                <el-progress
                  :percentage="percentage(latest.memoryUsed, latest.memoryTotal)"
                  class="progress-warp m-t-16"
                  :stroke-width="14"
                  :show-text="true"
                  :format="getLabel"
                  :text-inside="true"
                />
                <div class="f-r-c-b m-t-16 fz-14">
                  <span>{{ latest.memoryUsed }}{{ latest.memoryunits }}</span>
                  <span>{{ latest.memoryAvi }}{{ latest.memoryunits }}</span>
                </div>
                <div class="f-r-c-b m-t-10 c-6 fz-13">
                  <span>内存已开通</span>
                  <span>内存剩余量</span>
                </div>
              </div>
            </div>
            <div class="p-20 bs-b br-6 bgfe f-1 m-l-16">
              <div class="bs-b">
                <p class="fz-18 fz-w">vCPU</p>
                <div class="f-r f-1 f-r-c-b">
                  <div class="m-t-32 f-r f-j-c custom-progress w50">
                    <el-progress
                      type="circle"
                      :percentage="percentage(latest.vcpuUsed, latest.vcpuTotal)"
                      :color="'#487fef'"
                      :stroke-width="8"
                      :width="120"
                      :indeterminate="true"
                    >
                      <template #default>
                        <!-- 进度条 -->
                        <p style="color: rgba(0, 12, 31, 1)" class="fz-w fz-16 m-t-12">
                          {{ latest.vcpuTotal
                          }}<span style="font-weight: normal" class="fz-13 c-9 m-l-2">核</span>
                        </p>
                        <!-- 文本数据 -->
                        <p style="color: rgba(166, 170, 177, 0.8)" class="fz-14 m-t-8">vCpu总数</p>
                      </template>
                    </el-progress>
                    <div class="circleS"></div>
                  </div>
                  <div class="f-r f-j-a f-a-c f-d-c m-t-30 w50 t-a-c">
                    <div class="f-1">
                      <div class="f-r f-a-baseline">
                        <p class="adhesiveDot bg-theme m-r-10 m-t-18"></p>
                        <div>
                          <p class="fz-16 fz-ls-1">vCPU已开通数</p>
                          <p class="fz-18 m-t-10">
                            <span class="c-theme fz-w">{{ latest.vcpuUsed }}</span><span class="fz-14 c-9 m-l-4">核</span>
                          </p>
                        </div>
                      </div>
                      <div class="f-r f-a-baseline m-t-30">
                        <p class="adhesiveDot bg-d5 m-r-10 m-t-18"></p>
                        <div>
                          <p class="fz-16 fz-ls-1">vCPU已剩余数</p>
                          <p class="fz-18 m-t-10">
                            <span class="fz-w m-t-14">{{ latest.vcpuAvi }}</span><span class="fz-14 c-9 m-l-4">核</span>
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="f-1 m-l-16 bs-b gridStorageBox">
          <!-- 存储 -->
          <div class="topContentF_b topContentF_b_big bs-b f-1 f-r w100" style="height: 100%">
            <div class="topContentH bgfe br-6 p-20 w100 bs-b">
              <div class="bs-b">
                <p class="fz-18 fz-w">存储</p>
                <div class="m-t-32 f-r f-j-c custom-progress">
                  <template v-if="latest.storageTotalSsd || latest.storageTotalHhd">
                    <div class="storeAll">
                      <img src="@/assets/images/img/store.png" alt="" class="w100" height="70" />
                      <p class="text fz-14">普通「&nbsp;SSD&nbsp;」</p>
                      <p class="text_R fz-14">高性能「&nbsp;HDD&nbsp;」</p>
                    </div>
                  </template>
                  <template v-else>
                    <div class="storeAll_ w100 f-r f-a-c">
                      <img src="@/assets/images/img/storeAll.png" alt="" class="w100 img_" />
                      <p class="text">
                        <span class="fz-12 m-r-10">「&nbsp;存储总量&nbsp;」</span><span class="fz-20 fz-w m-r-4">{{ latest.storageTotal }}</span>{{ latest.units }}
                      </p>
                    </div>
                  </template>
                </div>
              </div>
              <div class="m-t-14 f-r">
                <template v-if="latest.storageTotalSsd || latest.storageTotalHhd">
                  <div
                    class="p-v-26 m-r-6 f-1 br-6 amountO"
                    style="box-shadow: var(--el-box-shadow-light)"
                  >
                    <p class="p-h-10 t-a-c">
                      <span class="fz-12 m-r-10">存储总量</span>
                      <span class="fz-18 fz-w">{{ latest.storageTotalSsd }}{{ latest.units }}</span>
                    </p>
                    <div class="f-r p-h-10 p-t-24 m-t-18 amountOx fz-12">
                      <div class="f-1 t-a-c">
                        <p class="fz-w c-c18">{{ latest.storageUsedSsd }}{{ latest.units }}</p>
                        <p class="m-t-6 c-9">存储已开通</p>
                      </div>
                      <div class="f-1 t-a-c">
                        <p class="fz-w">{{ latest.storageAviSsd }}{{ latest.units }}</p>
                        <p class="m-t-6 c-9">存储剩余量</p>
                      </div>
                    </div>
                  </div>
                  <div
                    class="p-v-26 f-1 br-6 amountO"
                    style="box-shadow: var(--el-box-shadow-light)"
                  >
                    <p class="p-h-10 t-a-c">
                      <span class="fz-12 m-r-10">存储总量</span>
                      <span class="fz-18 fz-w">{{ latest.storageTotalHhd }}{{ latest.units }}</span>
                    </p>
                    <div class="f-r p-h-10 p-t-24 m-t-18 amountOx fz-12">
                      <div class="f-1 t-a-c">
                        <p class="fz-w c-theme">{{ latest.storageUsedHhd }}{{ latest.units }}</p>
                        <p class="m-t-6 c-9">存储已开通</p>
                      </div>
                      <div class="f-1 t-a-c">
                        <p class="fz-w">{{ latest.storageAviHhd }}{{ latest.units }}</p>
                        <p class="m-t-6 c-9">存储剩余量</p>
                      </div>
                    </div>
                  </div>
                </template>
                <div v-else class="f-1 br-6">
                  <div
                    class="amountSummaryT p-h-20 fz-12 p-v-30 br-6 f-r f-a-c"
                    style="box-shadow: var(--el-box-shadow-light)"
                  >
                    <p class="fz-14 c-9 m-r-10">存储已开通</p>
                    <p class="fz-16 fz-w c-c18">{{ latest.storageUsed }}{{ latest.units }}</p>
                  </div>

                  <div
                    class="amountSummaryB p-h-20 fz-12 m-t-20 p-v-30 br-6 f-r f-a-c"
                    style="box-shadow: var(--el-box-shadow-light)"
                  >
                    <p class="fz-14 c-9 m-r-10">存储剩余量</p>
                    <p class="fz-16 fz-w c-theme">{{ latest.storageAvi }}{{ latest.units }}</p>
                  </div>
                </div>
              </div>
              <div class="m-t-30">
                <template v-if="latest.storageTotalSsd || latest.storageTotalHhd">
                  <div class="f-r-c-b w100 fz-12">
                    <span class="m-l-8">普通SSD</span>
                    <p class="">
                      {{ latest.storageUsedSsd }}{{ latest.units }}/{{ latest.storageTotalSsd
                      }}{{ latest.units }}
                    </p>
                  </div>

                  <el-progress
                    :percentage="percentage(latest.storageUsedSsd, latest.storageTotalSsd)"
                    class="progress-warpT m-t-10"
                    :stroke-width="14"
                    :show-text="true"
                    :format="getLabel"
                    :text-inside="true"
                  />
                  <div class="f-r-c-b w100 m-t-24 fz-12">
                    <span class="m-l-8">高性能HDD</span>
                    <p class="">
                      {{ latest.storageUsedHhd }}{{ latest.units }}/{{ latest.storageTotalHhd
                      }}{{ latest.units }}
                    </p>
                  </div>
                  <el-progress
                    :percentage="percentage(latest.storageUsedHhd, latest.storageTotalHhd)"
                    class="progress-warp m-t-10"
                    :stroke-width="14"
                    :show-text="true"
                    :format="getLabel"
                    :text-inside="true"
                  />
                </template>
                <template v-else>
                  <div class="f-r-c-b w100 m-t-24 fz-12">
                    <span class="m-l-8">存储总量</span>
                    <p class="">
                      {{ latest.storageUsed }}{{ latest.units }}/{{ latest.storageTotal
                      }}{{ latest.units }}
                    </p>
                  </div>
                  <el-progress
                    :percentage="percentage(latest.storageUsed, latest.storageTotal)"
                    class="progress-warp m-t-10"
                    :stroke-width="14"
                    :show-text="true"
                    :format="getLabel"
                    :text-inside="true"
                  />
                </template>
              </div>
            </div>

            <div class="p-20 bs-b topContentO_b bg-f br-6 f-1 gridDcnContainer">
              <div class="bs-b bgfe br-6 w100">
                <p class="fz-18 fz-w" style="height: 24px">DCN</p>
                <div class="f-r f-1 f-r-c-b m-t-30 m-b-36">
                  <div class="f-r f-a-c f-r w50">
                    <div class="bg-theme topContentF_i_big f-r f-j-c f-a-c">
                      <img
                        src="@/assets/images/overview/icon_dcn_normal.png"
                        alt="Icon Description"
                        class=""
                        width="75"
                        height="75"
                      />
                    </div>
                    <div class="m-l-14 title">
                      <p class="fz-18 t-r-1">
                        <span class="fz-w m-r-6">{{ latest.dcnTotal }}</span><span class="fz-13 c-6 m-l-4">个</span>
                      </p>
                      <p class="fz-13 m-t-6 c-6 t-r-1">DCN地址总数</p>
                    </div>
                  </div>
                  <div class="f-r f-j-a f-a-c f-d-c t-a-c w50 t-a-l p-l-5">
                    <p class="topContentF_l topContentF_l_xs f-r-c-b f-nw im-fz-12 w80 t-a-l">
                      <span class="f-1">地址已开通数</span>
                      <span class="m-r-4 fz-w m-t-2">{{ latest.dcnUsed }}</span>个
                    </p>
                    <p
                      class="topContentF_c topContentF_c_xs m-t-16 f-r-c-b f-nw im-fz-12 w80 t-a-l"
                    >
                      <span class="f-1">地址剩余量</span><span class="m-r-4 fz-w m-t-2">{{ latest.dcnAvi }}</span>个
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div
            class="topContentF_b topContentF_b_big m-l-16 bs-b f-1 f-r gridGpuContainer"
            style="height: auto"
          >
            <div class="topContentH bs-b bgfe p-20 br-6 f-d-c" style="">
              <p class="fz-18 fz-w f-r-c-b">
                <span>GPU</span>
                <!--          <el-popover popper-class="topContentF" placement="top-start" :width="240" trigger="hover">-->
                <!--            <template #reference>-->
                <!--              <span-->
                <!--                v-if="regionListsName.some((item: any) => item.platformCode === 'VMWARE')"-->
                <!--                class="c-theme fz-w c-p"-->
                <!--                >···</span>-->
                <!--              <span v-else></span>-->
                <!--            </template>-->
                <!--            <template #default>-->
                <!--              <p class="fz-16 fz-w">GPU-T4</p>-->
                <!--              <p class="topContentF_t m-t-14 f-r-c-b f-nw">-->
                <!--                <span class="f-1">GPUT4总数</span><span class="m-r-4">{{ latest.gpuT4Sum }}</span>张-->
                <!--              </p>-->
                <!--              <p class="topContentF_l m-t-14 f-r-c-b f-nw">-->
                <!--                <span class="f-1">GPUT4已开通数</span><span class="m-r-4">{{ latest.gpuT4Used }}</span>张-->
                <!--              </p>-->
                <!--              <p class="topContentF_c m-t-16 f-r-c-b f-nw">-->
                <!--                <span class="f-1">GPUT4剩余量</span><span class="m-r-4">{{ latest.gpuT4AviSale }}</span>张-->
                <!--              </p>-->
                <!--              <p class="fz-16 fz-w m-t-20">GPU-A10</p>-->
                <!--              <p class="topContentF_t m-t-14 f-r-c-b f-nw">-->
                <!--                <span class="f-1">GPUA10总数</span><span class="m-r-4">{{ latest.gpuA10Sum }}</span>张-->
                <!--              </p>-->
                <!--              <p class="topContentF_l m-t-14 f-r-c-b f-nw">-->
                <!--                <span class="f-1">GPUA10已开通数</span><span class="m-r-4">{{ latest.gpuA10Used }}</span>张-->
                <!--              </p>-->
                <!--              <p class="topContentF_c m-t-16 f-r-c-b f-nw">-->
                <!--                <span class="f-1">GPUA10剩余量</span><span class="m-r-4">{{ latest.gpuA10AviSale }}</span>张-->
                <!--              </p>-->
                <!--              <p class="fz-16 fz-w m-t-20">GPU-A40</p>-->
                <!--              <p class="topContentF_t m-t-14 f-r-c-b f-nw">-->
                <!--                <span class="f-1">GPUA40总数</span><span class="m-r-4">{{ latest.gpuA40Sum }}</span>张-->
                <!--              </p>-->
                <!--              <p class="topContentF_l m-t-14 f-r-c-b f-nw">-->
                <!--                <span class="f-1">GPUA40已开通数</span><span class="m-r-4">{{ latest.gpuA40Used }}</span>张-->
                <!--              </p>-->
                <!--              <p class="topContentF_c m-t-16 f-r-c-b f-nw">-->
                <!--                <span class="f-1">GPUA40剩余量</span><span class="m-r-4">{{ latest.gpuA40AviSale }}</span>张-->
                <!--              </p>-->
                <!--            </template>-->
                <!--          </el-popover>-->
              </p>

              <div class="f-r f-1 f-r-c-b m-b-30 topContentF">
                <div class="m-t-22 f-r f-a-c">
                  <div class="bg-theme topContentF_i_big f-r f-j-c f-a-c">
                    <img
                      src="@/assets/images/overview/icon_gpu_normal.png"
                      alt="Icon Description"
                      class=""
                      width="75"
                      height="75"
                    />
                  </div>
                  <div class="m-l-14 title">
                    <p class="fz-21">
                      <span class="fz-w m-r-6">{{ latest.gpuTotal }}</span><span class="fz-13 c-6 m-l-4">张</span>
                    </p>
                    <p class="fz-13 m-t-6 c-6">GPU总数</p>
                  </div>
                </div>

                <div class="f-r f-j-a f-a-c f-d-c m-t-10 t-a-c w50">
                  <p class="topContentF_l topContentF_l_xs m-t-14 f-r-c-b f-nw im-fz-15 w85 t-a-l">
                    <span class="f-1">GPU已开通数</span><span class="m-r-4">{{ latest.gpuUsed }}</span>张
                  </p>
                  <p class="topContentF_c topContentF_c_xs m-t-16 f-r-c-b f-nw im-fz-15 w85 t-a-l">
                    <span class="f-1">GPU剩余量</span><span class="m-r-4">{{ latest.gpuAvi }}</span>张
                  </p>
                </div>
              </div>
              <div class="f-d-c f-2">
                <div v-for="item in gpuListsData" :key="item.name" class="m-t-32 br-6">
                  <div class="f-r-c-b w100">
                    <div class="f-r f-a-c">
                      <img
                        src="@/assets/images/overview/icon_gpu_small.png"
                        alt="Icon Description"
                        class=""
                        width="16"
                        height="16"
                      />
                      <span class="fz-14 m-l-8">{{ item.name + '总量' }}</span>
                    </div>
                    <p class="c-theme fz-w fz-14">{{ item.total }}张</p>
                  </div>
                  <el-progress
                    :percentage="percentage(Number(item.used), Number(item.total))"
                    class="progress-warp m-t-10"
                    :stroke-width="14"
                    :show-text="true"
                    :format="getLabel"
                    :text-inside="true"
                  />
                  <div class="f-r-c-b m-t-6 fz-14">
                    <span>{{ item.used }}张</span>
                    <span>{{ item.other }}张</span>
                  </div>
                  <div class="f-r-c-b m-t-4 c-6 fz-13">
                    <span>已开通</span>
                    <span>剩余量</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!--      <div class="bgfe br-6 bs-b p-20 w100">-->
          <!--        <div class="bs-b topContentO_t">-->
          <!--          <p class="fz-18 fz-w">存储</p>-->
          <!--          <div class="m-t-24 f-r f-j-c custom-progress">-->
          <!--            <div class="storageBox">-->
          <!--              <img-->
          <!--                src="@/assets/images/overview/icon_green_bg.png"-->
          <!--                alt="Icon Description"-->
          <!--                class="storageBoxLeftImg"-->
          <!--              />-->
          <!--              <img-->
          <!--                src="@/assets/images/overview/icon_blue_bg.png"-->
          <!--                alt="Icon Description"-->
          <!--                class="storageBoxRightImg"-->
          <!--              />-->
          <!--            </div>-->
          <!--          </div>-->
          <!--          <div class="m-t-10 f-r f-j-c custom-progress">-->
          <!--            <div class="w50 t-a-c m-t-30 storageMiddleBg">-->
          <!--              &lt;!&ndash;              <p>左侧普通总量</p>&ndash;&gt;-->
          <!--              <div class="m-b-24">-->
          <!--                <span class="fz-13 c-3 i-b m-r-10">存储总量</span>-->
          <!--                <span class="fz-20 fz-w c-3">{{latest.storageTotalSsd}}{{ latest.units }}</span>-->
          <!--              </div>-->
          <!--              <div class="f-r f-j-a m-t-10 m-b-4 storageMiddleLeftBg">-->
          <!--                <div class="f-r">-->
          <!--                  <p class="adhesiveDot bg-theme m-r-10 m-t-18"></p>-->
          <!--                  <div>-->
          <!--                    <p class="fz-15 m-t-8">-->
          <!--                      <span class="fz-w c-green-theme">{{latest.storageUsedSsd}}{{ latest.units }}</span>-->
          <!--                    </p>-->
          <!--                    <p class="fz-12 fz-ls-1 m-t-4 c-9">存储已开通</p>-->
          <!--                  </div>-->
          <!--                </div>-->
          <!--                <div class="f-r">-->
          <!--                  <p class="adhesiveDot bg-d5 m-r-10 m-t-18"></p>-->
          <!--                  <div>-->
          <!--                    <p class="fz-15 m-t-8">-->
          <!--                      <span class="fz-w m-t-14">{{latest.storageAviSsd}}{{ latest.units }}</span>-->
          <!--                    </p>-->
          <!--                    <p class="fz-12 fz-ls-1 m-t-4 c-9">存储剩余量</p>-->
          <!--                  </div>-->
          <!--                </div>-->
          <!--              </div>-->
          <!--            </div>-->
          <!--            <div class="w50 t-a-c m-t-30 storageMiddleBg">-->
          <!--              &lt;!&ndash;              <p>左侧普通总量</p>&ndash;&gt;-->
          <!--              <div class="m-b-24">-->
          <!--                <span class="fz-13 c-3 i-b m-r-10">存储总量</span>-->
          <!--                <span class="fz-20 fz-w c-3">{{latest.storageTotalHhd}}{{ latest.units }}</span>-->
          <!--              </div>-->
          <!--              <div class="f-r f-j-a m-t-10 m-b-4 storageMiddleRightBg">-->
          <!--                <div class="f-r">-->
          <!--                  <p class="adhesiveDot bg-theme m-r-10 m-t-18"></p>-->
          <!--                  <div>-->
          <!--                    <p class="fz-15 m-t-8">-->
          <!--                      <span class="c-blue-theme fz-w">{{latest.storageUsedHhd}}{{ latest.units }}</span>-->
          <!--                    </p>-->
          <!--                    <p class="fz-12 fz-ls-1 m-t-4 c-9">存储已开通</p>-->
          <!--                  </div>-->
          <!--                </div>-->
          <!--                <div class="f-r">-->
          <!--                  <p class="adhesiveDot bg-d5 m-r-10 m-t-18"></p>-->
          <!--                  <div>-->
          <!--                    <p class="fz-15 m-t-8">-->
          <!--                      <span class="fz-w m-t-14">{{latest.storageAviHhd}}{{ latest.units }}</span>-->
          <!--                    </p>-->
          <!--                    <p class="fz-12 fz-ls-1 m-t-4 c-9">存储剩余量</p>-->
          <!--                  </div>-->
          <!--                </div>-->
          <!--              </div>-->
          <!--            </div>-->
          <!--          </div>-->
        </div>
      </div>
      <div class="f-r" style="">
        <div class="eipContainer" style="width: 66.6%">
          <div class="f-r">
            <div class="f-1 bs-b">
              <div class="bs-b topContentF_b topContentF_b_big br-6 m-t-16 w100">
                <div class="p-20 bs-b topContentO_b bg-f br-6 f-1">
                  <div class="bs-b bgfe br-6">
                    <p class="fz-18 fz-w">公网</p>
                    <div class="f-r f-1 f-r-c-b m-t-46 m-b-46">
                      <div class="f-r f-a-c f-r w50">
                        <div class="bg-theme topContentF_i_big f-r f-j-c f-a-c">
                          <img
                            src="@/assets/images/img/wifi.svg"
                            alt="Icon Description"
                            class=""
                            width="30"
                            height="30"
                          />
                        </div>
                        <div class="m-l-14 title">
                          <p class="fz-18">
                            <span class="fz-w m-r-6">{{ latest.eipTotal }}</span><span class="fz-13 c-6 m-l-4">个</span>
                          </p>
                          <p class="fz-13 m-t-6 c-6">公网IP总数</p>
                        </div>
                      </div>
                      <div class="f-r f-j-a f-a-c f-d-c t-a-c w50 t-a-l p-l-5">
                        <p class="topContentF_l topContentF_l_xs f-r-c-b f-nw im-fz-12 w80 t-a-l">
                          <span class="f-1">IP已开通数</span><span class="m-r-4 fz-w m-t-2">{{ latest.eipUsed }}</span>个
                        </p>
                        <p
                          class="topContentF_c topContentF_c_xs m-t-16 f-r-c-b f-nw im-fz-12 w80 t-a-l"
                        >
                          <span class="f-1">IP剩余量</span><span class="m-r-4 fz-w m-t-2">{{ latest.eipAvi }}</span>个
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="f-1 bs-b m-l-16">
              <div class="bs-b topContentF_b topContentF_b_big br-6 m-t-16 w100">
                <div class="p-20 bs-b topContentO_b bg-f br-6 f-1">
                  <div class="bs-b bgfe br-6">
                    <p class="fz-18 fz-w">业务</p>
                    <div class="f-r f-1 f-r-c-b m-b-46">
                      <div class="m-t-46 f-r f-a-c f-r">
                        <div class="bg-theme topContentF_i_big f-r f-j-c f-a-c">
                          <img
                            src="@/assets/images/overview/icon_yewu_normal.png"
                            alt="Icon Description"
                            class=""
                            width="75"
                            height="75"
                          />
                        </div>
                        <div class="m-l-14 title">
                          <p class="fz-18">
                            <span class="fz-w m-r-6">{{ latest.businessNum }}</span><span class="fz-13 c-6 m-l-4">个</span>
                          </p>
                          <p class="fz-13 m-t-6 c-6">业务开通数量</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="f-1 bs-b m-l-16 dcnContainer">
          <div class="bs-b topContentO br-6 m-t-16 dcnBox">
            <div class="p-20 bs-b topContentO_b bg-f br-6 f-1">
              <div class="bs-b bgfe br-6">
                <p class="fz-18 fz-w" style="height: 24px">DCN</p>
                <div class="f-r f-1 f-r-c-b m-t-46 m-b-46">
                  <div class="f-r f-a-c f-r w50">
                    <div class="bg-theme topContentF_i_big f-r f-j-c f-a-c">
                      <img
                        src="@/assets/images/overview/icon_dcn_normal.png"
                        alt="Icon Description"
                        class=""
                        width="75"
                        height="75"
                      />
                    </div>
                    <div class="m-l-14 title">
                      <p class="fz-18 t-r-1">
                        <span class="fz-w m-r-6">{{ latest.dcnTotal }}</span><span class="fz-13 c-6 m-l-4">个</span>
                      </p>
                      <p class="fz-13 m-t-6 c-6 t-r-1">DCN地址总数</p>
                    </div>
                  </div>
                  <div class="f-r f-j-a f-a-c f-d-c t-a-c w50 t-a-l p-l-5">
                    <p class="topContentF_l topContentF_l_xs f-r-c-b f-nw im-fz-12 w80 t-a-l">
                      <span class="f-1">地址已开通数</span>
                      <span class="m-r-4 fz-w m-t-2">{{ latest.dcnUsed }}</span>个
                    </p>
                    <p
                      class="topContentF_c topContentF_c_xs m-t-16 f-r-c-b f-nw im-fz-12 w80 t-a-l"
                    >
                      <span class="f-1">地址剩余量</span><span class="m-r-4 fz-w m-t-2">{{ latest.dcnAvi }}</span>个
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 多个 -->
    <div class="topContentF_b f-1 m-l-16 gpuContainer" style="padding-bottom: 16px">
      <div class="bs-b topContentF_b_big bgfe p-20 br-6 f-d-c" style="height: 100%">
        <p class="fz-18 fz-w f-r-c-b">
          <span>GPU</span>
          <!--          <el-popover popper-class="topContentF" placement="top-start" :width="240" trigger="hover">-->
          <!--            <template #reference>-->
          <!--              <span-->
          <!--                v-if="regionListsName.some((item: any) => item.platformCode === 'VMWARE')"-->
          <!--                class="c-theme fz-w c-p"-->
          <!--                >···</span>-->
          <!--              <span v-else></span>-->
          <!--            </template>-->
          <!--            <template #default>-->
          <!--              <p class="fz-16 fz-w">GPU-T4</p>-->
          <!--              <p class="topContentF_t m-t-14 f-r-c-b f-nw">-->
          <!--                <span class="f-1">GPUT4总数</span><span class="m-r-4">{{ latest.gpuT4Sum }}</span>张-->
          <!--              </p>-->
          <!--              <p class="topContentF_l m-t-14 f-r-c-b f-nw">-->
          <!--                <span class="f-1">GPUT4已开通数</span><span class="m-r-4">{{ latest.gpuT4Used }}</span>张-->
          <!--              </p>-->
          <!--              <p class="topContentF_c m-t-16 f-r-c-b f-nw">-->
          <!--                <span class="f-1">GPUT4剩余量</span><span class="m-r-4">{{ latest.gpuT4AviSale }}</span>张-->
          <!--              </p>-->
          <!--              <p class="fz-16 fz-w m-t-20">GPU-A10</p>-->
          <!--              <p class="topContentF_t m-t-14 f-r-c-b f-nw">-->
          <!--                <span class="f-1">GPUA10总数</span><span class="m-r-4">{{ latest.gpuA10Sum }}</span>张-->
          <!--              </p>-->
          <!--              <p class="topContentF_l m-t-14 f-r-c-b f-nw">-->
          <!--                <span class="f-1">GPUA10已开通数</span><span class="m-r-4">{{ latest.gpuA10Used }}</span>张-->
          <!--              </p>-->
          <!--              <p class="topContentF_c m-t-16 f-r-c-b f-nw">-->
          <!--                <span class="f-1">GPUA10剩余量</span><span class="m-r-4">{{ latest.gpuA10AviSale }}</span>张-->
          <!--              </p>-->
          <!--              <p class="fz-16 fz-w m-t-20">GPU-A40</p>-->
          <!--              <p class="topContentF_t m-t-14 f-r-c-b f-nw">-->
          <!--                <span class="f-1">GPUA40总数</span><span class="m-r-4">{{ latest.gpuA40Sum }}</span>张-->
          <!--              </p>-->
          <!--              <p class="topContentF_l m-t-14 f-r-c-b f-nw">-->
          <!--                <span class="f-1">GPUA40已开通数</span><span class="m-r-4">{{ latest.gpuA40Used }}</span>张-->
          <!--              </p>-->
          <!--              <p class="topContentF_c m-t-16 f-r-c-b f-nw">-->
          <!--                <span class="f-1">GPUA40剩余量</span><span class="m-r-4">{{ latest.gpuA40AviSale }}</span>张-->
          <!--              </p>-->
          <!--            </template>-->
          <!--          </el-popover>-->
        </p>

        <div class="f-r f-1 f-r-c-b m-b-30 topContentF">
          <div class="m-t-22 f-r f-a-c">
            <div class="bg-theme topContentF_i_big f-r f-j-c f-a-c">
              <img
                src="@/assets/images/overview/icon_gpu_normal.png"
                alt="Icon Description"
                class=""
                width="75"
                height="75"
              />
            </div>
            <div class="m-l-14 title">
              <p class="fz-21">
                <span class="fz-w m-r-6">{{ latest.gpuTotal }}</span><span class="fz-13 c-6 m-l-4">张</span>
              </p>
              <p class="fz-13 m-t-6 c-6">GPU总数</p>
            </div>
          </div>

          <div class="f-r f-j-a f-a-c f-d-c m-t-10 t-a-c w50">
            <p class="topContentF_l topContentF_l_xs m-t-14 f-r-c-b f-nw im-fz-15 w85 t-a-l">
              <span class="f-1">GPU已开通数</span><span class="m-r-4 fz-w m-t-2">{{ latest.gpuUsed }}</span>张
            </p>
            <p class="topContentF_c topContentF_c_xs m-t-16 f-r-c-b f-nw im-fz-15 w85 t-a-l">
              <span class="f-1">GPU剩余量</span><span class="m-r-4 fz-w m-t-2">{{ latest.gpuAvi }}</span>张
            </p>
          </div>
        </div>
        <div class="f-d-c f-2">
          <div v-for="item in gpuListsData" :key="item.name" class="m-t-32 br-6">
            <div class="f-r-c-b w100">
              <div class="f-r f-a-c">
                <img
                  src="@/assets/images/overview/icon_gpu_small.png"
                  alt="Icon Description"
                  class=""
                  width="16"
                  height="16"
                />
                <span class="fz-14 m-l-8">{{ item.name + '总量' }}</span>
              </div>
              <p class="c-theme fz-w fz-14">{{ item.total }}张</p>
            </div>
            <el-progress
              :percentage="percentage(Number(item.used), Number(item.total))"
              class="progress-warp m-t-10"
              :stroke-width="14"
              :show-text="true"
              :format="getLabel"
              :text-inside="true"
            />
            <div class="f-r-c-b m-t-6 fz-14">
              <span>{{ item.used }}张</span>
              <span>{{ item.other }}张</span>
            </div>
            <div class="f-r-c-b m-t-4 c-6 fz-13">
              <span>已开通</span>
              <span>剩余量</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { PropType } from 'vue'
import type { gpuItemType } from './interface/type'
// import { ref, } from 'vue'
defineProps({
  latest: {
    type: Object,
    default: () => ({
      vcpuTotal: 0,
      vcpuUsed: 0,
      vcpuAvi: 0,
      vcpuUsedRate: 0,
      memoryTotal: 0,
      memoryUsed: 0,
      memoryAvi: 0,
      memoryUsedRate: 0,
      storageTotalSsd: 0,
      storageUsedSsd: 0,
      storageAviSsd: 0,
      storageUsedRateSsd: 0,
      storageTotalHhd: 0,
      storageUsedHhd: 0,
      storageAviHhd: 0,
      storageUsedRateHhd: 0,
      storageTotal: 0,
      storageUsed: 0,
      storageAvi: 0,
      eipTotal: 0,
      eipUsed: 0,
      eipAvi: 0,
      eipUsedRate: 0,
      gpuTotal: 0,
      gpuUsed: 0,
      gpuAvi: 0,
      gpuUsedRate: 0,
      dcnTotal: 0,
      dcnUsed: 0,
      dcnAvi: 0,
      dcnUsedRate: 0,
      hostNum: 0,
      vmNum: 0,
      businessNum: 0,
      units: 'GB',
      hardwarePoolNum: 0,
      virtualPoolNum: 0,
    }),
  },
  regionListsName: {
    // 资源池集合名词
    type: Array,
    default: () => [],
  },
  gpuListsData: {
    // 资源池集合名词
    type: Array as PropType<gpuItemType[]>,
    default: () => [],
  },
})

let percentage = (Used: number, Total: number) => {
  let num = (Used / Total) * 100
  Math.round(num)
  return num || 0
}
let getLabel = (percent: number) => {
  let str = percent.toFixed(2).replace(/\.?0+$/, '')
  return str + '%' || '0%'
}
</script>
<style scoped lang="scss" src="./pandect.scss"></style>
