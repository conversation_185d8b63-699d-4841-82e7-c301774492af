<template>
  <div class="sl-tabs-wrapper">
    <el-tabs v-model="activeTab" type="card" @tab-change="handleTabChange">
      <el-tab-pane v-for="item in tabs" :key="item.name" :label="item.label" :name="item.name">
        <template #label>
          <span class="tab-label">{{ item.label }}</span>
          <span v-if="showCount && item.count !== undefined" class="tab-count">{{
            item.count
          }}</span>
        </template>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

interface Tab {
  name: string // 唯一标识
  label: string // 显示的标签
  count?: number
}

const props = defineProps({
  tabs: Array as () => Tab[], // Tabs 的数据列表
  modelValue: String, // 当前激活的 tab 名称（双向绑定）
  showCount: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void
  (e: 'tabChange', value: string): void
}>()

const activeTab = ref(props.modelValue)

// 监听 props.modelValue 的变化
watch(
  () => props.modelValue,
  (newValue) => {
    activeTab.value = newValue
  },
)

// 处理标签切换
const handleTabChange = (name: string | number) => {
  const tabName = String(name)
  emit('update:modelValue', tabName)
  emit('tabChange', tabName)
}
</script>

<style lang="scss" scoped>
.sl-tabs-wrapper {
  width: 100%;
  background: #f5f7fa;
  padding: 8px 8px 0 8px;

  // 重写 Element Plus tabs 样式
  :deep(.el-tabs) {
    .el-tabs__header {
      margin: 0;
      border-bottom: none;
      background: transparent;
    }

    .el-tabs__nav-wrap {
      &::after {
        display: none; // 隐藏默认的底部边框
      }
    }

    .el-tabs__nav {
      border: none;
      display: flex;
      position: relative;

      // 添加左侧辅助元素效果
      &::before {
        content: '';
        display: block;
        width: 8px;
        height: 100%;
        background: transparent;
        position: relative;
      }

      // 添加右侧辅助元素效果
      &::after {
        content: '';
        display: block;
        flex: 1;
        height: 100%;
        background: transparent;
        position: relative;
      }
    }

    .el-tabs__item {
      border: none !important;
      background: transparent;
      color: #909399;
      font-size: 14px;
      padding: 8px 20px;
      margin: 0;
      position: relative;
      border-radius: 0;
      transition: all 0.3s ease;

      // 背景层
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: transparent;
        z-index: 0;
        transition: all 0.3s ease;
      }

      // 选中状态
      &.is-active {
        color: var(--el-color-primary);
        font-weight: 500;
        background: #fff;
        border-radius: 8px 8px 0 0;
        box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);

        &::before {
          background-color: #fff;
          border-radius: 8px 8px 0 0;
        }

        // 左侧圆角过渡
        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: -8px;
          width: 8px;
          height: 8px;
          background: radial-gradient(circle at top right, transparent 8px, #fff 8px);
          z-index: 1;
        }
      }

      // 悬停效果
      &:hover:not(.is-active) {
        color: var(--el-color-primary);
        background: transparent;
      }

      // 右侧圆角过渡（选中标签的下一个标签）
      &.is-active + & {
        &::before {
          content: '';
          position: absolute;
          bottom: 0;
          left: 0;
          width: 8px;
          height: 8px;
          background: radial-gradient(circle at top left, transparent 8px, #fff 8px);
          z-index: 1;
        }
      }
    }

    .el-tabs__content {
      background: #fff;
      min-height: 200px;
      padding: 0;
      border-radius: 0 8px 8px 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }

  // 标签内容样式
  .tab-label {
    position: relative;
    z-index: 1;
  }

  .tab-count {
    position: relative;
    z-index: 1;
    background: rgba(72, 127, 239, 0.1);
    font-size: 12px;
    padding: 2px 4px;
    border-radius: 2px;
    margin-left: 4px;
    color: var(--el-color-primary);
  }
}
</style>
