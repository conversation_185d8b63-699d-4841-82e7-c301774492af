<template>
  <el-tabs v-model="activeTab" type="card">
    <el-tab-pane
      v-for="item in tabs"
      :key="item.path"
      :label="item.label"
      :name="item.name"
      :closable="item.close"
    >
      <template #label>
        {{ item.label }}
      </template>
    </el-tab-pane>
  </el-tabs>
</template>

<script setup lang="ts">
interface Tab {
  name: string // 唯一标识
  label: string // 显示的标签
  count?: number
}

defineProps({
  tabs: Array as () => Tab[], // Tabs 的数据列表
  modelValue: String, // 当前激活的 tab 名称（双向绑定）
  searchPlaceholder: {
    type: String,
    default: '工单类型/云平台/工单名称',
  },
  showCount: {
    type: Boolean,
    default: false,
  },
  showActionBar: {
    type: Boolean,
    default: false,
  },
})
</script>

<style></style>
