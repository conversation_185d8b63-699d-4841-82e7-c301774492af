<template>
  <div class="sl-tabs-wrapper">
    <el-tabs v-model="activeTab" type="card" @tab-change="handleTabChange">
      <el-tab-pane v-for="item in tabs" :key="item.name" :label="item.label" :name="item.name">
        <template #label>
          <span class="tab-label">{{ item.label }}</span>
          <span v-if="showCount && item.count !== undefined" class="tab-count">{{
            item.count
          }}</span>
        </template>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

interface Tab {
  name: string // 唯一标识
  label: string // 显示的标签
  count?: number
}

const props = defineProps({
  tabs: Array as () => Tab[], // Tabs 的数据列表
  modelValue: String, // 当前激活的 tab 名称（双向绑定）
  showCount: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void
  (e: 'tabChange', value: string): void
}>()

const activeTab = ref(props.modelValue)

// 监听 props.modelValue 的变化
watch(
  () => props.modelValue,
  (newValue) => {
    activeTab.value = newValue
  },
)

// 处理标签切换
const handleTabChange = (name: string | number) => {
  const tabName = String(name)
  emit('update:modelValue', tabName)
  emit('tabChange', tabName)
}
</script>

<style lang="scss" scoped>
.sl-tabs-wrapper {
  width: 100%;
  background: #fff;
  // 重写 Element Plus tabs 样式
  :deep(.el-tabs) {
    .el-tabs__header {
      margin: 0;
      border-bottom: none;
      background: transparent;
      height: 31px;
    }

    .el-tabs__nav-wrap {
      &::after {
        display: none; // 隐藏默认的底部边框
      }
    }

    .el-tabs__nav {
      border: none;
      display: flex;
      position: relative;
    }

    .el-tabs__item {
      border: none !important;
      background: transparent;
      color: #909399;
      font-size: 14px;
      padding: 0 20px;
      margin: 0;
      position: relative;
      border-radius: 0;
      cursor: pointer;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      will-change: background-color, color;
      margin-left: 9px;
      // 选中状态 - 参照HTML示例
      &.is-active {
        position: relative;
        color: var(--el-color-primary);
        font-weight: 400;
        background: var(--el-bg-color-page);
        border-radius: 12px 12px 0 0;

        // 左右两侧的圆角过渡效果
        &::before,
        &::after {
          position: absolute;
          bottom: 0;
          content: '';
          width: 30px;
          height: 30px;
          border-radius: 100%;
          box-shadow: 0 0 0 50px var(--el-bg-color-page); // 使用页面背景色，增大扩展范围
        }

        // 左侧圆角过渡 - 增大尺寸和裁剪范围
        &::before {
          left: -30px;
          clip-path: inset(50% -15px 0 50%);
        }

        // 右侧圆角过渡 - 增大尺寸和裁剪范围
        &::after {
          right: -30px;
          clip-path: inset(50% 50% 0 -15px);
        }

        // 所有选中标签都显示左右反向圆角，不做特殊处理
      }

      // 悬停效果
      &:hover:not(.is-active) {
        color: var(--el-color-primary);
        background: transparent;
      }
    }

    .el-tabs__content {
      background: #fff;
      padding: 0;
      min-height: 0;

      // 移除空内容时的占位
      &:empty {
        display: none !important;
        height: 0 !important;
        min-height: 0 !important;
      }

      // 如果有内容但内容为空，也隐藏
      .el-tab-pane {
        &:empty {
          display: none !important;
          height: 0 !important;
        }

        // 如果标签页只包含空白字符，也隐藏
        &:not(:has(*)) {
          display: none !important;
        }
      }
    }
  }

  // 标签内容样式
  .tab-label {
    position: relative;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
  }

  .tab-count {
    position: relative;
    z-index: 1;
    background: rgba(72, 127, 239, 0.1);
    font-size: 12px;
    padding: 2px 4px;
    border-radius: 2px;
    margin-left: 2px;
    color: var(--el-color-primary);
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
