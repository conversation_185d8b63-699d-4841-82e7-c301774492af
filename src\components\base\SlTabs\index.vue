<template>
  <div class="sl-tabs">
    <div class="tab-header">
      <div class="tab-item help first">
        <div class="bg"></div>
      </div>
      <div
        v-for="tab in tabs"
        :key="tab.name"
        :class="['tab-item', { active: activeTab === tab.name }]"
        @click="selectTab(tab.name)"
      >
        <div class="bg"></div>
        <span class="title">{{ tab.label }}</span>
        <span v-if="showCount && tab.count !== undefined" class="count">{{ tab.count }}</span>
      </div>
      <div class="tab-item help last">
        <div class="bg"></div>
      </div>
    </div>
    <div class="tab-content">
      <slot></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

interface Tab {
  name: string // 唯一标识
  label: string // 显示的标签
  count?: number
}

const props = defineProps({
  tabs: Array as () => Tab[], // Tabs 的数据列表
  modelValue: String, // 当前激活的 tab 名称（双向绑定）
  showCount: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void
}>()

const activeTab = ref(props.modelValue)

// 监听 props.modelValue 的变化
watch(
  () => props.modelValue,
  (newValue) => {
    activeTab.value = newValue
  },
)

// 选择标签时更新 activeTab 和触发双向绑定
const selectTab = (name: string) => {
  activeTab.value = name
  emit('update:modelValue', name)
}
</script>

<style lang="scss" scoped>
.sl-tabs {
  width: 100%;
  display: flex;
  flex-direction: column;
}

.tab-header {
  display: flex;
  font-size: 14px;
}

.tab-item {
  padding: 8px 20px;
  cursor: pointer;
  color: #606266;
  display: flex;
  align-items: center;
  position: relative;
}

.tab-item.help.last {
  flex-grow: 1;
}

.tab-item.help.first {
  padding: 0px 8px;
}

.tab-item.active {
  color: var(--el-color-primary);
  font-weight: 400;
  background: #fff;
}

.title {
  z-index: 1;
  position: relative;
}

.count {
  z-index: 1;
  background: rgba(72, 127, 239, 0.1);
  font-size: 12px;
  padding: 2px 4px;
  border-radius: 2px;
  margin-left: 2px;
}

.bg {
  position: absolute;
  top: 0px;
  left: 0px;
  right: 0px;
  bottom: 0px;
  background-color: #fff;
  z-index: 0;
}

.tab-item:has(+ .active) .bg {
  border-bottom-right-radius: 12px;
}

.tab-item.active + .tab-item .bg {
  border-bottom-left-radius: 12px;
}

.tab-item.active .bg {
  background-color: #f2f3f5;
  border-radius: 8px 8px 0 0;
}

.tab-item:hover {
  color: #409eff;
}

.tab-content {
  background: #f2f3f5;
  min-height: 200px;
}
</style>
