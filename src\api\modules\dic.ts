import type {
  // BusiSystemlistType,
  // GetResourceListParamsType,
  GetResourceListResType,
  // ListByUserIdType,
  // ResourceListType,
} from '@/views/approvalCenter/workOrder/interface/type'
import http from '@/api'
import { WOC } from '../config/servicePort'

/**
 * @name 云类型字典
 */
export const getCloudTypeDic = (config: any) =>
  http.post<any[]>(WOC + '/catalogueDomain/list', config)

/**
 * @name 云平台字典，无参数或参数parentCode为空时，返回所有云平台字典
 */
export const getCloudPlatformDic = (config: any) =>
  http.post<GetResourceListResType[]>(WOC + '/catalogueDomain/listByParentCode', config)

/**
 * @name 资源池字典
 */
export const getResourcePoolsDic = (config: any) =>
  http.post<GetResourceListResType[]>(WOC + '/region/list', config)

/**
 * @name 可用区字典
 */
export const getAzListDic = (config: any) =>
  http.post<GetResourceListResType[]>(WOC + '/az/list', config)

/**
 * @name 可用区字典-map
 */
export const getAzListBatchDic = (config: any) =>
  http.post<GetResourceListResType[]>(WOC + '/az/listBatch', config)

/**
 * @name 云资源部领导字典
 */
export const getCloudLeaderDic = (config: any) =>
  http.post<GetResourceListResType[]>(WOC + '/user/queryUsersByRoleCode', config)

/**
 * @name 字典查询
 */
export const getDic = (config: any) => http.get(WOC + '/config/dictionary/map', config)
