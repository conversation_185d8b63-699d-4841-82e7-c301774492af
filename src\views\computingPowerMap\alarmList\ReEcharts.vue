<script setup lang="ts">
import type { EChartsOption, ECharts } from 'echarts'
import { init } from 'echarts'
// import { useGlobalStore } from '@/stores/modules/global'
import { ref, watch, onMounted, onBeforeUnmount, onUnmounted, nextTick } from 'vue'

// 定义props
interface Props {
  width?: string
  height?: string
  option: EChartsOption
}
const props = withDefaults(defineProps<Props>(), {
  width: '100%',
  height: '100%',
  option: () => ({}),
})

let showPower = ref<boolean>(true)
let myChartsRef = ref<HTMLDivElement>()
let myChart: ECharts

let timer: any

interface SeriesType {
  data: number[] | string[] | object[]
}
// 初始化echarts
const initChart = (): void => {
  if (myChart !== undefined) {
    myChart.dispose()
  }
  myChart = init(myChartsRef.value as HTMLDivElement)

  // }
}

// 重新渲染echarts
const resizeChart = (): void => {
  timer = setTimeout(() => {
    if (myChart) {
      myChart.resize()
    }
  }, 50)
}

onMounted(() => {
  window.addEventListener('resize', resizeChart)
  if (myChartsRef.value) {
    resizeObserver = new ResizeObserver(handleResize)
    resizeObserver.observe(myChartsRef.value)
  }
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', resizeChart)
  clearTimeout(timer)
  timer = 0
})

// const globalStore = useGlobalStore()
// const isCollapse = computed(() => globalStore.isCollapse)

watch(
  props.option,
  (n) => {
    let seriesArray = n.series as SeriesType[]
    let optionData_ = seriesArray?.[0]?.data || []
    console.log(optionData_)
    if (optionData_.length == 0) {
      myChart?.setOption(n, true)
      showPower.value = true
      return
    } else {
      showPower.value = false
      nextTick(() => {
        if (!myChart) {
          initChart()
        }
        // 拿到option配置项，渲染echarts
        myChart?.setOption(n, true)
      })
    }
  },
  {
    deep: true,
  },
)
// watch(isCollapse, (newValue, oldValue) => {
//   console.log("isCollapse", newValue);
// });

let resizeObserver: ResizeObserver | null = null

const handleResize = (entries: ResizeObserverEntry[]) => {
  // for (const entry of entries) {
  if (entries.length > 0) {
    resizeChart()
  }
  // }
}

onUnmounted(() => {
  if (resizeObserver) {
    resizeObserver.disconnect()
  }
})
</script>

<template>
  <div
    v-show="!showPower"
    ref="myChartsRef"
    :style="{ height: height, width: width }"
    :option="option"
  ></div>
  <div v-show="showPower">
    <el-empty :image-size="200" />
  </div>
</template>
