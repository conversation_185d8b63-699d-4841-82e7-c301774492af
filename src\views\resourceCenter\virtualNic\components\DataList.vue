<template>
  <SlProTable
    ref="proTable"
    highlight-current-row
    :columns="columns"
    style="min-height: 300px"
    :request-api="getVirtualNicList"
    :init-param="queryParams"
    :current-change="currentChange"
    hidden-table-header
    row-key="id"
  >
  </SlProTable>
</template>
<script setup lang="tsx" name="DataList">
import { ref, type VNode, computed } from 'vue'
import type { ColumnProps, ProTableInstance } from '@/components/SlProTable/interface'
import { getVirtualNicList, deleteVirtualNic } from '@/api/modules/resourecenter'
import { ElMessageBox, ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'

const props = defineProps({
  queryParams: {
    type: Object,
    default: () => ({}),
  },
  isSelectMode: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['currentChange', 'selected'])

const currentChange = (currentRow: any, oldCurrentRow: any) => {
  emit('currentChange', currentRow, oldCurrentRow)
}

// 动态计算列配置
const columns = computed(() => {
  const baseColumns: ColumnProps<any>[] = [
    { type: 'index', label: '序号', width: 55, fixed: 'left' },
    { prop: 'vnicName', label: '虚拟网卡名称', fixed: 'left', width: 150 },
    { prop: 'businessSystemName', label: '业务系统' },
    { prop: 'catalogueDomainName', label: '云类型' },
    { prop: 'domainName', label: '云平台' },
    { prop: 'regionName', label: '资源池' },
    { prop: 'azName', label: '可用区' },
    { prop: 'vpcName', label: 'VPC/网络' },
    { prop: 'subnetName', label: '子网' },
    { prop: 'ipAddress', label: 'IP地址' },
    { prop: 'vmName', label: '云主机' },
    { prop: 'createTime', label: '创建时间', width: 150 },
  ]

  // 如果不是选择模式，添加操作列
  if (!props.isSelectMode) {
    baseColumns.push({
      prop: 'operation',
      label: '操作',
      width: 150,
      fixed: 'right',
      render: operationRender,
    })
  } else {
    // 选择模式下添加操作列（绑定操作）
    baseColumns.push({
      prop: 'operation',
      label: '操作',
      width: 100,
      fixed: 'right',
      render: selectOperationRender,
    })
  }

  return baseColumns
})

// 普通操作列渲染函数
function operationRender({ row }: any): VNode {
  return (
    <>
      <el-button onClick={() => handleEdit(row)} type="primary" link>
        编辑
      </el-button>
      <el-button onClick={() => handleDelete(row)} type="primary" link>
        删除
      </el-button>
    </>
  )
}

// 选择模式下的操作列渲染函数
function selectOperationRender({ row }: any): VNode {
  return (
    <>
      <el-button
        onClick={() => handleSelect(row)}
        type="primary"
        disabled={props.queryParams.operateType == 'BIND' && row.vmId}
        link
      >
        {props.queryParams.operateType == 'BIND' ? '绑定' : '解绑'}
      </el-button>
    </>
  )
}

// 选择虚拟网卡
const handleSelect = (row: any) => {
  emit('selected', row, props.queryParams.operateType)
}

const proTable = ref<ProTableInstance>()

const router = useRouter()
const handleEdit = (row: any) => {
  router.push({
    path: '/virtualNicCreate',
    query: { id: row.id },
  })
}

// 删除虚拟网卡
const handleDelete = async (row: any) => {
  ElMessageBox.confirm(`确定要删除虚拟网卡 "${row.vnicName}" 吗？`, '删除确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      try {
        const res = await deleteVirtualNic({
          id: row.id,
        })
        if (res.code == 200) {
          ElMessage.success('删除成功')
          proTable.value?.getTableList()
        }
      } catch (error) {
        console.error('删除虚拟网卡失败', error)
      }
    })
    .catch(() => {
      // 取消删除
    })
}

defineExpose({
  proTable,
})
</script>
