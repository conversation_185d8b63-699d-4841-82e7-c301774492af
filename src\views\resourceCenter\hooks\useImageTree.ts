import { imageTree as imageTreeApi } from '@/api/modules/resourecenter'
import { ref } from 'vue'

interface ImageItem {
  label: string
  value: string
  children: ImageItem[]
}

export const useImageTree = (config?: {
  azId?: string
  regionId?: string
  type?: string
  shares?: string
}) => {
  const imageTree = ref<ImageItem[]>([])
  const getImageTree = async () => {
    const { entity } = await imageTreeApi(config)
    imageTree.value.length = 0
    imageTree.value.push(...treeFeild(entity))
  }
  getImageTree()
  return imageTree
}

function treeFeild(tree: any[]): ImageItem[] {
  return tree.map((item: any) => {
    return {
      label: item.name,
      value: item.name,
      ...item,
      children: item.children ? treeFeild(item.children) : [],
    }
  })
}
