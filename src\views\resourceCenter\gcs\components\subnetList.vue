<template>
  <SlProTable
    ref="proTable"
    highlight-current-row
    :columns="columns"
    style="min-height: 300px"
    :data="props.subnetList"
    hidden-table-header
    :pagination="false"
  >
  </SlProTable>
</template>

<script setup lang="tsx" name="subnetList">
import { ref, reactive } from 'vue'
import type { ColumnProps, ProTableInstance } from '@/components/SlProTable/interface'

const props = defineProps<{
  subnetList: any[]
}>()

const proTable = ref<ProTableInstance>()

// 表格配置项
const columns = reactive<ColumnProps<any>[]>([
  { type: 'index', label: '序号', width: 55 },
  { prop: 'plan', label: '网络平面', width: 100 },
  { prop: 'type', label: '类型', width: 80 },
  { prop: 'name', label: '名称', width: 150 },
  { prop: 'subnetName', label: '子网名称', width: 150 },
  { prop: 'cidr', label: '子网网段', width: 150 },
  { prop: 'ipv4', label: 'ipv4', width: 150 },
  { prop: 'ipv6', label: 'ipv6', width: 150 },
])
</script>
