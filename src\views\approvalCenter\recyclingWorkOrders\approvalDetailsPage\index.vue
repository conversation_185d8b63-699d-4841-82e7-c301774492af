<template>
  <div class="table-box approvalDetailsPage">
    <sl-page-header
      :title="titleMap[opStatus]"
      :icon="{
        Vnode: Platform,
        color: '#0052D9',
        size: '20px',
      }"
      :show-back="true"
      :back="{
        title: '返回回收工单',
        function: handleBack,
      }"
    >
    </sl-page-header>
    <el-scrollbar wrap-class="shopping-carts-scroll-view" class="scroll-view">
      <!--步骤条 -->
      <div class="steps-con">
        <sl-steps :class="{ closed: orderDesc.orderStatus == 'CLOSE' }"></sl-steps>
      </div>
      <!-- Tabs 组件 -->
      <sl-base-tabs :show-count="false" :tabs="tabs" v-model="activeTab" v-if="currentTask">
      </sl-base-tabs>

      <div
        v-show="['home', 'profile'].includes(activeTab)"
        class="sl-page-content"
        v-if="currentTask"
      >
        <BasicInformation
          ref="basicInformationRef"
          v-show="activeTab == 'home'"
          :order-desc="orderDesc"
          :btn-auth="btnAuth"
        />

        <!-- 资源信息  -->
        <ResourceInformation
          ref="resourceInformationRef"
          v-show="activeTab == 'profile'"
          :order-desc="orderDesc"
          :btn-auth="btnAuth"
          v-model:disabled-btn="disabledBtn"
          @refresh="inintData"
        />
      </div>
      <div v-if="activeTab == 'settings'" class="sl-page-content table-main auditTable">
        <!-- 审核日志 -->
        <AuditTable :order-desc="orderDesc" />
      </div>
    </el-scrollbar>

    <!-- 按钮组件 -->
    <div
      v-if="pageStatus"
      v-show="['home', 'profile'].includes(activeTab)"
      class="sl-page-content page-footer"
    >
      <div class="sl-card">
        <RenderBtn />
      </div>
    </div>
    <!-- 审核组件 -->

    <SlAuditDialog
      v-show="visible"
      ref="SlAuditDialogRef"
      v-model:visible="visible"
      back-path="/recycleWorkOrder"
      :audit-api="recoveryWorkOrdersAuditApi"
    />
  </div>
</template>

<script setup lang="tsx">
import SlBaseTabs from '@/components/base/SlBaseTabs.vue'
import { Platform } from '@element-plus/icons-vue'
import SlSteps from '@/components/SlSteps/index.vue'
import { computed, provide, ref, nextTick } from 'vue'
import {
  getRecoveryWorkOrdersDetailApi,
  recoveryWorkOrdersAuditApi,
} from '@/api/modules/approvalCenter'
import { useRoute, useRouter } from 'vue-router'
import useWorkOrder from '@/hooks/useWorkOrder'
import BasicInformation from './components/BasicInformation.vue'
import ResourceInformation from './components/ResourceInformation/index.vue'
import AuditTable from '@/views/approvalCenter/components/AuditTable.vue'
import type { AuditDialogInstance } from '@/components/SlAuditDialog/interface'
import type { RecyclingBtnsType, RecyclingWorkorderType } from '../interface/type'

let orderDesc = ref<RecyclingWorkorderType>({
  activityTask: undefined,
})

const titleMap = {
  '1': '回收工单审批',
  '2': '回收工单查看',
  '3': '回收工单编辑',
  '4': '回收工单创建',
} as const

type IOpStatus = keyof typeof titleMap

const route = useRoute()
const router = useRouter()
const workOrderId = route.query?.workOrderId
const opStatus = route.query?.opStatus as IOpStatus
provide('workOrderId', workOrderId)
/**
 *  @readonly获取页面状态用来做表单权限控制
 *  */

const pageStatus = ref(opStatus === '1') //1:编辑 0:查看
provide('pageStatus', pageStatus)

let flag = ref(true) //判断是否第一次初始化参数
const inintData = async () => {
  if (!workOrderId) return
  // 获取工单详情
  const res = await getRecoveryWorkOrdersDetailApi({ workOrderId: String(workOrderId) })
  if (!res.entity) return
  // 给予默认值
  orderDesc.value = {
    ...res.entity,
  }
  getWorkOrderNode(orderDesc.value.activityTask!)
  nextTick(() => {
    resourceInformationRef.value?.initData(orderDesc.value, flag.value)
    basicInformationRef.value?.initData(orderDesc.value)
    flag.value = false
  })
}
inintData()

// 当前节点
const { allTasks, currentTask, currentSort, historyTasks, getWorkOrderNode } = useWorkOrder()
provide('allTasks', allTasks)
provide('currentSort', currentSort)
provide('currentTask', currentTask)
provide('historyTasks', historyTasks)

const handleBack = () => {
  router.push('/recycleWorkOrder')
}

const tabs = [
  { name: 'home', label: '基础信息' },
  { name: 'profile', label: '资源信息' },
  { name: 'settings', label: '审核日志' },
]
const activeTab = ref('profile') // 默认激活的 tab

// 按钮权限控制
const btnAuth = computed<RecyclingBtnsType>(() => {
  return {
    user_task: currentTask.value === 'user_task', // 架构审核
    business_depart_leader2: currentTask.value === 'business_depart_leader2', // 二级业务部门领导审核
    operation_group: currentTask.value === 'operation_group', // 运营组审核
    tenant_task: currentTask.value === 'tenant_task', // 租户审核
    end: currentTask.value === 'autodit end', //  结束节点
  }
})
provide('btnAuth', btnAuth)
//  表单组件
const resourceInformationRef = ref<InstanceType<typeof ResourceInformation> | null>(null)
const basicInformationRef = ref<InstanceType<typeof BasicInformation> | null>(null)

// ----------------------审核提交-----------------------

async function getAuditNode() {
  //后续别的表单可以根据这个逻辑去获取 添加if语句

  return []
}
// 审核组件
const SlAuditDialogRef = ref<AuditDialogInstance>()
const visible = ref(false)
//校验表单

// 获取提交的参数
async function changeParams(status: string) {
  let params: any = {
    orderId: route.query.workOrderId,
    currentNodeCode: currentTask.value,
    activiteStatus: status === 'pass' ? 1 : 0,
  }
  let fields: any[] = []

  if (status === 'reject') {
    params['nodeCode'] = allTasks.value[0].task
  }
  if (status === 'pass') fields = await getAuditNode()
  params = {
    ...params,
    ...(await resourceInformationRef.value?.submitForm()),
  }

  return {
    params,
    fields,
  }
}
/**
 * 审核提交
 * @param status 审核状态
 * @param falg 是否需要弹窗 默认打开
 *  */
const subAudit = async (status: string, falg: boolean = true) => {
  // 1. 校验表单 - 架构审核节点

  // 2.获取数据
  const { params, fields } = await changeParams(status)

  // 3.打开弹窗
  visible.value = true
  nextTick(() => {
    SlAuditDialogRef.value?.openDialog(
      {
        params,
        fields,
      },
      falg,
    )
  })
}

// ----------------------提交按钮-----------------------

const disabledBtn = ref(false)

// 渲染按钮组件
const RenderBtn = () => {
  const obj = {
    operation_group: '完成',
    tenant_task: '确认完成',
  }
  // 1 .查看表单

  return (
    <>
      <el-button v-show={btnAuth.value.business_depart_leader2} onclick={() => subAudit('reject')}>
        审批驳回
      </el-button>
      <el-button disabled={disabledBtn.value} type="primary" onclick={() => subAudit('pass')}>
        {obj[currentTask.value as keyof typeof obj] || '审批通过'}
      </el-button>
    </>
  )
}
</script>

<style lang="scss" scoped>
// 页面通用 标题下面的通用布局

.steps-con {
  background: #fff;
  padding: 20px;
}
.auditTable {
  min-height: 350px;
}
.page-footer {
  text-align: right;
  padding-top: 2px;
  padding-bottom: 0;
}
</style>
