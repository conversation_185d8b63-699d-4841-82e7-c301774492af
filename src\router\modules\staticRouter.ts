import type { RouteRecordRaw } from 'vue-router'

const HOME_URL = '/operationsOverview'
import managementCenterRouter from './managementCenter'
import resourceCenterRouter from './resourceCenter'
import configCenterRouter from './configCenter'
import overviewRouter from './computingPowerMap'
import approvalCenterRouter from './approvalCenter'
import productCenterRouter from './productCenter'

/**
 * staticRouter (静态路由)
 */
export const staticRouter: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: HOME_URL,
  },
  {
    path: '/login',
    name: 'login',
    component: () => import('@/views/login/index.vue'),
    meta: {
      title: '登录',
    },
  },
  {
    path: '/verificate',
    name: 'verificate',
    component: () => import('@/views/login/verificate.vue'),
    meta: {
      title: '验证',
    },
  },
  {
    path: '/layout',
    name: 'layout',
    component: () => import('@/layout/index.vue'),
    redirect: HOME_URL,
    children: [
      // 用动态路由的话直接删除调保持数组为空
      ...overviewRouter,
      ...managementCenterRouter,
      ...resourceCenterRouter,
      ...configCenterRouter,
      ...approvalCenterRouter,
      ...productCenterRouter,
    ],
  },
  {
    path: '/resArrangement',
    name: 'resArrangement',
    component: () => import('@/views/resArrangement/index.vue'),
    meta: {
      title: '验证',
    },
  },
  {
    path: '/mricSubApp',
    component: () => import('@/views/mricDemo/subApp.vue'),
    name: 'mricSubApp',
    meta: {
      title: '子应用',
      icon: 'icon-gongdan',
    },
  },
]

/**
 * errorRouter (错误页面路由)
 */
export const errorRouter = [
  // {
  //   path: '/403',
  //   name: '403',
  //   component: () => import('@/components/ErrorMessage/403.vue'),
  //   meta: {
  //     title: '403页面',
  //   },
  // },
  // {
  //   path: '/404',
  //   name: '404',
  //   component: () => import('@/components/ErrorMessage/404.vue'),
  //   meta: {
  //     title: '404页面',
  //   },
  // },
  // {
  //   path: '/500',
  //   name: '500',
  //   component: () => import('@/components/ErrorMessage/500.vue'),
  //   meta: {
  //     title: '500页面',
  //   },
  // },
  // Resolve refresh page, route warnings
  {
    path: '/:pathMatch(.*)*',
    component: () => import('@/components/SlErrorPage.vue'),
  },
]
