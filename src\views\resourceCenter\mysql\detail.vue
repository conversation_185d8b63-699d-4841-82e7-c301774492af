<template>
  <div id="EcsDetail" class="table-box">
    <sl-page-header
      title="云数据库详情"
      :icon="{
        Vnode: Platform,
        color: '#0052D9',
        size: '20px',
      }"
      :show-back="true"
      :back="{
        title: '返回列表',
        function: handleGoBack,
      }"
    >
    </sl-page-header>
    <el-scrollbar wrap-class="slb-detail-scroll-view" class="scroll-view">
      <div class="sl-card">
        <div class="operation-buttons" v-if="!shouldHideResourceOperations">
          <!-- 开关机按钮 -->
          <el-button
            v-if="['RUNING', 'STOPED'].includes(detailData.deviceStatus)"
            type="primary"
            @click="handleSwitchMachine"
          >
            {{ detailData.deviceStatus === 'RUNING' ? '关机' : '开机' }}
          </el-button>
          <!-- 重启按钮 -->
          <el-button
            type="primary"
            :disabled="detailData.deviceStatus !== 'RUNING'"
            @click="handleRestart"
          >
            重启
          </el-button>
          <!-- 更多操作下拉菜单 -->
          <el-dropdown @command="handleCommand">
            <el-button type="primary">
              更多操作
              <el-icon class="el-icon--right">
                <arrow-down />
              </el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="resetPwd" :disabled="detailData.resetPwd !== 1">
                  修改密码
                </el-dropdown-item>
                <el-dropdown-item command="bindOrUnbindSecurityGroup">
                  {{ detailData.securityGroupName ? '解绑安全组' : '绑定安全组' }}
                </el-dropdown-item>
                <el-dropdown-item command="bindVirtualNic">绑定虚拟网卡</el-dropdown-item>
                <el-dropdown-item v-if="detailData.vnicId" command="unbindVirtualNic">
                  解绑虚拟网卡
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
        <sl-form
          :show-block-title="false"
          :label-width="140"
          ref="slFormRef"
          v-model="detailData"
          :options="formOptions"
        >
          <template #subnetSlot>
            <span>
              <el-button @click="handleViewSubnet" type="primary" link> 查看子网 </el-button>
            </span>
          </template>
        </sl-form>
        <div class="tab-content">
          <el-tabs v-model="activeTab" class="demo-tabs" @tab-click="handleTabsClick">
            <el-tab-pane
              :label="item.label"
              :name="item.name"
              v-for="item in tabs"
              :key="item.name"
            >
              <div v-if="associatedResourceData[item.name].length > 0">
                <div
                  class="resource-item"
                  v-for="resource in associatedResourceData[item.name]"
                  :key="resource.id"
                >
                  <div class="resource-item-name">{{ resource.nameStr }}</div>
                </div>
              </div>
              <el-empty description="暂无绑定资源" v-else />
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </el-scrollbar>
    <SlDialog
      v-model="subnetDialogVisible"
      title="子网信息"
      width="1000px"
      :show-cancel="false"
      @confirm="subnetDialogVisible = false"
      destroy-on-close
    >
      <SubnetList :subnet-list="currentSubnetList" />
    </SlDialog>
    <!-- 修改密码弹窗 -->
    <SlDialog
      v-model="updatePasswordDialogVisible"
      title="修改密码"
      width="600px"
      destroy-on-close
      confirm-text="提交"
      @close="handleUpdatePasswordClose"
      @confirm="handleUpdatePasswordConfirm"
    >
      <sl-form
        ref="updatePasswordFormRef"
        :options="updatePasswordFormOptions"
        v-model="updatePasswordFormModel"
      >
      </sl-form>
      <div class="warning">
        提醒：密码格式必须包含字母和数字，且长度在8到26个字符之间，只可包含特殊字符()~!@#$%^&*-+=|{}[]∶;,.?/
      </div>
    </SlDialog>

    <!-- 安全组弹窗 -->
    <SlDialog
      v-model="securityGroupDialogVisible"
      title="选择安全组"
      width="1000px"
      @close="securityGroupDialogVisible = false"
      :show-confirm="false"
      cancel-text="关闭"
      destroy-on-close
    >
      <DataList
        ref="securityGroupListRef"
        :query-params="securityGroupQueryParams"
        :is-select-mode="true"
        @selected="handleSecurityGroupSelected"
      />
    </SlDialog>

    <!-- 虚拟网卡弹窗 -->
    <SlDialog
      v-model="virtualNicDialogVisible"
      title="选择虚拟网卡"
      width="1200px"
      @close="virtualNicDialogVisible = false"
      :show-confirm="false"
      cancel-text="关闭"
      destroy-on-close
    >
      <VirtualNicDataList
        ref="virtualNicListRef"
        :query-params="virtualNicQueryParams"
        :is-select-mode="true"
        @selected="handleVirtualNicSelected"
      />
    </SlDialog>
  </div>
</template>

<script setup lang="tsx">
import { onMounted, ref, reactive } from 'vue'
import { Platform, ArrowDown } from '@element-plus/icons-vue'
import SlPageHeader from '@/components/SlPageHeader/index.vue'
// import SlBaseTabs from '@/components/base/SlBaseTabs.vue'
import { useRouter, useRoute } from 'vue-router'
import {
  getResourceDetail,
  getSubnetList,
  securityGroupOperate,
  virtualNicOperate,
  getResourceList,
  getVirtualNicList,
  getSecurityGroupList,
} from '@/api/modules/resourecenter'
import SubnetList from './components/subnetList.vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  useResourceOperationFormModel,
  useUpdatePasswordFormOptions,
  executeResourceOperationConfirm,
  useResourceOperationFormClear,
  type ResourceOperationModel,
} from '../hooks/useResourceOperationModels'
import DataList from '../securityGroup/components/DataList.vue'
import VirtualNicDataList from '../virtualNic/components/DataList.vue'
import { useRolePermission } from '../hooks/useRolePermission'

const router = useRouter()
const route = useRoute()

const { shouldHideResourceOperations } = useRolePermission()

// 获取资源详情ID和是否显示主备服务器组标签
const resourceId = ref<string>((route.query.id as string) || '')
const deviceId = ref<string>((route.query.deviceId as string) || '')

// 定义标签页(如果不显示主备服务器，则移除对应标签)
const tabs = ref([
  { name: 'evs', label: '硬盘' },
  { name: 'vnic', label: '网卡' },
  { name: 'eip', label: '弹性公网' },
  { name: 'sg', label: '安全组' },
])

// 定义激活的标签页
const activeTab = ref('evs')
const handleTabsClick = (tab: any) => {
  activeTab.value = tab.name
}

const detailData = reactive<any>({
  deviceName: '',
  deviceId: '',
  handoverStatus: '',
  osVersion: '',
  spec: '',
  sysDisk: '',
  dataDisk: '',
  eip: '',
  ip: '',
  bandWidth: '',
  subnet: '',
  applyTime: '',
  tenantName: '',
  businessSysName: '',
  cloudPlatform: '',
  resourcePoolName: '',
  orderCode: '',
  projectName: '',
  securityGroupName: '',
  vnicName: '',
  createTime: '',
  expireTime: '',
  billId: '',
  instanceUuid: '',
  deviceStatusCn: '',
  deviceStatus: '', // 添加设备状态
  recoveryStatusCn: '',
  changeStatusCn: '',
  applyUserName: '',
  orderId: 0, // 添加orderId
  goodsOrderId: '', // 添加goodsOrderId
  resetPwd: 0, // 添加重置密码权限标识
  securityGroupIds: '', // 添加安全组IDs
  vnicId: '', // 添加虚拟网卡ID
  vpcName: '', // 添加VPC名称
  vpcId: '', // 添加VPC ID
  resourcePoolCode: '', // 添加资源池代码
  businessSysId: '', // 添加业务系统ID
})
const formOptions = reactive<any[]>([
  {
    groupItems: [
      {
        label: '云数据库名称',
        type: 'text',
        key: 'deviceName',
        span: 8,
      },
      {
        label: '资源ID',
        type: 'text',
        key: 'deviceId',
        span: 8,
      },
      {
        label: '交维状态',
        type: 'text',
        key: 'handoverStatus',
        span: 8,
      },
      {
        label: '系统版本',
        type: 'text',
        key: 'osVersion',
        span: 8,
      },
      {
        label: '实例规格',
        type: 'text',
        key: 'spec',
        span: 8,
      },
      {
        label: '系统盘',
        type: 'text',
        key: 'sysDisk',
        span: 8,
      },
      {
        label: '数据盘',
        type: 'text',
        key: 'dataDisk',
        span: 8,
      },
      {
        label: '弹性公网',
        type: 'text',
        key: 'eip',
        span: 8,
      },
      {
        label: 'IP',
        type: 'text',
        key: 'ip',
        span: 8,
      },
      {
        label: '带宽',
        type: 'text',
        key: 'bandWidth',
        span: 8,
      },

      {
        label: '子网信息',
        type: 'slot',
        slotName: 'subnetSlot',
        key: 'subnet',
        span: 8,
      },
      {
        label: '申请时长',
        type: 'text',
        key: 'applyTime',
        span: 8,
      },
      {
        label: '租户',
        type: 'text',
        key: 'tenantName',
        span: 8,
      },
      {
        label: '业务系统',
        type: 'text',
        key: 'businessSysName',
        span: 8,
      },
      {
        label: '所属云',
        type: 'text',
        key: 'cloudPlatform',
        span: 8,
      },
      {
        label: '资源池',
        type: 'text',
        key: 'resourcePoolName',
        span: 8,
      },
      {
        label: '工单编号',
        type: 'text',
        key: 'orderCode',
        span: 8,
      },
      {
        label: '项目名称',
        type: 'text',
        key: 'projectName',
        span: 8,
      },
      {
        label: '安全组',
        type: 'text',
        key: 'securityGroupName',
        span: 8,
      },
      {
        label: '虚拟网卡',
        type: 'text',
        key: 'vnicName',
        span: 8,
      },
      {
        label: '开通时间',
        type: 'text',
        key: 'createTime',
        span: 8,
      },
      {
        label: '到期时间',
        type: 'text',
        key: 'expireTime',
        span: 8,
      },
      {
        label: '计费号',
        type: 'text',
        key: 'billId',
        span: 8,
      },
      {
        label: 'UUID',
        type: 'text',
        key: 'instanceUuid',
        span: 8,
      },
      {
        label: '状态',
        type: 'text',
        key: 'deviceStatusCn',
        span: 8,
      },
      {
        label: '回收状态',
        type: 'text',
        key: 'recoveryStatusCn',
        span: 8,
      },
      {
        label: '变更状态',
        type: 'text',
        key: 'changeStatusCn',
        span: 8,
      },
      {
        label: '申请人',
        type: 'text',
        key: 'applyUserName',
        span: 8,
      },
    ],
  },
])

const fetchResouceDetail = async () => {
  const res = await getResourceDetail({
    id: resourceId.value,
  })
  if (res && res.entity) {
    for (const key in detailData) {
      detailData[key] = res.entity[key]
    }
  }
}

const associatedResourceData = reactive<any>({
  evs: [],
  vnic: [],
  eip: [],
  sg: [],
})

const fetchEvsList = async () => {
  const res = await getResourceList({
    type: 'evs',
    vmId: deviceId.value,
    pageNum: 1,
    pageSize: 9999,
  })
  if (res && res.entity) {
    associatedResourceData.evs = res.entity.records
    associatedResourceData.evs.forEach((item: any) => {
      item.nameStr = `${item.deviceName || '暂未命名'} | 数据盘 | ${item.dataDisk}`
    })
    associatedResourceData.evs.unshift({
      id: 999999,
      nameStr: `系统盘 | ${detailData.sysDisk || ''}`,
    })
  }
}

// 网卡列表
const fetchVnicList = async () => {
  const res = await getVirtualNicList({
    type: 'virtualNic',
    vmId: deviceId.value,
    pageNum: 1,
    pageSize: 9999,
  })
  if (res && res.entity) {
    associatedResourceData.vnic = res.entity.records
    associatedResourceData.vnic.forEach((item: any) => {
      item.nameStr = `${item.vnicName} | ${item.vpcName} | ${item.subnetName} | ${item.ipAddress}`
    })
  }
}

// 弹性公网列表
const fetchEipList = async () => {
  const res = await getResourceList({
    type: 'eip',
    vmId: deviceId.value,
    pageNum: 1,
    pageSize: 9999,
  })
  if (res && res.entity) {
    associatedResourceData.eip = res.entity.records
    associatedResourceData.eip.forEach((item: any) => {
      item.nameStr = `${item.deviceName} | ${item.deviceId} | ${item.eip}`
    })
  }
}

// 安全组列表
const fetchSgList = async () => {
  const res = await getSecurityGroupList({
    type: 'sg',
    securityGroupIds: detailData.securityGroupIds,
    pageNum: 1,
    pageSize: 9999,
  })
  if (res && res.entity) {
    associatedResourceData.sg = res.entity.records
    associatedResourceData.sg.forEach((item: any) => {
      item.nameStr = `${item.name} | ${item.regionName} | ${item.vpcName} | ${item.systemName}`
    })
  }
}

// 子网弹窗相关
const subnetDialogVisible = ref(false)
const currentSubnetList = ref<any[]>([])
// 查看子网信息
const handleViewSubnet = async () => {
  // 这里需要调用获取子网信息的接口
  const res = await getSubnetList({ id: resourceId.value })
  currentSubnetList.value = res.entity
  subnetDialogVisible.value = true
}

// 返回上一页
const handleGoBack = () => {
  router.push({
    path: '/mysqlList',
  })
}

// 开关机功能
const handleSwitchMachine = async () => {
  const msgStr = {
    RUNING: '确认关机吗？',
    STOPED: '确认开机吗？',
  }
  const deviceStatus = detailData.deviceStatus?.toUpperCase() || 'UNKNOWN'
  const msg = msgStr[deviceStatus as keyof typeof msgStr] || '未知状态，请确认操作'

  await ElMessageBox.confirm(msg, '信息提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      await executeResourceOperationConfirm({
        orderId: detailData.orderId,
        goodsOrderId: detailData.goodsOrderId,
        operationType: deviceStatus === 'RUNING' ? 'STOP' : 'START',
      })
      await fetchResouceDetail() // 刷新详情数据
    })
    .catch(() => {
      console.log(`操作取消`)
    })
}

// 重启功能
const handleRestart = async () => {
  if (detailData.deviceStatus != 'RUNING') {
    return ElMessage.warning('当前云数据库状态为非运行中，无法重启')
  }

  await ElMessageBox.confirm('确认重启吗？', '信息提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      await executeResourceOperationConfirm({
        orderId: detailData.orderId,
        goodsOrderId: detailData.goodsOrderId,
        operationType: 'RESTART',
      })
      await fetchResouceDetail() // 刷新详情数据
    })
    .catch(() => {
      console.log(`操作取消`)
    })
}

// 修改密码功能
const updatePasswordDialogVisible = ref(false)
const updatePasswordFormModel = reactive<ResourceOperationModel>(useResourceOperationFormModel())
const updatePasswordFormOptions = reactive(useUpdatePasswordFormOptions(updatePasswordFormModel))

async function handleUpdatePassword() {
  updatePasswordDialogVisible.value = true
  updatePasswordFormModel.orderId = detailData.orderId
  updatePasswordFormModel.goodsOrderId = detailData.goodsOrderId
  updatePasswordFormModel.deviceStatus = detailData.deviceStatus
}

const handleUpdatePasswordClose = () => {
  updatePasswordDialogVisible.value = false
  useResourceOperationFormClear(updatePasswordFormModel)
}

const updatePasswordFormRef = ref()
const handleUpdatePasswordConfirm = async () => {
  if (!(await updatePasswordFormRef.value?.validate(() => true))) return
  await executeResourceOperationConfirm({ ...updatePasswordFormModel, operationType: 'RESETPWD' })
  handleUpdatePasswordClose()
  await fetchResouceDetail() // 刷新详情数据
}

// 安全组相关功能
const securityGroupDialogVisible = ref(false)
const securityGroupListRef = ref()
const securityGroupQueryParams = ref<Record<string, any>>({ type: 'securityGroup' })

async function handleBindSecurityGroups() {
  if (detailData.securityGroupName) {
    // 如果已经绑定了安全组，则提示是否解绑
    ElMessageBox.confirm(`确定要解绑安全组 "${detailData.securityGroupName}" 吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(async () => {
        try {
          const res = await securityGroupOperate({
            operateType: 'UNBIND_SG',
            regionCode: detailData.resourcePoolCode,
            businessSystemId: detailData.businessSysId,
            vmId: detailData.deviceId,
            securityGroupIds: detailData.securityGroupIds,
          })
          if (res.code == 200) {
            ElMessage.success('已提交解绑安全组请求')
            await fetchResouceDetail() // 刷新详情数据
          }
        } catch (error) {
          console.error(error)
        }
      })
      .catch(() => {
        // 用户取消操作
      })
  } else {
    // 打开安全组选择弹窗，并根据vpcName进行过滤
    // 更新安全组查询参数，添加vpcName过滤条件
    securityGroupQueryParams.value = {
      type: 'securityGroup',
      vpcName: detailData.vpcName,
    }
    securityGroupDialogVisible.value = true
  }
}

async function handleSecurityGroupSelected(row: any) {
  if (!row.id) {
    ElMessage.warning('请选择要绑定的安全组')
    return
  }

  try {
    const res = await securityGroupOperate({
      operateType: 'BIND_SG',
      regionCode: detailData.resourcePoolCode,
      businessSystemId: detailData.businessSysId,
      vmId: detailData.deviceId,
      securityGroupIds: row.id,
    })
    if (res.code == 200) {
      ElMessage.success('已提交绑定安全组请求')
      securityGroupDialogVisible.value = false
      await fetchSgList() // 刷新详情数据
    }
  } catch (error) {
    console.error(error)
  }
}

// 虚拟网卡相关功能
const virtualNicDialogVisible = ref(false)
const virtualNicListRef = ref()
const virtualNicQueryParams = ref<Record<string, any>>({ type: 'virtualNic' })
const virtualNicSafeLock = ref(false)

async function handleBindVirtualNic(operateType: string) {
  virtualNicDialogVisible.value = true
  virtualNicQueryParams.value = {
    type: 'virtualNic',
    vnicId: detailData.vnicId,
    vmId: operateType == 'UNBIND' ? detailData.deviceId : undefined,
    vpcId: detailData.vpcId,
    operateType,
  }
}

async function handleVirtualNicSelected(row: any, operateType: string) {
  if (!row.id) {
    ElMessage.warning('请选择要操作的虚拟网卡')
    return
  }
  if (virtualNicSafeLock.value) return
  try {
    virtualNicSafeLock.value = true
    const res = await virtualNicOperate({
      operateType,
      deviceId: detailData.deviceId,
      vnicOrderId: row.id,
    })
    if (res.code == 200) {
      ElMessage.success(`已提交${operateType == 'BIND' ? '绑定' : '解绑'}虚拟网卡请求`)
      virtualNicDialogVisible.value = false
      await fetchVnicList() // 刷新详情数据
    }
  } catch (error) {
    console.error(error)
  } finally {
    virtualNicSafeLock.value = false
  }
}

// 处理下拉菜单命令
const handleCommand = (command: string) => {
  switch (command) {
    case 'resetPwd':
      handleUpdatePassword()
      break
    case 'bindOrUnbindSecurityGroup':
      handleBindSecurityGroups()
      break
    case 'bindVirtualNic':
      handleBindVirtualNic('BIND')
      break
    case 'unbindVirtualNic':
      handleBindVirtualNic('UNBIND')
      break
  }
}

// 初始化
onMounted(async () => {
  if (resourceId.value && deviceId.value) {
    await fetchResouceDetail()
    fetchEvsList()
    fetchVnicList()
    fetchEipList()
    if (detailData.securityGroupIds) {
      fetchSgList()
    }
  }
})
</script>

<style lang="scss" scoped>
.table-box {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.scroll-view {
  flex: 1;
  height: calc(100vh - 140px);
  overflow-x: hidden;
}

.sl-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 0;
  margin: 8px;
}

.tab-content {
  margin-top: 16px;
}

.operation-buttons {
  display: flex;
  gap: 8px;
}

.warning {
  width: 90%;
  margin: 0 auto;
  margin-top: -10px;
  word-break: break-all;
  color: red;
}

:deep(.el-form-item--default) {
  margin-bottom: 0;
}

.operation-buttons {
  background-color: white;
  margin: 8px 8px -8px 8px;
  padding: 8px;
  display: flex;
  justify-content: flex-end;
  border-bottom: 1px solid #e0e0e0;
}

.tab-content {
  background-color: white;
  margin: -8px 8px 8px 8px;
  padding: 8px 24px;
  :deep(.el-tabs__item) {
    min-width: 80px;
  }
}

.resource-item {
  height: 30px;
  padding: 0 8px;
  margin-bottom: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #3d3d3d;
  background-color: rgba(216, 216, 216, 0.6);
  opacity: 0.65;
  border-radius: 8px;
  font-size: 14px;
}
.el-button + .el-button {
  margin-left: 0;
}
</style>
