<template>
  <div id="security-group-create" class="table-box">
    <sl-page-header
      :title="pageTitle"
      :icon="{
        Vnode: Platform,
        color: '#0052D9',
        size: '20px',
      }"
      :show-back="true"
      :back="{
        title: '返回列表',
        function: goBack,
      }"
    >
    </sl-page-header>
    <el-scrollbar wrap-class="security-group-scroll-view" class="scroll-view">
      <!-- 基础信息 -->
      <div class="sl-card">
        <sl-block-title>基本信息</sl-block-title>
        <sl-form
          ref="formRef"
          v-model="formModel"
          :options="formOptions"
          label-width="120px"
          :disabled="isEditMode"
        ></sl-form>
      </div>

      <!-- 访问规则 -->
      <div class="sl-card mt-20">
        <sl-block-title>访问规则</sl-block-title>
        <div class="rule-header">
          <div class="rule-actions">
            <el-button type="primary" @click="addRule">
              <el-icon class="el-icon--left"><Plus /></el-icon>添加规则
            </el-button>
            <el-button v-if="ruleList.length > 0 && !isEditMode" type="danger" @click="clearRules">
              <el-icon class="el-icon--left"><Delete /></el-icon>清空规则
            </el-button>
          </div>
          <div class="rule-tips" v-if="ruleList.length > 0">
            <el-tag type="info">已添加 {{ ruleList.length }} 条规则</el-tag>
          </div>
        </div>
        <el-table
          :data="ruleList"
          border
          style="width: 100%"
          row-class-name="rule-table-row"
          header-row-class-name="rule-table-header"
        >
          <el-table-column type="index" label="序号" width="60" align="center" />
          <el-table-column prop="direction" label="方向" width="100">
            <template #default="{ row }">
              <span v-if="isEditMode">{{ row.directionText }}</span>
              <el-select
                v-else
                v-model="row.directionText"
                placeholder="请选择方向"
                style="width: 100%"
                required
              >
                <el-option label="出方向" value="出方向"></el-option>
                <el-option label="入方向" value="入方向"></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="accessStatus" label="授权策略" width="100">
            <template #default="{ row }">
              <span v-if="isEditMode">{{ row.policyText }}</span>
              <el-select
                v-else
                v-model="row.policyText"
                placeholder="请选择策略"
                style="width: 100%"
                required
              >
                <el-option label="允许" value="允许"></el-option>
                <el-option label="拒绝" value="拒绝"></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="priority" label="优先级" width="100">
            <template #default="{ row }">
              <span v-if="isEditMode">{{ row.priority }}</span>
              <el-input
                v-else
                v-model="row.priority"
                type="number"
                :min="1"
                placeholder="请输入优先级"
                required
              />
            </template>
          </el-table-column>
          <el-table-column prop="protocol" label="协议类型" width="120">
            <template #default="{ row }">
              <span v-if="isEditMode">{{ row.protocol }}</span>
              <el-select
                v-else
                v-model="row.protocol"
                placeholder="请选择协议"
                style="width: 100%"
                required
              >
                <el-option label="TCP" value="TCP"></el-option>
                <el-option label="UDP" value="UDP"></el-option>
                <el-option label="ICMP" value="ICMP"></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="portRange" label="端口范围" min-width="150">
            <template #default="{ row }">
              <span v-if="isEditMode">{{ row.portRange }}</span>
              <el-input
                v-else
                v-model="row.portRange"
                placeholder="请输入端口范围，多个用逗号分割"
                :disabled="row.protocol === 'ICMP'"
                required
              >
                <template #append>
                  <el-tooltip
                    content="示例：8080,90,8080-9090"
                    placement="top"
                    effect="dark"
                    :show-after="0"
                    popper-class="custom-tooltip"
                  >
                    <el-icon class="example-icon"><QuestionFilled /></el-icon>
                  </el-tooltip>
                </template>
              </el-input>
            </template>
          </el-table-column>
          <el-table-column prop="accreditIp" label="授权对象" min-width="150">
            <template #default="{ row }">
              <span v-if="isEditMode">{{ row.accreditIp }}</span>
              <el-input
                v-else
                v-model="row.accreditIp"
                placeholder="请输入CIDR，多个用逗号分割"
                required
              >
                <template #append>
                  <el-tooltip
                    content="示例：***********/24,********,********"
                    placement="top"
                    effect="dark"
                    :show-after="0"
                    popper-class="custom-tooltip"
                  >
                    <el-icon class="example-icon"><QuestionFilled /></el-icon>
                  </el-tooltip>
                </template>
              </el-input>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150" fixed="right">
            <template #default="{ $index, row }">
              <div v-if="isEditMode">
                <el-button type="primary" link @click="editRule(row, $index)">
                  <el-icon><Edit /></el-icon>编辑
                </el-button>
                <el-button type="primary" link @click="deleteRule($index)">
                  <el-icon><Delete /></el-icon>删除
                </el-button>
              </div>
              <el-button v-else type="primary" link @click="deleteRule($index)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-scrollbar>

    <!-- 底部操作按钮 -->
    <div class="footer">
      <div class="button-group">
        <el-button v-if="isEditMode" @click="goBack">返回</el-button>
        <template v-else>
          <el-button @click="goBack">取消</el-button>
          <el-button type="primary" :loading="loading" @click="submit">提交</el-button>
        </template>
      </div>
    </div>

    <!-- 规则编辑对话框 -->
    <el-dialog v-model="ruleDialogVisible" :title="ruleDialogTitle" width="650px" destroy-on-close>
      <el-form label-position="right" label-width="100px" v-if="currentRule">
        <el-form-item label="方向" required>
          <el-select v-model="currentRule.directionText" style="width: 100%">
            <el-option label="出方向" value="出方向"></el-option>
            <el-option label="入方向" value="入方向"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="授权策略" required>
          <el-select v-model="currentRule.policyText" style="width: 100%">
            <el-option label="允许" value="允许"></el-option>
            <el-option label="拒绝" value="拒绝"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="优先级" required>
          <el-input
            v-model="currentRule.priority"
            type="number"
            :min="1"
            placeholder="请输入优先级"
          />
        </el-form-item>
        <el-form-item label="协议类型" required>
          <el-select v-model="currentRule.protocol" style="width: 100%">
            <el-option label="TCP" value="TCP"></el-option>
            <el-option label="UDP" value="UDP"></el-option>
            <el-option label="ICMP" value="ICMP"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="端口范围" required>
          <el-input
            v-model="currentRule.portRange"
            placeholder="请输入端口范围，多个用逗号分割"
          ></el-input>
        </el-form-item>
        <el-form-item label="授权对象" required>
          <el-input
            v-model="currentRule.accreditIp"
            placeholder="请输入CIDR，多个用逗号分割"
          ></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="ruleDialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="loading" @click="submitRule">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { Platform, Plus, Delete, Edit, QuestionFilled } from '@element-plus/icons-vue'
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import SlPageHeader from '@/components/SlPageHeader/index.vue'
import SlBlockTitle from '@/components/base/SlBlockTitle.vue'
import SlForm from '@/components/form/SlForm.vue'
import {
  getCloudTypeDic,
  getCloudPlatformDic,
  getResourcePoolsDic,
  getAzListDic,
} from '@/api/modules/dic'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  createSecurityGroup,
  vpcList,
  getSecurityGroupDetail,
  addSecurityGroupRule,
  updateSecurityGroupRule,
  deleteSecurityGroupRule,
} from '@/api/modules/resourecenter'

// 定义规则类型接口
interface SecurityGroupRule {
  ruleId?: string
  direction: string
  accessStatus: number
  priority: number
  protocol: string
  portRange: string
  accreditIp: string
  directionText: string
  policyText: string
  securityGroupId?: string
  index?: number
  [key: string]: any // 添加索引签名以支持动态属性访问
}

const router = useRouter()
const route = useRoute()
const formRef = ref()
const loading = ref(false)

// 判断是创建模式还是编辑模式
const isEditMode = computed(() => route.query.id !== undefined)
const securityGroupId = computed(() => (route.query.id as string) || '')

// 判断是否为对公安全组创建
const isPublicCreate = computed(() => route.query.sourceType === 'DG')

// 标题文本
const pageTitle = computed(() => {
  if (isEditMode.value) {
    return '安全组详情'
  }
  return isPublicCreate.value ? '创建对公安全组' : '创建安全组'
})

// 表单数据
const formModel = reactive({
  name: '', // 安全组名称
  domainCode: '', // 云平台
  regionCode: '', // 资源池code
  regionName: '', // 资源池名称
  azCode: '', // 可用区编码
  vpcId: '', // VPC ID
  description: '', // 描述
  // 以下为级联选择需要的中间字段，不会提交到接口
  catalogueDomainCode: '',
  catalogueDomainName: '',
  domainName: '',
  resourcePoolId: '',
  azName: '',
  vpcName: '',
})

// 访问规则列表
const ruleList = ref<SecurityGroupRule[]>([])

// 当前正在编辑的规则
const currentRule = ref<SecurityGroupRule | null>(null)

// 规则编辑对话框
const ruleDialogVisible = ref(false)
const ruleDialogMode = ref<'add' | 'edit'>('add')
const ruleDialogTitle = computed(() => (ruleDialogMode.value === 'add' ? '添加规则' : '编辑规则'))

// 字典数据
const cloudTypeOptions = ref<any[]>([])
const cloudPlatformOptions = ref<any[]>([])
const resourcePoolOptions = ref<any[]>([])
const azOptions = ref<any[]>([])
const vpcOptions = ref<any[]>([])
// 表单配置
const formOptions = reactive([
  {
    groupItems: [
      {
        label: '安全组名称',
        type: 'input',
        key: 'name',
        span: 8,
        props: {
          disabled: isEditMode.value,
        },
        rules: [{ required: true, message: '请输入安全组名称', trigger: 'blur' }],
      },
      {
        label: '云类型',
        type: 'select',
        key: 'catalogueDomainCode',
        span: 8,
        options: cloudTypeOptions,
        labelField: 'name',
        valueField: 'code',
        rules: [{ required: true, message: '请选择云类型', trigger: 'change' }],
        onChange: function () {
          formModel.catalogueDomainName = ''
          if (formModel.catalogueDomainCode) {
            const obj = cloudTypeOptions.value.find(
              (item: any) => item.code === formModel.catalogueDomainCode,
            )
            formModel.catalogueDomainName = obj?.name
          }
          formModel.domainCode = ''
          formModel.domainName = ''
          formModel.resourcePoolId = ''
          formModel.regionName = ''
          formModel.regionCode = ''
          formModel.azCode = ''
          formModel.azName = ''
          formModel.vpcId = ''
          formModel.vpcName = ''
          cloudPlatformOptions.value = []
          resourcePoolOptions.value = []
          azOptions.value = []
          vpcOptions.value = []
          if (formModel.catalogueDomainCode) {
            getCloudPlatformList()
          }
        },
        props: {
          select: {
            filterable: true,
            clearable: true,
            disabled: isEditMode.value || isPublicCreate.value, // 对公安全组创建时禁用编辑
          },
        },
      },
      {
        label: '云平台',
        type: 'select',
        key: 'domainCode',
        span: 8,
        options: cloudPlatformOptions,
        labelField: 'name',
        valueField: 'code',
        rules: [{ required: true, message: '请选择云平台', trigger: 'change' }],
        onChange: function () {
          formModel.domainName = ''
          if (formModel.domainCode) {
            const obj = cloudPlatformOptions.value.find(
              (item: any) => item.code === formModel.domainCode,
            )
            formModel.domainName = obj?.name
          }
          formModel.resourcePoolId = ''
          formModel.regionName = ''
          formModel.regionCode = ''
          formModel.azCode = ''
          formModel.azName = ''
          formModel.vpcId = ''
          formModel.vpcName = ''
          resourcePoolOptions.value = []
          azOptions.value = []
          vpcOptions.value = []
          if (formModel.domainCode) {
            getResourcePools()
          }
        },
        props: {
          select: {
            filterable: true,
            clearable: true,
            disabled: isEditMode.value,
          },
        },
      },
      {
        label: '资源池',
        type: 'select',
        key: 'resourcePoolId',
        span: 8,
        options: resourcePoolOptions,
        labelField: 'name',
        valueField: 'id',
        rules: [{ required: true, message: '请选择资源池', trigger: 'change' }],
        onChange: function () {
          formModel.regionName = ''
          formModel.regionCode = ''
          if (formModel.resourcePoolId) {
            const obj = resourcePoolOptions.value.find(
              (item: any) => item.id === formModel.resourcePoolId,
            )
            if (obj) {
              formModel.regionName = obj.name
              formModel.regionCode = obj.code // 保存资源池code
            }
          }
          formModel.azCode = ''
          formModel.azName = ''
          formModel.vpcId = ''
          formModel.vpcName = ''
          azOptions.value = []
          vpcOptions.value = []
          if (formModel.resourcePoolId) {
            getAzList()
          }
        },
        props: {
          select: {
            filterable: true,
            clearable: true,
            disabled: isEditMode.value,
          },
        },
      },
      {
        label: '可用区',
        type: 'select',
        key: 'azCode',
        span: 8,
        options: azOptions,
        labelField: 'name',
        valueField: 'code',
        rules: [{ required: true, message: '请选择可用区', trigger: 'change' }],
        onChange: function () {
          formModel.azName = ''
          if (formModel.azCode) {
            const obj = azOptions.value.find((item: any) => item.code === formModel.azCode)
            formModel.azName = obj?.name
          }
          formModel.vpcId = ''
          formModel.vpcName = ''
          vpcOptions.value = []
          if (formModel.azCode && formModel.regionCode) {
            getVpcList()
          }
        },
        props: {
          select: {
            filterable: true,
            clearable: true,
            disabled: isEditMode.value,
          },
        },
      },
      {
        label: 'VPC',
        type: 'select',
        key: 'vpcId',
        span: 8,
        options: vpcOptions,
        labelField: 'vpcName',
        valueField: 'id',
        rules: [{ required: true, message: '请选择VPC', trigger: 'change' }],
        onChange: function () {
          formModel.vpcName = ''
          if (formModel.vpcId) {
            const obj = vpcOptions.value.find((item: any) => item.id === formModel.vpcId)
            formModel.vpcName = obj?.name
          }
        },
        props: {
          select: {
            filterable: true,
            clearable: true,
            disabled: isEditMode.value,
          },
        },
      },
      {
        label: '描述',
        type: 'input',
        key: 'description',
        span: 16,
        props: {
          type: 'textarea',
          rows: 4,
          maxlength: 200,
          showWordLimit: true,
          disabled: isEditMode.value,
        },
      },
    ],
  },
])

// 初始化数据
onMounted(async () => {
  // 获取云类型数据
  await getCloudTypeList()

  // 如果是对公安全组创建，设置默认云类型
  if (isPublicCreate.value && !isEditMode.value) {
    formModel.catalogueDomainCode = 'cloudst_group_moc'
    // 查找对应的云类型名称
    const cloudType = cloudTypeOptions.value.find((item) => item.code === 'cloudst_group_moc')
    if (cloudType) {
      formModel.catalogueDomainName = cloudType.name
      // 自动加载云平台列表
      await getCloudPlatformList()
    }
  }

  // 如果是编辑模式，加载安全组详情
  if (isEditMode.value) {
    await loadSecurityGroupDetail()
  } else {
    // 默认添加一条访问规则
    addRule()
  }
})

// 获取云类型列表
const getCloudTypeList = async () => {
  try {
    const { entity } = await getCloudTypeDic(null)
    cloudTypeOptions.value = entity || []
  } catch (error) {
    console.error('获取云类型失败', error)
  }
}

// 获取云平台列表
const getCloudPlatformList = async () => {
  try {
    const { entity } = await getCloudPlatformDic({
      parentCode: formModel.catalogueDomainCode,
    })
    cloudPlatformOptions.value = entity || []
  } catch (error) {
    console.error('获取云平台失败', error)
  }
}

// 获取资源池列表
const getResourcePools = async () => {
  try {
    const { entity } = await getResourcePoolsDic({
      domainCode: formModel.domainCode,
      realmType: isPublicCreate.value ? 'iaas' : '',
    })
    resourcePoolOptions.value = entity || []
  } catch (error) {
    console.error('获取资源池失败', error)
  }
}

// 获取可用区列表，使用dic中的getAzListDic方法，入参为resourcePoolId
const getAzList = async () => {
  try {
    const { entity } = await getAzListDic({
      regionId: formModel.resourcePoolId,
    })
    azOptions.value = entity || []
  } catch (error) {
    console.error('获取可用区失败', error)
  }
}

// 获取VPC列表
const getVpcList = async () => {
  try {
    const { entity } = await vpcList({
      pageNum: 1,
      pageSize: 9999,
      resourcePoolId: formModel.resourcePoolId,
      azCode: formModel.azCode,
    })
    vpcOptions.value = entity.records || []
  } catch (error) {
    console.error('获取VPC失败', error)
  }
}

// 加载安全组详情
const loadSecurityGroupDetail = async () => {
  try {
    loading.value = true
    const res = await getSecurityGroupDetail({ id: securityGroupId.value })

    if (res.code === 200 && res.entity) {
      const detail = res.entity

      // 设置基本信息
      formModel.name = detail.name || ''
      formModel.catalogueDomainCode = detail.catalogueDomainCode || ''
      formModel.domainCode = detail.domainCode || ''
      formModel.regionCode = detail.regionCode || ''
      formModel.regionName = detail.regionName || ''
      formModel.azCode = detail.azCode || ''
      formModel.vpcId = detail.vpcId || ''
      formModel.description = detail.description || ''
      formModel.resourcePoolId = detail.resourcePoolId || ''

      // 加载规则列表
      if (detail.ruleList && Array.isArray(detail.ruleList)) {
        ruleList.value = detail.ruleList.map((rule: any) => {
          const mappedRule = {
            ...rule,
            // 确保字段名称一致
            ruleId: rule.ruleId, // 只使用ruleId
            accreditIp: rule.accreditIp,
            // 添加UI显示需要的字段
            directionText: rule.direction === 'ingress' ? '入方向' : '出方向',
            policyText: rule.accessStatus === 1 ? '允许' : '拒绝',
          }
          return mappedRule
        })
      }

      // 加载资源池、可用区等关联数据
      if (detail.domainCode) {
        await getCloudPlatformList()
        if (detail.resourcePoolId) {
          await getResourcePools()
          await getAzList()
          if (detail.azCode && detail.vpcId) {
            await getVpcList()
          }
        }
      }
    } else {
      ElMessage.error(res.message || '获取安全组详情失败')
    }
  } catch (error) {
    console.error('获取安全组详情失败', error)
    ElMessage.error('获取安全组详情失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 添加规则（仅在创建模式下直接添加到表格）
const addRule = () => {
  if (isEditMode.value) {
    // 编辑模式下，打开添加规则对话框
    openAddRuleDialog()
  } else {
    // 创建模式下，直接添加到表格
    ruleList.value.push({
      direction: 'ingress', // 入方向 ingress, 出方向 egress
      accessStatus: 1, // 授权策略：拒绝-0/允许-1
      priority: 1, // 优先级
      protocol: 'TCP', // 协议类型
      portRange: '', // 默认常用端口
      accreditIp: '', // 默认所有IP
      // 用于UI展示的字段
      directionText: '入方向',
      policyText: '允许',
    })
  }
}

// 打开添加规则对话框
const openAddRuleDialog = () => {
  currentRule.value = {
    direction: 'ingress',
    accessStatus: 1,
    priority: 1,
    protocol: 'TCP',
    portRange: '',
    accreditIp: '',
    directionText: '入方向',
    policyText: '允许',
    securityGroupId: securityGroupId.value,
  }
  ruleDialogMode.value = 'add'
  ruleDialogVisible.value = true
}

// 打开编辑规则对话框
const editRule = (rule: SecurityGroupRule, index: number) => {
  currentRule.value = { ...rule, index }
  ruleDialogMode.value = 'edit'
  ruleDialogVisible.value = true
}

// 提交规则（添加或编辑）
const submitRule = async () => {
  if (!currentRule.value) return

  try {
    // 验证规则字段
    const rule = currentRule.value

    // 使用验证函数来检查字段
    const checkField = (field: keyof SecurityGroupRule, fieldName: string, minValue?: number) => {
      if (!rule[field]) {
        ElMessage.warning(`${fieldName}不能为空`)
        return false
      }

      if (minValue !== undefined && typeof rule[field] === 'number' && rule[field] < minValue) {
        ElMessage.warning(`${fieldName}不能小于${minValue}`)
        return false
      }

      return true
    }

    // 验证各个字段
    if (!checkField('directionText', '方向')) return
    if (!checkField('policyText', '授权策略')) return
    if (!checkField('priority', '优先级', 1)) return
    if (!checkField('protocol', '协议类型')) return
    if (!checkField('portRange', '端口范围')) return
    if (!checkField('accreditIp', '授权对象')) return

    // 准备提交数据
    const ruleData = { ...rule }

    // 将UI显示的中文转换为API需要的格式
    if (ruleData.directionText === '入方向') {
      ruleData.direction = 'ingress'
    } else if (ruleData.directionText === '出方向') {
      ruleData.direction = 'egress'
    }

    if (ruleData.policyText === '允许') {
      ruleData.accessStatus = 1
    } else if (ruleData.policyText === '拒绝') {
      ruleData.accessStatus = 0
    }

    // 确保priority是整数
    ruleData.priority = parseInt(String(ruleData.priority))

    loading.value = true
    let res

    // 根据接口需要的格式构造请求对象
    const securityGroupRule: any = {
      direction: ruleData.direction,
      accessStatus: ruleData.accessStatus,
      priority: ruleData.priority,
      protocol: ruleData.protocol,
      portRange: ruleData.portRange,
      accreditIp: ruleData.accreditIp, // 使用accreditIp作为字段名
    }

    if (ruleDialogMode.value === 'add') {
      // 添加规则
      const requestData = {
        id: securityGroupId.value,
        securityGroupRules: [securityGroupRule],
      }
      res = await addSecurityGroupRule(requestData)
    } else {
      // 编辑规则 - 需要添加ruleId
      securityGroupRule.ruleId = ruleData.ruleId
      const requestData = {
        id: securityGroupId.value,
        securityGroupRules: [securityGroupRule],
      }
      res = await updateSecurityGroupRule(requestData)
    }

    if (res.code === 200) {
      ElMessage.success(ruleDialogMode.value === 'add' ? '添加规则成功' : '编辑规则成功')
      ruleDialogVisible.value = false
      // 重新加载安全组详情
      await loadSecurityGroupDetail()
    } else {
      ElMessage.error(
        res.message || (ruleDialogMode.value === 'add' ? '添加规则失败' : '编辑规则失败'),
      )
    }
  } catch (error) {
    console.error(ruleDialogMode.value === 'add' ? '添加规则失败' : '编辑规则失败', error)
    ElMessage.error(
      ruleDialogMode.value === 'add' ? '添加规则失败，请稍后重试' : '编辑规则失败，请稍后重试',
    )
  } finally {
    loading.value = false
  }
}

// 删除规则
const deleteRule = (index: number) => {
  if (isEditMode.value) {
    // 编辑模式下需要确认并调用接口
    const rule = ruleList.value[index]
    const ruleId = rule.ruleId
    if (!ruleId) {
      ElMessage.warning('无法删除该规则，找不到规则ID')
      return
    }

    ElMessageBox.confirm('确定要删除该规则吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(async () => {
        try {
          loading.value = true
          // 构造符合接口要求的请求数据
          const requestData = {
            id: securityGroupId.value,
            ruleIds: [ruleId as string], // 确保类型为string
          }
          const res = await deleteSecurityGroupRule(requestData)

          if (res.code === 200) {
            ElMessage.success('删除规则成功')
            // 重新加载安全组详情
            await loadSecurityGroupDetail()
          } else {
            ElMessage.error(res.message || '删除规则失败')
          }
        } catch (error) {
          console.error('删除规则失败', error)
          ElMessage.error('删除规则失败，请稍后重试')
        } finally {
          loading.value = false
        }
      })
      .catch(() => {
        // 用户取消操作，不执行任何动作
      })
  } else {
    // 创建模式下直接从列表中删除
    ruleList.value.splice(index, 1)
  }
}

// 返回列表页
const goBack = () => {
  if (!isEditMode.value && (ruleList.value.length > 0 || formModel.name)) {
    ElMessageBox.confirm('确定要离开吗？未保存的数据将会丢失', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(() => {
        router.push('securityGroupList')
      })
      .catch(() => {
        // 用户取消操作，不执行任何动作
      })
  } else {
    router.push('securityGroupList')
  }
}

// 表单提交前处理数据
const prepareFormDataAndValidate = () => {
  // 验证表单
  if (!formModel.name) {
    ElMessage.warning('安全组名称不能为空')
    return false
  }

  if (!formModel.vpcId) {
    ElMessage.warning('VPC不能为空')
    return false
  }

  // 检查是否有规则
  if (ruleList.value.length === 0) {
    ElMessage.warning('请至少添加一条访问规则')
    return false
  }

  // 验证access rule字段
  for (let i = 0; i < ruleList.value.length; i++) {
    const rule = ruleList.value[i]
    const ruleIndex = i + 1

    // 使用一个验证函数来简化代码
    const checkField = (field: string, fieldName: string, minValue?: number) => {
      if (!rule[field]) {
        ElMessage.warning(`第${ruleIndex}条规则的${fieldName}不能为空`)
        return false
      }

      if (minValue !== undefined && typeof rule[field] === 'number' && rule[field] < minValue) {
        ElMessage.warning(`第${ruleIndex}条规则的${fieldName}不能小于${minValue}`)
        return false
      }

      return true
    }

    // 验证各个字段
    if (!checkField('directionText', '方向')) return false
    if (!checkField('policyText', '授权策略')) return false
    if (!checkField('priority', '优先级', 1)) return false
    if (!checkField('protocol', '协议类型')) return false
    if (!checkField('portRange', '端口范围')) return false
    if (!checkField('accreditIp', '授权对象')) return false
  }

  // 构造提交数据
  const submitData: any = {
    name: formModel.name,
    catalogueDomainCode: formModel.catalogueDomainCode || '',
    domainCode: formModel.domainCode || '',
    resourcePoolId: formModel.resourcePoolId || '',
    regionCode: formModel.regionCode || '',
    regionName: formModel.regionName || '',
    azCode: formModel.azCode || '',
    vpcId: formModel.vpcId || '',
    description: formModel.description,
    ruleList: mapRuleListForSubmit(ruleList.value),
  }

  // 如果是对公安全组创建，添加sourceType字段
  if (isPublicCreate.value) {
    submitData.sourceType = 'DG'
  }

  return submitData
}

// 映射规则列表以符合API要求的格式
const mapRuleListForSubmit = (rules: SecurityGroupRule[]) => {
  return rules.map((rule) => {
    // 将UI显示的中文方向转换为接口需要的英文
    if (rule.directionText === '入方向') {
      rule.direction = 'ingress'
    } else if (rule.directionText === '出方向') {
      rule.direction = 'egress'
    }

    // 将UI显示的中文授权策略转换为接口需要的数字
    if (rule.policyText === '允许') {
      rule.accessStatus = 1
    } else if (rule.policyText === '拒绝') {
      rule.accessStatus = 0
    }

    // 创建一个新对象，只包含需要的属性
    return {
      direction: rule.direction,
      accessStatus: rule.accessStatus,
      priority: parseInt(String(rule.priority)),
      protocol: rule.protocol,
      portRange: rule.portRange,
      accreditIp: rule.accreditIp,
    }
  })
}

// 表单提交
const submit = async () => {
  try {
    loading.value = true
    const formData = await prepareFormDataAndValidate()
    if (!formData) {
      loading.value = false
      return
    }

    const res = await createSecurityGroup(formData)

    if (res.code == 200) {
      ElMessage.success('创建安全组成功')
      router.push('securityGroupList')
      return
    }
  } catch (error) {
    console.error('创建安全组失败', error)
  } finally {
    loading.value = false
  }
}

// 清空规则
const clearRules = () => {
  ElMessageBox.confirm('确定要清空所有规则吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      ruleList.value = []
      ElMessage.success('已清空所有规则')
    })
    .catch(() => {
      // 用户取消操作，不执行任何动作
    })
}
</script>

<style scoped lang="scss">
.scroll-view {
  margin-top: 2px;
}
.footer {
  height: 48px;
  padding: 8px 20px;
  background: #ffffff;
  display: flex;
  align-items: center;
  .button-group {
    display: flex;
    align-items: center;
    justify-content: right;
    flex-grow: 1;
  }
}
.add-busisystem-btn {
  display: flex;
  align-items: center;
  font-size: 16px;
  cursor: pointer;
}

.rule-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  .rule-actions {
    display: flex;
    gap: 12px;
  }

  .rule-tips {
    margin-right: 12px;
  }
}

.rule-table-header {
  background-color: #f5f7fa;
  color: #303133;
  font-weight: bold;
}

.rule-table-row:hover {
  background-color: #ecf5ff;
}

:deep(.el-table) {
  margin-bottom: 20px;

  .el-button--link {
    padding: 4px 8px;
  }
}

.el-tag {
  padding: 4px 8px;
}

.example-icon {
  color: #487fef;
  font-size: 16px;
  cursor: pointer;
  &:hover {
    color: #66b1ff;
  }
}

:deep(.custom-tooltip) {
  background-color: #487fef !important;
  padding: 8px 12px;
  font-size: 14px;
  .el-popper__arrow {
    border-right-color: #487fef !important;
  }
  .el-popper__arrow::before {
    border-right-color: #487fef !important;
  }
}
</style>
