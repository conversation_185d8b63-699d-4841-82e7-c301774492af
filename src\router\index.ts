import { createRouter, createWebHistory } from 'vue-router'
import { staticRouter, errorRouter } from '@/router/modules/staticRouter'

/**
 * @description 📚 路由参数配置简介
 * @param path ==> 路由菜单访问路径
 * @param name ==> 路由 name (对应页面组件 name, 可用作 KeepAlive 缓存标识 && 按钮权限筛选)
 * @param redirect ==> 路由重定向地址
 * @param component ==> 视图文件路径
 * @param meta ==> 路由菜单元信息
 * @param meta.icon ==> 菜单和面包屑对应的图标
 * @param meta.title ==> 路由标题 (用作 document.title || 菜单的名称)
 * @param meta.activeMenu ==> 当前路由为详情页时，需要高亮的菜单
 * @param meta.isLink ==> 路由外链时填写的访问地址
 * @param meta.isHide ==> 是否在菜单中隐藏 (通常列表详情页需要隐藏)
 * @param meta.isFull ==> 菜单是否全屏 (示例：数据大屏页面)
 * @param meta.isAffix ==> 菜单是否固定在标签页中 (首页通常是固定项)
 * @param meta.isDisabled ==> 当前菜单禁用
 * @param meta.isKeepAlive ==> 当前路由是否缓存
 * */
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [...staticRouter, ...errorRouter],
})

let authStore: any
const getAuthStore = async () => {
  if (authStore) return authStore
  const { useAuthStore } = await import('@/stores/modules/auth')
  authStore = useAuthStore()
  return authStore
}

// 在路由变化时设置当前路由名称
router.beforeEach((to, from, next) => {
  const _authStore = getAuthStore()
  _authStore.then((res) => {
    res.setRouteName(to.name as string)
    next()
  })
})

export default router
