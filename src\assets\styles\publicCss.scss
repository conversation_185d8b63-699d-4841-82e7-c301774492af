.t-r-1 {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.t-r-2 {
  word-break: break-all;
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.a-s-c {
  align-self: center;
}

.a-s-e {
  align-self: flex-end;
}

.p-r {
  position: relative;
}

.p-a {
  position: absolute;
}

.bg-content {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.bg-line {
  height: 24px;
  background-color: #f4f3f8;
}

.f-w {
  flex-wrap: wrap;
}

.f-1 {
  flex: 1;
}

.f-2 {
  flex: 2;
}
.f-nw {
  flex-wrap: nowrap !important;
}
.f-r {
  /* #ifndef APP-PLUS-NVUE */
  display: flex;
  /* #endif */
  flex-direction: row;
}
.f-d-r {
  flex-direction: row;
}

.f-r-c-b {
  /* #ifndef APP-PLUS-NVUE */
  display: flex;
  /* #endif */
  flex-direction: row;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
}

.f-d-c {
  /* #ifndef APP-PLUS-NVUE */
  display: flex;
  /* #endif */
  flex-direction: column;
}

.f-j-c {
  justify-content: center;
}

.f-j-b {
  justify-content: space-between;
}

.f-j-a {
  justify-content: space-around;
}

.f-j-e {
  justify-content: flex-end;
}

.f-a-c {
  align-items: center;
}

.f-a-e {
  align-items: flex-end;
}

.f-a-baseline {
  align-items: baseline;
}

.t-a-l {
  text-align: left;
}
.t-a-c {
  text-align: center;
}

.t-a-r {
  text-align: right;
}

.bg-f {
  background-color: #ffffff;
}

.bg-3 {
  background-color: #333;
}

.bg-theme {
  background-color: #487fef !important;
}

.b-b-1 {
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #eee;
}

.b-t-1 {
  border-top-style: solid;
  border-top-width: 2px;
  border-top-color: #eee;
}

.b-r-1 {
  border-right-style: solid;
  border-right-width: 2px;
  border-right-color: #eee;
}

.b-l-1 {
  border-left-style: solid;
  border-left-width: 2px;
  border-left-color: #eee;
}

@for $i from 10 through 60 {
  $size: $i * 1;

  .fz-#{$size} {
    font-size: $size + px;
  }
  .lh-#{$size} {
    line-height: $size + px;
  }

  .im-fz-#{$size} {
    font-size: $size + px !important;
  }
}
.fz-w {
  font-weight: bold;
}

.c-theme {
  color: #487fef;
}
.c-green-theme {
  color: #5fb14f;
}
.c-blue-theme {
  color: #5f7ece;
}

.c-black-theme {
  color: #020a1d;
}

.c-f {
  color: #fff;
}

.c-3 {
  color: #333;
}

.c-6 {
  color: #666;
}

.c-9 {
  color: #999;
}

@for $m from 1 through 50 {
  $size: $m * 2;

  .m-#{$size} {
    margin: $size + px !important;
  }
  .m-v-#{$size} {
    margin-top: $size + px !important;
    margin-bottom: $size + px !important;
  }
  .m-h-#{$size} {
    margin-left: $size + px !important;
    margin-right: $size + px !important;
  }
  .m-t-#{$size} {
    margin-top: $size + px !important;
  }

  .m-b-#{$size} {
    margin-bottom: $size + px !important;
  }

  .m-l-#{$size} {
    margin-left: $size + px !important;
  }

  .m-r-#{$size} {
    margin-right: $size + px !important;
  }
}

@for $p from 1 through 50 {
  $size: $p * 2;

  .p-#{$size} {
    padding: $size + px;
  }
  .p-v-#{$size} {
    padding-top: $size + px;
    padding-bottom: $size + px;
  }
  .p-h-#{$size} {
    padding-left: $size + px;
    padding-right: $size + px;
  }
  .p-t-#{$size} {
    padding-top: $size + px;
  }

  .p-b-#{$size} {
    padding-bottom: $size + px;
  }

  .p-l-#{$size} {
    padding-left: $size + px;
  }

  .p-r-#{$size} {
    padding-right: $size + px;
  }
}
.fz-ls-1 {
  letter-spacing: 1px;
}
.fz-ls-2 {
  letter-spacing: 2px;
}
.br-10 {
  border-radius: 10px;
}
.br-6 {
  border-radius: 6px;
}

.br-12 {
  border-radius: 12px;
}
.i-b {
  display: inline-block;
}
.c-p {
  cursor: pointer;
}
.w100 {
  width: 100% !important;
}
.w100-16 {
  width: calc(100% - 16px) !important;
}
.w50 {
  width: 50% !important;
}
.w80 {
  width: 80% !important;
}
.w85 {
  width: 85% !important;
}
.h100 {
  height: 100% !important;
}
.ha {
  height: auto !important;
}
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.25);
  border-radius: 8px !important;
}

::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.48);
}

::-webkit-scrollbar-track {
  background-color: #fafafb;
}
.bs-b {
  box-sizing: border-box;
}
