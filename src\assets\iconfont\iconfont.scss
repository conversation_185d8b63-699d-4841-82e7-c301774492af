@font-face {
  font-family: 'iconfont'; /* Project id 4777010 */
  src: url('iconfont.ttf?t=1734933747489') format('truetype');
}

.iconfont {
  font-family: 'iconfont' !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.cart:before {
  content: '\e726';
}

.networkResources:before {
  content: '\e6c0';
}

.messageManagement:before {
  content: '\e758';
}

.schedulingConfiguration:before {
  content: '\e612';
}

.logManagement:before {
  content: '\e645';
}

.workOrderApproval:before {
  content: '\e7d1';
}

.apiGetway:before {
  content: '\e807';
}

.schedulingPolicy:before {
  content: '\e66b';
}

.regulatoryApproval:before {
  content: '\e604';
}

.basicConfiguration:before {
  content: '\e6f4';
}

.userCenter:before {
  content: '\e79e';
}

.productAutomaticOrchestration:before {
  content: '\e770';
}

.containerResources:before {
  content: '\e613';
}

.centralizedDispatcher:before {
  content: '\e7b6';
}

.middlewareResources:before {
  content: '\e611';
}

.costAnalysis:before {
  content: '\e63b';
}

.productManagement:before {
  content: '\e619';
}

.operationMaintenanceView:before {
  content: '\e610';
}

.tenantView:before {
  content: '\eb9e';
}

.operationsOverview:before {
  content: '\e67f';
}

.menu:before {
  content: '\e625';
}

.yiwen:before {
  content: '\e603';
}

.computingResources:before {
  content: '\e867';
}

.gly:before {
  content: '\e7ad';
}

.billManagement:before {
  content: '\e624';
}

.storageResources:before {
  content: '\e9ed';
}

.contentright:before {
  content: '\e72f';
}

.fangda:before {
  content: '\e62a';
}

.suoxiao:before {
  content: '\ed77';
}

.zy:before {
  content: '\e671';
}

.wl:before {
  content: '\e62e';
}

.sp:before {
  content: '\e601';
}

.gd:before {
  content: '\e68c';
}

.gl:before {
  content: '\ed76';
}

.zj:before {
  content: '\e640';
}

.yzj:before {
  content: '\e600';
}
