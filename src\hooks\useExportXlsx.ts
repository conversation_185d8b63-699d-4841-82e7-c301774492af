import { ElNotification } from 'element-plus'
import { ref } from 'vue'
import { utils, writeFile } from 'xlsx'
import SlMessage from '@/components/base/SlMessage'

// 定义用于导出的数据类型

// 定义导出的记录对象类型
interface Record {
  [key: string]: any // 动态属性，记录的每一项可以有不同的字段
}

// 定义接收的 header 配置类型
export interface HeaderConfig {
  label: string // 中文标题
  prop: string // 数据字段名
  width?: number // 列宽度（可选）
  'min-width'?: number // 最小列宽（可选）
}

export function useExportXlsx() {
  /**
   * 将传入的 header 配置转换为 headerMap 和 columnWidths
   *
   * @param headers 配置的列信息，包括 label, prop, width 和 min-width
   * @returns 返回一个包含 headerMap 和 columnWidths 的对象
   */
  const generateHeaderMapAndColumnWidths = (headers: HeaderConfig[], records: Record[]) => {
    const headerMap: { [key: string]: string } = {} // 用于存储 prop 到 label 的映射
    const columnWidths: number[] = [6] // 用于存储每列的宽度

    // 遍历传入的 headers 配置
    headers.forEach((header) => {
      headerMap[header.prop] = header.label // prop 到 label 的映射

      // 如果有 width，则使用 width，否则使用 min-width（如果存在），否则自动计算列宽
      if (header.width) {
        columnWidths.push(header.width)
      } else if (header['min-width']) {
        columnWidths.push(header['min-width'])
      } else {
        // 自动计算列宽（基于数据内容的最大长度）
        const maxLength = Math.max(
          ...records.map((record) => String(record[header.prop] ?? '').length),
          header.label.length, // 也要考虑表头的长度
        )
        columnWidths.push(Math.max(Math.min(maxLength + 2, 30), 10)) // 确保列宽在 10 到 30 之间
      }
    })

    return { headerMap, columnWidths }
  }

  /**
   * 导出 XLSX 文件的核心函数
   *
   * @param fileName 导出的文件名称（无扩展名）
   * @param records 要导出的数据记录（数组格式）
   * @param headers 配置的列信息（label, prop, width, min-width 等）
   * @param {Boolean} isNotify 是否有导出消息提示 (默认为 true)
   */
  const exportXlsx = (
    fileName: string,
    records: Record[],
    headers: HeaderConfig[],
    isNotify: boolean = true,
  ) => {
    if (records.length === 0) return SlMessage.error('没有数据记录，无法导出') // 如果没有数据记录，直接返回
    if (isNotify) {
      ElNotification({
        title: '温馨提示',
        message: '如果数据庞大会导致下载缓慢哦，请您耐心等待！',
        type: 'info',
        duration: 3000,
      })
    }
    // 生成 headerMap 和 columnWidths
    const { headerMap, columnWidths } = generateHeaderMapAndColumnWidths(headers, records)

    // 获取表头：根据传入的 headerMap 获取映射的中文字段，映射到表头字段
    const headerNames = ['序号', ...Object.values(headerMap)] // 在表头前添加序号列
    const filteredData = records.map((record, index) => {
      const filteredRecord: Record = {} // 用于存储经过过滤后的记录
      filteredRecord['序号'] = index + 1 // 序号从 1 开始
      headers.forEach((header) => {
        if (headerMap[header.prop]) {
          filteredRecord[headerMap[header.prop]] = record[header.prop]
        }
      })
      return filteredRecord // 返回经过筛选的记录
    })

    // 创建一个工作表（将 JSON 数据转换为工作表）
    const ws = utils.json_to_sheet(filteredData, { header: headerNames })

    // 设置工作表的列宽：`!cols` 属性用于设置列宽
    ws['!cols'] = columnWidths.map((width) => ({ wch: width }))

    const wb = utils.book_new()

    utils.book_append_sheet(wb, ws, 'Sheet1')

    writeFile(wb, `${fileName}.xlsx`)

    SlMessage.success(`导出${fileName}成功`)
  }

  const exportData = ref<any[]>([])

  /**
   * 获取导出的数据调用接口
   *
   * @param api  接口调用函数
   * @param params 接口调用参数
   * @param multipleSelection 选中的数据,或者是需要导出的数据
   */

  const getExportData = async (
    api: (param: any) => Promise<any>,
    params: any,
    multipleSelection?: any[],
  ) => {
    if (multipleSelection && multipleSelection.length > 0) {
      exportData.value = multipleSelection
      return
    }

    const { entity } = await api(params)
    exportData.value = entity
  }

  // 返回导出 XLSX 文件的函数
  return {
    exportXlsx,
    getExportData,
    exportData,
  }
}
