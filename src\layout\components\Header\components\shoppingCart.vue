<template>
  <el-badge
    :badge-style="{ fontSize: '10px' }"
    :style="style"
    :value="count"
    :offset="[6, -2]"
    :show-zero="false"
    class="item"
    @click="$router.push('/shoppingCarts')"
  >
    <el-icon style="font-size: 20px">
      <ShoppingCart />
    </el-icon>
    <span class="text">购物车</span>
  </el-badge>
</template>
<script setup lang="ts">
import { ShoppingCart } from '@element-plus/icons-vue'
import { shoppingCartCount } from '@/api/modules/resourecenter'
import { useRoute } from 'vue-router'
import { ref, onUnmounted, watch } from 'vue'
import eventBus from '@/utils/eventBus'

const style = ref('')
const route = useRoute()
watch(
  () => route.path,
  (newVal) => {
    if (newVal === '/shoppingCarts') {
      style.value = 'color: var(--el-color-primary)'
    } else {
      style.value = ''
    }
  },
  { immediate: true },
)
const count = ref(0)
const getShoppingCartCount = async () => {
  const { entity, code } = await shoppingCartCount()
  if (code === 200) {
    count.value = entity
  }
}
getShoppingCartCount()
eventBus.on('shoppingCarts:updateCount', getShoppingCartCount)
onUnmounted(() => {
  eventBus.off('shoppingCarts:updateCount', getShoppingCartCount)
})
</script>
<style scoped lang="scss">
.item {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  margin: 0 20px;
  margin-right: 8px;
  &:hover {
    color: var(--el-color-primary) !important;
  }
  .text {
    font-size: 14px;
    margin-left: 4px;
  }
}
</style>
