<!-- 经典布局 -->
<template>
  <el-container class="layout">
    <el-header>
      <div class="header-lf mask-image">
        <div class="logo flx-center">
          <img class="logo-img" src="@/assets/images/logo.png" alt="logo" />
          <span class="logo-text"> 算力工作台 </span>
        </div>
        <ToolBarLeft />
      </div>
      <div class="header-ri">
        <ToolBarRight />
      </div>
    </el-header>
    <el-container class="classic-content">
      <el-aside>
        <div class="aside-box" :style="{ width: isCollapse ? '65px' : '210px' }">
          <el-scrollbar class="sidebar" :class="{ 'is-collapse': isCollapse }">
            <el-menu
              :router="false"
              :default-active="activeMenu"
              :collapse="isCollapse"
              :unique-opened="accordion"
              :collapse-transition="false"
              :default-openeds="defaultOpeneds"
              class="custom-menu"
            >
              <el-menu-item-group v-for="item in menuList" :key="item.name">
                <template #title v-if="!isCollapse">
                  <div class="menu-title">{{ item.meta.title }}</div>
                  <img
                    src="../assets/images/menu/icon_computing_workbench.png"
                    v-if="item.meta.icon == 'icon_computing_workbench'"
                  />
                  <img
                    src="../assets/images/menu/icon_dispatching_center.png"
                    v-if="item.meta.icon == 'icon_dispatching_center'"
                  />
                  <img
                    src="../assets/images/menu/icon_expense_center.png"
                    v-if="item.meta.icon == 'icon_expense_center'"
                  />
                  <img
                    src="../assets/images/menu/icon_management.png"
                    v-if="item.meta.icon == 'icon_management'"
                  />
                  <img
                    src="../assets/images/menu/icon_product_center.png"
                    v-if="item.meta.icon == 'icon_product_center'"
                  />
                  <img
                    src="../assets/images/menu/icon_resource_center.png"
                    v-if="item.meta.icon == 'icon_resource_center'"
                  />
                  <img
                    src="../assets/images/menu/icon_work_order.png"
                    v-if="item.meta.icon == 'icon_work_order'"
                  />
                </template>
                <SubMenu :menu-list="item.children!" />
              </el-menu-item-group>
            </el-menu>
          </el-scrollbar>
        </div>
      </el-aside>
      <el-container class="classic-main">
        <Main />
      </el-container>
    </el-container>
  </el-container>
</template>

<script setup lang="ts" name="layoutClassic">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/modules/auth'
import { useGlobalStore } from '@/stores/modules/global'
import Main from '@/layout/components/Main/index.vue'
import SubMenu from '@/layout/components/Menu/SubMenu.vue'
import ToolBarLeft from '@/layout/components/Header/ToolBarLeft.vue'
import ToolBarRight from '@/layout/components/Header/ToolBarRight.vue'
import { useGlobalDicStore } from '@/stores/modules/dic'

const globalDic = useGlobalDicStore()
globalDic.initDic()

const route = useRoute()
const authStore = useAuthStore()
const globalStore = useGlobalStore()
const accordion = computed(() => globalStore.accordion)
const isCollapse = computed(() => globalStore.isCollapse)
const menuList = computed(() => authStore.showMenuListGet)
// 详情页面需要高亮的菜单
const activeMenu = computed(
  () => (route.meta.activeMenu ? route.meta.activeMenu : route.name) as string,
)
const defaultOpeneds = computed(() => {
  return menuList.value.map((i) => i.name)
})
</script>

<style scoped lang="scss">
.el-container {
  width: 100%;
  height: 100%;
  :deep(.el-header) {
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 55px;
    padding: 0 15px 0 0;
    background-color: var(--el-header-bg-color);
    border-bottom: 1px solid var(--el-header-border-color);
    .header-lf {
      display: flex;
      align-items: center;
      overflow: hidden;
      white-space: nowrap;
      .logo {
        flex-shrink: 0;
        width: 210px;
        margin-right: 16px;
        .logo-img {
          width: 28px;
          object-fit: contain;
        }
        .logo-text {
          margin-left: 6px;
          font-size: 21.5px;
          font-weight: bold;
          color: var(--el-header-logo-text-color);
          white-space: nowrap;
        }
      }
    }
  }
  .classic-content {
    display: flex;
    height: calc(100% - 55px);
    :deep(.el-aside) {
      width: auto;
      background-color: var(--el-menu-bg-color);
      border-right: 1px solid var(--el-aside-border-color);
      .aside-box {
        display: flex;
        flex-direction: column;
        height: 100%;
        transition: width 0.3s ease;
        .el-menu {
          width: 100%;
          overflow-x: hidden;
          border-right: none;
        }
      }
    }
    .classic-main {
      display: flex;
      flex-direction: column;
    }
  }
}
.layout {
  background-color: #fff;
  height: 100vh;
  .menu-title {
    font-size: 16px;
    font-weight: 600;
  }
  :deep(.el-menu-item-group__title) {
    height: 40px;
    padding: 0 10px;
    margin-top: 10px;
    margin-bottom: 5px;
    color: #333;
    font-size: 14px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(to bottom, white, rgb(224, 232, 246));
    img {
      width: 30px;
      height: 30px;
      margin-top: 10px;
    }
  }

  :deep(.sidebar .el-scrollbar__view) {
    padding: 0 10px;
  }
  :deep(.sidebar.is-collapse .el-scrollbar__view) {
    padding: 0 10px 0 0;
  }

  :deep(.el-menu--collapse .el-menu-item-group__title) {
    height: 0;
  }
}
// 下面是根据产品图修改的样式  -- 后面估计会该
.aside-box {
  border-right: 1px solid rgba(50, 60, 76, 0.1);
}
</style>
