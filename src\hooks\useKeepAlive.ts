import { useKeepAliveStore } from '@/stores/modules/keepAlive'
import { ref, watch } from 'vue'
import { useRoute } from 'vue-router'

interface CacheConfig {
  pageName: string // 页面名称（路由 name）
  componentName: string // 组件名称
}

const cacheConfig = ref<CacheConfig[]>([
  {
    pageName: 'orderApproval',
    componentName: 'workOrder',
  },
  {
    pageName: 'recyclingPage',
    componentName: 'recycleWorkOrder',
  },
  {
    pageName: 'changePage',
    componentName: 'changeWorkOrder',
  },
  // 可以添加更多缓存规则
])

const useKeepAlive = () => {
  const route = useRoute()

  const keepAliveStore = useKeepAliveStore()

  // 监听路由变化，动态更新 cachedViews
  watch(
    () => route.name,
    (newRouteName) => {
      // 遍历缓存配置对象
      cacheConfig.value.forEach((config) => {
        if (newRouteName === config.componentName) {
          // 如果进入指定页面，则添加该组件的缓存
          keepAliveStore.addKeepAliveName(config.componentName)
        } else if (newRouteName !== config.pageName) {
          // 如果离开指定页面，则移除该组件的缓存
          keepAliveStore.removeKeepAliveName(config.componentName)
        }
      })
    },
    { immediate: true },
  )
}

export default useKeepAlive
