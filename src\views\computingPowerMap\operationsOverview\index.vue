<template>
  <div class="content">
    <div class="content_Box">
      <div ref="myElement">
        <div class="m-b-20 f-r-c-b">
          <div>
            <span class="fz-14 c-9 m-r-10">
              <i :class="route.meta.icon" class="iconfont c-theme fz-14 m-r-10"> </i>算力地图
            </span>
            /
            <span class="m-l-10 fz-16 fz-w">{{ route_.title }}</span>
          </div>
          <div class="resourcePoolT f-r">
            <!-- <el-button type="primary" class="titleTopBtn m-r-10 fz-16">
            <el-icon class="el-icon--right m-r-6" :size="16"> <Upload /> </el-icon>导出
          </el-button> -->
            <div class="f-r f-j-e m-r-10">
              <div class="f-r-c-b search c-p">
                <p class="p-h-10 f-r-c-b p-v-10 f-1" @click="drawerO = true">
                  <span class="fz-16">{{
                    cloudPlatform.filter((item: CloudPlatformType) => item.check === true)[0]
                      ?.name || '全部'
                  }}</span>
                  <span>
                    <el-icon :size="18"> <ArrowDown /> </el-icon>
                  </span>
                </p>
              </div>
            </div>
            <div class="f-r f-j-e">
              <div class="f-r-c-b search c-p searchT">
                <p class="p-h-10 f-r-c-b p-v-10 f-1" @click="drawerO = true">
                  <span class="fz-16">{{
                    regionListsName?.[0]?.platformName ||
                    (cloudPlatform.filter((item: CloudPlatformType) => item.check === true)
                      .length == 0
                      ? '全部'
                      : '--')
                  }}</span>
                  <span class="searchT_j" v-if="regionListsName.length > 1">
                    +{{ regionListsName.length - 1 }}
                  </span>
                  <span>
                    <el-icon :size="18"> <ArrowDown /> </el-icon>
                  </span>
                </p>
                <el-icon :size="18" class="m-h-10" @click="drawer = true">
                  <View />
                </el-icon>
              </div>
            </div>
          </div>
        </div>
        <div class="f-r boxContiner">
          <Equipment
            :latest="latest"
            :gpu-lists-data="gpuListsData"
            :region-lists-name="regionListsName"
          ></Equipment>
        </div>
      </div>

      <div class="f-r-c-b resourcePool p-b-20">
        <div class="resourcePoolL f-1 bgfe br-6">
          <div class="p-20 bs-b br-6 f-r f-j-b">
            <p class="fz-18 fz-w">资源池分配率TOP5</p>
            <div class="f-r">
              <el-date-picker
                class="m-r-10"
                @change="getReportTop5ApiHttp"
                :disabled-date="disabledDateReport"
                v-model="resourcePoolreportTime"
                type="date"
                placeholder="请选择日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
              <div class="fz-15 f-r f-a-c btn">
                <p
                  :class="[
                    'p-v-9',
                    'p-h-36',
                    't-a-c',
                    'c-p',
                    resourcePoolreport == 1 ? 'checked_l' : 'unChecked_l',
                  ]"
                  @click="handresourcePool(1, 'resourcePoolreport')"
                >
                  周
                </p>
                <p
                  :class="[
                    'p-v-9',
                    'p-h-36',
                    't-a-c',
                    'c-p',
                    resourcePoolreport == 1 ? 'unChecked_r' : 'checked_r',
                  ]"
                  @click="handresourcePool(2, 'resourcePoolreport')"
                >
                  月
                </p>
              </div>
            </div>
          </div>
          <!-- <div> -->
          <!-- <div id="main" style=" height: 400px"></div> -->
          <!-- </div> -->
          <div :style="{ height: divHeight + 'px', 'max-width': '99%' }">
            <div class="f-r-c-b">
              <div class="f-r f-a-c m-l-20">
                <template v-if="resourcePoolreportTime">
                  <img src="@/assets/images/img/hint.svg" alt="" class="20" height="20" />
                  <span class="c-theme m-l-6">
                    当前展示时间段{{
                      subtractDaysFromDate(
                        resourcePoolreportTime,
                        resourcePoolreport == 1 ? 7 : 30,
                      )
                    }}~{{ resourcePoolreportTime }}
                  </span>
                </template>
              </div>
              <el-radio-group
                v-model="reportRadioreport"
                class="radioBtn"
                @change="handchangeReportRadio(1)"
              >
                <el-radio :value="1" class="button-1"> <span class="d m-r-4"></span>vCPU </el-radio>
                <el-radio :value="2" class="button-2"> <span class="d m-r-4"></span>内存</el-radio>
                <el-radio :value="3" class="button-3"> <span class="d m-r-4"></span>储存</el-radio>
              </el-radio-group>
            </div>
            <Echarts :option="option" ref="reportEcharts" />
          </div>
        </div>
        <div class="resourcePoolL resourcePoolR f-1 bgfe m-l-16 br-6">
          <div class="p-20 bs-b f-r f-j-b">
            <p class="fz-18 fz-w">资源池利用率TOP5</p>
            <div class="f-r">
              <el-date-picker
                @change="getPerformanceTopApiHttp"
                class="m-r-10"
                v-model="resourcePoolTime"
                type="date"
                placeholder="请选择日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                :disabled-date="disabledDate"
              />
              <div class="fz-15 f-r f-a-c btn">
                <p
                  :class="[
                    'p-v-9',
                    'p-h-36',
                    't-a-c',
                    'c-p',
                    resourcePool == 1 ? 'checked_l' : 'unChecked_l',
                  ]"
                  @click="handresourcePool(1, 'resourcePool')"
                >
                  周
                </p>
                <p
                  :class="[
                    'p-v-9',
                    'p-h-36',
                    't-a-c',
                    'c-p',
                    resourcePool == 1 ? 'unChecked_r' : 'checked_r',
                  ]"
                  @click="handresourcePool(2, 'resourcePool')"
                >
                  月
                </p>
              </div>
            </div>
          </div>
          <div>
            <div :style="{ height: divHeight + 'px', 'max-width': '99%' }">
              <div class="f-r-c-b">
                <div class="f-r f-a-c m-l-20">
                  <template v-if="resourcePoolTime">
                    <img src="@/assets/images/img/hint.svg" alt="" class="20" height="20" />
                    <span class="c-theme m-l-6">
                      当前展示时间段{{
                        subtractDaysFromDate(resourcePoolTime, resourcePool == 1 ? 7 : 30)
                      }}
                      ~{{ resourcePoolTime }}
                    </span>
                  </template>
                </div>
                <el-radio-group
                  v-model="reportRadio"
                  class="radioBtn"
                  @change="handchangeReportRadio(2)"
                >
                  <el-radio :value="1" class="button-2">
                    <span class="d m-r-4"></span>vCPU
                  </el-radio>
                  <el-radio :value="2" class="button-2">
                    <span class="d m-r-4"></span>内存
                  </el-radio>
                  <el-radio :value="3" class="button-2">
                    <span class="d m-r-4"></span>储存
                  </el-radio>
                </el-radio-group>
              </div>
              <Echarts :option="options" />
            </div>
            <!-- <div id="main1" style="width: 98%; height: 400px"></div> -->
          </div>
        </div>
      </div>
    </div>

    <!-- 展示所选信息选中资源池的drawer -->
    <el-drawer
      class="resourcePoolO"
      v-model="drawer"
      title="I am the title"
      :size="'300px'"
      :with-header="false"
      :modal="true"
      :overlay-color="`rgba(0, 0, 0, 0.3)`"
      @close="drawer = false"
    >
      <resourcePoolO
        @isClose="handClose"
        :cloud-platform="cloudPlatform"
        :drawer="drawer"
        :region-lists="regionLists"
      >
      </resourcePoolO>
    </el-drawer>
    <!-- 选择云、城市、地区、资源池等信息 -->
    <el-drawer
      class="resourcePoolT"
      v-model="drawerO"
      title="I am the title"
      :size="'696px'"
      :with-header="false"
      :modal="false"
      :overlay-color="`rgba(0, 0, 0, 0.3)`"
    >
      <resourcePoolT
        ref="resourcePoolTRef"
        :cloud-platform="cloudPlatform"
        :drawer-o="drawerO"
        :confirm-page="confirmPage"
      >
      </resourcePoolT>
      <template #footer>
        <div style="flex: auto">
          <el-button @click="cancelClick" size="large">取消</el-button>
          <el-button type="primary" @click="confirmClick" size="large">确认</el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<script lang="ts" setup>
import Echarts from './ReEcharts.vue'
import Equipment from './Equipment.vue'
import resourcePoolO from './resourcePoolO.vue'
import resourcePoolT from './resourcePoolT.vue'
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import cloneDeep from 'lodash/cloneDeep'
import SlMessage from '@/components/base/SlMessage'
import {
  getPlatformTypesApi,
  getLatestApi,
  getReportTop5Api,
  getRegionsApi,
  getPerformanceTopApi,
  getbyUserApi,
} from '@/api/modules/operationsOverview'
import { View, ArrowDown } from '@element-plus/icons-vue'
import type {
  CloudPlatformType,
  latestType,
  reportType,
  regionsApiType,
  PerformanceType,
  gpuItemType,
  byUserType,
} from './interface/type'
import { useUserStore } from '@/stores/modules/user'
// import { getPerformanceTopApiData } from './data.json'
const userStore = useUserStore()
const userInfo = userStore.userInfo

//动态高度
let divHeight = ref(400) //设置初始值
const myElement = ref<HTMLDivElement | null>(null) //获取上半部分元素的高度
const getDivHeight = () => {
  if (myElement.value) {
    let tableH = 100 //距离页面下方的高度
    let tableHeightDetil = window.innerHeight * 0.92 - tableH - myElement.value.offsetHeight
    if (tableHeightDetil <= 400) {
      divHeight.value = 400
    } else {
      divHeight.value = tableHeightDetil
    }
  }
}
// 监听窗口变化动态高度
window.onresize = () => {
  if (myElement.value) {
    getDivHeight()
  }
}
//日期选择时间范围 禁用今天往前超过三个月的日期
const disabledDateReport = (time: Date): boolean => {
  return disabledDateTime(time, resourcePoolreport.value)
}
const disabledDate = (time: Date): boolean => {
  return disabledDateTime(time, resourcePool.value)
}
let disabledDateTime = (time: Date, value: number) => {
  const today = new Date()
  const threeMonthsAgo = new Date(today)
  if (value == 1) {
    threeMonthsAgo.setMonth(today.getMonth() - 3)
    threeMonthsAgo.setDate(threeMonthsAgo.getDate() + 7)
  } else {
    threeMonthsAgo.setMonth(today.getMonth() - 2)
  }
  return time.getTime() < threeMonthsAgo.getTime() || time.getTime() > today.getTime()
}

//抽屉当前所选资源池
const drawer = ref(false)
//抽屉资源池选择
const drawerO = ref(false)
//全选
// const value1 = ref(false)
//路由名称
const route = useRoute()
let route_ = reactive<{ title: any; parentTitle: any }>({ parentTitle: '', title: '' })
// console.log('route_', route)
// route_.parentTitle =
//   route.matched && route.matched.length > 1
//     ? route.matched[route.matched.length - 2].meta.title
//     : null
route_.title = route.meta.title
//抽屉2
const resourcePoolTRef = ref()
let cancelClick = () => {
  confirmPage.value = 'cancel'
  drawerO.value = false
}
//抽屉选中参数
let cloudPlatformList = reactive([])
let confirmPage = ref<string>('ALL')
let confirmClick = () => {
  // Object.assign(cloudPlatformList, [])
  cloudPlatformList.length = 0
  // 弹窗点击确认时，拼接资源池信息
  let stashRegionLists: any = []
  stashRegionLists = regionfun(resourcePoolTRef.value.regionList, true, false)
  function hasAnyRegionsValue(data: any) {
    let allRegions: any[] = []

    data.forEach((item: any) => {
      if (item.regions && item.regions.length > 0) {
        allRegions = allRegions.concat(item.regions)
      }
    })

    return allRegions.length > 0
  }
  if (hasAnyRegionsValue(resourcePoolTRef.value.regionList) && stashRegionLists.length == 0) {
    return SlMessage.error('请选择资源池进行查询！')
  } else {
    regionLists.value = regionfun(resourcePoolTRef.value.regionList, true, true)
  }
  //格式化参数
  let cloudPlatformList_ = resourcePoolTRef.value.regionList.map((item: any) => {
    if (item.regions && item.regions.length != 0) {
      return {
        cloudPlatformCode: item.platformCode,
        cloudPlatformType: item.platformType,
        regionCodes: item.regions
          .filter((item: any) => item.check === true)
          .map((item: any) => item.code),
      }
    }
  })
  cloudPlatformList_ = cloudPlatformList_.filter((item: any) => {
    // 检查 item 是否是对象，并且 regionCodes 属性不是空数组
    return (
      typeof item === 'object' &&
      item !== null &&
      Array.isArray(item.regionCodes) &&
      item.regionCodes.length > 0
    )
  })
  Object.assign(cloudPlatformList, cloudPlatformList_)
  confirmPage.value = 'confirm'
  //判断所选云是否有改变
  let newCloudPlatformId =
    (cloudPlatform.value.filter((item: CloudPlatformType) => item.check === true)[0] &&
      cloudPlatform.value.filter((item: CloudPlatformType) => item.check === true)[0].id) ||
    ''
  let oldCloudPlatformId =
    (resourcePoolTRef.value.cloudPlatforms.filter(
      (item: CloudPlatformType) => item.check === true,
    )[0] &&
      resourcePoolTRef.value.cloudPlatforms.filter(
        (item: CloudPlatformType) => item.check === true,
      )[0].id) ||
    ''
  // console.log('newCloudPlatformId', newCloudPlatformId, oldCloudPlatformId)
  if (newCloudPlatformId != oldCloudPlatformId) {
    cloudPlatform.value = cloneDeep(resourcePoolTRef.value.cloudPlatforms)
    getReportTop5ApiHttp()
    getPerformanceTopApiHttp()
  }
  // if (regionLists.value.length == 0) {
  //   getRegionsApiHttp()
  // }
  // console.log('确认', cloudPlatformList)
  getLatestApiHttp(true)

  // console.log('确认', resourcePoolTRef.value.regionList)
  // console.log('页面数按钮', cloudPlatformList)
}
//抽屉当前所选资源池关闭
let handClose = (e: boolean) => {
  drawer.value = e
}
// TODO
onMounted(() => {
  getPlatformTypesApiHttp()
  getDivHeight()
  //--
  // getPerformanceTopApiHttp()
  // console.log('resourcePoolTRef', resourcePoolTRef)
})

// 查询云平台
//云平台
let cloudPlatform = ref<CloudPlatformType[]>([])
let getPlatformTypesApiHttp = () => {
  getPlatformTypesApi().then((res: any) => {
    if (res.code == 200) {
      const { entity } = res
      let list = entity.map((item: CloudPlatformType) => {
        return {
          ...item,
          check: false,
          // check: item.code == 'cloudst_prov_edge' ? true : false,
        }
      })
      cloudPlatform.value = list
      getgetbyUserHttp()
      // getLatestApiHttp()
      getReportTop5ApiHttp()
      getRegionsApiHttp()
      getPerformanceTopApiHttp()
    }
  })
}
//查询当前登录人的租户列表
let byUserDetail = ref<byUserType[]>([])
let getgetbyUserHttp = () => {
  getbyUserApi({ userId: userInfo.id }).then((res: any) => {
    if (res.code == 200) {
      const { entity } = res
      byUserDetail.value = entity
      getLatestApiHttp()
    } else {
      getLatestApiHttp()
    }
  })
}
//总览数据
let latest = ref<latestType[]>()
let getLatestApiHttp = (type: boolean = false) => {
  let parms: any = {}
  if (type) {
    parms = {
      platTypeId:
        (resourcePoolTRef.value.cloudPlatforms.filter(
          (item: CloudPlatformType) => item.check === true,
        )[0] &&
          resourcePoolTRef.value.cloudPlatforms.filter(
            (item: CloudPlatformType) => item.check === true,
          )[0].id) ||
        '',
      cloudPlatformList: cloudPlatformList,
    }
  } else {
    parms = {
      platTypeId:
        (cloudPlatform.value.filter((item: CloudPlatformType) => item.check === true)[0] &&
          cloudPlatform.value.filter((item: CloudPlatformType) => item.check === true)[0].id) ||
        '',
    }
  }
  parms['tenantIds'] = tenants()
  getLatestApi(parms).then((res: any) => {
    if (res.code == 200) {
      let { entity } = res
      // entity.storageTotalHhd = '2'
      // entity.storageUsedHhd = '1'
      // entity.storageAviHhd = '1'
      // entity.storageTotalSsd = '2'
      // entity.storageUsedSsd = '1'
      // entity.storageAviSsd = '1'
      // 内存字段添加单位
      if (entity.memoryTotal) {
        entity['memoryunits'] = determineUnit(entity.memoryTotal)
        entity.memoryTotal = convertGbWithUnit(entity.memoryTotal, entity['memoryunits'])
        entity.memoryUsed = convertGbWithUnit(entity.memoryUsed, entity['memoryunits'])
        entity.memoryAvi = convertGbWithUnit(entity.memoryAvi, entity['memoryunits'])
      }

      if (entity.storageTotalSsd || entity.storageTotalHhd) {
        // console.log('字节转换',determineUnit(entity.storageTotal))
        if (entity.storageTotalSsd && !entity.storageTotalHhd) {
          entity['units'] = determineUnit(entity.storageTotalSsd)
        } else if (!entity.storageTotalSsd && entity.storageTotalHhd) {
          entity['units'] = determineUnit(entity.storageTotalHhd)
        } else {
          entity['units'] = determineUnit(entity.storageTotalSsd)
        }
        entity.storageTotalSsd = convertGbWithUnit(entity.storageTotalSsd, entity['units'])
        entity.storageUsedSsd = convertGbWithUnit(entity.storageUsedSsd, entity['units'])
        entity.storageAviSsd = convertGbWithUnit(entity.storageAviSsd, entity['units'])
        entity.storageTotalHhd = convertGbWithUnit(entity.storageTotalHhd, entity['units'])
        entity.storageUsedHhd = convertGbWithUnit(entity.storageUsedHhd, entity['units'])
        entity.storageAviHhd = convertGbWithUnit(entity.storageAviHhd, entity['units'])
      } else {
        entity['units'] = determineUnit(entity.storageTotal)
        //  console.log('字节转换unitType',entity['units'])
        entity.storageTotal = convertGbWithUnit(entity.storageTotal, entity['units'])
        entity.storageUsed = convertGbWithUnit(entity.storageUsed, entity['units'])
        entity.storageAvi = convertGbWithUnit(entity.storageAvi, entity['units'])
      }
      latest.value = entity

      gpuListsData.value[0].used = entity.gpuT4Used
      gpuListsData.value[0].other = entity.gpuT4AviSale
      gpuListsData.value[0].total = entity.gpuT4Sum
      gpuListsData.value[1].used = entity.gpuA10Used
      gpuListsData.value[1].other = entity.gpuA10AviSale
      gpuListsData.value[1].total = entity.gpuA10Sum
      gpuListsData.value[2].used = entity.gpuA40Used
      gpuListsData.value[2].other = entity.gpuA40AviSale
      gpuListsData.value[2].total = entity.gpuA40Sum
      if (type) {
        drawerO.value = false
      }
    }
  })
}
// 判断需要转换到的单位
let determineUnit = (gb: number) => {
  const gbToTb = 1024 // 1 TB = 1024 GB
  const tbToPb = 1024 // 1 PB = 1024 TB
  // const pbToEb = 1024 // 1 EB = 1024 PB

  // if (gb >= gbToTb * tbToPb * pbToEb) {
  //   return 'EB'
  // } else
  if (gb >= gbToTb * tbToPb) {
    return 'PB'
  } else if (gb >= gbToTb) {
    return 'TB'
  } else {
    return 'GB'
  }
}
// 执行转换
let convertGbWithUnit = (val: number, unit: string) => {
  // console.log('执行转换',val,unit)
  let value
  // if (unit === 'EB') {
  //   value = val / (1024 * 1024 * 1024) // GB to EB
  // } else
  if (unit === 'PB') {
    value = val / (1024 * 1024) // GB to PB
  } else if (unit === 'TB') {
    value = val / 1024 // GB to TB
  } else {
    value = val // 保持为GB
  }

  // 保留两位小数
  value = value.toFixed(2)
  return value
}
let tenants = () => {
  let list: any[] = []
  // if (userInfo.oacRoles.some((oacRoles: any) => oacRoles.id == 11)) {
  //   list = []
  // } else if (byUserDetail.value && byUserDetail.value.length > 0) {
  //   list = byUserDetail.value.map((item: any) => item.id)
  // } else {
  //   list = [-1]
  // }
  return list
}
// top5数据接口
let resourcePoolreport = ref(1)

let getReportTop5ApiHttp = () => {
  let startTime_ = subtractDaysFromDate(
    resourcePoolreportTime.value,
    resourcePoolreport.value == 1 ? 7 : 30,
  )
  let parms = {
    resType:
      reportRadioreport.value == 1
        ? 'VCPU'
        : reportRadioreport.value == 2
          ? 'MEMORY'
          : reportRadioreport.value == 3
            ? 'STORAGE'
            : '',
    // reportType: resourcePoolreport.value == 1 ? 'WEEK' : 'MONTH',
    startTime: startTime_,
    endTime: resourcePoolreportTime.value,
    platTypeId:
      (cloudPlatform.value.filter((item: CloudPlatformType) => item.check === true)[0] &&
        cloudPlatform.value.filter((item: CloudPlatformType) => item.check === true)[0].id) ||
      '',
  }
  getReportTop5Api(parms).then((res: any) => {
    if (res.code == 200) {
      const { entity } = res
      optionfun(entity, parms.resType)
    }
  })
}
//资源池数据接口
let report = ref<PerformanceType>()
let getPerformanceTopApiHttp = () => {
  let startTime_ = subtractDaysFromDate(resourcePoolTime.value, resourcePool.value == 1 ? 7 : 30)
  let parms = {
    startTime: startTime_,
    endTime: resourcePoolTime.value,
    cloudCode:
      (cloudPlatform.value.filter((item: CloudPlatformType) => item.check === true)[0] &&
        cloudPlatform.value.filter((item: CloudPlatformType) => item.check === true)[0].code) ||
      '',
  }
  getPerformanceTopApi(parms).then((res: any) => {
    if (res.code == 200) {
      // const { entity } = getPerformanceTopApiData
      const { entity } = res
      report.value = entity

      optionfuns()
    }
  })
}
//资源池图
let option = reactive({})
let optionfun = (i: reportType[], colorType: string) => {
  const color_ = {
    VCPU: '#14be26',
    MEMORY: '#487dea',
    STORAGE: '#eec724',
  } as const
  let datas = []
  if (i.length) {
    datas = i.map((item: any, index: number) => {
      return {
        ...item,
        value: (Number(item.usedRate) * 100).toFixed(2),
        regionName_: `TOP${index + 1}\n${item.regionName}`,
      }
    })
  }

  let options = {
    tooltip: {
      trigger: 'axis',
      // formatter: function (params: any) {
      //   return `
      //   <p style="color:#000;font-size:13px;color:#999"">${params[0].data.regionName}</p>
      //   <p style="color:#000;font-size:12px"><span style="color:#999">分配率:</span> ${params[0].data.value}%</p>
      //   <p style="color:#000;font-size:12px"><span style="color:#999">剩余量:</span> ${params[0].data.avi}G</p>
      //   <p style="color:#000;font-size:12px"><span style="color:#999">已分配:</span> ${params[0].data.used}G</p>`
      // },
      formatter: (params: any) => {
        // console.log(params)
        // tooltip标题
        let titleHtmlStr = `<div style="font-size:14px;color:#666;font-weight:400;line-height:1;"> ${params[0].marker} ${params[0].name}</div>`
        let itemHtmlStrArr = ''
        // })
        if (colorType == 'VCPU') {
          itemHtmlStrArr = `<div style="display: flex;align-items:center; margin-top: 10px;">
          <div style="font-size: 14px;color: #666;margin: 0 20px 0 2px;">分配率:</div>
          <span style="margin-left: auto;text-align: right;font-weight: 900;">${params[0].data.value}%</span>
        </div><div style="display: flex;align-items:center;">
          <div style="font-size: 14px;color: #666;margin: 0 20px 0 2px;">剩余量:</div>
          <span style="margin-left: auto;text-align: right;font-weight: 900;">${params[0].data.avi}核</span>
        </div><div style="display: flex;align-items:center;">
          <div style="font-size: 14px;color: #666;margin: 0 20px 0 2px;">已分配:</div>
          <span style="margin-left: auto;text-align: right;font-weight: 900;">${params[0].data.used}核</span>
        </div>`
        } else {
          itemHtmlStrArr = `<div style="display: flex;align-items:center; margin-top: 10px;">
          <div style="font-size: 14px;color: #666;margin: 0 20px 0 2px;">分配率:</div>
          <span style="margin-left: auto;text-align: right;font-weight: 900;">${params[0].data.value}%</span>
        </div><div style="display: flex;align-items:center;">
          <div style="font-size: 14px;color: #666;margin: 0 20px 0 2px;">剩余量:</div>
          <span style="margin-left: auto;text-align: right;font-weight: 900;">${params[0].data.avi}GB</span>
        </div><div style="display: flex;align-items:center;">
          <div style="font-size: 14px;color: #666;margin: 0 20px 0 2px;">已分配:</div>
          <span style="margin-left: auto;text-align: right;font-weight: 900;">${params[0].data.used}GB</span>
        </div>`
        }
        return titleHtmlStr + itemHtmlStrArr
      },
    },
    grid: {
      left: '60px',
      top: '10px',
      right: '10px',
      bottom: '18%',
    },
    xAxis: {
      data: datas.map((item_: any) => item_['regionName_']),
      splitLine: {
        show: true,
        lineStyle: {
          width: 1,
          color: '#eee',
          type: 'solid',
        },
      },
      axisTick: {
        show: true, //隐藏X轴刻度
        alignWithLabel: true,
      },
      axisLabel: {
        show: true,
        color: '#333', //X轴文字颜色
        fontSize: 12,
        interval: 0, // 强制显示所有标签
        formatter: function (value: any) {
          // 如果文本长度超过一定限制，则添加省略号
          if (value.length > 17) {
            return value.slice(0, 17) + '...'
          }
          return value
        },
      },
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: function (value: any) {
          return value + '%' // 在这里添加单位
        },
      },
      splitLine: {
        show: true,
        lineStyle: {
          width: 1,
          color: '#eee',
          type: 'solid',
        },
      },
    },
    series: [
      {
        name: '',
        type: 'bar',
        barWidth: 18,
        itemStyle: {
          borderRadius: [10, 10, 0, 0],
          color: color_[colorType as keyof typeof color_],
        },
        data: datas,
      },
    ],
  }
  Object.assign(option, options)
}
//资源率图
let options = reactive({})
//单选
let optionfuns = () => {
  let i: any = report.value
  let colorType =
    reportRadio.value == 1
      ? 'cpuData'
      : reportRadio.value == 2
        ? 'memData'
        : reportRadio.value == 3
          ? 'diskData'
          : ''
  let datas = i[colorType as keyof PerformanceType].dataUtil.map((item: any) => {
    return {
      ...item,
      name: item.regionName,
      type: 'line',
      data: item.performanceData.map((value: any) => {
        if (Number(value) === 0) return 0
        // const percent = Number(value) * 100;
        const percent = Number(value)
        return parseFloat(percent.toFixed(2))
      }),
    }
  })

  let options_ = {
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        let paramsData = cloneDeep(params)
        paramsData.sort(function (a: any, b: any) {
          return b.value - a.value // 降序排序
        })
        // tooltip标题
        let titleHtmlStr = `<div style="font-size:14px;color:#666;font-weight:400;line-height:1;">${params[0].name}</div>`

        // tooltip详情内容
        const itemHtmlStrArr = paramsData.map((item: any) => {
          return `<div style="display: flex;align-items:center;">
          ${item.marker}
          <div style="font-size: 14px;color: #666;margin: 0 20px 0 2px;">${item.seriesName}</div>
          <span style="margin-left: auto;text-align: right;font-weight: 900;">${item.value}%</span>
        </div>`
        })
        const contentHtmlStr = `<div style="display: flex;flex-direction: column;margin-top: 10px;">
        ${itemHtmlStrArr.join('')}
      </div>`

        // 最终html字符串
        const resHtmlStr = titleHtmlStr + contentHtmlStr
        return resHtmlStr
      },
    },
    grid: {
      left: '30px',
      top: '10px',
      right: '20px',
      bottom: '45px',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: i[colorType as keyof PerformanceType].days,
      splitLine: {
        show: true,
        lineStyle: {
          width: 1,
          color: '#eee',
          type: 'solid',
        },
      },
      axisLabel: {
        show: true,
        interval: 0, // 强制显示所有标签
        rotate: resourcePool.value == 1 ? 0 : 30, // 30度角倾斜显示
        formatter: function (value: any) {
          // 自定义格式，例如添加前缀或后缀
          return value.slice(5, 10)
        },
      },
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: function (value: any) {
          return value + '%' // 在这里添加单位
        },
      },
      splitLine: {
        show: true,
        lineStyle: {
          width: 1,
          color: '#eee',
          type: 'solid',
        },
      },
    },
    series: datas,
  }
  Object.assign(options, options_)
}
const reportRadio = ref(2)
const reportRadioreport = ref(2)
//月份点击
let resourcePool = ref(1)
let handresourcePool = (i: number, type: string) => {
  // resourcePoolTime  resourcePoolreportTime
  if (type == 'resourcePool') {
    resourcePool.value = i
    resourcePoolTime.value = getTodayFormatted()
    getPerformanceTopApiHttp()
  } else {
    resourcePoolreport.value = i
    resourcePoolreportTime.value = getTodayFormatted()
    getReportTop5ApiHttp()
  }
}
//top 切换
let handchangeReportRadio = (i: number) => {
  if (i == 1) {
    getReportTop5ApiHttp()
  } else {
    optionfuns()
  }
}
//默认展示顶部和抽屉数据
let regionLists = ref<regionsApiType[]>([])
let regionListsName = ref<any[]>([])
// Gpu数组
let gpuListsData = ref<gpuItemType[]>([])
gpuListsData.value = [
  {
    name: 'GPU-T4',
    used: '0',
    other: '0',
    total: '0',
  },
  {
    name: 'GPUA10',
    used: '0',
    other: '0',
    total: '0',
  },
  {
    name: 'GPU-A40',
    used: '0',
    other: '0',
    total: '0',
  },
]
//查询资源池下集合信息
let getRegionsApiHttp = () => {
  getRegionsApi({
    platTypeId:
      (cloudPlatform.value.filter((item: CloudPlatformType) => item.check === true)[0] &&
        cloudPlatform.value.filter((item: CloudPlatformType) => item.check === true)[0].id) ||
      '',
  }).then((res: any) => {
    if (res.code == 200) {
      const { entity } = res
      // TODO zxb
      if (cloudPlatform.value.filter((item: CloudPlatformType) => item.check === true).length > 0) {
        regionLists.value = regionfun(entity, false, true)
      } else {
        regionLists.value = regionfun([], false, true)
      }

      // console.log('getRegionsApiHttp', entity)
      // console.log('regionListsName', regionListsName.value)
    }
  })
}
// 拼接资源池名称信息
let regionfun = (arr: any, type: boolean, isContactName: boolean) => {
  let list: any = []
  let Name: any = []
  arr.forEach((item: any) => {
    if (item.regions && item.regions.length) {
      item.regions.forEach((item_: any) => {
        if (type) {
          if (item_.check == true) {
            list.push(item_)
            // Name.push(item.platformName)
            if (!Name.some((items: any) => items.platformCode == item.platformCode)) {
              Name.push({ platformName: item.platformName, platformCode: item.platformCode })
            }
          }
        } else {
          list.push(item_)
          // Name.push(item.platformName)
          if (!Name.some((items: any) => items.platformCode == item.platformCode)) {
            Name.push({ platformName: item.platformName, platformCode: item.platformCode })
          }
        }
      })
    }
  })
  // regionListsName.value = [...new Set(Name)]
  if (isContactName) {
    regionListsName.value = Name
  }
  // console.log('regionListsName', regionListsName.value)
  return list
}

let getTodayFormatted = (): string => {
  const today = new Date()
  const year = today.getFullYear()
  const month = String(today.getMonth() + 1).padStart(2, '0') // 月份从0开始，需要加1并补零
  const day = String(today.getDate()).padStart(2, '0') // 补零
  return `${year}-${month}-${day}`
}
const resourcePoolTime = ref(getTodayFormatted())
const resourcePoolreportTime = ref(getTodayFormatted())
// 日期计算
let subtractDaysFromDate = (dateString: string, daysToSubtract: number): string => {
  // 创建一个Date对象，解析输入的日期字符串
  const date = new Date(dateString.replace(/-/g, '/')) // 有些浏览器可能无法正确解析YYYY-MM-DD格式的日期，所以这里用/替换-

  // 检查日期是否有效
  if (isNaN(date.getTime())) {
    throw new Error('Invalid date string')
  }

  // 使用setDate方法减去指定的天数
  date.setDate(date.getDate() - daysToSubtract)

  // 格式化日期为YYYY-MM-DD字符串
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0') // 月份从0开始计数，所以需要加1
  const day = String(date.getDate()).padStart(2, '0')

  return `${year}-${month}-${day}`
}
</script>

<style scoped lang="scss" src="./pandect.scss"></style>
