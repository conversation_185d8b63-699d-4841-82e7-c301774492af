<template>
  <div v-loading.fullscreen.lock="tableLoading" element-loading-text="操作中，请稍候...">
    <SlProTable
      ref="proTable"
      highlight-current-row
      :columns="columns"
      style="min-height: 300px"
      :request-api="containerPageApi"
      :init-param="queryParams"
      :current-change="currentChange"
      @selection-change="handleSelectionChange"
      hidden-table-header
      row-key="goodsOrderId"
    >
    </SlProTable>

    <!-- 资源变更弹窗 -->
    <ResourceChangeDialog
      v-model:visible="changeDialogVisible"
      resource-type="eip"
      :selected-resources="selectedResources"
      :allowed-change-types="allowedChangeTypes"
      @confirm="handleConfirm"
    />

    <!-- 资源延期弹窗 -->
    <ResourceChangeDialog
      v-model:visible="delayDialogVisible"
      resource-type="eip"
      :selected-resources="selectedResources"
      :is-delay="true"
      @confirm="handleConfirm"
    />

    <!-- 绑定设备弹窗 -->
    <DeviceBindDialog
      v-model:visible="bindDialogVisible"
      v-model:business-sys-id="currentEip.businessSysId"
      @selectDevice="handleDeviceSelect"
    />
  </div>
</template>
<script setup lang="tsx" name="dataList">
import { ref, reactive, type VNode, computed } from 'vue'
import type { ColumnProps, ProTableInstance } from '@/components/SlProTable/interface'
import { containerPageApi, cycleBinCreate, eipBind, eipUnbind } from '@/api/modules/resourecenter'
import { ElMessage, ElMessageBox } from 'element-plus'
import eventBus from '@/utils/eventBus'
import ResourceChangeDialog from '../components/ResourceChangeDialog.vue'
import DeviceBindDialog from './components/DeviceBindDialog.vue'
import type { ResourceChangeType } from '../types'
import { useResourceChange } from '../hooks/useResourceChange'
import { useRecycleValidation } from '../hooks/useRecycleValidation'
import { useGlobalDicStore } from '@/stores/modules/dic'

const { queryParams } = defineProps<{
  queryParams: Record<string, any>
}>()

const { validateResources } = useResourceChange()

const emit = defineEmits(['currentChange'])
const currentChange = (currentRow: any, oldCurrentRow: any) => {
  emit('currentChange', currentRow, oldCurrentRow)
}

const globalDic = useGlobalDicStore()
const { getDic } = globalDic
// 表格配置项
const columns = reactive<ColumnProps<any>[]>([
  { type: 'selection', width: 55 },
  { type: 'index', label: '序号', width: 55, fixed: 'left' },
  { prop: 'cqName', label: '配额名称', minWidth: 150, fixed: 'left' },
  { label: 'vCPU(核)', prop: 'vCpus', minWidth: 100 },
  { label: '内存(GB)', prop: 'ram', minWidth: 100 },
  { label: 'GPU算力', prop: 'gpuRatio', minWidth: 100 },
  { label: 'GPU显存(GB)', prop: 'gpuVirtualMemory', minWidth: 130 },
  { label: 'GPU卡数量(个)', prop: 'gpuCore', minWidth: 130 },
  { prop: 'catalogueDomainName', label: '云类型', minWidth: 100, filter: true },
  { prop: 'domainName', label: '云平台', minWidth: 150 },
  { prop: 'regionName', label: '资源池', minWidth: 180, filter: true },
  { prop: 'applyUserName', label: '申请人', minWidth: 100 },
  { label: '申请时长', prop: 'applyTime', minWidth: 100, enum: getDic('time') },
  { prop: 'a4Account', label: '4A账号', minWidth: 100 },
  { prop: 'a4Phone', label: '4A账号绑定的手机号', minWidth: 150 },
  { prop: 'businessSystemName', label: '业务系统', minWidth: 150, filter: true },
  { prop: 'createTime', label: '创建时间', minWidth: 180 },

  {
    prop: 'operation',
    label: '操作',
    width: 100,
    fixed: 'right',
    isShow: false,
    render: operationRender,
  },
])
function operationRender({ row }: any): VNode {
  return (
    <>
      {row.relatedDeviceId ? (
        <el-button
          onClick={() => handleUnbind(row)}
          type="primary"
          disabled={row.recoveryStatus != 0 || row.changeStatusCn !== '未变更'}
          v-permission="Unbind"
          link
        >
          解绑
        </el-button>
      ) : (
        <el-button
          onClick={() => handleBind(row)}
          type="primary"
          disabled={row.recoveryStatus != 0 || row.changeStatusCn !== '未变更'}
          v-permission="Bind"
          link
        >
          绑定
        </el-button>
      )}
    </>
  )
}

const proTable = ref<ProTableInstance>()
const tableLoading = ref(false)

const currentRecycleIdsList = ref<any[]>([])

// 使用回收校验钩子函数
const { validateRecycle, validateChange } = useRecycleValidation()

const handleCreateRecycle = async (goodsItems: any[]) => {
  const res = await cycleBinCreate({
    goodsType: 'eip',
    goodsItems,
  })
  if (res.code != 200) {
    return ElMessage.error(res.message || '接口请求失败')
  }
  ElMessage.success('已加入回收站')
  proTable.value?.getTableList()
  proTable.value?.clearSelection()
  eventBus.emit('cycleBins:updateCount')
}

// 批量回收功能
const handleBatchRecycle = async () => {
  const selectedList = proTable.value?.selectedList || []

  // 使用新的验证逻辑
  if (validateRecycle(selectedList, 'eip')) {
    currentRecycleIdsList.value = selectedList.map((i) => ({ goodsId: i.id.trim() }))
    handleCreateRecycle(currentRecycleIdsList.value)
  }
}

// 多选数据，使用计算属性优化性能
const multipleSelection = ref<any[]>([])
const changeDialogVisible = ref(false)
const delayDialogVisible = ref(false)
const selectedResources = ref<any[]>([])

// 使用计算属性缓存处理过的资源列表以提高性能
const processedResources = computed(() => {
  if (multipleSelection.value.length === 0) return []
  return multipleSelection.value.map((resource) => ({
    ...resource,
    _resourceId: resource.id, // 添加一个唯一标识，用于跟踪变化
  }))
})

// 使用计算属性判断是否禁用变更和延期按钮
const isChangeDisabled = computed(() => multipleSelection.value.length === 0)

// EIP允许的变更类型
const allowedChangeTypes: ResourceChangeType[] = ['bandwidth_expand']

// 重构资源变更处理方法，性能更好
const handleResourceChange = async () => {
  if (isChangeDisabled.value) {
    ElMessage.warning('请选择要变更的资源')
    return
  }

  // 使用缓存的处理过的资源列表
  selectedResources.value = processedResources.value

  // 使用新的校验逻辑
  if (validateChange(selectedResources.value)) {
    // 使用钩子函数中的validateResources进行业务系统一致性校验
    const isValid = await validateResources(selectedResources.value)
    if (isValid) {
      // 校验通过，才显示弹窗
      changeDialogVisible.value = true
    }
  }
}

const handleResourceDelay = async () => {
  if (isChangeDisabled.value) {
    ElMessage.warning('请选择要延期的资源')
    return
  }

  // 使用缓存的处理过的资源列表
  selectedResources.value = processedResources.value

  // 使用新的校验逻辑
  if (validateChange(selectedResources.value)) {
    // 使用钩子函数中的validateResources进行业务系统一致性校验
    const isValid = await validateResources(selectedResources.value)
    if (isValid) {
      // 校验通过，才显示弹窗
      delayDialogVisible.value = true
    }
  }
}

// 处理确认
const handleConfirm = () => {
  proTable.value?.getTableList()
}

// 选择变化事件
const handleSelectionChange = (selection: any[]) => {
  multipleSelection.value = selection
}

// 绑定设备相关变量
const bindDialogVisible = ref(false)
const currentEip = ref<any>({})
const bindingLoading = ref('')
const unbindingLoading = ref('')

// 处理绑定设备
const handleBind = (row: any) => {
  currentEip.value = row
  bindDialogVisible.value = true
}

// 处理设备选择
const handleDeviceSelect = async (device: any) => {
  if (bindingLoading.value) return
  if (!currentEip.value.id) return

  try {
    bindingLoading.value = currentEip.value.id
    tableLoading.value = true
    await eipBind({
      id: currentEip.value.id,
      deviceId: device.deviceId,
      deviceType: device.deviceType,
    })

    ElMessage.success('绑定设备成功')
    proTable.value?.getTableList()
    bindDialogVisible.value = false
    currentEip.value = {}
  } catch (error) {
    console.error(error)
  } finally {
    bindingLoading.value = ''
    tableLoading.value = false
  }
}

// 处理解绑设备
const handleUnbind = async (row: any) => {
  try {
    await ElMessageBox.confirm('确定要解绑该设备吗？', '解绑确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    unbindingLoading.value = row.id
    tableLoading.value = true
    const res = await eipUnbind({
      id: row.id,
    })

    if (res.code !== 200) {
      return
    }

    ElMessage.success('解绑设备成功')
    proTable.value?.getTableList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error(error)
    }
  } finally {
    unbindingLoading.value = ''
    tableLoading.value = false
  }
}

defineExpose({
  handleBatchRecycle,
  handleResourceChange,
  handleResourceDelay,
})
</script>
