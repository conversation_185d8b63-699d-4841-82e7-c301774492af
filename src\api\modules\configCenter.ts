import http from '@/api'
import { V1CLOUD } from '../config/servicePort'
import { changeDateFormat } from '@/utils'
import type { RoleType } from '@/views/managementCenter/roleManagement/interface/type'
import type { flavorType } from '@/views/configCenter/standards/interface/type'

/**
 * @name 镜像列表
 */
export const imageList = (config: any) => {
  return http.get(V1CLOUD + '/image/list', changeDateFormat(config, ['createdTime']))
}

/**
 * @name 镜像管理-删除
 */
export const deleteImageByIdApi = (config: any) => http.delete(V1CLOUD + `/image/delete`, config)

/**
 * @name 镜像管理-批量删除
 */
export const batchDeleteImageByIdApi = (config: any) =>
  http.post(V1CLOUD + `/image/batch_delete`, config)

/**
 * @name 镜像管理-添加
 */
export const addImageApi = (config: RoleType) => http.post(V1CLOUD + '/image/create', config)

/**
 * @name 镜像管理-修改
 */
export const updateImageApi = (config: RoleType) => http.post(V1CLOUD + '/image/update', config)

/**
 * @name 镜像管理-上传文件
 */
export const uploadImageApi = (data: any, config: any) =>
  http.post<any>(V1CLOUD + '/image/upload', data, config)

/**
 * @name 镜像管理-获取操作系统信息
 */
export const getImageOsTypes = (config: any) => {
  return http.get(V1CLOUD + '/image/osTypes', config)
}
/**
 * @name 资源池列表
 */
export const resourcePoolList = (config: any) => {
  return http.get(V1CLOUD + '/region/regionsSelect', changeDateFormat(config, ['createdTime']))
}

/**
 * @name 资源池管理-删除
 */
export const deleteResourcePoolByIdApi = (config: any) =>
  http.post(V1CLOUD + `/region/delete/` + config.id)

/**
 * @name 资源池管理-添加
 */
export const addResourcePoolApi = (config: RoleType) =>
  http.post(V1CLOUD + '/region/createRegion', config)

/**
 * @name 资源池管理-修改
 */
export const updateResourcePoolApi = (config: RoleType) => {
  http.post(V1CLOUD + '/region/updateRegion', config)
}

/**
 * @name 资源池管理-上传文件
 */
export const uploadRegionApi = (data: any, config: any) =>
  http.post<any>(V1CLOUD + '/region/upload', data, config)

// /**
//  * @name 获取云平台列表
//  */
// export const getPlatformListApi = () => {
//   return http.get(V1CLOUD + '/platform/platforms')
// }
/**
 * @name 获取云平台列表
 */
export const getPlatformListApi = (config: any) => {
  return http.get(V1CLOUD + '/flavor/platforms', config)
}
/**
 * @name 获取资源池列表
 */
export const getRegionsListApi = (config: any) => {
  // return http.get(V1CLOUD + '/region/regions',changeDateFormat(config, ['createdTime']))
  return http.get(V1CLOUD + '/region/regionsSelect', changeDateFormat(config, ['createdTime']))
}
/**
 * @name 获取规格类型列表(规格新建-根据规格类型查询产品型号)
 */
export const getFlavorTypeListApi = (config: any) => {
  return http.get(V1CLOUD + '/flavor/products', changeDateFormat(config, ['createdTime']))
}
/**
 * @name 获取可用区列表
 */
export const getAzsListApi = (config: any) => {
  return http.get(V1CLOUD + '/az/azs', changeDateFormat(config, ['createdTime']))
}

/**
 * @name 规格列表
 */
export const flavorList = (config: any) => {
  return http.get(V1CLOUD + '/flavor/flavorsSelect', changeDateFormat(config, ['createdTime']))
}
/**
 * @name 新建规格查询信息
 */
export const getFlavorUseSelectApi = () => {
  return http.get(V1CLOUD + '/flavor/flavorUseSelect')
}
/**
 * @name 根据云平台查询资源类型
 */
export const getFlavorResTypesApi = (config: any) => {
  return http.get(V1CLOUD + '/flavor/restypes', config)
}

/**
 * @name 规格管理-上线下线操作
 */
export const changeStatusFlavorByIdApi = (config: any) =>
  http.put(V1CLOUD + `/flavor/change/status/` + config.id + '/' + config.status, config)

/**
 * @name 规格管理-批量删除
 */
// export const batchDeleteImageByIdApi = (config: any) =>
//   http.post(V1CLOUD + `/image/batch_delete`, config)

/**
 * @name 规格管理-添加
 */
export const addFlavorApi = (config: flavorType) =>
  http.post(V1CLOUD + '/flavor/addFlavorModel', config)

/**
 * @name 规格管理-修改
 */
export const updateFlavorApi = (config: flavorType) =>
  http.post(V1CLOUD + '/flavor/updateFlavorModel', config)

/**
 * @name 规格管理-上传文件
 */
export const uploadFlavorApi = (data: any) => http.post(V1CLOUD + '/flavor/upload', data)
