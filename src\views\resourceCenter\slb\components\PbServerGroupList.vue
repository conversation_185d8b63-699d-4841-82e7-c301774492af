<template>
  <SlProTable
    ref="proTable"
    highlight-current-row
    :columns="columns"
    style="min-height: 300px"
    :request-api="getSlbServerGroupList"
    :init-param="queryParams"
    hidden-table-header
    row-key="goodsOrderId"
  >
  </SlProTable>
</template>

<script setup lang="tsx" name="ListenerList">
import { ref, reactive } from 'vue'
import type { ColumnProps, ProTableInstance } from '@/components/SlProTable/interface'
import { getSlbServerGroupList } from '@/api/modules/resourecenter'

// 定义props
const props = defineProps<{
  slbId: string
}>()

// 表格ref
const proTable = ref<ProTableInstance>()

// 查询参数
const queryParams = reactive({
  slbResourceDetailId: props.slbId,
  groupType: 1, // 0表示虚拟服务器组,1表示主备服务器组
})

// 表格配置项
const columns = reactive<ColumnProps<any>[]>([
  { type: 'index', label: '序号', width: 100 },
  { prop: 'groupName', label: '分组名称' },
  { prop: 'id', label: '分组ID' },
  { prop: 'slbListenerName', label: '关联监听' },
])

// 暴露刷新方法供父组件调用
defineExpose({
  refresh: () => {
    proTable.value?.getTableList()
  },
})
</script>
