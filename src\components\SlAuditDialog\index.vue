<template>
  <div class="sl-audit-dialog">
    <el-dialog
      :title="form.activiteStatus == 1 ? '审批通过' : '审批驳回'"
      v-model="visible"
      :close-on-click-modal="false"
      width="640px"
      @close="handleClose"
      v-if="visible"
    >
      <div class="sl-audit-dialog__content">
        <!-- 提示语 -->
        <p class="textTip">{{ textTip }}</p>
        <el-form ref="ruleFormRef" :model="form">
          <el-row>
            <el-col :span="24">
              <el-form-item
                label="审批建议:"
                prop="auditAdvice"
                label-position="top"
                :rules="[
                  { required: true, message: '请输入审批建议', trigger: ['blur', 'change'] },
                ]"
              >
                <template #label>
                  <span class="mr10">审批建议:</span>
                  <!-- 常用语 -->
                  <el-button type="primary" @click="openListDialog" :icon="DocumentCopy" link>
                    常用语
                  </el-button>
                </template>
                <el-input
                  v-model="form.auditAdvice"
                  type="textarea"
                  :autosize="{ minRows: 4 }"
                  placeholder="请输入审批建议"
                  maxlength="500"
                  show-word-limit
                />
              </el-form-item>
            </el-col>

            <template v-for="(field, index) in visibleFields" :key="index">
              <el-col :span="field.span || 18">
                <component
                  v-if="field.render"
                  :is="field.render"
                  :form="form"
                  :field="field"
                ></component>
                <el-form-item
                  v-else
                  :label="field.label + ':'"
                  :prop="field.key"
                  v-model="form[field.key]"
                  :rules="field.rules"
                >
                  <el-select v-model="form[field.key]" placeholder="请选择" clearable filterable>
                    <el-option
                      v-for="option in dicCollection[field.dicKey] ?? field.options"
                      :key="option[field.valueField || 'value']"
                      :value="option[field.valueField || 'value']"
                      :label="option[field.labelField || 'label'] as any"
                      v-bind="field.props?.option || {}"
                    >
                      {{ option[field.labelField || 'label'] }}
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </template>
          </el-row>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="visible = false">取 消</el-button>
          <sl-button type="primary" :api-function="() => submit(true)">提 交</sl-button>
        </span>
      </template>
    </el-dialog>
    <!-- 常用语言 -->
    <SlPhrases ref="slPhrasesRef" :visible-btn="false" @update:phrases-vlaue="updatePhrases" />
  </div>
</template>

<script setup lang="tsx">
import { standardWorkorderAuditApi } from '@/api/modules/approvalCenter'
import type { ActiviteDetailVoListType } from '@/views/approvalCenter/workOrder/interface/type'
import { computed, inject, ref, type Ref } from 'vue'
import { DocumentCopy } from '@element-plus/icons-vue'
import SlPhrases from '@/components/SlPhrases/index.vue'
import { useVModel } from '@vueuse/core'
import type { FormInstance } from 'element-plus'
import { useRouter } from 'vue-router'
import SlMessage from '@/components/base/SlMessage'

export type AuditDialogProps = {
  auditApi?: (params: any) => Promise<any>
  visible: boolean
  dicCollection?: any // 字典集合
  backPath?: string // 提交完成跳转的路由地址
  beforeSubmit?: (params?: any) => Promise<any> //前置函数
  afterSubmit?: (params?: any) => Promise<any> //后置函数
}
const props = withDefaults(defineProps<AuditDialogProps>(), {
  auditApi: standardWorkorderAuditApi,
  dicCollection: () => ({}),
})
const emit = defineEmits(['submit', 'update:visible'])
const visible = useVModel(props, 'visible', emit)
// 获取之前的节点
const historyTasks = inject<Ref<ActiviteDetailVoListType[]>>('historyTasks', ref([]))

const form = ref<FormDataType>({
  auditAdvice: '',
  activiteStatus: '', //审批状态
})

// 提示语
const obj = {
  pass: '审批通过后该工单将会流转至下一节点，确定要通过该审批吗？请填写审批建议。',
  reject:
    '审批驳回后该工单将默认退回到租户节点，可选择之前任意流程节点, 确定要【审批驳回】吗？请填写审批建议。',
}
const textTip = computed(() => {
  return form.value.activiteStatus === 0 ? obj.reject : obj.pass
})

const rejectFields = [
  {
    label: '流程节点',
    type: 'select',
    key: 'nodeCode',
    labelWidth: 90,
    rules: [{ required: true, message: '请选择流程节点', trigger: 'change' }],
    options: historyTasks.value,
    labelField: 'taskName',
    valueField: 'task',
    props: {
      select: {
        clearable: true,
        filterable: true,
      },
    },
  },
  {
    span: 20,
    render: () => (
      <p class={' ml10'} style="color: red; font-size: 12px; ">
        提示: 驳回默认退回租户节点,可选择当前审批节点之前的任意流程节点
      </p>
    ),
  },
]

const visibleFields = ref<any[]>([])
const router = useRouter()

/**
 *
 * @params: FormDataType 提交的参数
 * @fields: any[] 表单字段配置
 * flag: boolean 是否打开弹窗
 */
const openDialog = (
  {
    params,
    fields,
  }: {
    params: FormDataType
    fields?: any[]
  },
  flag: boolean = true,
) => {
  // 1.打开弹窗
  visible.value = flag
  // 2.设置表单字段 以及 设置表单
  if (fields) visibleFields.value = fields
  form.value = {
    auditAdvice: '',
    ...params,
  }
  if (params.activiteStatus === 0) {
    visibleFields.value = rejectFields.map((item) => {
      return {
        ...item,
        options: item.key === 'nodeCode' ? historyTasks.value : item.options,
      }
    })
  }
  //3.不打开弹窗的 直接提交
  if (!flag) {
    submit()
    return
  }
  //4.重置表单检验
  ruleFormRef.value?.resetFields()
}

const handleClose = () => {
  form.value = {
    auditAdvice: '',
    activiteStatus: '', //审批状态
  }
  visibleFields.value = [
    {
      hideBlockTitle: false,
      slot: 'auditAdvice',
    },
  ]
}
const ruleFormRef = ref<FormInstance>()
/**
 *  审核提交
 * @param flag 是否校验表单
 */
const submit = async (flag: boolean = false) => {
  //1.校验表单  执行前置函数
  if (flag && !(await ruleFormRef.value?.validate())) return
  props.beforeSubmit && (await props.beforeSubmit(form.value))
  // 2.提交
  await props.auditApi(form.value)
  // 3.执行后置函数
  props.afterSubmit && (await props.afterSubmit(form.value))
  // 4.关闭弹窗
  SlMessage.success('提交成功')
  props.backPath ? router.push(props.backPath) : router.go(-1)
  emit('submit')
  visible.value = false
}

// 常用语
const slPhrasesRef = ref<InstanceType<typeof SlPhrases> | null>(null)
const openListDialog = () => {
  slPhrasesRef.value?.openListDialog()
}

const updatePhrases = (value: string) => {
  form.value.auditAdvice = value
}
defineExpose({
  openDialog,
})
</script>

<style lang="scss" scoped>
.textTip {
  font-size: 14px;
  color: #323233;
  line-height: 16px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
</style>
