<template>
  <SlProTable
    ref="proTable"
    highlight-current-row
    :columns="columns"
    style="min-height: 300px"
    :request-api="getSecurityGroupList"
    :init-param="queryParams"
    :current-change="currentChange"
    hidden-table-header
    row-key="id"
  >
  </SlProTable>
</template>
<script setup lang="tsx" name="DataList">
import { ref, type VNode, computed } from 'vue'
import type { ColumnProps, ProTableInstance } from '@/components/SlProTable/interface'
import { getSecurityGroupList, deleteSecurityGroup } from '@/api/modules/resourecenter'
import { ElMessageBox, ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'

const props = defineProps({
  queryParams: {
    type: Object,
    default: () => ({}),
  },
  isSelectMode: {
    type: Boolean,
    default: false,
  },
  hideOperations: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['currentChange', 'selected'])

const currentChange = (currentRow: any, oldCurrentRow: any) => {
  emit('currentChange', currentRow, oldCurrentRow)
}

// 动态计算列配置
const columns = computed(() => {
  const baseColumns: ColumnProps<any>[] = [
    { type: 'index', label: '序号', width: 55, fixed: 'left' },
    {
      prop: 'name',
      label: '安全组名称',
      fixed: 'left',
      render: ({ row }) => {
        return (
          <el-button onClick={() => handleViewDetail(row)} type="primary" link>
            {row.name}
          </el-button>
        )
      },
    },
    { prop: 'regionName', label: '资源池', filter: true },
    { prop: 'vpcName', label: 'VPC' },
    { prop: 'tenantName', label: '租户', filter: true },
    { prop: 'description', label: '描述', width: 200 },
    { prop: 'createTime', label: '创建时间', width: 150 },
  ]

  // 如果不是选择模式，添加操作列
  if (!props.hideOperations) {
    if (!props.isSelectMode) {
      baseColumns.push({
        prop: 'operation',
        label: '操作',
        width: 150,
        fixed: 'right',
        render: operationRender,
      })
    } else {
      // 选择模式下添加操作列（绑定操作）
      baseColumns.push({
        prop: 'operation',
        label: '操作',
        width: 100,
        fixed: 'right',
        render: selectOperationRender,
      })
    }
  }
  return baseColumns
})

// 普通操作列渲染函数
function operationRender({ row }: any): VNode {
  return (
    <>
      <el-button onClick={() => handleUpdateRule(row)} type="primary" link>
        编辑规则
      </el-button>
      <el-button onClick={() => handleDelete(row)} type="primary" link>
        删除
      </el-button>
    </>
  )
}

// 选择模式下的操作列渲染函数
function selectOperationRender({ row }: any): VNode {
  return (
    <>
      <el-button onClick={() => handleSelect(row)} type="primary" link>
        绑定
      </el-button>
    </>
  )
}

// 选择安全组
const handleSelect = (row: any) => {
  emit('selected', row)
}

const proTable = ref<ProTableInstance>()

const router = useRouter()
const handleUpdateRule = (row: any) => {
  router.push({
    path: 'securityGroupCreate',
    query: { id: row.id },
  })
}

// 删除安全组
const handleDelete = async (row: any) => {
  ElMessageBox.confirm(`确定要删除安全组 "${row.name}" 吗？`, '删除确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      try {
        const res = await deleteSecurityGroup({
          ids: row.id,
        })
        if (res.code == 200) {
          ElMessage.success('删除成功')
          proTable.value?.getTableList()
        }
      } catch (error) {
        console.error('删除安全组失败', error)
      }
    })
    .catch(() => {
      // 取消删除
    })
}

// 处理查看详情
const handleViewDetail = (row: any) => {
  // 跳转到安全组详情页面
  router.push({
    path: '/securityGroupDetail',
    query: {
      id: row.id,
      sourceType: 'DG',
    },
  })
}

defineExpose({
  proTable,
})
</script>
