<template>
  <div class="mb10">
    <SlProTable ref="proTable" :data="tableData" :columns="columns" :pagination="false">
      <template #tableHeader>
        <sl-block-title title="资源容量概览"> </sl-block-title>
        <div class="search-form mt-20">
          <sl-form
            ref="slFormRef"
            v-model="form"
            :options="options"
            :dic-collection="dicCollection"
          >
            <template #buttom>
              <el-button type="primary" @click="onSearch">查询</el-button>
            </template>
          </sl-form>
        </div>
      </template>
    </SlProTable>
  </div>
</template>

<script setup lang="tsx" name="AuditTable">
import { onMounted, reactive, ref, watchEffect } from 'vue'
import type { ColumnProps } from '@/components/SlProTable/interface'
import SlMessage from '@/components/base/SlMessage'
import {
  getLatestAPi,
  getPlatformTypesDicAPi,
  getRegionsDicAPi,
} from '@/api/modules/externalDocking'

const form = ref<FormDataType>({
  regionCodes: '',
  platformCode: '',
  platTypeId: '',
})

const dicCollection = ref<FormDataType>({
  cloudTypeDic: [],
  cloudPlatformDic: [],
  resourcePoolsDic: [],
})

const ininData = async () => {
  const { entity } = await getPlatformTypesDicAPi()

  dicCollection.value.cloudTypeDic = entity
  form.value.platTypeId = entity[0].id
}

// 获取云平台字典
const getDomainCode = async () => {
  const { entity } = await getRegionsDicAPi(form.value.platTypeId)
  dicCollection.value.cloudPlatformDic = entity
}
watchEffect(() => {
  if (form.value.platTypeId) getDomainCode()
})

// 获取资源池字典
const getResourcePools = async () => {
  if (!form.value.platformCode) return
  let obj = dicCollection.value.cloudPlatformDic.find(
    (item: any) => item.platformCode === form.value.platformCode,
  )
  dicCollection.value.resourcePoolsDic = obj?.regions || []
}

const tableKey = [
  {
    name: 'vCPU',
    unit: null,
    vcpuTotal: 'total',
    vcpuUsed: 'the',
    vcpuAvi: 'surplus',
  },
  {
    name: '内存总量',
    unit: 'GB',
    memoryTotal: 'total',
    memoryUsed: 'the',
    memoryAvi: 'surplus',
  },
  {
    name: '存储容量',
    unit: 'GB',
    storageTotal: 'total',
    storageUsed: 'the',
    storageAvi: 'surplus',
  },
  {
    name: '公网IP容量',
    unit: null,
    eipTotal: 'total',
    eipUsed: 'the',
    eipAvi: 'surplus',
  },
  {
    name: 'GPU卡总量',
    unit: null,
    gpuTotal: 'total',
    gpuUsed: 'the',
    gpuAvi: 'surplus',
  },
  {
    name: 'DCN地址总量',
    unit: null,
    dcnTotal: 'total',
    dcnUsed: 'the',
    dcnAvi: 'surplus',
  },
]

const tableData = ref<FormDataType[]>([])

const options = reactive([
  {
    groupName: '资源容量概览',
    hideBlockTitle: true,
    gutter: 20,
    groupItems: [
      {
        label: '云类型',
        type: 'select',
        key: 'platTypeId',
        span: 7,
        labelWidth: '80px',
        dicKey: 'cloudTypeDic',
        labelField: 'name',
        valueField: 'id',
        onChange: function () {
          form.value.regionCodes = ''
          form.value.platformCode = ''

          dicCollection.value.cloudPlatformDic = []
          dicCollection.value.resourcePoolsDic = []
        },
        props: {
          select: {
            clearable: true,
            filterable: true,
          },
        },
      },
      {
        label: '云平台',
        type: 'select',
        key: 'platformCode',
        span: 7,
        labelWidth: '80px',
        dicKey: 'cloudPlatformDic',
        labelField: 'platformName',
        valueField: 'platformCode',
        onChange: function () {
          form.value.regionCodes = ''
          dicCollection.value.resourcePoolsDic = []
          getResourcePools()
        },
        props: {
          select: {
            clearable: true,
            filterable: true,
          },
        },
      },
      {
        label: '资源池',
        type: 'select',
        key: 'regionCodes',
        span: 7,
        labelWidth: '80px',
        dicKey: 'resourcePoolsDic',
        labelField: 'name',
        valueField: 'code',
        props: {
          select: {
            clearable: true,
            filterable: true,
          },
        },
      },
      {
        span: 3,
        render: () => {
          return (
            <el-button type="primary" onClick={onSearch}>
              查 询
            </el-button>
          )
        },
      },
    ],
  },
])

const changeForm = () => {
  let formParams: any = {}
  if (!form.value.platTypeId) return formParams
  formParams.platTypeId = form.value.platTypeId
  if (form.value.platformCode) {
    let obj = dicCollection.value.cloudPlatformDic.find(
      (item: any) => item.platformCode === form.value.platformCode,
    )
    let newObj: any = {
      cloudPlatformCode: obj?.platformCode,
      cloudPlatformType: obj?.platformType,
    }
    if (form.value.regionCodes) {
      newObj.regionCodes = [form.value.regionCodes]
    }
    formParams.cloudPlatformList = [newObj]
  }
  return formParams
}
const onSearch = async () => {
  if (!form.value.platTypeId) {
    return SlMessage.warning('请选择平台类型')
  }
  const { entity } = await getLatestAPi(changeForm())

  // 组合个数据
  tableData.value = tableKey.map((item: any) => {
    let obj: any = {}
    Object.keys(item).forEach((key: any) => {
      if (!['name', 'unit'].includes(key)) {
        obj[item[key]] = entity[key]
      } else {
        obj[key] = item[key]
      }
    })
    return obj
  })
}

const columns = ref<ColumnProps[]>([
  {
    type: 'index',
    label: '序号',
    width: 55,
  },
  {
    prop: 'name',
    label: '资源名称',
  },
  {
    prop: 'unit',
    label: '单位',
  },
  {
    prop: 'total',
    label: '总量',
  },
  {
    prop: 'the',
    label: '已用量',
  },
  {
    prop: 'surplus',
    label: '剩余量',
  },
])

onMounted(async () => {
  await ininData()
  await onSearch()
})
</script>
<style lang="scss" scoped>
.search-form {
  width: 100%;
}
</style>
