<template>
  <div id="ResourceNumberList" :class="list.length > 3 ? 'flex-space-between' : ''">
    <div class="resource" v-for="(item, index) in list" :key="item.key">
      <div class="resource-header">
        <img
          class="width-40"
          src="@/assets/images/overview/icon_ecs.png"
          v-if="item.key === 'ecs'"
        />
        <img
          class="width-40"
          src="@/assets/images/overview/icon_gcs.png"
          v-if="item.key === 'gcs'"
        />
        <img src="@/assets/images/overview/icon_evs.png" v-if="item.key === 'evs'" />
        <img
          class="width-40"
          src="@/assets/images/overview/icon_obs.png"
          v-if="item.key === 'obs'"
        />
        <img src="@/assets/images/overview/icon_nat.png" v-if="item.key === 'nat'" />
        <img src="@/assets/images/overview/icon_slb.png" v-if="item.key === 'slb'" />
        <img src="@/assets/images/overview/icon_eip.png" v-if="item.key === 'eip'" />
        <img src="@/assets/images/overview/icon_vpc.png" v-if="item.key === 'vpc'" />
        <img
          class="width-40"
          src="@/assets/images/overview/icon_network.png"
          v-if="item.key === 'network'"
        />
        <span>{{ item.name }}</span>
        <i
          class="iconfont cart"
          @click.stop="handleCartAdd(item.key)"
          v-if="!['vpc', 'network'].includes(item.key)"
          v-permission="'AddToCart'"
        ></i>
      </div>
      <div class="resource-body">
        <div class="resource-amount" @click="handleRouterGo(item.key)">
          <span>{{ item.data?.amount }}</span>
          <span class="unit">{{ item.unit }}</span>
        </div>
        <div class="resource-ratio" :class="item.data?.diffAmount! >= 0 ? 'increase' : 'decrease'">
          {{ item.data?.ratio }}
        </div>
      </div>
      <div class="resource-footer">
        本月{{ item.data?.diffAmount! >= 0 ? '增加' : '减少' }}
        <span :class="item.data?.diffAmount! >= 0 ? 'increase' : 'decrease'">{{
          item.data?.diffAmount! >= 0 ? item.data?.diffAmount : -item.data?.diffAmount!
        }}</span>
        相比上个月
      </div>
      <div
        class="resource-line"
        v-show="(list.length > 4 && index <= 3) || (list.length > 8 && index <= 7)"
      ></div>
    </div>
    <div
      class="resource empty-placeholder"
      v-for="n in list.length + 1 - list.length"
      :key="n"
    ></div>
  </div>
</template>
<script setup lang="ts" name="ResourceNumberList">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { shoppingCartCreate } from '@/api/modules/resourecenter'
import SlMessage from '@/components/base/SlMessage'
import eventBus from '@/utils/eventBus'
interface ResourceData {
  amount?: number
  diffAmount?: number
  ratio?: string
}
interface ResourceItem {
  key: string
  name: string
  show: boolean
  unit?: string
  data: ResourceData
}
const props = defineProps<{
  resourceList: ResourceItem[]
}>()

// 已选业务系统列表
const list = computed(() => {
  return props.resourceList.filter((item) => item.show)
})
// 路由跳转
const router = useRouter()
/**
 * 跳转至对应资源中心页面
 * @param key
 */
const handleRouterGo = (key: string) => {
  router.push(`/${key}List`)
}

const handleCartAdd = async (key: string) => {
  const entity = await shoppingCartCreate({
    [`${key}List`]: [
      {
        goodsType: key,
        orderJson: {},
      },
    ],
  })
  if (entity.code == 200) {
    SlMessage({
      message: '已加入到购物车',
      type: 'success',
    })
    eventBus.emit('shoppingCarts:updateCount')
    // 触发全局更新购物车商品数量
  } else {
    SlMessage.error('提交失败')
  }
}
</script>
<style lang="scss" scoped>
#ResourceNumberList {
  display: flex;
  flex-wrap: wrap;
  padding: 20px 10px;
  &.flex-space-between {
    justify-content: space-between;
  }
  .resource {
    width: 22%;
    margin-bottom: 20px;
    .resource-header {
      height: 50px;
      padding: 0 10px;
      display: flex;
      align-items: center;
      background-color: #f3f4f9;
      img {
        width: 50px;
        height: 50px;
      }
      & > span {
        width: 100%;
        margin-left: 10px;
      }
      .cart {
        width: 30px;
        height: 30px;
        display: flex;
        justify-content: center;
        align-items: center;
        color: var(--el-color-primary);
        cursor: pointer;
      }
      .width-40 {
        width: 40px;
        height: 40px;
        margin-left: 5px;
        margin-right: 5px;
      }
    }
    .resource-body {
      height: 50px;
      padding: 10px;
      padding-bottom: 0;
      display: flex;
      align-items: center;
      .resource-amount {
        font-size: 28px;
        cursor: pointer;
        .unit {
          font-size: 20px;
        }
      }
      .resource-ratio {
        padding: 0 5px;
        height: 24px;
        margin-left: 10px;
        border-radius: 10px;
        font-size: 14px;
        display: flex;
        justify-content: center;
        align-items: center;
        &.increase {
          color: var(--el-color-primary);
          background-color: #ecf2fd;
        }
        &.decrease {
          color: #fcb72e;
          background-color: #fff1dd;
        }
      }
    }
    .resource-footer {
      padding: 0 10px;
      font-size: 14px;
      .increase {
        color: #70cda8;
      }
      .decrease {
        color: #fcb72e;
      }
    }
    .resource-line {
      height: 1px;
      background-color: #eee;
      margin-top: 20px;
    }
  }
}
</style>
