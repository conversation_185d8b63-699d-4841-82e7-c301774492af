{"code": 200, "entity": [{"path": "/computingWorkbench", "name": "computingWorkbench", "redirect": "/computingWorkbench", "meta": {"icon": "icon_computing_workbench", "title": "算力地图", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}, "children": [{"path": "/operationsOverview", "name": "operationsOverview", "component": "/computingPowerMap/operationsOverview/index.vue", "meta": {"icon": "operationsOverview", "title": "运营总览", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/tenantView", "name": "tenantView", "component": "/computingPowerMap/tenantView/index.vue", "meta": {"icon": "tenantView", "title": "租户视图", "activeMenu": "/tenantView", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/operationsOverview", "name": "undeveloped", "component": "/computingPowerMap/operationsOverview/index.vue", "meta": {"icon": "operationMaintenanceView", "title": "运维视图", "isLink": "", "isHide": true, "isFull": false, "isAffix": false, "isDisabled": true, "isKeepAlive": true}}]}, {"path": "/workOrder", "name": "workOrder", "component": "/approvalCenter/workOrder/index.vue", "meta": {"icon": "icon_work_order", "title": "审批中心", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}, "children": [{"path": "/workOrder", "name": "workOrder", "component": "/approvalCenter/workOrder/index.vue", "meta": {"icon": "workOrderApproval", "title": "开通工单审批", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/changeWorkOrder", "name": "changeWorkOrder", "component": "/approvalCenter/changeWorkOrders/index.vue", "meta": {"icon": "workOrderApproval", "title": "变更工单审批", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/recycleWorkOrder", "name": "recycleWorkOrder", "component": "/approvalCenter/recyclingWorkOrders/index.vue", "meta": {"icon": "workOrderApproval", "title": "回收工单审批", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/nonStandardOrder", "name": "nonStandardOrder", "component": "/approvalCenter/nonStandardOrders/index.vue", "meta": {"icon": "workOrderApproval", "title": "非标工单审批", "isLink": "", "isHide": true, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}]}, {"path": "/resourceCenter", "name": "resourceCenter", "redirect": "/ecsList", "meta": {"icon": "icon_resource_center", "title": "资源中心", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}, "children": [{"path": "/ecsList", "name": "ecsList", "component": "/resourceCenter/ecs/list.vue", "meta": {"icon": "computingResources", "title": "计算资源", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}, "children": [{"path": "/ecsList", "name": "ecsList", "component": "/resourceCenter/ecs/list.vue", "meta": {"icon": "", "title": "云主机", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/imagesList", "name": "imagesList", "component": "/resourceCenter/images/list.vue", "meta": {"icon": "", "title": "镜像文件", "isLink": "", "isHide": true, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/ecsForm", "name": "ecsForm", "component": "/resourceCenter/ecs/form.vue", "meta": {"icon": "", "title": "云主机资源开通", "activeMenu": "/ecsList", "isLink": "", "isHide": true, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/gcsList", "name": "gcsList", "component": "/resourceCenter/gcs/list.vue", "meta": {"icon": "", "title": "GPU云主机", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/gcsForm", "name": "gcsForm", "component": "/resourceCenter/gcs/form.vue", "meta": {"icon": "", "title": "GPU云主机资源开通", "activeMenu": "/gcsList", "isLink": "", "isHide": true, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/mirrorImage", "name": "mirrorImage", "component": "/configCenter/mirrorImage/list.vue", "meta": {"icon": "", "title": "镜像", "isLink": "", "isHide": true, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/resourcePool", "name": "resourcePool", "component": "/configCenter/resourcepool/list.vue", "meta": {"icon": "", "title": "资源池", "isLink": "", "isHide": true, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/standards", "name": "standards", "component": "/configCenter/standards/list.vue", "meta": {"icon": "", "title": "规格", "isLink": "", "isHide": true, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}]}, {"path": "/evsList", "name": "evsList", "component": "/resourceCenter/evs/list.vue", "meta": {"icon": "storageResources", "title": "存储资源", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}, "children": [{"path": "/evsList", "name": "evsList", "component": "/resourceCenter/evs/list.vue", "meta": {"icon": "", "title": "云硬盘", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/evsForm", "name": "evsForm", "component": "/resourceCenter/evs/form.vue", "meta": {"icon": "", "title": "云硬盘资源开通", "activeMenu": "/evsList", "isLink": "", "isHide": true, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/obsList", "name": "obsList", "component": "/resourceCenter/obs/list.vue", "meta": {"icon": "", "title": "对象存储", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/obsForm", "name": "obsForm", "component": "/resourceCenter/obs/form.vue", "meta": {"icon": "", "title": "对象存储资源开通", "activeMenu": "/obsList", "isLink": "", "isHide": true, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/operationsOverview", "name": "undeveloped", "component": "/computingPowerMap/operationsOverview/index.vue", "meta": {"icon": "", "title": "云备份", "isLink": "", "isHide": true, "isFull": false, "isAffix": false, "isDisabled": true, "isKeepAlive": true}}]}, {"path": "/vpcList", "name": "vpcList", "component": "/resourceCenter/vpc/list.vue", "meta": {"icon": "networkResources", "title": "网络资源", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}, "children": [{"path": "/vpcList", "name": "vpcList", "component": "/resourceCenter/vpc/list.vue", "meta": {"icon": "", "title": "VPC", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/vpcForm", "name": "vpcForm", "component": "/resourceCenter/vpc/form.vue", "meta": {"icon": "", "title": "VPC资源开通", "activeMenu": "/vpcList", "isLink": "", "isHide": true, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/networkList", "name": "networkList", "component": "/resourceCenter/network/list.vue", "meta": {"icon": "", "title": "网络", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/networkForm", "name": "networkForm", "component": "/resourceCenter/network/form.vue", "meta": {"icon": "", "title": "网络资源开通", "activeMenu": "/networkList", "isLink": "", "isHide": true, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/slbList", "name": "slbList", "component": "/resourceCenter/slb/list.vue", "meta": {"icon": "", "title": "负载均衡", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/slbForm", "name": "slbForm", "component": "/resourceCenter/slb/form.vue", "meta": {"icon": "", "title": "负载均衡资源开通", "activeMenu": "/slbList", "isLink": "", "isHide": true, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/eipList", "name": "eipList", "component": "/resourceCenter/eip/list.vue", "meta": {"icon": "", "title": "弹性公网", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/natList", "name": "natList", "component": "/resourceCenter/nat/list.vue", "meta": {"icon": "", "title": "NAT", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/natForm", "name": "natForm", "component": "/resourceCenter/nat/form.vue", "meta": {"icon": "", "title": "NAT网关资源开通", "activeMenu": "/natList", "isLink": "", "isHide": true, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/natRuleList", "name": "natRuleList", "component": "/resourceCenter/nat/ruleList.vue", "meta": {"icon": "", "title": "NAT网关规则", "activeMenu": "/natRuleList", "isLink": "", "isHide": true, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/securityGroupList", "name": "securityGroupList", "component": "/resourceCenter/securityGroup/list.vue", "meta": {"icon": "", "title": "安全组", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/virtualNicList", "name": "virtualNicList", "component": "/resourceCenter/virtualNic/list.vue", "meta": {"icon": "", "title": "虚拟网卡", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/virtualIpList", "name": "virtualIpList", "component": "/resourceCenter/virtualIpList/list.vue", "meta": {"icon": "", "title": "虚拟IP", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/certificateList", "name": "certificateList", "component": "/resourceCenter/certificate/list.vue", "meta": {"icon": "", "title": "证书管理", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/operationsOverview", "name": "undeveloped", "component": "/computingPowerMap/operationsOverview/index.vue", "meta": {"icon": "", "title": "云端口", "isLink": "", "isHide": true, "isFull": false, "isAffix": false, "isDisabled": true, "isKeepAlive": true}}]}, {"path": "/containerResources", "name": "containerResources", "component": "/computingPowerMap/containerResources/index.vue", "meta": {"icon": "containerResources", "title": "容器资源", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}, "children": [{"path": "/containerQuotas", "name": "containerQuotas", "component": "/computingPowerMap/containerQuotas/index.vue", "meta": {"icon": "", "title": "容器配额", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}]}, {"path": "/operationsOverview", "name": "middlewareResources", "component": "/computingPowerMap/operationsOverview/index.vue", "meta": {"icon": "middlewareResources", "title": "中间件资源", "isLink": "", "isHide": true, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}, "children": [{"path": "/operationsOverview", "name": "undeveloped", "component": "/computingPowerMap/operationsOverview/index.vue", "meta": {"icon": "", "title": "数据库", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": true, "isKeepAlive": true}}, {"path": "/operationsOverview", "name": "undeveloped", "component": "/computingPowerMap/operationsOverview/index.vue", "meta": {"icon": "", "title": "中间件", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": true, "isKeepAlive": true}}]}]}, {"path": "/productCenter", "name": "productCenter", "component": "/computingPowerMap/operationsOverview/index.vue", "meta": {"icon": "icon_product_center", "title": "产品中心", "isLink": "", "isHide": true, "isFull": false, "isAffix": false, "isDisabled": true, "isKeepAlive": true}, "children": [{"path": "/operationsOverview", "name": "undeveloped", "component": "/computingPowerMap/operationsOverview/index.vue", "meta": {"icon": "productAutomaticOrchestration", "title": "产品自动编排", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": true, "isKeepAlive": true}, "chidren": [{"path": "/operationsOverview", "name": "undeveloped", "component": "/computingPowerMap/operationsOverview/index.vue", "meta": {"icon": "", "title": "编排模板", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": true, "isKeepAlive": true}}, {"path": "/operationsOverview", "name": "undeveloped", "component": "/computingPowerMap/operationsOverview/index.vue", "meta": {"icon": "", "title": "编排实例", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": true, "isKeepAlive": true}}, {"path": "/operationsOverview", "name": "undeveloped", "component": "/computingPowerMap/operationsOverview/index.vue", "meta": {"icon": "", "title": "服务管理", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": true, "isKeepAlive": true}}]}, {"path": "/operationsOverview", "name": "undeveloped", "component": "/computingPowerMap/operationsOverview/index.vue", "meta": {"icon": "productManagement", "title": "产品管理", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": true, "isKeepAlive": true}}]}, {"path": "/expenseCenter", "name": "expenseCenter", "component": "/computingPowerMap/operationsOverview/index.vue", "meta": {"icon": "icon_expense_center", "title": "费用中心", "isLink": "", "isHide": true, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}, "children": [{"path": "/operationsOverview", "name": "undeveloped", "component": "/computingPowerMap/operationsOverview/index.vue", "meta": {"icon": "billManagement", "title": "账单管理", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": true, "isKeepAlive": true}}, {"path": "/operationsOverview", "name": "undeveloped", "component": "/computingPowerMap/operationsOverview/index.vue", "meta": {"icon": "costAnalysis", "title": "费用分析", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": true, "isKeepAlive": true}}]}, {"path": "/managementCenter", "name": "managementCenter", "redirect": "/workOrder", "meta": {"icon": "icon_management", "title": "管理中心", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}, "children": [{"path": "/userManagement", "name": "userCenter", "component": "/managementCenter/userManagement/index.vue", "meta": {"icon": "userCenter", "title": "用户中心", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}, "children": [{"path": "/userManagement", "name": "userManagement", "component": "/managementCenter/userManagement/index.vue", "meta": {"icon": "", "title": "用户管理", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/externalUserManagement", "name": "externalUserManagement", "component": "/managementCenter/externalUserManagement/index.vue", "meta": {"icon": "", "title": "外部用户管理", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/roleManagement", "name": "roleManagement", "component": "/managementCenter/roleManagement/index.vue", "meta": {"icon": "", "title": "角色管理", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/permissionManagement", "name": "permissionManagement", "component": "/managementCenter/permissionManagement/index.vue", "meta": {"icon": "", "title": "权限管理", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/tenantManagement", "name": "tenantManagement", "component": "/managementCenter/tenantManagement/index.vue", "meta": {"icon": "", "title": "租户管理", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}]}, {"path": "/operationalAnalysis", "name": "operationalAnalysis", "component": "/managementCenter/operationalAnalysis/index.vue", "meta": {"icon": "operationsOverview", "title": "运营分析", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/operationsOverview", "name": "undeveloped", "component": "/computingPowerMap/operationsOverview/index.vue", "meta": {"icon": "apiGetway", "title": "API网关", "isLink": "", "isHide": true, "isFull": false, "isAffix": false, "isDisabled": true, "isKeepAlive": true}}, {"path": "/systemLogs", "name": "systemLogs", "component": "/computingPowerMap/systemLogs/index.vue", "meta": {"icon": "logManagement", "title": "系统日志", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/reportCenter", "name": "reportCenter", "component": "/managementCenter/reportCenter/list.vue", "meta": {"icon": "logManagement", "title": "报表中心", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/projectManagement", "name": "projectManagement", "component": "/computingPowerMap/projectManagement/index.vue", "meta": {"icon": "logManagement", "title": "项目管理", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/operationsOverview", "name": "undeveloped", "component": "/computingPowerMap/operationsOverview/index.vue", "meta": {"icon": "messageManagement", "title": "消息管理", "isLink": "", "isHide": true, "isFull": false, "isAffix": false, "isDisabled": true, "isKeepAlive": true}}]}], "message": "成功"}