/*
 * @LastEditTime: 2023-11-23 10:13:41
 * @FilePath: /4.0/src/api/interface/index.ts
 */
// 请求响应参数（不包含data）
export interface Result {
  success: number
  code: number
  message: string
}

// 请求响应参数（包含data）
export interface ResultData<T = any> extends Result {
  entity: T
}

// 分页响应参数
export interface ResPage<T> {
  records: T[]
  pageNum: number
  pageSize: number
  total: number
}

// 分页请求参数
export interface ReqPage {
  pageNum: number
  pageSize: number
}

// 文件上传模块
export namespace Upload {
  export interface ResFileUrl {
    url: string
    fileId: string
    name: string
    suf: string
  }
}
