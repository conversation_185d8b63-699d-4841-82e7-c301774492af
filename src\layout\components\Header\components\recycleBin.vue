<template>
  <el-badge
    :badge-style="{ fontSize: '10px' }"
    :style="style"
    :value="count"
    :offset="[6, -2]"
    :show-zero="false"
    class="item"
    @click="$router.push('/recycleBin')"
  >
    <el-icon style="font-size: 18px">
      <Delete />
    </el-icon>
    <span class="text">回收站</span>
  </el-badge>
</template>
<script setup lang="ts">
import { Delete } from '@element-plus/icons-vue'
import { cycleBinCount } from '@/api/modules/resourecenter'
import eventBus from '@/utils/eventBus'
import { ref, onUnmounted, watch } from 'vue'
import { useRoute } from 'vue-router'

const style = ref('')
const route = useRoute()
watch(
  () => route.path,
  (newVal) => {
    if (newVal === '/recycleBin') {
      style.value = 'color: var(--el-color-primary)'
    } else {
      style.value = ''
    }
  },
  { immediate: true },
)
const count = ref(0)
const getrecycleBinCount = async () => {
  const { entity, code } = await cycleBinCount()
  if (code === 200) {
    count.value = entity
  }
}
getrecycleBinCount()
eventBus.on('cycleBins:updateCount', getrecycleBinCount)
onUnmounted(() => {
  eventBus.off('cycleBins:updateCount', getrecycleBinCount)
})
</script>
<style scoped lang="scss">
.item {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  margin-right: 8px;
  &:hover {
    color: var(--el-color-primary) !important;
  }
  .text {
    font-size: 14px;
    margin-left: 4px;
  }
}
</style>
