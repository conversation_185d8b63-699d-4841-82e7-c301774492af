<template>
  <sl-form class="goods-info-form" ref="slFormRef" :options="goodsInfoOptions" :model-value="goods">
    <!-- 删除按钮 -->
    <template #globalFormSlot>
      <div @click="handleGoodsDelete" class="goods-del-btn">
        <el-icon><CircleCloseFilled /></el-icon>
      </div>
    </template>
  </sl-form>
</template>
<script setup lang="tsx">
import { reactive } from 'vue'
import type {
  IPropsListItem,
  IPropsItem,
} from '@/views/resourceCenter/hooks/usePropertyChangeModels'
import { CircleCloseFilled, Delete } from '@element-plus/icons-vue'
import slForm from '@/components/form/SlForm.vue'
import { useGlobalDicStore } from '@/stores/modules/dic'
import eventBus from '@/utils/eventBus'

const globalDic = useGlobalDicStore()
const { getDic } = globalDic

const emit = defineEmits(['deleteGoods'])
const props = defineProps<{
  goods: IPropsListItem<'evs'>
  imageOptions: any
  flavorOptions: any
}>()

function handleGoodsDelete() {
  emit('deleteGoods')
}
function setRef(props: IPropsItem<'evs'>, ref: any) {
  props.ref = ref
}

const propsMapOptions = {
  delay: [
    {
      style: 'margin:0;padding:0;padding-top: 16px;',
      gutter: 40,
      groupItems: [
        {
          label: '变更类型',
          type: 'text',
          key: 'changeTypeCn',
          span: 8,
        },
        {
          label: '到期时间',
          type: 'text',
          key: 'before',
          span: 8,
        },
        {
          label: '延长时间',
          type: 'select',
          options: getDic('time'),
          rules: [{ required: true, message: '请选择申请时长', trigger: ['blur', 'change'] }],
          key: 'after',
          span: 8,
        },
      ],
    },
  ],
  storage_expand: [
    {
      style: 'margin:0;padding:0;padding-top: 16px;',
      gutter: 40,
      groupItems: [
        {
          label: '变更类型',
          type: 'text',
          key: 'changeTypeCn',
          span: 8,
        },
        {
          label: '变更前',
          type: 'slot',
          key: 'before',
          slotName: 'storageExpand',
          options: getDic('sysDisk'),
          disabled: true,
          span: 8,
        },
        {
          label: '变更后',
          type: 'slot',
          key: 'after',
          slotName: 'storageExpand',
          options: getDic('sysDisk'),
          span: 8,
        },
      ],
    },
  ],
}

const goodsInfoOptions = reactive([
  {
    style: 'margin:0;padding:0;background: rgb(237 245 255);padding-top: 10px;',
    gutter: 40,
    groupItems: [
      {
        label: '资源类型',
        type: 'text',
        key: 'resourceTypeCn',
        span: 8,
      },
      {
        label: '所属云',
        type: 'text',
        key: 'cloudPlatform',
        span: 8,
      },
      {
        label: '资源池',
        type: 'text',
        key: 'resourcePoolName',
        span: 7,
      },
      {
        span: 24,
        render: () => {
          return props.goods.props.map((model, index) => (
            <el-row class="change-row" style="background:#edf5ff;margin:10px 0">
              <el-col span={23}>
                <sl-form
                  key={model}
                  ref={(ref: any) => setRef(model, ref)}
                  size="small"
                  options={propsMapOptions[model.changeType]}
                  modelValue={model}
                >
                  {{
                    storageExpand: ({ form, item }: { form: any; item: any }) => (
                      <div>
                        {form[item.key].map((evs: any, evsIndex: any) => (
                          <el-form-item
                            class="evs-item"
                            key={evsIndex}
                            prop={`${item.key}.${evsIndex}`}
                            rules={[
                              {
                                validator: (rule: any, value: any, callback: any) => {
                                  if (
                                    item.key === 'after' &&
                                    form.before[evsIndex][1] >= form.after[evsIndex][1]
                                  ) {
                                    callback(new Error('变更后需大于变更前大小'))
                                  } else {
                                    callback()
                                  }
                                },
                                trigger: ['blur', 'change'],
                              },
                            ]}
                          >
                            <div class="evs-item-content">
                              <el-select
                                style={{ flex: 1 }}
                                clearable
                                disabled={item.disabled || item.key === 'after'}
                                v-model={form[item.key][evsIndex][0]}
                              >
                                {item.options?.map((option: any) => (
                                  <el-option
                                    key={option.value}
                                    label={option.label}
                                    value={option.value}
                                  />
                                ))}
                              </el-select>
                              <el-input-number
                                disabled={item.disabled}
                                min={form.before[evsIndex][1]}
                                max={2048}
                                step={1}
                                v-model={form[item.key][evsIndex][1]}
                                style={{ margin: '0 4px', minWidth: '90px' }}
                              />
                              <span>GB</span>
                            </div>
                          </el-form-item>
                        ))}
                      </div>
                    ),
                  }}
                </sl-form>
              </el-col>
              <el-col span={1} class="goods-del-col">
                <el-icon
                  onClick={() => handleDelete(props.goods.props, index)}
                  color="red"
                  class="goods-del-btn-l3"
                >
                  <Delete />
                </el-icon>
              </el-col>
            </el-row>
          ))
        },
      },
    ],
  },
])

function handleDelete(props: any, index: number) {
  props.splice(index, 1)
  if (props.length === 0) {
    handleGoodsDelete()
  } else {
    eventBus.emit('propertyChange:updateGoods')
  }
}
</script>
<style scoped>
:deep(.change-row) {
  .goods-del-col {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .goods-del-btn-l3 {
    cursor: pointer;
  }
}

.disaster-recovery-tip {
  margin-left: 8px;
  color: var(--el-color-warning);
  flex-basis: auto;
  position: absolute;
  top: 18px;
}
.goods-del-btn {
  position: absolute;
  top: -7px;
  right: -6px;
  z-index: 10;
  color: var(--el-color-danger);
  font-size: 1.2rem;
  cursor: pointer;
  display: none;
}
.goods-info-form:hover .goods-del-btn {
  display: block;
}

:deep(.evs-item) {
  width: 100%;
  margin-bottom: 15px;
  .evs-item-content {
    width: 100%;
    display: flex;
    align-items: center;
  }
  &:last-child {
    margin-bottom: 0;
  }
  .evs-icons {
    display: flex;
    align-items: center;
    margin-left: 10px;
    .el-icon {
      cursor: pointer;
    }
  }
}
</style>
