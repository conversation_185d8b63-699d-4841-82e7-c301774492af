<template>
  <div class="vpn-detail">
    <sl-page-header
      title="VPN详情"
      :icon="{
        Vnode: Platform,
        color: '#0052D9',
        size: '50px',
      }"
    >
    </sl-page-header>
    <div class="detail-content">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>VPN基本信息</span>
          </div>
        </template>
        <div class="detail-info">
          <p>VPN详情页面待开发</p>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Platform } from '@element-plus/icons-vue'
import SlPageHeader from '@/components/SlPageHeader/index.vue'
</script>

<style lang="scss" scoped>
.vpn-detail {
  padding: 20px;
}

.detail-content {
  margin-top: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-info {
  padding: 20px;
}
</style>
