<template>
  <div class="mric-container">
    <WujieVue
      width="100%"
      height="100%"
      name="mric-sub"
      :url="subAppUrl"
      :sync="true"
      :props="props"
      @beforeLoad="beforeLoad"
      @beforeMount="beforeMount"
      @afterMount="afterMount"
      @beforeUnmount="beforeUnmount"
      @afterUnmount="afterUnmount"
    ></WujieVue>
  </div>
</template>

<script setup lang="ts">
import WujieVue from 'wujie-vue3'
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/modules/user'

const userStore = useUserStore()

const router = useRouter()
const logOut = () => {
  window.location.href = 'login'
}
// 子应用地址
const subAppUrl = ref('http://localhost:8082/mricSubApp') // 替换为实际的子应用URL
// 传递给子应用的数据
const props = {
  data: {
    router,
    logOut,
    token: userStore.token,
  },
}

// 生命周期钩子
const beforeLoad = (url: string) => {
  console.log('[主应用] 子应用开始加载', url)
}

const beforeMount = () => {
  console.log('[主应用] 子应用开始挂载')
}

const afterMount = () => {
  console.log('[主应用] 子应用挂载完成')
}

const beforeUnmount = () => {
  console.log('[主应用] 子应用开始卸载')
}

const afterUnmount = () => {
  console.log('[主应用] 子应用卸载完成')
}
</script>

<style scoped>
.mric-container {
  width: 100%;
  height: 100vh;
  padding: 20px;
}
</style>
