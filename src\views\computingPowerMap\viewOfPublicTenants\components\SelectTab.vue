<template>
  <div id="SelectTab">
    <el-popover :visible="popoverVisible" placement="bottom-end" :width="600" trigger="click">
      <template #reference>
        <div class="select" @click="handleSelectedShow">
          <i class="iconfont menu"></i>
          <span>请选择</span>
        </div>
      </template>
      <div class="list">
        <div class="list-header">
          <div class="list-title">租户：</div>
          <div class="list-switch">
            <span>全选</span>
            <el-switch size="small" v-model="selectAll" />
          </div>
        </div>
        <div class="list-items">
          <div
            class="list-item"
            :class="{ active: selectedIdsRef.includes(system.busiSystemId) }"
            v-for="system in props.dicCollection"
            :key="system.busiSystemId"
            @click="handleSystemItemClick(system.busiSystemId)"
          >
            {{ system.busiSystemName }}
          </div>
        </div>
        <div class="list-footer">
          <el-button @click="popoverVisible = false"> 取消 </el-button>
          <el-button type="primary" @click="handleSubmitSelectedSystemIds"> 确认 </el-button>
        </div>
      </div>
    </el-popover>
    <div class="selected-items">
      <div
        class="selected-item"
        v-for="system in dicCollection.filter((item) => selectedIds.includes(item.busiSystemId))"
        :key="system.busiSystemId"
      >
        <span>{{ system.busiSystemName }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import type { BusinessSystemListType } from '../../tenantView/interface/type'

const props = defineProps<{
  selectedIds: number[]
  dicCollection: BusinessSystemListType[]
}>()

const emit = defineEmits<{
  (e: 'update:selectedIds', ids: number[]): void
  (e: 'change'): void
}>()

const selectedIdsRef = ref<number[]>([])

const selectAll = computed({
  get() {
    return selectedIdsRef.value.length == props.dicCollection.length
  },
  set(value) {
    if (value) {
      const ids = props.dicCollection.map((item) => item.busiSystemId)
      selectedIdsRef.value = ids
    } else {
      selectedIdsRef.value = []
    }
  },
})

const handleSelectedShow = () => {
  popoverVisible.value = true
  selectedIdsRef.value = JSON.parse(JSON.stringify(props.selectedIds))
}

const popoverVisible = ref(false)
const handleSystemItemClick = (id: number) => {
  const index = selectedIdsRef.value.indexOf(id)
  if (index == -1) {
    selectedIdsRef.value.push(id)
  } else {
    selectedIdsRef.value.splice(index, 1)
  }
}

const handleSubmitSelectedSystemIds = () => {
  if (selectedIdsRef.value.length == 0) {
    return ElMessage({
      message: '请至少选择一个租户',
      type: 'warning',
    })
  }
  emit('update:selectedIds', selectedIdsRef.value)
  emit('change')
  popoverVisible.value = false
}
</script>

<style lang="scss" scoped>
#SelectTab {
  display: flex;
}
.select {
  display: flex;
  align-items: center;
  cursor: pointer;
  height: 28px;
  margin-bottom: 5px;
  font-size: 14px;
  color: var(--el-color-primary);
  .menu {
    margin: 0 5px;
  }
}
.list {
  .list-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .list-title {
      font-weight: 600;
    }
    .list-switch {
      display: flex;
      align-items: center;
      & > span {
        margin-right: 5px;
      }
    }
  }
  .list-items {
    display: flex;
    margin-top: 10px;
    flex-wrap: wrap;
    max-height: 300px;
    overflow: scroll;
    &::after {
      content: '';
      flex: auto;
    }
    .list-item {
      height: 30px;
      padding: 0 10px;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #666;
      background-color: #f5f6f8;
      margin-right: 10px;
      margin-bottom: 10px;
      border-radius: 5px;
      cursor: pointer;
      white-space: nowrap;
      &.active {
        color: white;
        background-color: var(--el-color-primary);
      }
    }
  }
  .list-footer {
    text-align: right;
  }
}

.selected-items {
  flex: 1;
  margin-left: 10px;
  display: flex;
  flex-wrap: wrap;
  max-height: 110px;
  overflow: scroll;
  &::-webkit-scrollbar {
    display: none;
  }
  .selected-item {
    height: 28px;
    padding: 0 10px;
    margin-right: 10px;
    margin-bottom: 5px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: white;
    background-color: var(--el-color-primary);
    border-radius: 5px;
    .el-icon {
      opacity: 0.7;
      margin-left: 5px;
      cursor: pointer;
    }
  }
}
</style>
